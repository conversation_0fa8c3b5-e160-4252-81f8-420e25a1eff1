name: Playwright Tests
permissions:
  contents: read
on:
  deployment_status:
env:
  CI: true
jobs:
  test:
    if: github.event.deployment_status.state == 'success'
    timeout-minutes: 60
    runs-on: ubuntu-latest
    env:
      VERCEL_AUTOMATION_BYPASS_SECRET: ${{ secrets.VERCEL_AUTOMATION_BYPASS_SECRET }}
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 18
      - name: Install dependencies
        run: npm ci
      - name: Install Playwright browsers
        run: npx playwright install --with-deps
      - name: Run tests
        run: npx playwright test
        env:
          BASE_URL: ${{ github.event.deployment_status.environment_url }}
      - uses: actions/upload-artifact@v4
        if: ${{ !cancelled() }}
        with:
          name: playwright-report
          path: playwright-report/
          retention-days: 30
