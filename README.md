# FIS Pricing and Billing Frontend

This the frontend for the [FIS Pricing and Billing](https://wiki.fisglobal.com/display/EntPlat/Snow+White) product, owned by the [Automated Finance](https://wiki.fisglobal.com/display/EntPlat/Automated+Finance) team.

## Getting started

> _Before getting started with this repo, read through the [Automated Finance Onboarding](https://wiki.fisglobal.com/display/EntPlat/Automated+Finance+Onboarding) wiki if you haven't already. In particular, you'll need to configure NodeJS to use FIS's root SSL certs._

> _If you are required to log into an admin account to create self-signed certificates and get `SecTrustSettingsSetTrustSetting authorization is denied` errors, raise a mac support request through [Support](https://support.fis.app/home) where you will use the `Permission` application on your device to elevate your user's permissions to be able to `npm run dev` to create self-signed SSL certs and add them to the trusted keychain on your Mac_

Clone the repo:

`<NAME_EMAIL>:bond-tech/af-fpb.git && cd af-fpb`

Copy env file:
`cp .env.sample .env.local`

Install dependencies:

`npm i`

Run the development server:

`npm run dev`

Go to [`http://localhost:3000`](http://localhost:3000) to see the application.

## Contributing

Create a new branch:

`git checkout -b <namespace>/<JIRA-ID>-<short description>`

We use the following branch namespaces to organize our work:

- `feature/` is used for any work that adds, modifies, or removes behavior (includes adding, modifying, or removing tests).
- `fix/` denotes work that fixes unwanted behavior (could be a bug, behavior that didn't match the design, etc.).
- `docs/` indicates work to add, modify, or remove documentation.
- `experiment/` should be used infrequently for explorations that may require input from team members, but may be abandoned.

The short description in the branch name should more-or-less match the short description of the JIRA ticket and the first (or most significant) commit on the branch.

Some example branch names:

```
feature/AFIN-22-product-catalog-layouts-and-routing
fix/AFIN-22-adds-component-displayname
```

> _Branch names use snake-case with Jira ticket IDs in ALL-CAPS._

> _Branches do not need to map 1-to-1 to Jira tickets -- multiple branches can reference the same Jira ticket._

Make changes and commit:

`git commit`

Your commit message should adhere to the following format:

```
<Namespace>: <short description>

Detailed description. PR reviewers will read this description
prior to reviewing your change -- you should briefly describe
what is changing, *why* the change is being made, and any
relevant context that may help a reviewer understand your
change.

The commit message is also useful as a historical record of
our design and implementation decisions. Include links to
relevant design docs, do not be afraid to go into detail
about the requirements driving the change. More context here
is generally better.

[JIRA-ID](https://jira.fis.dev/browse/JIRA-ID)
```

> _The namespace should be the same as the branch._

> _The short description should generally match the branch name -- but if breaking down a larger task into multiple commits on the same, the short descriptions will be more specific than the branch description._

> _A git prepare-commit-msg hook will fill in the namespace and Jira link_

> _Try to [follow the rules](https://cbea.ms/git-commit/)._

Push your commit:

`git push origin -u <branch name>`

Open the link to create a PR, add relevant reviewers, and publish.

Once published, Vercel bot will post a comment on the PR with a link to a preview deployment. Reviewers will use this deployment to review UI changes and test functionality.

### Environments

In vercel all environments use main code but each environment uses different environment variables. This is because we PR into main and we do not want to merge from main into lower environments and the main differentiation between the environments is just Data.

- prod
- preprod
- test

### Writing tests

First, install [Playwright](https://playwright.dev/) browsers and dependencies with:

`npx playwright install --with-deps`

Then run the tests:

`npm run e2e`

Test cases can be found in the `/test` directory. Page objects (classes used to encapsulate the functionality of specific pages, making test cases easier to read and write) are located under `/src/app` alongside the specific page they model.

See the [Playwright docs](https://playwright.dev/docs/writing-tests) for tips on writing and debugging tests.

### Updating OpenApi schema

This project uses [`openapi-typescript`](https://openapi-ts.dev/) to generate
TypeScript types from an OpenApi schema definition. This definition is authored
in the backend repository and any changes must be manually copied into this
repository (at least for now -- this could be automated in the future).

The source-of-truth schema is found here [/fpb/src/main/resources/static/openapi.json](https://bitbucket.fis.dev/projects/AFIN/repos/af_fpb/browse/fpb/src/main/resources/static/openapi.json)

Whenever a change is made to the OpenApi schema, first:

`cp openapi.json src/api/schema.json`

This places the new schema file in the correct location in this repo.

`npm run codegen`

This runs `openapi-typescript` to produce a corresponding `schema.d.ts` file.

Depending on the nature of the OpenApi schema change, there may then be code
changes required to satisfy the compiler.

> Note: The [OpenApi extention](https://marketplace.visualstudio.com/items?itemName=42Crunch.vscode-openapi) may be useful for viewing the JSON schema file.

### Runtime `zod` schemas

We use [zod](https://zod.dev/) to define runtime schemas for form validation and
to transform API data to be compatible with TanStack Form, and to transform form
data back into the API format.

We use the OpenApi schema to generate these zod schemas. Whenever the OpenApi
schema is updated, we'll want to also update these zod schemas with:

`npm run schemagen`

These schemas form a basis for implementing additional validations, for example:

```ts
const minMyField = formToApiSchemas.myObject.shape.myNumberField.pipe(
  z.number().min(1),
)
```

This derived schema could then be used to validate a form field.

### How to populate local db instance with test data

Make sure that your local postgress up and running and you followed all the onboarding steps from backend repo: https://bitbucket.fis.dev/projects/AFIN/repos/af_fpb/browse

Fill local instance with test data:

```sh
npm run dbinsert
```

Cleanup local instance:

```sh
npm run dbcleanup
```

Re-generate SQL scripts:

```sh
npm run sqlgen
```

## Branching model

We use a simplified version of the [git-flow](https://nvie.com/posts/a-successful-git-branching-model/) branching strategy.

```
          ┌───────────────┐     ┌─────────┐      ┌────────┐
 Time     │ task branches │     │ staging │      │  main  │
          └───────────────┘     └─────────┘      └────────┘
   │                                 │                │
   │                                 │                │
   │                                 ▼                │
   │                                 ●                │
   │                                ╱│                │
   │               ╱───────────────╱ │                │
   │  feature/... ▼                  │                │
   │              ●                  │                │
   │              │                  │                │
   │              ▼                  │                │
   │              ●                  │                │
   │              │                  │                │
   │              ▼                  │                │
   │              ●                  │                │
   │               ╲  ci runs on     │                │
   │                ╲─task branch───╲│                │
   │                                 ▼                │
   │                                 ●   pr to merge staging -> main
   │                                ╱ ╲  (no changes necessary for
   │               ╱───────────────╱ │ ╲─approval)───╲│
   │  feature/... ▼                  │                ▼
   │              ●                  │                ●
   │               ╲                 │                │
   │                ╲───────────────╲│                │
   │                                 ▼                │
   │                 changes         ●                │
   │                 necessary for  ╱│                │
   │               ╱─staging -> ───╱ │                │
   │      fix/... ▼  main approval   │                │
   │              ●                  │                │
   │              │               pr to merge         │
   │              ▼               staging -> main     │
   │              ●                  │                │
   │              │                  │                │
   │              ▼                  │                │
   │              ●                  │                │
   │               ╲                 │                │
   │                ╲───────────────╲│                │
   │                                 ●                │
   │                                  ╲               │
   │                                   ╲─────────────╲│
   │                                                  ▼
   ▼                                                  ●
```

This approach eliminates release branches and runs the full CI suite on every task (i.e. feature) branch. Every commit to the `staging` branch is a candidate for promotion to the `main` branch. Every commit to the `main` branch is deployed to production.

To promote a commit from `staging` to `main`, open a PR to get the required approvals (e.g. from UX, QA, etc.). If additional changes are necessary to obtain those approvals, those should be implemented in a separate task branch and merged to `staging` (just like any other task branch).

### Follow these important guidelines:

1. **Feature branches should be short-lived.** They should contain one primary commit, with additional commits to address PR feedback. Feature branches should live for days-to-weeks, not weeks-to-months. If a feature branch lives too long, it's scope was too large.

2. **Merge to `prod` often, ideally after every commit to `staging`.** Do not let commits build up in `staging` -- each commit represents more risk when deploying to `prod`.

## Application design

### Routing

The web and browsers were designed under the assumption that every distinct view presented to a user is identified by a unique URL. In applications that make use of client-side routing (such as this one), developers have the opportunity to break that assumption. Doing so is rarely justified.

As a general rule of thumb, **every distinct view should have its own route.** This allows users to share links, reload without losing their place, use the browser's back/forward buttons, etc.

Sometimes there will be a judgement call involved -- e.g. should a particular modal get its own route -- but the question should always be "Why does this _not_ need a route?" rather than "Why does this need a route?"

## Key Technology & Tools

### Preface

The largest learning curve for this repo for backend devs & angular devs apart from React would be type safety, how to fetch data, and styling with Tailwind.

React is unopinionated out of the box, so developers must use or create tools to solve problems.

[The tools listed below are very standard within the ecosystem so finding documentation and seeing what problems they solve is encouraged](https://dev.to/avinashvagh/react-ecosystem-in-2024-418k)

We do not leverage a state management tool outside of react because we simply don't need to since React Query invalidation and rehydration on Mutation is powerful enough for us. The only state heavy part within our application is the amount of form state which is handled by Tanstack Form

### NextJS

We use NextJS primarily for its file based routing and ability to define layouts.

NextJS features we don't use as default:

- server side fetching & suspense
- server actions

We do not buy into the NextJS server features as heavily as the client due to FIS Security constraints and having NextJS be purely a Frontend, not a Backend For Frontend.

### React

We use React as the way to update the DOM and basic state management.

React now allows for basic state management by default using context. If you see yourself [prop drilling](https://www.freecodecamp.org/news/prop-drilling-in-react-explained-with-examples/), refactor to use context to make it easier to memoize components. We do not have a heavily "state" driven application outside of forms so we do not need to reach for many outside of React state management tools. If there were to be a situation, our recommendation is Zustand.

### Zod

Validates data at runtime, ensuring that the data conforms to the expected types.

Ensures that form inputs are of the correct type, preventing issues like submitting a number where a string is expected.

Data transformation to API types. Converts data to the required API types, such as transforming a Date object to a string format suitable for JSON.

### Tanstack Form

Provides APIs for subscribing to changes in form data, allowing for reactive updates.

Uses Zod to validate form data, ensuring type safety and correctness.

Manages the state of forms, including handling input values, validation errors, and submission status.

### Tailwind

Uses utility classes to apply styles directly in the HTML, making it easy to create responsive designs.

Allows for extensive customization of styles through configuration files, enabling consistent design across the application.

Reduces the need for writing and managing custom CSS through a concept called Atomic Styles by providing a comprehensive set of pre-defined classes.

### Headless UI

Provides components without default styles, allowing developers to apply their own styles using Tailwind CSS.

Ensures that components are accessible out of the box, adhering to best practices for web accessibility.

Offers flexibility in styling and behavior, enabling developers to conform to branding and design standards.

### Tanstack Query

Manages fetch requests, handling success, error, and loading states automatically.
Caches data to improve performance and reduce unnecessary network requests.

Embeds requests into a queryClient, allowing TypeScript to validate the correct fields and types of our OpenAPI schema

Query is used for fetching data from the server.

Mutation is used for sending data to the server to create, update, or delete resources.

Provides built-in mechanisms for handling errors, reducing the need for raw fetch or axios requests.

Example [repo](https://github.com/AlexRapala/react-query-fun) and [demo](https://react-query-fun.vercel.app)

### Type safety

Type safety generally means that the types generated from the backend Java application are equal and just as rigourous. This is enforced through the OpenAPI Schema generated by the backend. From there, we run `npm run schemagen` to output types such as `Account`. Since this is a stub that can (and will) change on the backend, we need to make sure if there is a breaking change that it breaks and tells us where we need to update within the frontend. And acts as a check to make sure things that return an `Account` return the same data as other endpoints returning an `Account`
