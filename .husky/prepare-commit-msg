#!/usr/bin/env sh

commit_file=$1
commit_source=$2

# Prepends the given message to the default commit message (which includes helpful
# stuff like the branch name and list of files to be committed). We remove the
# first four lines of the default message, as they're made redundant by our message.
write_commit_msg() {
    sed -i.bak '1,4d' "$commit_file"
    echo "$1\n$(cat $commit_file)" > $commit_file
}

if [ "$commit_source" != "" ]; then
  echo "Using default commit message from $commit_source"
  exit 0
fi;

# See README for a full description of git branch naming conventions.
branch_name=$(git rev-parse --abbrev-ref HEAD)

# Use default commit message for branches that don't match the naming conventions.
matching_branch_name=$(echo $branch_name | sed -E 's/^(feature|fix|docs|experiment)\/.+$//')
if [ "$matching_branch_name" != "" ]; then
  echo "Using default commit message because branch is not a recognized feature branch."
  exit 0
fi;

# For commits after the first commit, provide a simpler prompt. We assume subsequent
# commits on a feature branch are PR revisions that typically require only the
# short description (as these will be merged into the final merge commit message)
prior_commits=$(git rev-list main..$branch_name -1)
if [ "$prior_commits" != "" ]; then
  message="# Add a short description of your revisions.
# 50 characters is here:                         |
"
  write_commit_msg "$message"
  exit 0
fi;

# Matches: (namespace)/* and capitalizes the first letter. Since "feature" is by
# far the most common namespace, we won't use up message characters printing
# "Feature:" for every commmit -- only Fix, Docs, Experiment will appear in the message.
branch_namespace=$(echo $branch_name | sed -E 's/^(.+)\/.*/\1:/' | awk '{$1=toupper(substr($1,0,1))substr($1,2)}1')
if [ "$branch_namespace" = "Feature:" ]; then
  branch_namespace=''
fi;

# Matches: */(JIRA-123)*
ticket_id=$(echo $branch_name | sed -E 's/.*\/([A-Z]+-[0-9]+).*/\1/')
if [ "$ticket_id" = "$branch_name" ]; then
  ticket_link=""
else
  ticket_link="

# Link to Jira:
[$ticket_id](https://jira.fis.dev/browse/$ticket_id)"
fi;

message="# Add your commit message below.
# 
# This message will be used as the PR description.
#
# Please [follow the rules](https://cbea.ms/git-commit/).
#
# Add a short title.
# 50 characters is here:                         |
$branch_namespace

# Add your full description below.
# 72 characters is here:                                               |
$ticket_link
"

write_commit_msg "$message"