import { InvestableBalanceFormSchema } from '@/app/[bankId]/configuration/investable-balance/_components/InvestableBalanceFormTypes'
import { match } from '@/lib/unions/match'
import { variant } from '@/lib/unions/Union'
import { hasMaxDecimalPlaces } from '@/lib/validation/hasMaxDecimalPlaces'
import { z } from 'zod'

/**
 * Common
 */

export const requiredString = z
  .string({ invalid_type_error: 'Required' })
  .min(1, 'Required')

export const alphanumericString = requiredString.regex(
  /^[a-z0-9]+$/i,
  'May only contain numbers and letters',
)

export const alphaString = requiredString.regex(
  /^[a-z]+$/i,
  'May only contain letters',
)

export const number = z.coerce.number({
  invalid_type_error: 'Must be a valid number',
})
export const int = number.int('Must be a whole number')
export const threeDigitNumber = int
  .positive('Must be a postive number')
  .max(999, 'Must contain at most 3 digits')

export const code = (min = 3, max = 10) =>
  alphanumericString
    .min(min, `Must contain at least ${min} characters`)
    .max(max, `Must contain at most ${max} characters`)

export const name = requiredString.max(50, 'Must contain at most 50 characters')

export const dateSchema = z.coerce.date()
export const effectiveDate = dateSchema

export const effectiveDateMonth = z.preprocess((val) => {
  if (typeof val === 'string' && val.match(/^[0-9]{4}-[0-9]{2}$/)) {
    const parts = val.split('-')
    return new Date(Date.UTC(parseInt(parts[0]), parseInt(parts[1]) - 1))
  }
  return val
}, effectiveDate) as unknown as z.ZodDate // hack to get type inference to work

export const stringToNumber = z.string().pipe(z.coerce.number())

export const fee = z
  .string()
  .pipe(z.coerce.number().gt(0).max(1_000_000_000))
  .nullable()

export const units = (min: 0 | 1) => z.string().pipe(int.min(min).max(10))

export const percentage = z
  .string()
  .pipe(z.coerce.number().min(0).max(100))
  .superRefine(hasMaxDecimalPlaces(4))

/**
 * Accounts
 */

export const applicationId = z.enum(['C', 'D'])

export const accountNumber = requiredString
  .max(20, 'Must contain at most 20 digits.')
  .regex(/^[0-9]+$/i, 'May only contain numbers.')

export const shortName = requiredString
  .min(3, 'Must contain at least 3 characters')
  .max(24, 'Must contain at most 24 characters')

export const addressApplicationId = z.enum(['A', 'D'])

export const zipCode = z
  .string({ invalid_type_error: 'Required' })
  .regex(/^[0-9]{5}$/, 'Invalid ZIP code')

/**
 * Services and pricing
 */

export const pricingHierarchyEntryType = z.enum([
  'STANDARD',
  'PRICE_LIST', // This may be demographic or promotional - the price list itself determines that
  'OVERRIDE', // This may be on a composite or deposit account - the account itself determines that
])

export const serviceType = z.enum([
  'VOLUME_BASED',
  'RECURRING',
  'BALANCE_BASED',
  'PRE_PRICED',
  'SERVICE_SET',
])

export const serviceTypeLabel = (value: z.infer<typeof serviceType>) =>
  match(variant(value), {
    VOLUME_BASED: () => 'Volume based',
    RECURRING: () => 'Recurring',
    BALANCE_BASED: () => 'Balance based',
    PRE_PRICED: () => 'Pre priced',
    SERVICE_SET: () => 'Service set',
  })

export type ServiceType = z.infer<typeof serviceType>

export const priceType = z.enum([
  'NOT_PRICED',
  'UNIT_PRICED',
  'FLAT_FEE',
  'THRESHOLD_TIER',
  'PARTITIONED_TIER',
  'PERCENTAGE',
  'INDEXED',
  'OUTSIDE_PRICE',
])

export type PriceType = z.infer<typeof priceType>

export const subPriceType = priceType.extract([
  'UNIT_PRICED',
  'FLAT_FEE',
  'PERCENTAGE',
  'INDEXED',
])

export type SubPriceType = z.infer<typeof subPriceType>

export const priceTypeLabel = (value: PriceType) =>
  match(variant(value), {
    NOT_PRICED: () => 'Not priced' as const,
    UNIT_PRICED: () => 'Unit priced' as const,
    FLAT_FEE: () => 'Flat fee' as const,
    THRESHOLD_TIER: () => 'Threshold tier' as const,
    PARTITIONED_TIER: () => 'Partitioned tier' as const,
    PERCENTAGE: () => 'Percentage' as const,
    INDEXED: () => 'Indexed' as const,
    OUTSIDE_PRICE: () => 'Outside price' as const,
  })

export type PriceTypeLabel = ReturnType<typeof priceTypeLabel>

export const disposition = z.enum([
  'ANALYSED',
  'HARD_CHARGE',
  'IMMEDIATE_CHARGE',
  'WAIVED',
])

export const currency = z.literal('USD')

export type Currency = z.infer<typeof currency>

export type Disposition = z.infer<typeof disposition>

export const dispositionLabel = (value: z.infer<typeof disposition>) =>
  match(variant(value), {
    ANALYSED: () => 'Analyzed' as const,
    HARD_CHARGE: () => 'Hard charge' as const,
    IMMEDIATE_CHARGE: () => 'Immediate charge' as const,
    WAIVED: () => 'Waived' as const,
  })

export type DispositionLabel = ReturnType<typeof dispositionLabel>

export const costType = z.enum(['NO_COST', 'UNIT_COST', 'FLAT_COST'])

export const costTypeLabel = (value: z.infer<typeof costType>) =>
  match(variant(value), {
    NO_COST: () => 'No cost',
    UNIT_COST: () => 'Unit cost',
    FLAT_COST: () => 'Flat cost',
  })

export const balanceType = z.enum([
  'AVERAGE_COLLECTED',
  'AVERAGE_LEDGER',
  'AVERAGE_NEGATIVE_COLLECTED',
  'AVERAGE_NEGATIVE_LEDGER',
  'AVERAGE_POSITIVE_COLLECTED',
  'AVERAGE_POSITIVE_LEDGER',
  'AVERAGE_UNCOLLECTED_FUNDS',
  'COMPENSATING_BALANCE',
  'END_OF_MONTH_LEDGER',
  'INVESTABLE_BALANCE',
  'AVERAGE_FLOAT',
  'AVERAGE_CLEARINGHOUSE_FLOAT',
  'REQUIRED_BALANCE',
])

type BalanceType = z.infer<typeof balanceType>

export const baseBalanceTypeLabel = (
  value: z.infer<typeof InvestableBalanceFormSchema.shape.baseBalanceType>,
) =>
  match(variant(value), {
    AVERAGE_COLLECTED: () => 'Average Collected',
    AVERAGE_LEDGER: () => 'Average Ledger',
    AVERAGE_NEGATIVE_COLLECTED: () => 'Average Negative Collected',
    AVERAGE_NEGATIVE_LEDGER: () => 'Average Negative Ledger',
    AVERAGE_POSITIVE_COLLECTED: () => 'Average Positive Collected',
    AVERAGE_POSITIVE_LEDGER: () => 'Average Positive Ledger',
    AVERAGE_UNCOLLECTED_FUNDS: () => 'Average Uncollected Funds',
    COMPENSATING_BALANCE: () => 'Compensating Balance',
    END_OF_MONTH_LEDGER: () => 'End of Month Ledger',
    INVESTABLE_BALANCE: () => 'Investable Balance',
    AVERAGE_FLOAT: () => 'Average Float',
    AVERAGE_CLEARINGHOUSE_FLOAT: () => 'Average Clearinghouse Float',
    REQUIRED_BALANCE: () => 'Required Balance',
  })

export const applyServiceTo = z.enum([
  'SELECT_DEPOSIT_ACCOUNTS',
  'ALL_DEPOSIT_ACCOUNTS',
])

export const basisDays = z.enum(['365', '366', '360'])

export type BasisDays = z.infer<typeof basisDays>

export const costCenter = z
  .string({ invalid_type_error: 'Required' })
  .min(1, 'Required.')
  .max(7, 'Must contain at most 7 digits.')

export const officerCode = z
  .string({ invalid_type_error: 'Required' })
  .min(1, 'Required.')
  .max(7, 'Must contain at most 7 digits.')
  .regex(/^[0-9]+$/i, 'May only contain numbers.')

/**
 * Configurations
 */

export const configCode = z.coerce
  .string({ invalid_type_error: 'Required' })
  .min(1, 'Required')
  .max(5, 'Must contain at most 5 digits.')
  .regex(/^[0-9]+$/i, 'May only contain numbers.')
