import { entries } from '@/lib/functional/entries'
import fs from 'node:fs'
import openapiTS, {
  ArraySubtype,
  BooleanSubtype,
  IntegerSubtype,
  NumberSubtype,
  ObjectSubtype,
  ReferenceObject,
  SchemaObject,
  StringSubtype,
} from 'openapi-typescript'

type SchemaNode = {
  name: string
  contentString: string
  dependencies: Set<string>
  schemaObject: SchemaObject
  schemaContent: SchemaContent
}
type SchemaGraph = Map<string, SchemaNode>

type SchemaContent = {
  zodValidator: string
  transformerFields?: Record<string, string>
  defaultValue?: string
  defaultValueOmittedFields?: string[]
  defaultValueCustomTypes?: Record<string, string>
  // TODO remove deprecated schemas once all references are removed
  zodDeprecated: string
}

const nullToUndefinedName = `nullToUndefined`
const nullToUndefined = `const ${nullToUndefinedName} = <T>(v: T) => v === null ? undefined : v`

const undefinedToNullName = `undefinedToNull`
const undefinedToNull = `const ${undefinedToNullName} = <T>(v: T) => v === undefined ? null : v`

/**
 * Uses the openapi schema to generate zod schemas that can be used at runtime
 * to convert between API data and Form data.
 *
 * Data obtained from or sent to APIs are not exactly the same as the data we
 * need to populate Forms.
 *
 * This script generates zod schemas with transforms that can convert between
 * API and Form data.
 *
 * A basic example:
 *
 * ```tsx
 * import { apiToFormSchemas } from '@/api/apiToFormSchema'
 * import { formToApiSchemas, formValidators, FooForm } from '@/api/formToApiSchema'
 *
 * function MyComponent() {
 *   const { data } = useQuery(query('/getFoo'))
 *   const { mutate } = useMutation(mutation('/updateFoo'))
 *
 *   const form = useForm<FooForm>({
 *     defaultValues: apiToFormSchemas.foo.parse(data),
 *     onSubmit: ({ value }) => {
 *       mutate(formToApiSchemas.foo.parse(value))
 *     }
 *     validators: {
 *       onChange: formValidators.foo,
 *       onSubmit: formValidators.foo,
 *     }
 *   })
 * }
 * ```
 */
async function main(direction: 'apiToForm' | 'formToApi') {
  const seen = new Set<string>()
  const schemaGraph: SchemaGraph = new Map()

  await openapiTS(new URL('./schema.json', import.meta.url), {
    transform: (schemaObject, metadata) => {
      if (!metadata.path?.includes('components/schemas')) return undefined

      const schemaName = metadata.path.split('/').slice(-1)[0]
      const schemaNameCamelCase = toCamelCase(schemaName)
      // only used in 'formToApi'
      const formValidatorName = `${schemaNameCamelCase}Validator`
      const formTransformerName = `${schemaNameCamelCase}Transformer`

      if (schemaObject.type !== 'object') return undefined
      if (seen.has(schemaName)) return undefined
      seen.add(schemaName)

      const visit = (
        s: SchemaObject | ReferenceObject,
        dependencies: Set<string>,
        notRequiredSuffix: string,
        transformSuffix: string,
        parent?: SchemaObject,
        nameUnderParent?: string,
      ): SchemaContent => {
        if (isReferenceObject(s)) {
          const referenceName = getReferenceName(s)
          dependencies.add(referenceName)
          return {
            zodValidator: `${toCamelCase(referenceName)}Validator`,
            defaultValue: `default${referenceName}`,
            // TODO remove deprecated schemas once all references are removed
            zodDeprecated: `${toCamelCase(referenceName)}Deprecated`,
          }
        }

        if (isObjectSchema(s)) {
          const properties = Object.fromEntries(
            entries(s.properties!).map(([name, value]) => [
              name,
              {
                content: visit(
                  value,
                  dependencies,
                  notRequiredSuffix,
                  transformSuffix,
                  s,
                  name as string,
                ),
                value,
              },
            ]),
          )

          const transformerFields: Record<string, string> = {}

          const zodValidator = `z.object({\n  ${entries(properties)
            .map(([name, { content, value }]) => {
              const notRequired = !isRequired(s, name as string)

              const fieldSchema = `${content.zodValidator}${notRequired ? notRequiredSuffix : ''}`

              if (isReferenceObject(value)) {
                // If value is a reference object, validator schema must use reference's transformer schema.
                transformerFields[name] =
                  `${toCamelCase(getReferenceName(value))}Transformer${notRequired ? `${notRequiredSuffix}${transformSuffix}` : ''}`
              } else if (
                isArraySchema(value) &&
                value.items &&
                !Array.isArray(value.items) &&
                isReferenceObject(value.items)
              ) {
                // If value is an array schema where the item type is a reference,
                // transformer schema must use reference's transformer schema as the array item type.
                transformerFields[name] =
                  `z.array(${toCamelCase(getReferenceName(value.items!))}Transformer)${notRequired ? `${notRequiredSuffix}${transformSuffix}` : ''}`
              } else if (notRequired) {
                // If field is optional, transformer schema must transform `nulls`s into `undefined`s
                transformerFields[name] =
                  `${formValidatorName}.shape.${name}${transformSuffix}`
              }

              return `${name}: ${fieldSchema},`
            })
            .join('\n  ')}\n})`

          const defaultValue = `{\n  ${entries(properties)
            .filter(
              ([
                name,
                {
                  content: { defaultValue },
                },
              ]) => {
                return typeof defaultValue !== 'undefined'
              },
            )
            .map(
              ([
                name,
                {
                  content: { defaultValue },
                },
              ]) => {
                return `${name}: ${defaultValue},`
              },
            )
            .join('\n  ')}\n}`

          const omittedFields = entries(properties)
            .filter(
              ([
                _,
                {
                  content: { defaultValue },
                },
              ]) => typeof defaultValue === 'undefined',
            )
            .map(([name]) => name as string)

          // TODO remove deprecated schemas once all references are removed
          const zodDeprecated = `z.object({\n  ${entries(properties)
            .map(([name, { content, value }]) => {
              // deprecated behavior skips propagating "optional" logic to references & arrays
              const shouldNotBeTransformed =
                isReferenceObject(value) ||
                isArraySchema(value) ||
                isObjectSchema(value) ||
                isRequired(s, name as string)

              return (
                `${name}: ${content.zodDeprecated}` +
                `${shouldNotBeTransformed ? '' : notRequiredSuffix},`
              )
            })
            .join('\n  ')}\n})`

          return {
            zodValidator,
            transformerFields,
            defaultValue,
            defaultValueOmittedFields:
              omittedFields.length > 0 ? omittedFields : undefined,
            // TODO remove deprecated schemas once all references are removed
            zodDeprecated,
          }
        }

        if (isArraySchema(s) && s.items && !Array.isArray(s.items)) {
          return {
            zodValidator: `z.array(${visit(s.items, dependencies, '', '').zodValidator})`,
            defaultValue: '[]',
            // TODO remove deprecated schemas once all references are removed
            zodDeprecated: `z.array(${visit(s.items, dependencies, '', '').zodDeprecated})`,
          }
        }

        if (isEnumSchema(s)) {
          const enumSchema = `z.enum([\n    ${s.enum
            ?.map((value) => `'${value}'`)
            .join(',')}\n  ])`
          return {
            zodValidator: enumSchema,
            defaultValue:
              shouldOmitDefaultValue(parent, nameUnderParent) ? undefined : (
                'null'
              ),
            // TODO remove deprecated schemas once all references are removed
            zodDeprecated: enumSchema,
          }
        }

        if (isStringSchema(s)) {
          const stringSchema = `z.string()${s.format === 'date' ? '.date()' : ''}`
          return {
            zodValidator: stringSchema,
            defaultValue: `''`,
            // TODO remove deprecated schemas once all references are removed
            zodDeprecated: stringSchema,
          }
        }

        if (isNumberSchema(s)) {
          const defaultValue =
            shouldOmitDefaultValue(parent, nameUnderParent) ? undefined : 'null'

          let numberSchema =
            direction === 'formToApi' ?
              `z.string().pipe(z.coerce.number())`
            : `z.number().transform((v) => v.toFixed(4))`

          if (toFixedSix.some((name) => nameUnderParent === name)) {
            numberSchema =
              direction === 'formToApi' ?
                `z.string().pipe(z.coerce.number())`
              : `z.number().transform((v) => v.toFixed(6))`
          }
          if (toFixedTwo.some((name) => nameUnderParent === name)) {
            numberSchema =
              direction === 'formToApi' ?
                `z.string().pipe(z.coerce.number())`
              : `z.number().transform((v) => v.toFixed(2))`
          }

          return {
            zodValidator: numberSchema,
            defaultValue,
            // TODO remove deprecated schemas once all references are removed
            zodDeprecated: numberSchema,
          }
        }

        if (isIntegerSchema(s)) {
          const intSchema = `z.number().int()`
          return {
            zodValidator: intSchema,
            defaultValue:
              shouldOmitDefaultValue(parent, nameUnderParent) ? undefined : (
                'null'
              ),
            // TODO remove deprecated schemas once all references are removed
            zodDeprecated: intSchema,
          }
        }

        if (isBooleanSchema(s)) {
          const booleanSchema = `z.boolean()`
          return {
            zodValidator: booleanSchema,
            defaultValue:
              shouldOmitDefaultValue(parent, nameUnderParent) ? undefined : (
                'null'
              ),
            // TODO remove deprecated schemas once all references are removed
            zodDeprecated: booleanSchema,
          }
        }

        return { zodValidator: '', defaultValue: '', zodDeprecated: '' }
      }

      const dependencies = new Set<string>()

      const notRequiredSuffix =
        direction === 'formToApi' ? '.nullable()' : '.optional().nullable()'
      const transformSuffix =
        direction === 'formToApi' ?
          `.transform(${nullToUndefinedName})`
        : `.transform(${undefinedToNullName})`

      const schemaContent = visit(
        schemaObject,
        dependencies,
        notRequiredSuffix,
        transformSuffix,
      )

      const validatorSchemaDefinition = `const ${formValidatorName} = ${schemaContent.zodValidator}`
      let contentString = `\n\n${validatorSchemaDefinition}`

      let transformerSchemaDefinition = `const ${formTransformerName} = ${schemaNameCamelCase}Validator`
      if (
        schemaContent.transformerFields &&
        Object.keys(schemaContent.transformerFields).length > 0
      ) {
        transformerSchemaDefinition =
          `const ${formTransformerName} = z.object({\n` +
          `  ...${formValidatorName}.shape,\n` +
          `  ${entries(schemaContent.transformerFields)
            .map(([fieldName, fieldSchema]) => `${fieldName}: ${fieldSchema},`)
            .join('\n  ')}\n` +
          '})'
      }
      contentString += `\n${transformerSchemaDefinition}`

      if (direction === 'formToApi') {
        const schemaInputType = `export type ${schemaName}Form = z.input<typeof ${formTransformerName}>`
        const schemaOutputType = `export type ${schemaName} = z.infer<typeof ${formTransformerName}>`
        contentString += `\n${schemaInputType}\n${schemaOutputType}`
      }

      // TODO remove deprecated schemas once all references are removed
      const deprecatedSchemaDefinition = `const ${schemaNameCamelCase}Deprecated = ${schemaContent.zodDeprecated}`
      contentString += `\n${deprecatedSchemaDefinition}`

      schemaGraph.set(schemaName, {
        name: schemaName,
        contentString,
        dependencies,
        schemaObject,
        schemaContent,
      })
    },
  })

  const transformFunction =
    direction === 'formToApi' ? nullToUndefined : undefinedToNull

  const output =
    `// This file is generated via the \`npm run schemagen\` command.\n//\n` +
    `// That command runs the \`./generateZodSchemas.ts\` script.\n` +
    `// Edit that file and not this one.\n\n` +
    `import { z } from 'zod'\n\n` +
    `${transformFunction}` +
    `${writeSchemaGraph(schemaGraph, direction)}\n\n` +
    `export const ${direction}Schemas = {\n  ${Array.from(schemaGraph.keys())
      .map((name) => `${toCamelCase(name)}: ${toCamelCase(name)}Transformer,`)
      .join('\n  ')}\n}` +
    (direction === 'formToApi' ?
      `\n\nexport const formValidators = {\n  ${Array.from(schemaGraph.keys())
        .map((name) => `${toCamelCase(name)}: ${toCamelCase(name)}Validator,`)
        .join('\n  ')}\n}` +
      `\n\nexport const defaultValues = {\n  ${Array.from(schemaGraph.keys())
        .map((name) => `${toCamelCase(name)}: default${name},`)
        .join('\n  ')}\n}`
    : '') +
    // TODO remove deprecated schemas once all references are removed
    `\n\n/**\n * @deprecated we should remove all references to these\n */\nexport const deprecated${toTitleCase(direction)}Schemas = {\n  ${Array.from(
      schemaGraph.keys(),
    )
      .map((name) => `${toCamelCase(name)}: ${toCamelCase(name)}Deprecated,`)
      .join('\n  ')}\n}`

  fs.writeFileSync(
    new URL(
      direction === 'formToApi' ?
        './formToApiSchema.ts'
      : './apiToFormSchema.ts',
      import.meta.url,
    ),
    output,
  )
}

/**
 * Put the graph into topological order.
 */
function writeSchemaGraph(
  graph: SchemaGraph,
  direction: 'apiToForm' | 'formToApi',
): string {
  const visiting = new Set<string>()
  const visited = new Set<string>()
  const result: string[] = []
  const visit = (node?: SchemaNode) => {
    if (!node || visited.has(node.name)) return
    if (visiting.has(node.name)) throw new Error('Circular dependency')
    visiting.add(node.name)
    for (const dependency of node.dependencies.values()) {
      visit(graph.get(dependency))
    }
    result.push(node.contentString)
    if (direction === 'formToApi')
      // had to move generating "default values" content from initial traversal to here
      // (where we are traversing in topological order) so that we can resolve references
      // to determine whether a given Schema needs a custom `Default{Schema}Form` type
      result.push(writeDefaultValuesContent(node, graph))
    visiting.delete(node.name)
    visited.add(node.name)
  }
  for (const node of graph.values()) visit(node)
  return result.join('')
}

function toCamelCase(s: string): string {
  return s[0].toLowerCase() + s.slice(1)
}

function toTitleCase(s: string): string {
  return s[0].toUpperCase() + s.slice(1)
}

function isArraySchema(
  value: SchemaObject,
): value is SchemaObject & ArraySubtype {
  return value.type === 'array'
}

function isBooleanSchema(
  value: SchemaObject,
): value is SchemaObject & BooleanSubtype {
  return value.type === 'boolean'
}

function isObjectSchema(
  value: SchemaObject,
): value is SchemaObject & ObjectSubtype {
  return value.type === 'object'
}

function isStringSchema(
  value: SchemaObject,
): value is SchemaObject & StringSubtype {
  return value.type === 'string'
}

function isEnumSchema(
  value: SchemaObject,
): value is SchemaObject & StringSubtype & { enum: string[] } {
  return isStringSchema(value) && 'enum' in value
}

function isNumberSchema(
  value: SchemaObject,
): value is SchemaObject & NumberSubtype {
  return value.type === 'number'
}

function isIntegerSchema(
  value: SchemaObject,
): value is SchemaObject & IntegerSubtype {
  return value.type === 'integer'
}

function isReferenceObject(
  value: SchemaObject | ReferenceObject,
): value is ReferenceObject {
  return '$ref' in value
}

function getReferenceName(value: ReferenceObject) {
  return value.$ref.split('/').slice(-1)[0]
}

function isRequired(schema: SchemaObject, name: string): boolean {
  return !!schema.required?.includes(name)
}

/**
 * Determine whether we should omit this field from "default" schema form value.
 * Invoke this for open api schema types where we can't safely assume a default value, e.g.,
 * isEnumSchema, isNumberSchema, isIntegerSchema, isBooleanSchema.
 *
 * @param parent
 * @param nameUnderParent
 * @returns
 */
function shouldOmitDefaultValue(
  parent?: SchemaObject,
  nameUnderParent?: string,
) {
  return parent && nameUnderParent && isRequired(parent, nameUnderParent)
}

/**
 * Generate code to for the "default" schema form values for the schema represented by `node`.
 * If necessary, invokes `generateDefaultValueType` to generate code for a custom default value type.
 *
 * @param node
 * @param graph
 * @returns
 */
function writeDefaultValuesContent(
  node: SchemaNode,
  graph: SchemaGraph,
): string {
  let defaultValuesContent = '\n'
  let schemaDefaultValuesType = `${node.name}Form`
  if (needsCustomDefaultValueType(node, graph)) {
    defaultValuesContent += generateDefaultValueType(node, graph)
    schemaDefaultValuesType = `Default${node.name}Form`
  }
  defaultValuesContent += `const default${node.name}: ${schemaDefaultValuesType} = ${node.schemaContent.defaultValue}`
  return defaultValuesContent
}

/**
 * Determines whether the schema represented by `node` requires us to generate a custom default value type.
 * As a side effect, this function recursively populates `defaultValueCustomTypes` on the `node`'s `schemaContent`
 * in order to make this determination.
 *
 * We need to generate a custom default value type for the schema:
 * - if the schema has any fields that are required
 * but we can't safely assume a default value (see where `shouldOmitDefaultValue` is invoked), in which case,
 * we omit the field from the default values, forcing the consumer of the schema to supply default values on a
 * case-by-case basis, which is much safer.
 * - or if the schema has any fields that are references to schemas that require a custom default value type
 *
 * @param node
 * @param graph
 * @returns
 */
function needsCustomDefaultValueType(node: SchemaNode, graph: SchemaGraph) {
  if (!(node.name && node.schemaContent)) return false
  if (!node.schemaContent.defaultValueCustomTypes) {
    if (!isObjectSchema(node.schemaObject)) {
      node.schemaContent.defaultValueCustomTypes = {}
    } else {
      // recursively populate schema.defaultValueCustomTypes on nodes as needed
      node.schemaContent.defaultValueCustomTypes = Object.fromEntries(
        entries(node.schemaObject.properties!)
          .filter(
            ([_, subSchema]) =>
              isReferenceObject(subSchema) &&
              needsCustomDefaultValueType(
                graph.get(getReferenceName(subSchema))!,
                graph,
              ),
          )
          .map(([name, subSchema]) => [
            name as string,
            getReferenceName(subSchema as ReferenceObject),
          ]),
      )
    }
  }

  const defaultValueCustomTypes: Record<string, string> =
    node.schemaContent.defaultValueCustomTypes
  const defaultValueOmittedFields = node.schemaContent.defaultValueOmittedFields

  return (
    (defaultValueOmittedFields && defaultValueOmittedFields.length > 0) ||
    (defaultValueCustomTypes && Object.keys(defaultValueCustomTypes).length > 0)
  )
}

function generateDefaultValueType(node: SchemaNode, graph: SchemaGraph) {
  const fieldsToOmit = [
    ...(node.schemaContent.defaultValueOmittedFields ?? []),
    ...Object.keys(node.schemaContent.defaultValueCustomTypes ?? {}),
  ]

  // omit both the required fields without suitable defaults and the fields that are references to schemas
  // that require a custom default value type
  const baseType = `Omit<${node.name}Form, ${fieldsToOmit.map((fieldName) => `'${fieldName}'`).join(' | ')}>`
  // restore the fields that are references to schemas that require a custom default value type with the
  // custom default value type
  const customFields =
    (
      node.schemaContent.defaultValueCustomTypes &&
      Object.keys(node.schemaContent.defaultValueCustomTypes).length > 0
    ) ?
      ' & {\n  ' +
      entries(node.schemaContent.defaultValueCustomTypes)
        .map(
          ([fieldName, referenceName]) =>
            `${fieldName}: Default${referenceName}Form`, // the custom default value type
        )
        .join('\n  ') +
      '}\n'
    : ''

  return `export type Default${node.name}Form = ${baseType}${customFields}\n`
}

main('apiToForm')
main('formToApi')

const toFixedSix = ['indexAdjustment']
const toFixedTwo = ['tierMinBalanceInclusive', 'tierMaxBalanceExclusive']
