{"components": {"schemas": {"Account": {"properties": {"accountNumber": {"type": "string"}, "accountStatus": {"enum": ["Z", "B", "C", "P", "X"], "type": "string"}, "analysisAccountTypeCode": {"type": "string"}, "applicationId": {"enum": ["C", "D"], "type": "string"}, "bankNumber": {"type": "string"}, "branchCode": {"type": "string"}, "closeDate": {"format": "date", "type": "string"}, "costCenter": {"type": "string"}, "currencyCode": {"type": "string"}, "customerSpecificPricingIndicator": {"description": "The customer-specific pricing can not be true for deposit accounts and must be present for composite accounts", "type": "boolean"}, "depositAccountTypeCode": {"type": "string"}, "depositCategory": {"enum": ["A", "C", "D", "L", "M", "N", "R", "S", "P", "T"], "type": "string"}, "effectiveDate": {"format": "date", "type": "string"}, "openDate": {"format": "date", "type": "string"}, "primaryOfficerCode": {"type": "string"}, "processingIndicator": {"enum": ["A", "B"], "type": "string"}, "secondaryOfficerCode": {"type": "string"}, "shortName": {"type": "string"}, "treasuryOfficerCode": {"type": "string"}}, "required": ["accountNumber", "analysisAccountTypeCode", "applicationId", "bankNumber", "branchCode", "effectiveDate", "openDate", "shortName"], "type": "object"}, "AccountCode": {"properties": {"accountNumber": {"type": "string"}, "applicationId": {"enum": ["C", "D"], "type": "string"}, "bankNumber": {"type": "string"}}, "required": ["accountNumber", "applicationId", "bankNumber"], "type": "object"}, "AccountCodeWithEffectivedateReq": {"properties": {"accountCode": {"$ref": "#/components/schemas/AccountCode"}, "effectiveDate": {"format": "date", "type": "string"}}, "required": ["accountCode", "effectiveDate"], "type": "object"}, "AccountCodesDateLeadAccountRequest": {"properties": {"accountCodes": {"items": {"$ref": "#/components/schemas/AccountCode"}, "type": "array"}, "effectiveDate": {"format": "date", "type": "string"}, "leadAccount": {"$ref": "#/components/schemas/AccountCode"}}, "required": ["accountCodes", "effectiveDate", "leadAccount"], "type": "object"}, "AccountCodesRetrieveRequest": {"properties": {"accountCodes": {"items": {"$ref": "#/components/schemas/AccountCode"}, "type": "array"}, "effectiveDate": {"format": "date", "type": "string"}}, "required": ["accountCodes", "effectiveDate"], "type": "object"}, "AccountEffectiveDateRequest": {"properties": {"accountNumber": {"type": "string"}, "applicationId": {"enum": ["C", "D"], "type": "string"}, "bankNumber": {"type": "string"}, "effectiveDate": {"format": "date", "type": "string"}}, "required": ["accountNumber", "applicationId", "bankNumber", "effectiveDate"], "type": "object"}, "AccountEntity": {"properties": {"accountNumber": {"maxLength": 20, "minLength": 0, "type": "string"}, "accountStatus": {"enum": ["Z", "B", "C", "P", "X"], "type": "string"}, "analysisAccountTypeCode": {"type": "string"}, "applicationId": {"enum": ["C", "D"], "type": "string"}, "bankNumber": {"type": "string"}, "branchCode": {"type": "string"}, "closeDate": {"format": "date", "type": "string"}, "code": {"maxLength": 21, "minLength": 0, "type": "string"}, "costCenter": {"type": "string"}, "currencyCode": {"type": "string"}, "customerSpecificPricingIndicator": {"type": "boolean"}, "depositAccountTypeCode": {"type": "string"}, "depositCategory": {"enum": ["A", "C", "D", "L", "M", "N", "R", "S", "P", "T"], "type": "string"}, "effectiveDate": {"format": "date", "type": "string"}, "openDate": {"format": "date", "type": "string"}, "primaryOfficerCode": {"maxLength": 10, "minLength": 0, "type": "string"}, "processingIndicator": {"enum": ["A", "B"], "type": "string"}, "secondaryOfficerCode": {"maxLength": 10, "minLength": 0, "type": "string"}, "shortName": {"maxLength": 24, "minLength": 1, "type": "string"}, "treasuryOfficerCode": {"maxLength": 10, "minLength": 0, "type": "string"}}, "required": ["accountNumber", "analysisAccountTypeCode", "applicationId", "bankNumber", "branchCode", "effectiveDate", "openDate"], "type": "object"}, "AccountMappingCreateRequest": {"properties": {"childAccountCode": {"$ref": "#/components/schemas/AccountCode"}, "effectiveDate": {"format": "date", "type": "string"}, "parentAccountCode": {"$ref": "#/components/schemas/AccountCode"}}, "required": ["childAccountCode", "effectiveDate", "parentAccountCode"], "type": "object"}, "AccountMappingEntity": {"properties": {"code": {"type": "string"}, "effectiveDate": {"format": "date", "type": "string"}, "endDate": {"format": "date", "type": "string"}, "parentCode": {"type": "string"}}, "required": ["effectiveDate"], "type": "object"}, "AccountMappingUpdateRequest": {"properties": {"childAccountCodes": {"items": {"$ref": "#/components/schemas/AccountCode"}, "type": "array"}, "effectiveDate": {"format": "date", "type": "string"}, "parentAccountCode": {"$ref": "#/components/schemas/AccountCode"}}, "required": ["childAccountCodes", "effectiveDate", "parentAccountCode"], "type": "object"}, "AccountType": {"properties": {"accountTypeCode": {"type": "string"}, "analysisResultOptionsPlanCode": {"type": "string"}, "balanceRequirementDefinitionCode": {"type": "string"}, "description": {"type": "string"}, "earningsCreditDefinitionCode": {"type": "string"}, "effectiveDate": {"format": "date", "type": "string"}, "interestRequirementDefinitionCode": {"type": "string"}, "investableBalanceDefinitionCode": {"type": "string"}, "reserveRequirementDefinitionCode": {"type": "string"}, "settlementCyclePlanCode": {"type": "string"}, "statementCyclePlanCode": {"type": "string"}, "statementFormatPlanCode": {"type": "string"}, "statementMessagePlanCode": {"type": "string"}}, "required": ["accountTypeCode", "analysisResultOptionsPlanCode", "balanceRequirementDefinitionCode", "description", "effectiveDate", "investableBalanceDefinitionCode", "settlementCyclePlanCode", "statementCyclePlanCode", "statementFormatPlanCode"], "type": "object"}, "AccountTypeOverride": {"properties": {"accountCode": {"$ref": "#/components/schemas/AccountCode"}, "analysisResultOptionsPlanCode": {"type": "string"}, "analysisResultOptionsPlanCodeExpiry": {"format": "date", "type": "string"}, "balanceRequirementDefinitionCode": {"type": "string"}, "balanceRequirementDefinitionCodeExpiry": {"format": "date", "type": "string"}, "chargeAccountCode": {"type": "string"}, "earningsCreditDefinitionCode": {"type": "string"}, "earningsCreditDefinitionCodeExpiry": {"format": "date", "type": "string"}, "effectiveDate": {"format": "date", "type": "string"}, "interestRequirementDefinitionCode": {"type": "string"}, "interestRequirementDefinitionCodeExpiry": {"format": "date", "type": "string"}, "investableBalanceDefinitionCode": {"type": "string"}, "investableBalanceDefinitionCodeExpiry": {"format": "date", "type": "string"}, "isOverrideAsSettlementAccount": {"type": "boolean"}, "reserveRequirementDefinitionCode": {"type": "string"}, "reserveRequirementDefinitionCodeExpiry": {"format": "date", "type": "string"}, "settlementCyclePlanCode": {"type": "string"}, "settlementCyclePlanCodeExpiry": {"format": "date", "type": "string"}, "statementCyclePlanCode": {"type": "string"}, "statementCyclePlanCodeExpiry": {"format": "date", "type": "string"}, "statementFormatPlanCode": {"type": "string"}, "statementFormatPlanCodeExpiry": {"format": "date", "type": "string"}, "statementMessagePlanCode": {"type": "string"}, "statementMessagePlanCodeExpiry": {"format": "date", "type": "string"}}, "required": ["accountCode", "effectiveDate", "isOverrideAsSettlementAccount"], "type": "object"}, "AccountWithKey": {"properties": {"accountNumber": {"type": "string"}, "accountStatus": {"enum": ["Z", "B", "C", "P", "X"], "type": "string"}, "analysisAccountTypeCode": {"type": "string"}, "applicationId": {"enum": ["C", "D"], "type": "string"}, "bankNumber": {"type": "string"}, "branchCode": {"type": "string"}, "closeDate": {"format": "date", "type": "string"}, "costCenter": {"type": "string"}, "currencyCode": {"type": "string"}, "customerSpecificPricingIndicator": {"description": "The customer-specific pricing can not be true for deposit accounts and must be present for composite accounts", "type": "boolean"}, "depositAccountTypeCode": {"type": "string"}, "depositCategory": {"enum": ["A", "C", "D", "L", "M", "N", "R", "S", "P", "T"], "type": "string"}, "effectiveDate": {"format": "date", "type": "string"}, "isKeyAccount": {"type": "boolean"}, "keyAccountCode": {"$ref": "#/components/schemas/AccountCode"}, "openDate": {"format": "date", "type": "string"}, "primaryOfficerCode": {"type": "string"}, "processingIndicator": {"enum": ["A", "B"], "type": "string"}, "secondaryOfficerCode": {"type": "string"}, "shortName": {"type": "string"}, "treasuryOfficerCode": {"type": "string"}}, "required": ["accountNumber", "analysisAccountTypeCode", "applicationId", "bankNumber", "branchCode", "effectiveDate", "openDate", "shortName"], "type": "object"}, "Address": {"properties": {"accountCode": {"$ref": "#/components/schemas/AccountCode"}, "addressLine2": {"type": "string"}, "addressLine3": {"type": "string"}, "addressLine4": {"type": "string"}, "addressLine5": {"type": "string"}, "addressLine6": {"type": "string"}, "addressLine7": {"type": "string"}, "addressNumber": {"format": "int32", "type": "integer"}, "applicationId": {"enum": ["A", "D"], "type": "string"}, "effectiveDate": {"format": "date", "type": "string"}, "name": {"type": "string"}}, "required": ["accountCode", "addressNumber", "applicationId", "effectiveDate"], "type": "object"}, "AddressCode": {"properties": {"accountCode": {"$ref": "#/components/schemas/AccountCode"}, "addressNumber": {"format": "int32", "type": "integer"}, "applicationId": {"enum": ["A", "D"], "type": "string"}}, "required": ["accountCode", "addressNumber", "applicationId"], "type": "object"}, "AddressCodeAvailabilityRequest": {"properties": {"accountApplicationId": {"enum": ["C", "D"], "type": "string"}, "accountNumber": {"type": "string"}, "addressApplicationId": {"enum": ["A", "D"], "type": "string"}, "addressNumber": {"format": "int32", "type": "integer"}, "bankNumber": {"type": "string"}}, "required": ["accountApplicationId", "accountNumber", "addressApplicationId", "addressNumber", "bankNumber"], "type": "object"}, "AddressCodeAvailabilityResponse": {"properties": {"available": {"type": "boolean"}, "suggestedAddressNumber": {"format": "int32", "type": "integer"}}, "required": ["available", "suggestedAddressNumber"], "type": "object"}, "AddressRetrieveRequest": {"properties": {"accountApplicationId": {"enum": ["C", "D"], "type": "string"}, "accountCode": {"$ref": "#/components/schemas/AccountCode"}, "accountNumber": {"type": "string"}, "addressApplicationId": {"enum": ["A", "D"], "type": "string"}, "addressNumber": {"format": "int32", "type": "integer"}, "bankNumber": {"type": "string"}, "effectiveDate": {"format": "date", "type": "string"}}, "required": ["accountApplicationId", "accountNumber", "addressApplicationId", "addressNumber", "bankNumber", "effectiveDate"], "type": "object"}, "AnalysisResultOption": {"properties": {"analysisChargeType": {"enum": ["DIRECT_DEBIT", "WAIVE"], "type": "string"}, "analysisDirectDebitTrailer": {"type": "string"}, "code": {"type": "string"}, "daysAfter": {"format": "int32", "type": "integer"}, "delay": {"type": "number"}, "effectiveDate": {"format": "date", "type": "string"}, "excessCredits": {"enum": ["WAIVE"], "type": "string"}, "hardCharge": {"enum": ["DIRECT_DEBIT", "WAIVE"], "type": "string"}, "hardDirectDebitTrailer": {"type": "string"}, "markdownRate": {"type": "number"}, "markdownStatementLabel": {"type": "string"}, "markupRate": {"type": "number"}, "markupStatementLabel": {"type": "string"}, "maxChargeWaiveAmount": {"type": "number"}, "minChargeWaiveAmount": {"type": "number"}, "name": {"type": "string"}, "settlementOverrideDateEachMonth": {"format": "int32", "type": "integer"}, "settlementOverrideType": {"enum": ["NO_OVERRIDE", "SAME_DATE_EACH_MONTH", "SPECIFIC_DAYS_AFTER_PRELIM_ANALYSIS"], "type": "string"}, "waiveCycle": {"format": "int32", "type": "integer"}}, "required": ["analysisChargeType", "code", "effectiveDate", "excessCredits", "hardCharge", "name", "settlementOverrideType"], "type": "object"}, "BalanceRequirementDefinition": {"properties": {"addOtherBalanceLabel": {"type": "string"}, "baseBalanceType": {"enum": ["AVERAGE_COLLECTED", "AVERAGE_LEDGER", "AVERAGE_NEGATIVE_COLLECTED", "AVERAGE_NEGATIVE_LEDGER", "AVERAGE_POSITIVE_COLLECTED", "AVERAGE_POSITIVE_LEDGER", "AVERAGE_UNCOLLECTED_FUNDS", "COMPENSATING_BALANCE", "END_OF_MONTH_LEDGER", "INVESTABLE_BALANCE", "AVERAGE_FLOAT", "AVERAGE_CLEARINGHOUSE_FLOAT", "REQUIRED_BALANCE"], "type": "string"}, "code": {"type": "string"}, "effectiveDate": {"format": "date", "type": "string"}, "name": {"type": "string"}, "subtractCompensatingBalance": {"type": "boolean"}, "subtractInterestPaidMonthToDate": {"type": "boolean"}, "subtractOtherBalanceLabel": {"type": "string"}}, "required": ["baseBalanceType", "code", "effectiveDate", "name", "subtractCompensatingBalance", "subtractInterestPaidMonthToDate"], "type": "object"}, "BalanceTier": {"properties": {"indexAdjustmentRate": {"type": "number"}, "indexRate": {"type": "number"}, "maxTierExclusive": {"type": "number"}, "minTierInclusive": {"type": "number"}}, "required": ["maxTierExclusive", "minTierInclusive"], "type": "object"}, "BankOptions": {"properties": {"accountNumberDigits": {"format": "int32", "type": "integer"}, "accountNumberMasking": {"type": "boolean"}, "accountNumberPattern": {"type": "string"}, "assignDefaultStatementPackageToDepositAccount": {"type": "boolean"}, "automaticPrelimAnalysis": {"type": "boolean"}, "balanceCycleDays": {"enum": ["ACTUAL_DAYS_IN_MONTH", "OPEN_DAYS_IN_MONTH"], "type": "string"}, "calculatingBalanceFeeBasis": {"enum": ["360", "365", "366"], "type": "string"}, "calculatingEarningsCreditBasis": {"enum": ["360", "365", "366"], "type": "string"}, "code": {"type": "string"}, "copyAccountTypeOfKeyAccount": {"type": "boolean"}, "copyUserFieldsFromKeyAccount": {"type": "boolean"}, "defaultCurrency": {"enum": ["US_DOLLARS"], "type": "string"}, "defaultTermInMonthsForPromoAndOverrides": {"format": "int32", "type": "integer"}, "earningsCycleDays": {"enum": ["ACTUAL_DAYS_IN_MONTH", "OPEN_DAYS_IN_MONTH", "THIRTY"], "type": "string"}, "effectiveDate": {"format": "date", "type": "string"}, "finalAnalysis": {"enum": ["SAME_DATE_EACH_MONTH", "SPECIFIC_DAYS_POST_PRELIM"], "type": "string"}, "finalAnalysisDays": {"format": "int32", "type": "integer"}, "name": {"type": "string"}, "prelimAnalysis": {"enum": ["LastBusinessDayPriorMonth", "1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27"], "type": "string"}, "retentionMonths": {"format": "int32", "type": "integer"}, "statementArchivingFrequency": {"enum": ["AT_FINAL_AND_REANALYSIS", "AT_FINAL", "NOT_ARCHIVED"], "type": "string"}}, "required": ["accountNumberDigits", "accountNumberMasking", "accountNumberPattern", "assignDefaultStatementPackageToDepositAccount", "automaticPrelimAnalysis", "balanceCycleDays", "calculatingBalanceFeeBasis", "calculatingEarningsCreditBasis", "code", "copyAccountTypeOfKeyAccount", "copyUserField<PERSON><PERSON><PERSON><PERSON><PERSON>Account", "defaultCurrency", "defaultTermInMonthsForPromoAndOverrides", "earningsCycleDays", "effectiveDate", "finalAnalysis", "finalAnalysisDays", "name", "prelimAnalysis", "retentionMonths", "statementArchivingFrequency"], "type": "object"}, "Branch": {"properties": {"abaNumbers": {"items": {"type": "string"}, "properties": {"empty": {"type": "boolean"}}, "type": "array"}, "addressLine1": {"type": "string"}, "addressLine2": {"type": "string"}, "addressLine3": {"type": "string"}, "bankNumber": {"type": "string"}, "branchName": {"type": "string"}, "code": {"type": "string"}, "effectiveDate": {"format": "date", "type": "string"}, "phoneNumber": {"type": "string"}}, "required": ["abaNumbers", "addressLine1", "addressLine2", "addressLine3", "bankNumber", "branchName", "code", "effectiveDate", "phoneNumber"], "type": "object"}, "CodeRequestBody": {"properties": {"code": {"type": "string"}}, "required": ["code"], "type": "object"}, "CodesAndEffectiveDateRequestBody": {"properties": {"codes": {"items": {"type": "string"}, "type": "array"}, "effectiveDate": {"format": "date", "type": "string"}}, "required": ["codes", "effectiveDate"], "type": "object"}, "CompositeAccountCreateRequest": {"properties": {"account": {"$ref": "#/components/schemas/Account"}, "accountTypeOverride": {"$ref": "#/components/schemas/AccountTypeOverride"}, "addresses": {"items": {"$ref": "#/components/schemas/Address"}, "type": "array"}, "childAccountCodes": {"items": {"$ref": "#/components/schemas/AccountCode"}, "properties": {"empty": {"type": "boolean"}}, "type": "array"}, "keyChildAccountCode": {"$ref": "#/components/schemas/AccountCode"}, "statementPackages": {"items": {"$ref": "#/components/schemas/StatementPackageCreateUpdateRequest"}, "type": "array"}, "userFieldSelections": {"items": {"$ref": "#/components/schemas/UserFieldSelection"}, "type": "array"}}, "required": ["account", "childAccountCodes"], "type": "object"}, "CreateOrUpdateService": {"properties": {"service": {"$ref": "#/components/schemas/Service"}, "serviceCategoryCode": {"type": "string"}, "serviceCodes": {"items": {"type": "string"}, "type": "array"}, "servicePrice": {"items": {"$ref": "#/components/schemas/ServicePrice"}, "properties": {"empty": {"type": "boolean"}}, "type": "array"}}, "required": ["service", "servicePrice"], "type": "object"}, "CreateServiceCategory": {"properties": {"parentServiceCategoryCode": {"description": "Provide null if service category has no parent Service Category", "type": "string"}, "serviceCategory": {"$ref": "#/components/schemas/ServiceCategory"}}, "required": ["serviceCategory"], "type": "object"}, "CycleDefinition": {"properties": {"code": {"type": "string"}, "cycleType": {"enum": ["STATEMENT", "SETTLEMENT"], "type": "string"}, "description": {"type": "string"}, "effectiveDate": {"format": "date", "type": "string"}, "includedMonths": {"items": {"enum": ["JANUARY", "FEBRUARY", "MARCH", "APRIL", "MAY", "JUNE", "JULY", "AUGUST", "SEPTEMBER", "OCTOBER", "NOVEMBER", "DECEMBER"], "type": "string"}, "type": "array", "uniqueItems": true}, "isAprilSelected": {"type": "boolean"}, "isAugustSelected": {"type": "boolean"}, "isDecemberSelected": {"type": "boolean"}, "isFebruarySelected": {"type": "boolean"}, "isJanuarySelected": {"type": "boolean"}, "isJulySelected": {"type": "boolean"}, "isJuneSelected": {"type": "boolean"}, "isMarchSelected": {"type": "boolean"}, "isMaySelected": {"type": "boolean"}, "isNovemberSelected": {"type": "boolean"}, "isOctoberSelected": {"type": "boolean"}, "isSeptemberSelected": {"type": "boolean"}}, "required": ["code", "cycleType", "description", "effectiveDate"], "type": "object"}, "DailyBalanceHistory": {"properties": {"accountNumber": {"type": "string"}, "applicationId": {"enum": ["C", "D"], "type": "string"}, "bankNumber": {"type": "string"}, "clearingHouseFundsFloat": {"type": "number"}, "floatBalance": {"type": "number"}, "isBusinessDay": {"type": "boolean"}, "ledgerBalance": {"type": "number"}, "processDate": {"format": "date", "type": "string"}, "todaysAccruedInterest": {"type": "number"}, "todaysPostedInterest": {"type": "number"}}, "required": ["accountNumber", "applicationId", "bankNumber", "clearingHouseFundsFloat", "floatBalance", "isBusinessDay", "ledgerBalance", "processDate", "todaysAccruedInterest", "todaysPostedInterest"], "type": "object"}, "DemographicCriteria": {"properties": {"booleanValue": {"type": "boolean"}, "code": {"type": "string"}, "codeKey": {"type": "string"}, "codeValue": {"type": "string"}, "criteriaCode": {"type": "string"}, "demographicCriteriaId": {"$ref": "#/components/schemas/DemographicCriteriaId"}, "effectiveDate": {"format": "date", "type": "string"}, "keyType": {"enum": ["USERFIELD", "ACCOUNTFIELD"], "type": "string"}, "priceListCode": {"type": "string"}, "stringKey": {"type": "string"}, "stringValue": {"type": "string"}, "valueCode": {"type": "string"}}, "required": ["code", "criteriaCode", "demographicCriteriaId", "effectiveDate", "keyType", "priceListCode", "valueCode"], "type": "object"}, "DemographicCriteriaId": {"properties": {"codeKey": {"type": "string"}, "criteriaCode": {"type": "string"}, "keyType": {"enum": ["USERFIELD", "ACCOUNTFIELD"], "type": "string"}, "priceListCode": {"type": "string"}, "stringKey": {"type": "string"}, "valueCode": {"type": "string"}}, "required": ["criteriaCode", "keyType", "priceListCode", "valueCode"], "type": "object"}, "DemographicPriceList": {"properties": {"code": {"type": "string"}, "currency": {"type": "string"}, "effectiveDate": {"format": "date", "type": "string"}, "isLeadPriceList": {"type": "boolean"}, "name": {"type": "string"}}, "required": ["code", "currency", "effectiveDate", "isLeadPriceList"], "type": "object"}, "DemographicPriceListRanking": {"properties": {"effectiveDate": {"format": "date", "type": "string"}, "ranking": {"type": "string"}}, "required": ["effectiveDate", "ranking"], "type": "object"}, "DemographicPriceListSummaryDTO": {"properties": {"code": {"type": "string"}, "effectiveDate": {"format": "date", "type": "string"}, "leadPriceList": {"type": "boolean"}, "name": {"type": "string"}, "numberOfServices": {"format": "int64", "type": "integer"}, "priority": {"format": "int32", "type": "integer"}}, "type": "object"}, "DemographicPriceListWithCount": {"properties": {"code": {"type": "string"}, "currency": {"type": "string"}, "effectiveDate": {"format": "date", "type": "string"}, "isLeadPriceList": {"type": "boolean"}, "name": {"type": "string"}, "serviceCount": {"format": "int32", "type": "integer"}}, "required": ["code", "currency", "effectiveDate", "isLeadPriceList", "serviceCount"], "type": "object"}, "DemographicUpdateRecordEntity": {"properties": {"aasAccountType": {"type": "string"}, "accountNumber": {"type": "string"}, "accountStatus": {"type": "string"}, "alternatePrimaryOfficer": {"type": "string"}, "alternateSecondaryOfficer": {"type": "string"}, "alternateTreasuryOfficer": {"type": "string"}, "applicationId": {"type": "string"}, "bankNumber": {"type": "string"}, "blendOverrideIndicator": {"type": "string"}, "branchNumber": {"type": "string"}, "closeDate": {"format": "date", "type": "string"}, "control": {"type": "string"}, "costCenter": {"type": "string"}, "cpsAasIndicator": {"type": "string"}, "createdAt": {"format": "date-time", "type": "string"}, "currencyCode": {"type": "string"}, "customerPricingIndicator": {"type": "string"}, "dailyOffsetIndicator": {"type": "string"}, "depositAccountType": {"type": "string"}, "depositCategory": {"type": "string"}, "effectiveDate": {"format": "date", "type": "string"}, "fileType": {"enum": ["CORE_UNPACKED", "IBS_CORE_PACKED", "ARPPA", "ACH_TRACKER", "CASH_MANAGER", "D1B", "DIRECT_LINK_MERCHANT", "EWIRE"], "type": "string"}, "id": {"format": "uuid", "type": "string"}, "inboundRecordStatus": {"enum": ["PARSED", "CONVERSION_ERROR", "VALIDATION_ERROR", "PERSIST_ERROR", "PROCESSED_CREATE", "PROCESSED_UPDATE", "PROCESSED_SKIP"], "type": "string"}, "openDate": {"format": "date", "type": "string"}, "parsingKey": {"type": "string"}, "postingDate": {"format": "date", "type": "string"}, "primaryOfficer": {"type": "string"}, "processingIndicator": {"type": "string"}, "secondaryOfficer": {"type": "string"}, "settleAtAccountIndicator": {"type": "string"}, "shortName": {"type": "string"}, "source": {"type": "string"}, "sourceFilePath": {"type": "string"}, "treasuryOfficer": {"type": "string"}, "updatedAt": {"format": "date-time", "type": "string"}}, "type": "object"}, "EarningsCreditDefinition": {"properties": {"balanceTiers": {"items": {"$ref": "#/components/schemas/BalanceTier"}, "properties": {"empty": {"type": "boolean"}}, "type": "array"}, "baseBalanceType": {"enum": ["AVERAGE_COLLECTED", "AVERAGE_LEDGER", "AVERAGE_NEGATIVE_COLLECTED", "AVERAGE_NEGATIVE_LEDGER", "AVERAGE_POSITIVE_COLLECTED", "AVERAGE_POSITIVE_LEDGER", "AVERAGE_UNCOLLECTED_FUNDS", "COMPENSATING_BALANCE", "END_OF_MONTH_LEDGER", "INVESTABLE_BALANCE", "AVERAGE_FLOAT", "AVERAGE_CLEARINGHOUSE_FLOAT", "REQUIRED_BALANCE"], "type": "string"}, "code": {"type": "string"}, "description": {"type": "string"}, "effectiveDate": {"format": "date", "type": "string"}, "indexRateCode": {"type": "string"}, "maxRateInclusive": {"type": "number"}, "minRateInclusive": {"type": "number"}, "rateSource": {"enum": ["INDEX_RATE", "MANUAL"], "type": "string"}, "tierMethod": {"enum": ["THRESHOLD", "PARTITIONED"], "type": "string"}}, "required": ["balanceTiers", "baseBalanceType", "code", "description", "effectiveDate", "rateSource", "tierMethod"], "type": "object"}, "EffectiveDateRequestBody": {"properties": {"effectiveDate": {"format": "date", "type": "string"}}, "required": ["effectiveDate"], "type": "object"}, "GetDailyBalanceHistoryRequest": {"properties": {"bankNumber": {"type": "string"}, "endDateExclusive": {"format": "date", "type": "string"}, "startDateInclusive": {"format": "date", "type": "string"}}, "required": ["bankNumber", "endDateExclusive", "startDateInclusive"], "type": "object"}, "HydratedAccountWithKeyAccountMapping": {"description": "List of parent-child account mappings. The parent is null for root child", "properties": {"child": {"$ref": "#/components/schemas/AccountWithKey"}, "parent": {"$ref": "#/components/schemas/AccountWithKey"}}, "required": ["child"], "type": "object"}, "HydratedCategoryToCategoryMapping": {"properties": {"child": {"$ref": "#/components/schemas/ServiceCategory"}, "parent": {"$ref": "#/components/schemas/ServiceCategory"}}, "required": ["child"], "type": "object"}, "HydratedDemographicPriceList": {"properties": {"demographicCriteria": {"items": {"$ref": "#/components/schemas/DemographicCriteria"}, "properties": {"empty": {"type": "boolean"}}, "type": "array"}, "demographicPriceList": {"$ref": "#/components/schemas/DemographicPriceList"}, "ranking": {"$ref": "#/components/schemas/DemographicPriceListRanking"}, "servicePricing": {"items": {"$ref": "#/components/schemas/ServicePrice"}, "properties": {"empty": {"type": "boolean"}}, "type": "array"}}, "required": ["demographicCriteria", "demographicPriceList", "ranking", "servicePricing"], "type": "object"}, "HydratedServiceCatalogMapping": {"properties": {"categoryToCategoryMappings": {"items": {"$ref": "#/components/schemas/HydratedCategoryToCategoryMapping"}, "properties": {"empty": {"type": "boolean"}}, "type": "array"}, "serviceToCategoryMappings": {"items": {"$ref": "#/components/schemas/HydratedServiceToCategoryMapping"}, "properties": {"empty": {"type": "boolean"}}, "type": "array"}}, "required": ["categoryToCategoryMappings", "serviceToCategoryMappings"], "type": "object"}, "HydratedServicePrice": {"properties": {"cycleDefinition": {"$ref": "#/components/schemas/CycleDefinition"}, "indexRate": {"$ref": "#/components/schemas/IndexRate"}, "servicePrice": {"$ref": "#/components/schemas/ServicePrice"}}, "type": "object"}, "HydratedServiceToCategoryMapping": {"properties": {"child": {"$ref": "#/components/schemas/Service"}, "parent": {"$ref": "#/components/schemas/ServiceCategory"}}, "required": ["child"], "type": "object"}, "HydratedStatementPackage": {"properties": {"address": {"$ref": "#/components/schemas/Address"}, "selectedAccounts": {"items": {"$ref": "#/components/schemas/AccountCode"}, "properties": {"empty": {"type": "boolean"}}, "type": "array", "uniqueItems": true}, "statementPackage": {"$ref": "#/components/schemas/StatementPackage"}}, "required": ["address", "selectedAccounts", "statementPackage"], "type": "object"}, "HydratedUserFieldConfiguration": {"properties": {"availableForPriceList": {"type": "boolean"}, "code": {"format": "int64", "type": "integer"}, "createdDate": {"format": "date", "type": "string"}, "fieldType": {"enum": ["FREEFORM", "DROPDOWN", "BOOLEAN"], "type": "string"}, "lastUpdatedDate": {"format": "date", "type": "string"}, "name": {"type": "string"}, "newDropdownOptions": {"items": {"$ref": "#/components/schemas/UserFieldDropdownOptionCreate"}, "type": "array"}, "updatedDropdownOptions": {"items": {"$ref": "#/components/schemas/UserFieldDropdownOptionUpdate"}, "type": "array"}}, "required": ["availableForPriceList", "code", "fieldType", "name"], "type": "object"}, "HydratedUserFieldConfigurationCreate": {"properties": {"availableForPriceList": {"type": "boolean"}, "fieldType": {"enum": ["FREEFORM", "DROPDOWN", "BOOLEAN"], "type": "string"}, "name": {"type": "string"}, "newDropdownOptions": {"items": {"$ref": "#/components/schemas/UserFieldDropdownOptionCreate"}, "type": "array"}}, "required": ["availableForPriceList", "fieldType", "name"], "type": "object"}, "HydratedUserFieldConfigurationUpdate": {"properties": {"availableForPriceList": {"type": "boolean"}, "code": {"format": "int64", "type": "integer"}, "fieldType": {"enum": ["FREEFORM", "DROPDOWN", "BOOLEAN"], "type": "string"}, "name": {"type": "string"}, "newDropdownOptions": {"items": {"$ref": "#/components/schemas/UserFieldDropdownOptionCreate"}, "type": "array"}, "updatedDropdownOptions": {"items": {"$ref": "#/components/schemas/UserFieldDropdownOptionUpdate"}, "type": "array"}}, "required": ["availableForPriceList", "code", "fieldType", "name"], "type": "object"}, "IndexRate": {"properties": {"code": {"type": "string"}, "effectiveDate": {"format": "date", "type": "string"}, "indexRate": {"type": "number"}, "name": {"type": "string"}}, "required": ["code", "effectiveDate", "indexRate", "name"], "type": "object"}, "InvestableBalanceDefinition": {"properties": {"addOtherBalanceLabel": {"type": "string"}, "baseBalanceType": {"enum": ["AVERAGE_COLLECTED", "AVERAGE_LEDGER", "AVERAGE_NEGATIVE_COLLECTED", "AVERAGE_NEGATIVE_LEDGER", "AVERAGE_POSITIVE_COLLECTED", "AVERAGE_POSITIVE_LEDGER", "AVERAGE_UNCOLLECTED_FUNDS", "COMPENSATING_BALANCE", "END_OF_MONTH_LEDGER", "INVESTABLE_BALANCE", "AVERAGE_FLOAT", "AVERAGE_CLEARINGHOUSE_FLOAT", "REQUIRED_BALANCE"], "type": "string"}, "code": {"type": "string"}, "effectiveDate": {"format": "date", "type": "string"}, "name": {"type": "string"}, "subtractCompensatingBalance": {"type": "boolean"}, "subtractFederalReserveRequirement": {"type": "boolean"}, "subtractInterestPaidMonthToDate": {"type": "boolean"}, "subtractOtherBalanceLabel": {"type": "string"}}, "required": ["baseBalanceType", "code", "effectiveDate", "name", "subtractCompensatingBalance", "subtractFederalReserveRequirement", "subtractInterestPaidMonthToDate"], "type": "object"}, "KeyAccountMapping": {"properties": {"accountCode": {"$ref": "#/components/schemas/AccountCode"}, "childAccountCode": {"$ref": "#/components/schemas/AccountCode"}}, "required": ["accountCode", "childAccountCode"], "type": "object"}, "NextAvailableAccountNumberResponse": {"properties": {"nextAvailableAccountNumber": {"type": "string"}}, "required": ["nextAvailableAccountNumber"], "type": "object"}, "Officer": {"properties": {"bankNumber": {"type": "string"}, "code": {"type": "string"}, "effectiveDate": {"format": "date", "type": "string"}, "name": {"type": "string"}, "phone": {"type": "string"}}, "required": ["bankNumber", "code", "effectiveDate", "name", "phone"], "type": "object"}, "PromotionalPriceList": {"properties": {"code": {"type": "string"}, "currency": {"type": "string"}, "effectiveDate": {"format": "date", "type": "string"}, "expirationDate": {"format": "date", "type": "string"}, "name": {"type": "string"}, "term": {"format": "int32", "type": "integer"}}, "required": ["code", "currency", "effectiveDate", "expirationDate", "term"], "type": "object"}, "ReserveRequirementDefinition": {"properties": {"baseBalanceType": {"enum": ["AVERAGE_COLLECTED", "AVERAGE_POSITIVE_COLLECTED"], "type": "string"}, "calculationMethodType": {"enum": ["INDEXED", "PERCENTAGE"], "type": "string"}, "ceiling": {"type": "number"}, "code": {"type": "string"}, "effectiveDate": {"format": "date", "type": "string"}, "floor": {"type": "number"}, "indexAdjustment": {"type": "number"}, "indexRateCode": {"type": "string"}, "name": {"type": "string"}, "reserveRate": {"type": "number"}}, "required": ["baseBalanceType", "calculationMethodType", "code", "effectiveDate", "name"], "type": "object"}, "Service": {"properties": {"code": {"type": "string"}, "description": {"type": "string"}, "domesticAfpCode": {"type": "string"}, "effectiveDate": {"format": "date", "type": "string"}, "globalAfpCode": {"type": "string"}, "internalNote": {"type": "string"}, "serviceType": {"enum": ["VOLUME_BASED", "RECURRING", "BALANCE_BASED", "PRE_PRICED", "SERVICE_SET"], "type": "string"}}, "required": ["code", "effectiveDate", "serviceType"], "type": "object"}, "ServiceCategory": {"properties": {"code": {"type": "string"}, "effectiveDate": {"format": "date", "type": "string"}, "name": {"type": "string"}}, "required": ["code", "effectiveDate", "name"], "type": "object"}, "ServiceDetails": {"properties": {"service": {"$ref": "#/components/schemas/Service"}, "serviceCategory": {"$ref": "#/components/schemas/ServiceCategory"}, "servicePrice": {"items": {"$ref": "#/components/schemas/HydratedServicePrice"}, "properties": {"empty": {"type": "boolean"}}, "type": "array"}, "servicesInServiceSet": {"items": {"$ref": "#/components/schemas/Service"}, "type": "array"}}, "required": ["service", "servicePrice"], "type": "object"}, "ServicePrice": {"properties": {"applyServiceTo": {"enum": ["SELECT_DEPOSIT_ACCOUNTS", "ALL_DEPOSIT_ACCOUNTS"], "type": "string"}, "balanceDivisor": {"format": "int32", "type": "integer"}, "balanceType": {"enum": ["AVERAGE_COLLECTED", "AVERAGE_LEDGER", "AVERAGE_NEGATIVE_COLLECTED", "AVERAGE_NEGATIVE_LEDGER", "AVERAGE_POSITIVE_COLLECTED", "AVERAGE_POSITIVE_LEDGER", "AVERAGE_UNCOLLECTED_FUNDS", "COMPENSATING_BALANCE", "END_OF_MONTH_LEDGER", "INVESTABLE_BALANCE", "AVERAGE_FLOAT", "AVERAGE_CLEARINGHOUSE_FLOAT", "REQUIRED_BALANCE"], "type": "string"}, "baseFee": {"type": "number"}, "basisDays": {"enum": ["360", "365", "366"], "type": "string"}, "costType": {"enum": ["NO_COST", "UNIT_COST", "FLAT_COST"], "type": "string"}, "costValue": {"type": "number"}, "currency": {"type": "string"}, "cycleDefinitionCode": {"type": "string"}, "disposition": {"enum": ["ANALYSED", "HARD_CHARGE", "IMMEDIATE_CHARGE", "WAIVED"], "type": "string"}, "effectiveDate": {"format": "date", "type": "string"}, "expirationDate": {"format": "date", "type": "string"}, "includeReferenceInformationOnStatements": {"type": "boolean"}, "indexAdjustment": {"type": "number"}, "indexMultiplier": {"type": "number"}, "indexRateCode": {"type": "string"}, "lastFinalsDate": {"format": "date", "type": "string"}, "maximumFee": {"type": "number"}, "minimumFee": {"type": "number"}, "priceType": {"enum": ["NOT_PRICED", "UNIT_PRICED", "FLAT_FEE", "THRESHOLD_TIER", "PARTITIONED_TIER", "PERCENTAGE", "OUTSIDE_PRICE", "INDEXED"], "type": "string"}, "priceValue": {"type": "number"}, "pricingHierarchyEntryCode": {"type": "string"}, "pricingHierarchyEntryType": {"enum": ["STANDARD", "PRICE_LIST", "OVERRIDE"], "type": "string"}, "serviceCode": {"type": "string"}, "subjectToDiscountOrPremium": {"type": "boolean"}, "tierMaxBalanceExclusive": {"type": "number"}, "tierMaxVolumeExclusive": {"format": "int32", "type": "integer"}, "tierMinBalanceInclusive": {"type": "number"}, "tierMinVolumeInclusive": {"format": "int32", "type": "integer"}, "tierNumber": {"format": "int32", "type": "integer"}, "tierPriceType": {"enum": ["UNIT_PRICED", "FLAT_FEE", "PERCENTAGE", "INDEXED"], "type": "string"}, "units": {"format": "int32", "type": "integer"}}, "required": ["effectiveDate", "pricingHierarchyEntryCode", "pricingHierarchyEntryType", "serviceCode", "tierNumber"], "type": "object"}, "ServicePriceRequest": {"properties": {"asOfDate": {"format": "date", "type": "string"}, "pricingHierarchyEntryCode": {"type": "string"}, "pricingHierarchyEntryType": {"enum": ["STANDARD", "PRICE_LIST", "OVERRIDE"], "type": "string"}, "serviceCodes": {"items": {"type": "string"}, "type": "array"}}, "required": ["asOfDate"], "type": "object"}, "SettlementProcessingOptionsResponseObj": {"properties": {"accountCode": {"$ref": "#/components/schemas/AccountCode"}, "accountTypeOverride": {"$ref": "#/components/schemas/AccountTypeOverride"}, "chargeAccountCode": {"type": "string"}, "effectiveDate": {"format": "date", "type": "string"}, "overrideAsSettlementAccount": {"type": "boolean"}}, "required": ["accountCode", "effectiveDate"], "type": "object"}, "StatementFormatPlan": {"properties": {"afpServiceCode": {"type": "boolean"}, "balanceAndResultsLayout": {"enum": ["STACKED", "SIDE_BY_SIDE"], "type": "string"}, "balanceSummary": {"type": "boolean"}, "balanceSummaryLabel": {"type": "string"}, "balanceSummaryLocation": {"type": "string"}, "boldServiceCategoryLabel": {"type": "boolean"}, "boldServiceCategorySubtotalLabel": {"type": "boolean"}, "code": {"type": "string"}, "dailyBalanceSummary": {"enum": ["NO", "YES_ALL_ACCOUNTS", "YES_COMPOSITE_ONLY"], "type": "string"}, "dailyBalanceSummaryLabel": {"type": "string"}, "dailyBalanceSummaryLocation": {"type": "string"}, "description": {"type": "string"}, "earningsCreditRate": {"enum": ["EARNINGS_CREDIT_DEFINITION", "CALCULATED_RATE"], "type": "string"}, "effectiveDate": {"format": "date", "type": "string"}, "enableBorderColor": {"type": "boolean"}, "enableServiceCategory": {"type": "boolean"}, "footerImageDisplay": {"enum": ["SHOW_ON_FIRST_PAGE_ONLY", "REPEAT_ON_EVERY_PAGE"], "type": "string"}, "headerLogoFileName": {"type": "string"}, "historicalSummaryLabel": {"type": "string"}, "historicalSummaryLocation": {"type": "string"}, "historicalSummaryType": {"enum": ["ROLLING_12_MONTHS", "YTD", "NO"], "type": "string"}, "includeOfficerName": {"type": "boolean"}, "includeOfficerPhone": {"type": "boolean"}, "printOfficer": {"enum": ["PRIMARY_OFFICER", "SECONDARY_OFFICER", "TREASURY_OFFICER", "NO_PRINT"], "type": "string"}, "relationshipSummary": {"type": "boolean"}, "relationshipSummaryLabel": {"type": "string"}, "relationshipSummaryLocation": {"type": "string"}, "requiredBalance": {"type": "boolean"}, "requiredBalanceMultiplier": {"type": "boolean"}, "resultsSummaryLabel": {"type": "string"}, "resultsSummaryLocation": {"type": "string"}, "returnAddress": {"type": "string"}, "returnAddressType": {"enum": ["NO", "DYNAMIC_BRANCH_ADDRESS", "ADDRESS_OF_BRANCH_ASSIGNED"], "type": "string"}, "sectionBackgroundColor": {"type": "string"}, "sectionBorderColor": {"type": "string"}, "sectionTextColor": {"type": "string"}, "serviceCategoryBackgroundColor": {"type": "string"}, "serviceCategoryLevel": {"enum": ["ONE", "TWO", "THREE"], "type": "string"}, "serviceCategoryPieChart": {"type": "boolean"}, "serviceCategoryPieChartLabel": {"type": "string"}, "serviceCategoryPieChartLocation": {"type": "string"}, "serviceCategorySort": {"enum": ["USER_DEFINED", "ALPHABETICAL_BY_CATEGORY_NAME", "BY_LOWEST_TO_HIGHEST_SERVICE_CODE"], "type": "string"}, "serviceCategorySubtotal": {"type": "boolean"}, "serviceCategorySubtotalBackgroundColor": {"type": "string"}, "serviceChargesDueBarChart": {"type": "boolean"}, "serviceChargesDueBarChartLabel": {"type": "string"}, "serviceChargesDueBarChartLocation": {"type": "string"}, "serviceCode": {"type": "boolean"}, "serviceDetailLabel": {"type": "string"}, "serviceDetailLocation": {"type": "string"}, "sortServicesType": {"enum": ["USER_DEFINED", "ALPHABETICAL_BY_CATEGORY_NAME", "BY_LOWEST_TO_HIGHEST_SERVICE_CODE"], "type": "string"}, "statementImageLocations": {"items": {"type": "string"}, "properties": {"empty": {"type": "boolean"}}, "type": "array"}, "statementMessage": {"type": "boolean"}, "statementMessageImages": {"items": {"type": "string"}, "properties": {"empty": {"type": "boolean"}}, "type": "array"}, "statementMessageLocation": {"type": "string"}}, "required": ["code", "dailyBalanceSummary", "description", "earningsCreditRate", "effectiveDate", "headerLogoFileName", "historicalSummaryType", "printOfficer", "resultsSummaryLabel", "resultsSummaryLocation", "sectionBackgroundColor", "sectionBorderColor", "sectionTextColor", "serviceDetailLabel", "sortServicesType", "statementImageLocations", "statementMessageImages"], "type": "object"}, "StatementMessage": {"properties": {"code": {"type": "string"}, "effectiveDate": {"format": "date", "type": "string"}, "message": {"type": "string"}, "name": {"type": "string"}}, "required": ["code", "effectiveDate", "message", "name"], "type": "object"}, "StatementPackage": {"properties": {"accountCode": {"$ref": "#/components/schemas/AccountCode"}, "addressCode": {"$ref": "#/components/schemas/AddressCode"}, "effectiveDate": {"format": "date", "type": "string"}, "packageDelivery": {"enum": ["ELECTRONIC", "ELECTRONIC_AND_PRINT", "PRINT", "NO_STATEMENT"], "type": "string"}, "packageType": {"enum": ["ALL_ACCOUNTS", "COMPOSITE_ACCOUNTS", "DEPOSIT_ACCOUNTS", "SELECTED_ACCOUNTS"], "type": "string"}, "statementPackageNumber": {"format": "int32", "type": "integer"}}, "required": ["accountCode", "addressCode", "effectiveDate", "packageDelivery", "packageType", "statementPackageNumber"], "type": "object"}, "StatementPackageCreateUpdateRequest": {"properties": {"accountApplicationId": {"enum": ["C", "D"], "type": "string"}, "accountNumber": {"type": "string"}, "addressAccountApplicationId": {"enum": ["C", "D"], "type": "string"}, "addressAccountNumber": {"type": "string"}, "addressApplicationId": {"enum": ["A", "D"], "type": "string"}, "addressNumber": {"format": "int32", "type": "integer"}, "bankNumber": {"type": "string"}, "effectiveDate": {"format": "date", "type": "string"}, "packageDelivery": {"enum": ["ELECTRONIC", "ELECTRONIC_AND_PRINT", "PRINT", "NO_STATEMENT"], "type": "string"}, "packageType": {"enum": ["ALL_ACCOUNTS", "COMPOSITE_ACCOUNTS", "DEPOSIT_ACCOUNTS", "SELECTED_ACCOUNTS"], "type": "string"}, "selectedAccounts": {"items": {"$ref": "#/components/schemas/AccountCode"}, "type": "array", "uniqueItems": true}, "statementPackageNumber": {"format": "int32", "type": "integer"}}, "required": ["accountApplicationId", "accountNumber", "addressAccountApplicationId", "addressAccountNumber", "addressApplicationId", "addressNumber", "bankNumber", "effectiveDate", "packageDelivery", "packageType", "selectedAccounts", "statementPackageNumber"], "type": "object"}, "UserFieldDropdownOptionCreate": {"properties": {"value": {"type": "string"}}, "required": ["value"], "type": "object"}, "UserFieldDropdownOptionUpdate": {"properties": {"code": {"format": "int64", "type": "integer"}, "value": {"type": "string"}}, "required": ["code", "value"], "type": "object"}, "UserFieldSelection": {"properties": {"accountNumber": {"type": "string"}, "applicationId": {"enum": ["C", "D"], "type": "string"}, "applyToChildAccounts": {"type": "boolean"}, "bankNumber": {"type": "string"}, "booleanValue": {"type": "boolean"}, "dropdownOptionCode": {"format": "int64", "type": "integer"}, "effectiveDate": {"format": "date", "type": "string"}, "expiry": {"format": "date", "type": "string"}, "freeformValue": {"type": "string"}, "isUnset": {"type": "boolean"}, "userFieldCode": {"format": "int64", "type": "integer"}}, "required": ["accountNumber", "applicationId", "applyToChildAccounts", "bankNumber", "effectiveDate", "isUnset", "userFieldCode"], "type": "object"}, "VersionableEntityId": {"properties": {"code": {"type": "string"}, "effectiveDate": {"format": "date", "type": "string"}}, "required": ["code", "effectiveDate"], "type": "object"}}}, "info": {"title": "OpenAPI definition", "version": "v0"}, "openapi": "3.1.0", "paths": {"/": {"get": {"operationId": "root", "responses": {"200": {"description": "OK"}, "400": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Bad Request"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "tags": ["api-gateway-controller"]}}, "/addAccountMapping": {"post": {"operationId": "addAccountMapping", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountMappingCreateRequest"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/HydratedAccountWithKeyAccountMapping"}, "type": "array"}}}, "description": "Created the account mapping"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Create account mapping for open, parentless account. Return mapping of the parent account", "tags": ["Accounts360"]}}, "/addAnalysisResultOption": {"post": {"operationId": "addAnalysisResultOption", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AnalysisResultOption"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AnalysisResultOption"}}}, "description": "New Analysis Result Option added"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Add a new Analysis Result Option", "tags": ["api-gateway-controller"]}}, "/addBankOptions": {"post": {"operationId": "addBankOptions", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BankOptions"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BankOptions"}}}, "description": "New Bank options added"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Add a new Bank Option", "tags": ["api-gateway-controller"]}}, "/addIndexRate": {"post": {"operationId": "addIndexRate", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/IndexRate"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/IndexRate"}}}, "description": "New Index Rate added"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Add a new Index Rate", "tags": ["api-gateway-controller"]}}, "/addService": {"post": {"operationId": "addService", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateOrUpdateService"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Service"}}}, "description": "New Service added"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Adds a new Service", "tags": ["Services"]}}, "/addServiceCategory": {"post": {"operationId": "addServiceCategory", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateServiceCategory"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ServiceCategory"}}}, "description": "New Service Category added"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Adds a new Service Category", "tags": ["Services"]}}, "/addStatementMessage": {"post": {"operationId": "addStatementMessage", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StatementMessage"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StatementMessage"}}}, "description": "New Statement Message added"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Add a new Statement Message", "tags": ["api-gateway-controller"]}}, "/addStatementPackage": {"post": {"operationId": "createStatementPackage", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StatementPackageCreateUpdateRequest"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StatementPackageCreateUpdateRequest"}}}, "description": "statement package created"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Create a statement package", "tags": ["Statement Packages"]}}, "/addUserFieldConfiguration": {"post": {"operationId": "createUserFieldConfiguration", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HydratedUserFieldConfigurationCreate"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HydratedUserFieldConfiguration"}}}, "description": "User field configuration added"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Add new user field configuration", "tags": ["api-gateway-controller"]}}, "/analyze": {"post": {"operationId": "analyze", "parameters": [{"in": "query", "name": "bankIdentifier", "required": true, "schema": {"type": "string"}}, {"in": "query", "name": "startDateInclusive", "required": true, "schema": {"type": "string"}}, {"in": "query", "name": "endDateExclusive", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}, "400": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Bad Request"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "tags": ["api-gateway-controller"]}}, "/auth": {"get": {"operationId": "auth", "responses": {"200": {"description": "OK"}, "400": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Bad Request"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "tags": ["api-gateway-controller"]}}, "/changeLogLevel": {"post": {"operationId": "changeLogLevel", "parameters": [{"in": "query", "name": "level", "required": true, "schema": {"enum": ["TRACE", "DEBUG", "INFO", "WARN", "ERROR"], "type": "string"}}], "responses": {"200": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Bad Request"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Change Logging level at root", "tags": ["api-gateway-controller"]}}, "/checkAddressCodeAvailability": {"post": {"operationId": "checkAddressCodeAvailability", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddressCodeAvailabilityRequest"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddressCodeAvailabilityResponse"}}}, "description": "Address code availability checked"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Check availability of an address code and suggest next available number if needed", "tags": ["api-gateway-controller"]}}, "/createAccountType": {"post": {"operationId": "createAccountType", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountType"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountType"}}}, "description": "Created"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Create account type", "tags": ["api-gateway-controller"]}}, "/createAccountTypeOverride": {"post": {"operationId": "createCompositeAccountTypeOverride", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountTypeOverride"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountTypeOverride"}}}, "description": "Created the override for the account"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Create an account type override", "tags": ["Accounts Settlement and Processing"]}}, "/createAddress": {"post": {"operationId": "createAddress", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Address"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Address"}}}, "description": "Address created"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Create an app generated address", "tags": ["api-gateway-controller"]}}, "/createBranch": {"post": {"operationId": "createBranch", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Branch"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Branch"}}}, "description": "Created"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Create Branch", "tags": ["api-gateway-controller"]}}, "/createCompositeAccount": {"post": {"operationId": "createCompositeAccount", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompositeAccountCreateRequest"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountWithKey"}}}, "description": "Created the composite account"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Create a composite account", "tags": ["api-gateway-controller"]}}, "/createCycleDefinition": {"post": {"operationId": "createCycleDefinition", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CycleDefinition"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CycleDefinition"}}}, "description": "New Cycle Definition added"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Add a new Cycle Definition", "tags": ["api-gateway-controller"]}}, "/createDemographicPriceList": {"post": {"operationId": "createDemographicPriceList", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HydratedDemographicPriceList"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DemographicPriceList"}}}, "description": "Successfully created demographic price list"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Create a new demographic price list", "tags": ["Demographic Price List"]}}, "/createEarningsCreditDefinition": {"post": {"operationId": "createEarningsCreditDefinition", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EarningsCreditDefinition"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EarningsCreditDefinition"}}}, "description": "Earnings Credit Definition created"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Create a Earnings Credit Definition", "tags": ["api-gateway-controller"]}}, "/createInvestableBalanceDefinition": {"post": {"operationId": "createInvestableBalanceDefinition", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvestableBalanceDefinition"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvestableBalanceDefinition"}}}, "description": "Investable Balance Definition created"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Create an Investable Balance Definition", "tags": ["api-gateway-controller"]}}, "/createOfficer": {"post": {"operationId": "createOfficer", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Officer"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Officer"}}}, "description": "Created"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Create Officer", "tags": ["api-gateway-controller"]}}, "/createRequiredBalanceDefinition": {"post": {"operationId": "createRequiredBalanceDefinition", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BalanceRequirementDefinition"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BalanceRequirementDefinition"}}}, "description": "Required Balance Definition created"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Create a Required Balance Definition", "tags": ["api-gateway-controller"]}}, "/createReserveRequirementDefinition": {"post": {"operationId": "createReserveRequirementDefinition", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReserveRequirementDefinition"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReserveRequirementDefinition"}}}, "description": "Reserve requirement was successfully created"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Add a new Reserve Requirement definition", "tags": ["api-gateway-controller"]}}, "/createServiceOverride": {"post": {"operationId": "createServiceOverride", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ServicePrice"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {}}, "description": "Service override created"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Create a new service override", "tags": ["Service Overrides"]}}, "/createStatementFormatPlan": {"post": {"operationId": "createStatementFormatPlan", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StatementFormatPlan"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StatementFormatPlan"}}}, "description": "New Statement Format Plan created"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Create a new Statement Format Plan", "tags": ["api-gateway-controller"]}}, "/createUserFieldSelections": {"post": {"operationId": "createUserFieldSelections", "requestBody": {"content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/UserFieldSelection"}, "type": "array"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/UserFieldSelection"}, "type": "array"}}}, "description": "Created the user field selections"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Create user field selections", "tags": ["api-gateway-controller"]}}, "/deleteServiceOverride": {"post": {"operationId": "deleteServiceOverride", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ServicePrice"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {}}, "description": "Service override deleted"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "edit eligible service override from a service if they exist", "tags": ["Service Overrides"]}}, "/editServiceOverride": {"post": {"operationId": "editServiceOverride", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ServicePrice"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {}}, "description": "Service override updated/edited"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "edit an existing service override", "tags": ["Service Overrides"]}}, "/getAccount": {"post": {"operationId": "getAccountWithKey", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountEffectiveDateRequest"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountWithKey"}}}, "description": "Account was successfully returned"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Get account details by account code", "tags": ["Accounts360"]}}, "/getAccountDemographicPricelists": {"post": {"operationId": "getAccountDemographicPricelists", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountEffectiveDateRequest"}}}, "required": true}, "responses": {"200": {"content": {"*/*": {"schema": {"items": {"$ref": "#/components/schemas/DemographicPriceListSummaryDTO"}, "type": "array"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Bad Request"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "tags": ["api-gateway-controller"]}}, "/getAccountMappings": {"post": {"operationId": "getMappingsOfKeyAccountAsOf", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VersionableEntityId"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/HydratedAccountWithKeyAccountMapping"}, "type": "array"}}}, "description": "Found the mappings"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Get account mappings as of a specific date", "tags": ["Accounts360"]}}, "/getAccountTimeline": {"post": {"operationId": "getAccountTimeline", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountCode"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"items": {"format": "date", "type": "string"}, "type": "array"}}}, "description": "Account timelines retrieved"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Get unique dates of account updates", "tags": ["api-gateway-controller"]}}, "/getAccountType": {"post": {"operationId": "getAccountType", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VersionableEntityId"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountType"}}}, "description": "Found the account type"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Get account type", "tags": ["api-gateway-controller"]}}, "/getAccountTypeOverride": {"post": {"operationId": "getAccountTypeOverride", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountEffectiveDateRequest"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountTypeOverride"}}}, "description": "Found overrides for the account"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Get account type overrides", "tags": ["Accounts Settlement and Processing"]}}, "/getAccountTypeTimeline": {"post": {"operationId": "getAccountType_1", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CodeRequestBody"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/AccountType"}, "type": "array"}}}, "description": "Got timeline values for the account type"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Get timeline values for an account type", "tags": ["api-gateway-controller"]}}, "/getAccountTypes": {"post": {"operationId": "getAccountTypes", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EffectiveDateRequestBody"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/AccountType"}, "type": "array"}}}, "description": "Found the account types"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Get account types", "tags": ["api-gateway-controller"]}}, "/getAccounts": {"post": {"operationId": "getAccounts", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EffectiveDateRequestBody"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/Account"}, "type": "array"}}}, "description": "Accounts were successfully returned"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Get list of accounts by effective date", "tags": ["api-gateway-controller"]}}, "/getAccountsByCodes": {"post": {"operationId": "getAccountsByCodes", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountCodesRetrieveRequest"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/Account"}, "type": "array"}}}, "description": "Accounts were successfully returned"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Get list of accounts through account codes", "tags": ["api-gateway-controller"]}}, "/getAddress": {"post": {"operationId": "get<PERSON><PERSON><PERSON>", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddressRetrieveRequest"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Address"}}}, "description": "Address found"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Get an address", "tags": ["api-gateway-controller"]}}, "/getAddresses": {"post": {"operationId": "getAddresses", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountEffectiveDateRequest"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/Address"}, "type": "array"}}}, "description": "Addresses found"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Get addresses for an account", "tags": ["api-gateway-controller"]}}, "/getAllFieldsStatementPackage": {"post": {"operationId": "getAllFieldsStatementPackage", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountEffectiveDateRequest"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {}}, "description": "Get statement packages"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Get statement packages", "tags": ["Accounts Statement Packages"]}}, "/getAnalysisResultOptionsByCode": {"post": {"operationId": "getAnalysisResultOptionsByCode", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CodeRequestBody"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/AnalysisResultOption"}, "type": "array"}}}, "description": "Analysis Result Options were successfully returned"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Get list of analysis result options through a code", "tags": ["api-gateway-controller"]}}, "/getBankOptionsByCode": {"post": {"operationId": "getBankOptionsByCode", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CodeRequestBody"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/BankOptions"}, "type": "array"}}}, "description": "All Bank Options retrieved for the given code"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Get a Bank Option with code", "tags": ["api-gateway-controller"]}}, "/getBranch": {"post": {"operationId": "getBranch", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VersionableEntityId"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Branch"}}}, "description": "Get branch by effective date and code"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Get branch by effective date and code", "tags": ["api-gateway-controller"]}}, "/getBranchTimeline": {"post": {"operationId": "retrieveBranchTimeline", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CodeRequestBody"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/Branch"}, "type": "array"}}}, "description": "Got branch timeline"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Get branch timeline", "tags": ["api-gateway-controller"]}}, "/getBranches": {"post": {"operationId": "getBranches", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EffectiveDateRequestBody"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/Branch"}, "type": "array"}}}, "description": "Got branches by effective date"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Get branches by effective date", "tags": ["api-gateway-controller"]}}, "/getCycleDefinition": {"post": {"operationId": "getCycleDefinition", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VersionableEntityId"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CycleDefinition"}}}, "description": "Cycle Definition successfully returned"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Get Cycle Definition by code and an effective date", "tags": ["api-gateway-controller"]}}, "/getCycleDefinitionByCode": {"post": {"operationId": "getCycleDefinitionByCode", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CodeRequestBody"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/CycleDefinition"}, "type": "array"}}}, "description": "Cycle definitions list were successfully returned"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Get list of cycle definition versions by code", "tags": ["api-gateway-controller"]}}, "/getCycleDefinitions": {"post": {"operationId": "getCycleDefinitions", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EffectiveDateRequestBody"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/CycleDefinition"}, "type": "array"}}}, "description": "List of Cycle Definition successfully returned"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "List all Cycle Definition by an effective date", "tags": ["api-gateway-controller"]}}, "/getDemographicCriteria": {"post": {"operationId": "getDemographicCriteria", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VersionableEntityId"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/DemographicCriteria"}, "type": "array"}}}, "description": "Found the demographic criteria"}, "400": {"content": {}, "description": "Demographic criteria not found"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Get a demographic criteria as of a specific date", "tags": ["api-gateway-controller"]}}, "/getDemographicPriceList": {"post": {"operationId": "getDemographicPriceList", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VersionableEntityId"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DemographicPriceList"}}}, "description": "Found the price list"}, "400": {"content": {}, "description": "Price list not found"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Get a demographic price list as of a specific date", "tags": ["Demographic Price List"]}}, "/getDemographicPriceListRanking": {"post": {"operationId": "getDemographicPriceListRanking", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EffectiveDateRequestBody"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DemographicPriceListRanking"}}}, "description": "Found the price list ranking"}, "400": {"content": {}, "description": "Price list ranking not found"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Get a demographic price list ranking as of a specific date", "tags": ["api-gateway-controller"]}}, "/getDemographicPriceListVersions": {"post": {"operationId": "getDemographicPriceListVersions", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CodeRequestBody"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"items": {"format": "date", "type": "string"}, "type": "array"}}}, "description": "Found the price list"}, "400": {"content": {}, "description": "Price list not found"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Get the versions of a demographic price list", "tags": ["Demographic Price List"]}}, "/getDemographicPriceLists": {"post": {"operationId": "getDemographicPriceLists", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CodesAndEffectiveDateRequestBody"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/DemographicPriceList"}, "type": "array"}}}, "description": "Found the price lists"}, "400": {"content": {}, "description": "Price list not found"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Get list of demographic price list for list of codes as of a specific date", "tags": ["Demographic Price List"]}}, "/getDepositAccounts": {"post": {"operationId": "getDepositAccounts", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EffectiveDateRequestBody"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {}}, "description": "Accounts were successfully returned"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Get list of deposit accounts by effective date", "tags": ["Accounts Settlement and Processing"]}}, "/getEarningsCreditDefinition": {"post": {"operationId": "getEarningsCreditDefinition", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VersionableEntityId"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EarningsCreditDefinition"}}}, "description": "Earnings Credit Definition retrieved"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Get a Earnings Credit Definition by code and effective date", "tags": ["api-gateway-controller"]}}, "/getEarningsCreditDefinitions": {"post": {"operationId": "getEarningsCreditDefinitions", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EffectiveDateRequestBody"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/EarningsCreditDefinition"}, "type": "array"}}}, "description": "Earnings Credit Definition retrieved"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Get Earnings Credit Definitions by effective date", "tags": ["api-gateway-controller"]}}, "/getEarningsCreditDefinitionsByCode": {"post": {"operationId": "getEarningsCreditDefinitionsByCode", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CodeRequestBody"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/EarningsCreditDefinition"}, "type": "array"}}}, "description": "Earnings Credit Definition retrieved"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Get Earnings Credit Definitions by code", "tags": ["api-gateway-controller"]}}, "/getEarningsCreditForAccount": {"post": {"operationId": "getEarningsCreditForAccount", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountCode"}}}, "required": true}, "responses": {"200": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/EarningsCreditDefinition"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Bad Request"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "tags": ["api-gateway-controller"]}}, "/getIndexRatesByCode": {"post": {"operationId": "getIndexRatesByCode", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CodeRequestBody"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/IndexRate"}, "type": "array"}}}, "description": "Index rates were successfully returned"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Get list of index rates through an index rate code", "tags": ["api-gateway-controller"]}}, "/getInvestableBalanceDefinition": {"post": {"operationId": "getInvestableBalanceDefinition", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VersionableEntityId"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvestableBalanceDefinition"}}}, "description": "Investable Balance Definition successfully returned"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Get an Investable Balance Definition by an effective date and code", "tags": ["api-gateway-controller"]}}, "/getInvestableBalanceDefinitionByCode": {"post": {"operationId": "getInvestableBalanceDefinitionByCode", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CodeRequestBody"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/InvestableBalanceDefinition"}, "type": "array"}}}, "description": "Investable Balance Definition successfully returned"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Get an Investable Balance Definition by code", "tags": ["api-gateway-controller"]}}, "/getInvestableBalanceDefinitions": {"post": {"operationId": "getInvestableBalanceDefinitions", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EffectiveDateRequestBody"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/InvestableBalanceDefinition"}, "type": "array"}}}, "description": "List of Investable Balance Definitions successfully returned"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "List all Investable Balance Definitions by an effective date", "tags": ["api-gateway-controller"]}}, "/getLeadCompositeAccountCode": {"post": {"operationId": "getLeadCompositeAccountCode", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VersionableEntityId"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountCode"}}}, "description": "Returning lead comp account code"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Return the lead composite account for a given account code", "tags": ["Accounts360"]}}, "/getNextAvailableAccountNumber": {"post": {"operationId": "getNextAvailableAccountNumber", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EffectiveDateRequestBody"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NextAvailableAccountNumberResponse"}}}, "description": "Next available account number successfully returned"}, "400": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Bad Request"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Get next available account number", "tags": ["api-gateway-controller"]}}, "/getOfficer": {"post": {"operationId": "retrieveOfficer", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VersionableEntityId"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Officer"}}}, "description": "Get officer by effective date and code"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Get officer by effective date and code", "tags": ["api-gateway-controller"]}}, "/getOfficerTimeline": {"post": {"operationId": "retrieveOfficerTimeline", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CodeRequestBody"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/Officer"}, "type": "array"}}}, "description": "Got officer timeline"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Get officer timeline", "tags": ["api-gateway-controller"]}}, "/getOfficers": {"post": {"operationId": "getOfficers", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EffectiveDateRequestBody"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/Officer"}, "type": "array"}}}, "description": "Got officers by effective date"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Get officers by effective date", "tags": ["api-gateway-controller"]}}, "/getParentlessOpenAccounts": {"post": {"operationId": "getParentlessOpenAccounts", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EffectiveDateRequestBody"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/Account"}, "type": "array"}}}, "description": "Found the accounts"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Get parentless and open accounts", "tags": ["api-gateway-controller"]}}, "/getPromotionalPriceList": {"post": {"operationId": "getPromotionalPriceList", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VersionableEntityId"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PromotionalPriceList"}}}, "description": "Found the price list"}, "400": {"content": {}, "description": "Price list not found"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Get a promotional price list as of a specific date", "tags": ["Promo Price List"]}}, "/getRequiredBalanceDefinition": {"post": {"operationId": "getRequiredBalanceDefinition", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VersionableEntityId"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BalanceRequirementDefinition"}}}, "description": "Required Balance Definition successfully returned"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Get a Required Balance Definition by effective date", "tags": ["api-gateway-controller"]}}, "/getRequiredBalanceDefinitionByCode": {"post": {"operationId": "getRequiredBalanceDefinitionByCode", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CodeRequestBody"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/BalanceRequirementDefinition"}, "type": "array"}}}, "description": "Required Balance Definition successfully returned"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Get a Required Balance Definition by code", "tags": ["api-gateway-controller"]}}, "/getRequiredBalanceDefinitions": {"post": {"operationId": "getRequiredBalanceDefinitions", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EffectiveDateRequestBody"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/BalanceRequirementDefinition"}, "type": "array"}}}, "description": "List of Required Balance Definitions successfully returned"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "List all Required Balance Definitions by an effective date", "tags": ["api-gateway-controller"]}}, "/getReserveRequirementDefinitions": {"post": {"operationId": "getReserveRequirementDefinitions", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EffectiveDateRequestBody"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/ReserveRequirementDefinition"}, "type": "array"}}}, "description": "List of reserve requirements successfully returned"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "List all reserve requirement definitions by an effective date", "tags": ["api-gateway-controller"]}}, "/getReserveRequirementsByCode": {"post": {"operationId": "getReserveRequirementsByCode", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CodeRequestBody"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/ReserveRequirementDefinition"}, "type": "array"}}}, "description": "List of reserve requirements successfully returned"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "List all reserve requirement definitions by code", "tags": ["api-gateway-controller"]}}, "/getServiceCatalogMapping": {"post": {"operationId": "getServiceCatalogMapping", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EffectiveDateRequestBody"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HydratedServiceCatalogMapping"}}}, "description": "Service catalog mapping retrieved"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Gets service catalog mappings", "tags": ["Services"]}}, "/getServiceDetailsAsOf": {"post": {"operationId": "getServiceDetailsAsOf", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VersionableEntityId"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ServiceDetails"}}}, "description": "Service details retrieved"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Gets service details given code and effective date", "tags": ["Services"]}}, "/getServiceOverridesByAccount": {"post": {"operationId": "getServiceOverrideDetailsForAccount", "parameters": [{"in": "query", "name": "filterLevel", "required": false, "schema": {"default": "noFilterLvl", "type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VersionableEntityId"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/ServiceDetails"}, "type": "array"}}}, "description": "Service override details retrieved"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Gets service override details given account code and effective date", "tags": ["Accounts Service Override"]}}, "/getServicePricesAsOf": {"post": {"operationId": "getServicePricesAsOf", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ServicePriceRequest"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/ServicePrice"}, "type": "array"}}}, "description": "Service Prices retrieved"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Gets filtered list of service prices", "tags": ["Service Pricing"]}}, "/getServiceTimeline": {"post": {"operationId": "getServiceTimeline", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CodeRequestBody"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"items": {"format": "date", "type": "string"}, "type": "array"}}}, "description": "Service timelines retrieved"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Gets unique dates of service updates", "tags": ["Services"]}}, "/getSettlementProcessingOptions": {"post": {"operationId": "getSettlementProcessingOptions", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountCodeWithEffectivedateReq"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SettlementProcessingOptionsResponseObj"}}}, "description": "get account and related override details"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "get settlement and processing options", "tags": ["Accounts Settlement and Processing"]}}, "/getSettlementProcessingOptionsTimeline": {"post": {"operationId": "getSettlementProcessingOptionsTimeline", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountCode"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"items": {"format": "date", "type": "string"}, "type": "array"}}}, "description": "List account type overrides"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Get settlement and processing options timeline", "tags": ["Accounts Settlement and Processing"]}}, "/getStatementFormatPlans": {"post": {"operationId": "getStatementFormatPlans", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EffectiveDateRequestBody"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/StatementFormatPlan"}, "type": "array"}}}, "description": "List of Statement Format Plans retrieved by effective date."}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Get Statement Format Plans by effective date", "tags": ["api-gateway-controller"]}}, "/getStatementFormatPlansByCode": {"post": {"operationId": "getStatementFormatPlanByCode", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CodeRequestBody"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/StatementFormatPlan"}, "type": "array"}}}, "description": "Statement Format Plan is retrieved by code."}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Get Statement Format Plans by code", "tags": ["api-gateway-controller"]}}, "/getStatementMessageByCode": {"post": {"operationId": "getStatementMessageByCode", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CodeRequestBody"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/StatementMessage"}, "type": "array"}}}, "description": "List of Statement Message versions successfully returned"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Get all Statement Message versions by code", "tags": ["api-gateway-controller"]}}, "/getUserFieldSelections": {"post": {"operationId": "getUserFieldSelections", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountEffectiveDateRequest"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/UserFieldSelection"}, "type": "array"}}}, "description": "Found the user field selections"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Retrieve user field selections for a given account", "tags": ["api-gateway-controller"]}}, "/getUserFieldsConfigurations": {"post": {"operationId": "getUserFieldConfigurations", "responses": {"200": {"content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/HydratedUserFieldConfiguration"}, "type": "array"}}}, "description": "Found the user field configurations"}, "400": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Bad Request"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Retrieve all user field configurations", "tags": ["api-gateway-controller"]}}, "/grabAccountMappings": {"post": {"operationId": "grabAccountMappings", "responses": {"200": {"content": {"*/*": {"schema": {"items": {"$ref": "#/components/schemas/AccountMappingEntity"}, "type": "array"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Bad Request"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "tags": ["api-gateway-controller"]}}, "/grabAllAccounts": {"post": {"operationId": "grabAllAccounts", "responses": {"200": {"content": {"*/*": {"schema": {"items": {"$ref": "#/components/schemas/AccountEntity"}, "type": "array"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Bad Request"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "tags": ["api-gateway-controller"]}}, "/grabDemographicsUpdateRecords": {"post": {"operationId": "testGetDemoUpdateRecords", "responses": {"200": {"content": {"*/*": {"schema": {"items": {"$ref": "#/components/schemas/DemographicUpdateRecordEntity"}, "type": "array"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Bad Request"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "tags": ["api-gateway-controller"]}}, "/health": {"get": {"operationId": "healthCheck", "responses": {"200": {"description": "OK"}, "400": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Bad Request"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "tags": ["api-gateway-controller"]}}, "/listAllKeyAccountMappings": {"post": {"deprecated": true, "operationId": "listAllKeyAccountMappings", "responses": {"200": {"content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/KeyAccountMapping"}, "type": "array"}}}, "description": "All key account mappings returned"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Get all key account mappings", "tags": ["Accounts360"]}}, "/listAnalysisResultOptionsByEffectiveDate": {"post": {"operationId": "listAnalysisResultOptionsByEffectiveDate", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EffectiveDateRequestBody"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/AnalysisResultOption"}, "type": "array"}}}, "description": "List of Analysis Result Option successfully returned"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "List all Analysis Result Option by an effective date", "tags": ["api-gateway-controller"]}}, "/listBankOptionsByEffectiveDate": {"post": {"operationId": "getBankOptions", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EffectiveDateRequestBody"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/BankOptions"}, "type": "array"}}}, "description": "New Bank options added"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "List Bank Options by an effective date", "tags": ["api-gateway-controller"]}}, "/listDemographicPriceLists": {"post": {"operationId": "listDemographicPriceLists", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EffectiveDateRequestBody"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/DemographicPriceListWithCount"}, "type": "array"}}}, "description": "Found the price lists"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "List all demographic price lists as of a specific date", "tags": ["Demographic Price List"]}}, "/listIndexRatesByEffectiveDate": {"post": {"operationId": "listIndexRatesByEffectiveDate", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EffectiveDateRequestBody"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/IndexRate"}, "type": "array"}}}, "description": "List of index rates successfully returned"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "List all index rates by an effective date", "tags": ["api-gateway-controller"]}}, "/listMetrics": {"get": {"operationId": "listMetrics", "responses": {"200": {"content": {"*/*": {"schema": {"items": {"type": "string"}, "type": "array"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Bad Request"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "tags": ["metrics-test-controller"]}}, "/listPromotionalPriceLists": {"post": {"operationId": "listPromotionalPriceLists", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EffectiveDateRequestBody"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/PromotionalPriceList"}, "type": "array"}}}, "description": "Found the price lists"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "List all promotional price lists as of a specific date", "tags": ["Promo Price List"]}}, "/listStatementMessagesByEffectiveDate": {"post": {"operationId": "listStatementMessageByEffectiveDate", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EffectiveDateRequestBody"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/StatementMessage"}, "type": "array"}}}, "description": "List of Statement Messages successfully returned"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "List all Statement Message by an effective date", "tags": ["api-gateway-controller"]}}, "/listStatementPackages": {"post": {"operationId": "listStatementPackages", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountEffectiveDateRequest"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/HydratedStatementPackage"}, "type": "array"}}}, "description": "List statement packages"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "List statement packages", "tags": ["Accounts Statement Packages"]}}, "/removeAccountMappings": {"post": {"operationId": "removeMappingsOfAccountAsOf", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountCodesDateLeadAccountRequest"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/HydratedAccountWithKeyAccountMapping"}, "type": "array"}}}, "description": "Remove mappings"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Remove account mappings as of a specific date", "tags": ["Accounts360"]}}, "/removeKeyAccount": {"post": {"operationId": "removeKeyAccountFromParent", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountCode"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {}}, "description": "The key account code"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Given a parent account code, remove the key account associated with it", "tags": ["Accounts360"]}}, "/removeKeyAccountMapping": {"post": {"operationId": "removeKeyAccountMapping", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountCode"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/AccountCode"}, "type": "array"}}}, "description": "Removed key account mapping"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Remove key account mapping", "tags": ["Accounts360"]}}, "/setDemographicPriceListRanking": {"post": {"operationId": "setDemographicPriceListRanking", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DemographicPriceListRanking"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DemographicPriceListRanking"}}}, "description": "Successfully saved the price list ranking"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Set a demographic price list ranking for a specific date", "tags": ["Demographic Price List"]}}, "/setKeyAccount": {"post": {"operationId": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountMappingCreateRequest"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {}}, "description": "The key account code"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Given a parent and a child (child can be multiple relations down)set the child to the parent as a key account", "tags": ["Accounts360"]}}, "/tagServiceOverride": {"post": {"operationId": "tagServiceOverride", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ServicePrice"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {}}, "description": "Service override Added"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "add service override to a service if they don't exist", "tags": ["Service Overrides"]}}, "/testGet": {"get": {"operationId": "testGet", "responses": {"200": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Bad Request"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "tags": ["api-gateway-controller"]}}, "/testGetDailyBalanceHistories": {"post": {"operationId": "testGetDailyBalanceHistories", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetDailyBalanceHistoryRequest"}}}, "required": true}, "responses": {"200": {"content": {"*/*": {"schema": {"items": {"$ref": "#/components/schemas/DailyBalanceHistory"}, "type": "array"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Bad Request"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "tags": ["api-gateway-controller"]}}, "/testPost": {"post": {"operationId": "testPost", "responses": {"200": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Bad Request"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "tags": ["api-gateway-controller"]}}, "/updateAccount": {"post": {"operationId": "updateAccount", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Account"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Account"}}}, "description": "Account timelines retrieved"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Update an existing account", "tags": ["api-gateway-controller"]}}, "/updateAccountMappings": {"post": {"operationId": "updateAccountMappings", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountMappingUpdateRequest"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/HydratedAccountWithKeyAccountMapping"}, "type": "array"}}}, "description": "Updated the account mappings"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Update list of accounts to point to new parent", "tags": ["Accounts360"]}}, "/updateAccountType": {"post": {"operationId": "updateAccountType", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountType"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountType"}}}, "description": "Updated the account type"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Update account type", "tags": ["api-gateway-controller"]}}, "/updateAddress": {"post": {"operationId": "updateAddress", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Address"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Address"}}}, "description": "Address updated"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Update an address", "tags": ["api-gateway-controller"]}}, "/updateAnalysisResultOption": {"post": {"operationId": "updateAnalysisResultOption", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AnalysisResultOption"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AnalysisResultOption"}}}, "description": "Analysis Result Option updated"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Update an existing Analysis Result Option", "tags": ["api-gateway-controller"]}}, "/updateBankOptions": {"post": {"operationId": "updateBankOptions", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BankOptions"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BankOptions"}}}, "description": "Bank Options updated for given code"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Update a Bank Option", "tags": ["api-gateway-controller"]}}, "/updateCycleDefinition": {"post": {"operationId": "updateCycleDefinition", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CycleDefinition"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CycleDefinition"}}}, "description": "Cycle Definition updated"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Update an existing Cycle Definition", "tags": ["api-gateway-controller"]}}, "/updateDemographicPriceList": {"post": {"operationId": "updateDemographicPriceList", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HydratedDemographicPriceList"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DemographicPriceList"}}}, "description": "Successfully updated demographic price list"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Update an existing demographic price list", "tags": ["Demographic Price List"]}}, "/updateEarningsCreditDefinition": {"post": {"operationId": "updateEarningsCreditDefinition", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EarningsCreditDefinition"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EarningsCreditDefinition"}}}, "description": "Earnings Credit Definition updated"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Update Earnings Credit Definition", "tags": ["api-gateway-controller"]}}, "/updateIndexRate": {"post": {"operationId": "updateIndexRate", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/IndexRate"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/IndexRate"}}}, "description": "Index Rate updated"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Update an existing Index Rate", "tags": ["api-gateway-controller"]}}, "/updateInvestableBalanceDefinition": {"post": {"operationId": "updateInvestableBalanceDefinition", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvestableBalanceDefinition"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvestableBalanceDefinition"}}}, "description": "Investable Balance Definition successfully returned"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Update an Investable Balance Definition by code", "tags": ["api-gateway-controller"]}}, "/updateRequiredBalanceDefinition": {"post": {"operationId": "updateRequiredBalanceDefinition", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BalanceRequirementDefinition"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BalanceRequirementDefinition"}}}, "description": "Required Balance Definition successfully returned"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Update a Required Balance Definition by code", "tags": ["api-gateway-controller"]}}, "/updateReserveRequirementDefinition": {"post": {"operationId": "updateReserveRequirementDefinition", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReserveRequirementDefinition"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReserveRequirementDefinition"}}}, "description": "Reserve requirement was successfully updated"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Update an existing Reserve Requirement definition", "tags": ["api-gateway-controller"]}}, "/updateService": {"post": {"operationId": "updateService", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateOrUpdateService"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Service"}}}, "description": "Service updated"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Updates an existing Service", "tags": ["Services"]}}, "/updateServiceCategory": {"post": {"operationId": "updateServiceCategory", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ServiceCategory"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ServiceCategory"}}}, "description": "Service Category updated"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Updates an existing Service Category", "tags": ["Services"]}}, "/updateSettlementProcessingOptions": {"post": {"operationId": "updateSettlementProcessingOptions", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountTypeOverride"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {}}, "description": "Updated account and account type override"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Update settlement and processing options", "tags": ["Accounts Settlement and Processing"]}}, "/updateStatementFormatPlan": {"post": {"operationId": "updateStatementFormatPlan", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StatementFormatPlan"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StatementFormatPlan"}}}, "description": "Update a Statement Format Plan."}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Update Statement Format Plan", "tags": ["api-gateway-controller"]}}, "/updateStatementMessage": {"post": {"operationId": "updateStatementMessage", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StatementMessage"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StatementMessage"}}}, "description": "Statement Message updated"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Update an existing Statement Message", "tags": ["api-gateway-controller"]}}, "/updateStatementPackage": {"post": {"operationId": "updateStatementPackage", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StatementPackageCreateUpdateRequest"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Address"}}}, "description": "Address updated"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Create a statement package", "tags": ["Statement Packages"]}}, "/updateUserFieldConfiguration": {"post": {"operationId": "updateUserFieldConfiguration", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HydratedUserFieldConfigurationUpdate"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HydratedUserFieldConfiguration"}}}, "description": "User field configuration updated"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Update an existing user field configuration", "tags": ["api-gateway-controller"]}}, "/upsertKeyAccountMapping": {"post": {"deprecated": true, "operationId": "upsertKeyAccountMapping", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/KeyAccountMapping"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/KeyAccountMapping"}}}, "description": "Upserted key account mapping"}, "400": {"content": {}, "description": "Invalid input"}, "404": {"content": {"*/*": {"schema": {"type": "string"}}}, "description": "Not Found"}}, "summary": "Upsert a key account mapping", "tags": ["Accounts360"]}}}, "servers": [{"description": "Generated server url", "url": "http://localhost"}]}