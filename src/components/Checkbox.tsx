import {
  Field,
  Label,
  Checkbox as HeadlessCheckbox,
  CheckboxProps as HeadlessCheckboxProps,
} from '@headlessui/react'
import clsx from 'clsx'

interface CheckboxProps {
  label?: string
  prefix?: boolean
}

export function Checkbox({
  className,
  label,
  prefix,
  ...props
}: React.PropsWithoutRef<
  Omit<HeadlessCheckboxProps, 'prefix'> & CheckboxProps
>) {
  return (
    <Field className={clsx('flex items-center gap-3', className)}>
      {prefix && <Label>{label}</Label>}
      <HeadlessCheckbox
        className='group block min-h-4 min-w-4 rounded border-2 bg-white data-[disabled]:cursor-not-allowed data-[checked]:border-0 data-[checked]:bg-indigo-600 data-[checked]:data-[disabled]:bg-zinc-400'
        {...props}
      >
        <svg
          className='stroke-white opacity-0 group-data-[checked]:opacity-100'
          viewBox='0 0 14 14'
          fill='none'
        >
          <path
            d='M3 8L6 11L11 3.5'
            strokeWidth={2}
            strokeLinecap='round'
            strokeLinejoin='round'
          />
        </svg>
      </HeadlessCheckbox>
      {!prefix && <Label>{label}</Label>}
    </Field>
  )
}
