import { Page } from '@playwright/test'

export class DatePickerPageObject {
  constructor(readonly page: Page) {}

  async selectDay(day: number) {
    await this.page
      .locator('button')
      .filter({ hasText: new RegExp(`^${day}$`) })
      .first()
      .click()
  }

  async apply() {
    const applyBtn = this.page.getByRole('button', { name: 'Apply' })
    await applyBtn.scrollIntoViewIfNeeded()
    await applyBtn.click()
  }
}
