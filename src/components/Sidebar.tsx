'use client'

import { Button, ButtonProps } from '@headlessui/react'
import {
  BanknotesIcon,
  CurrencyDollarIcon,
  HomeIcon,
  RectangleStackIcon,
} from '@heroicons/react/24/outline'
import { clsx } from 'clsx'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import React from 'react'

import { routeTo as routeToAccount } from '@/app/[bankId]/accounts/routing'
import { routeTo as routeToPricing } from '@/app/[bankId]/pricing/routing'
import { useEffectiveDate } from '@/app/[bankId]/services/_hooks/useEffectiveDate'
import { routeTo as routeToService } from '@/app/[bankId]/services/routing'
import {
  formatToMonthYearFromDate,
  startOfTheMonth,
  toServerFormat,
} from '@/lib/date'
export function Sidebar({
  className,
  ...props
}: React.ComponentPropsWithoutRef<'nav'>) {
  const bankId = usePathname().split('/')[1]
  const effectiveDate = useEffectiveDate()

  return (
    <nav
      className={clsx(className, 'flex h-full min-h-0 flex-col pt-[59px]')}
      {...props}
    >
      <div className='flex flex-1 flex-col overflow-y-auto'>
        <div className='flex flex-col gap-1'>
          <SidebarItem href={`/${bankId}#`}>
            <HomeIcon />
            <span className='truncate'>Dashboard</span>
          </SidebarItem>
          <SidebarItem
            href={routeToAccount('/accounts/[effectiveMonth]', {
              bankId,
              effectiveMonth: formatToMonthYearFromDate(effectiveDate),
            })}
            isActive={(path) => path.includes('/accounts')}
          >
            <BanknotesIcon />
            <span className='truncate'>Accounts</span>
          </SidebarItem>
          <SidebarItem
            href={routeToService(
              '/services/[effectiveDate]/catalog/[categoryCode]',
              {
                bankId,
                effectiveDate: toServerFormat(startOfTheMonth()),
                categoryCode: 'root',
              },
              { view: 'column', column: undefined },
            )}
            isActive={(path) => path.includes('/services')}
          >
            <RectangleStackIcon />
            <span className='truncate'>Service catalog</span>
          </SidebarItem>
          <SidebarItem
            href={routeToPricing('/pricing/[effectiveDate]/price-list', {
              bankId,
              effectiveDate,
            })}
            isActive={(path) => path.includes('/pricing')}
          >
            <CurrencyDollarIcon />
            <span className='truncate'>Pricing</span>
          </SidebarItem>
        </div>
      </div>
    </nav>
  )
}

function SidebarItem({
  children,
  isActive,
  ...props
}: {
  children: React.ReactNode
  isActive?: (path: string, href?: string) => boolean
} & (ButtonProps | React.ComponentPropsWithoutRef<typeof Link>)) {
  const pathname = usePathname()
  const active =
    isActive ?
      isActive(pathname, 'href' in props ? props.href.toString() : undefined)
    : 'href' in props ? pathname.startsWith(props.href.toString())
    : false

  const classes = [
    'flex w-full items-center gap-3 rounded-lg px-4 py-2.5 text-left font-medium',
    'data-[slot=icon]:*:size-5 data-[slot=icon]:*:shrink-0',
  ]

  if (active) {
    classes.push('bg-indigo-200 text-indigo-800')
  } else {
    classes.push(
      'hover:bg-indigo-100 hover:text-indigo-800',
      'active:bg-indigo-100 hover:text-indigo-800',
    )
  }

  const classNames = classes.join(' ')

  return (
    <span className='relative'>
      {'href' in props ?
        <Link {...props} className={classNames}>
          {children}
        </Link>
      : <Button {...props} className={'cursor-default ' + classNames}>
          {children}
        </Button>
      }
    </span>
  )
}
