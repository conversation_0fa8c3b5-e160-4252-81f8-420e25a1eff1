import { Switch } from '@headlessui/react'
import { clsx } from 'clsx'
import {
  ComponentPropsWithoutRef,
  createContext,
  PropsWithChildren,
  useContext,
  useEffect,
  useState,
} from 'react'

export type ToggleRenderPropArg = {
  checked: boolean
  toggle: () => void
  setChecked: (val: boolean) => void
}

const toggleContext = createContext({
  checked: false,
  toggle: () => {},
  setChecked: (val: boolean) => {},
})

export function Toggle({
  children,
  defaultChecked,
}: PropsWithChildren & { defaultChecked?: boolean }) {
  const [checked, setChecked] = useState(defaultChecked ?? false)
  const toggle = () => setChecked(!checked)

  return (
    <toggleContext.Provider value={{ checked, toggle, setChecked }}>
      {children}
    </toggleContext.Provider>
  )
}

export function ToggledOn({ children }: PropsWithChildren) {
  const { checked } = useContext(toggleContext)
  return checked ? children : undefined
}

export function ToggledOff({ children }: PropsWithChildren) {
  const { checked } = useContext(toggleContext)
  return !checked ? children : undefined
}

export type ToggleSwitchProps = Omit<
  ComponentPropsWithoutRef<typeof Switch>,
  'enabled'
> & { label?: string; defaultChecked?: boolean }

export function ToggleSwitch({
  className,
  label,
  defaultChecked,
  onChange,
  ...props
}: ToggleSwitchProps) {
  const { checked, toggle, setChecked } = useContext(toggleContext)
  useEffect(() => {
    if (defaultChecked !== undefined) {
      setChecked(defaultChecked)
    }
  }, [defaultChecked])

  return (
    <Switch
      className={clsx(
        className,
        'group relative inline-flex h-6 w-11 shrink-0',
        'rounded-full border-2 border-transparent bg-zinc-200',
        'transition-colors duration-200 ease-in-out',
        'focus:outline-none focus:ring-2 focus:ring-indigo-600 focus:ring-offset-2',
        'data-[checked]:bg-indigo-600',
        'cursor-pointer',
      )}
      checked={checked}
      onChange={(checked) => {
        toggle()
        onChange?.(checked)
      }}
      {...props}
    >
      {label && <span className='sr-only'>{label}</span>}
      <span
        aria-hidden='true'
        className={clsx(
          'inline-block size-5 rounded-full bg-white shadow ring-0',
          'transform transition duration-200 ease-in-out',
          'group-data-[checked]:translate-x-5',
          'pointer-events-none',
        )}
      />
    </Switch>
  )
}
