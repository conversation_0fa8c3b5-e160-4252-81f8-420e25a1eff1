export function AtelioLogo() {
  return (
    <div>
      <svg
        width='81'
        height='31'
        viewBox='0 0 81 31'
        fill='none'
        xmlns='http://www.w3.org/2000/svg'
      >
        <g clipPath='url(#clip0_287_59676)'>
          <mask
            id='mask0_287_59676'
            maskUnits='userSpaceOnUse'
            x='0'
            y='0'
            width='81'
            height='31'
          >
            <path d='M81 0H0V31H81V0Z' fill='white' />
          </mask>
          <g mask='url(#mask0_287_59676)'>
            <path
              d='M31.3267 21.5177C29.8622 21.5177 28.685 21.1017 27.7973 20.27C26.9094 19.4382 26.4651 18.1991 26.4651 16.5568V9.0705H23.8872L23.4219 6.30779H26.4651V1.85059H29.6586V6.30682H33.4667V9.06953H29.6586V16.3792C29.6586 17.1116 29.8395 17.6723 30.2 18.0582C30.5615 18.4443 31.0545 18.6381 31.6788 18.6381C32.3818 18.6381 32.9681 18.5196 33.4362 18.2832L33.6702 21.1646C32.9671 21.4018 32.1849 21.5196 31.3267 21.5196V21.5177Z'
              fill='black'
            />
            <path
              d='M48.9139 13.2309C48.9139 11.9813 48.6293 10.8127 48.0641 9.72517C47.497 8.63475 46.6681 7.75373 45.5738 7.08115C44.4795 6.40662 43.1511 6.0708 41.6467 6.0708C40.1423 6.0708 38.85 6.42207 37.7663 7.12457C36.6804 7.829 35.8564 8.76888 35.2911 9.9471C34.724 11.1253 34.4414 12.4271 34.4414 13.7935C34.4414 15.1599 34.7345 16.4395 35.3187 17.6273C35.905 18.8171 36.7558 19.7608 37.869 20.4652C38.9822 21.1677 40.2917 21.5191 41.7933 21.5191C43.5317 21.5191 44.9972 21.1225 46.1876 20.3321C47.3798 19.539 48.1697 18.4205 48.5609 16.9751L45.5435 16.201C45.3483 17.0136 44.923 17.6427 44.2683 18.0886C43.6136 18.5343 42.8086 18.7563 41.8521 18.7563C40.9731 18.7563 40.2118 18.5536 39.5657 18.1465C38.9215 17.7411 38.4248 17.1612 38.0718 16.4096C37.8167 15.8644 37.6549 15.2506 37.5874 14.5674H48.854C48.8939 14.1708 48.9128 13.725 48.9128 13.2309H48.9139ZM37.7159 12.1019C37.8948 11.2508 38.2126 10.5415 38.674 9.97702C39.4068 9.0767 40.3972 8.62412 41.6467 8.62412C42.781 8.62412 43.6849 8.94256 44.3577 9.57656C45.0313 10.2096 45.4158 11.0501 45.5139 12.1019H37.7159Z'
              fill='black'
            />
            <path
              d='M63.5885 2.01015C63.5885 0.899354 62.6939 0 61.5891 0C60.4843 0 59.5898 0.899354 59.5898 2.01015V2.04461C59.5898 3.15539 60.4843 4.05475 61.5891 4.05475C62.6939 4.05475 63.5885 3.15539 63.5885 2.04461V2.01015Z'
              fill='#4BCD3E'
            />
            <path
              d='M67.9438 2.01015C67.9438 0.899354 67.0494 0 65.9446 0C64.8397 0 63.9453 0.899354 63.9453 2.01015V2.04461C63.9453 3.15539 64.8397 4.05475 65.9446 4.05475C67.0494 4.05475 67.9438 3.15539 67.9438 2.04461V2.01015Z'
              fill='#4BCD3E'
              fillOpacity='0.8'
            />
            <path
              d='M72.2954 2.01015C72.2954 0.899354 71.4009 0 70.2961 0C69.1913 0 68.2969 0.899354 68.2969 2.01015V2.04461C68.2969 3.15539 69.1913 4.05475 70.2961 4.05475C71.4009 4.05475 72.2954 3.15539 72.2954 2.04461V2.01015Z'
              fill='#4BCD3E'
              fillOpacity='0.6'
            />
            <path
              d='M76.6469 2.01015C76.6469 0.899354 75.7525 0 74.6477 0C73.543 0 72.6484 0.899354 72.6484 2.01015V2.04461C72.6484 3.15539 73.543 4.05475 74.6477 4.05475C75.7525 4.05475 76.6469 3.15539 76.6469 2.04461V2.01015Z'
              fill='#4BCD3E'
              fillOpacity='0.4'
            />
            <path
              d='M80.9985 2.01015C80.9985 0.899354 80.1039 0 78.9991 0C77.8946 0 77 0.899354 77 2.01015V2.04461C77 3.15539 77.8946 4.05475 78.9991 4.05475C80.1039 4.05475 80.9985 3.15539 80.9985 2.04461V2.01015Z'
              fill='#4BCD3E'
              fillOpacity='0.2'
            />
            <path
              d='M63.2011 6.30713H60.0078V21.2815H63.2011V6.30713Z'
              fill='black'
            />
            <path
              d='M69.1911 20.5233C68.0396 19.8593 67.1368 18.9396 66.4819 17.7604C65.8273 16.5822 65.5 15.2612 65.5 13.7934C65.5 12.3257 65.8273 10.9815 66.4819 9.81101C67.1368 8.64339 68.0396 7.72762 69.1911 7.06372C70.3425 6.39984 71.6518 6.06787 73.1182 6.06787C74.5846 6.06787 75.8911 6.39984 77.0425 7.06372C78.1937 7.72762 79.0996 8.64339 79.7517 9.81101C80.4062 10.9786 80.7336 12.3064 80.7336 13.7934C80.7336 15.2805 80.4062 16.6054 79.7517 17.7759C79.0968 18.9435 78.1937 19.8593 77.0425 20.5233C75.8911 21.187 74.5816 21.519 73.1182 21.519C71.6548 21.519 70.3453 21.187 69.1911 20.5233ZM75.402 18.073C76.0671 17.6572 76.5838 17.0773 76.955 16.3342C77.3269 15.5902 77.5116 14.7439 77.5116 13.7934C77.5116 12.843 77.3269 11.9967 76.955 11.2527C76.5827 10.5096 76.0671 9.9297 75.402 9.51379C74.7388 9.09789 73.9765 8.88848 73.1173 8.88848C72.2581 8.88848 71.4959 9.09789 70.8325 9.51379C70.1693 9.9297 69.6506 10.5048 69.2797 11.2372C68.9074 11.9696 68.7228 12.8208 68.7228 13.7925C68.7228 14.7642 68.9074 15.6153 69.2797 16.3477C69.6516 17.0801 70.1674 17.6553 70.8325 18.0711C71.4959 18.487 72.2581 18.6965 73.1173 18.6965C73.9765 18.6965 74.7388 18.487 75.402 18.0711V18.073Z'
              fill='black'
            />
            <path
              d='M21.6253 21.5383L20.6936 20.9236C16.7771 18.3402 13.9328 16.4644 11.1724 15.4415C10.5795 17.163 9.85346 18.4918 8.96377 19.4886C7.83998 20.7479 6.48968 21.4282 4.95102 21.5102L4.87109 21.5121H4.85205C3.47324 21.5121 2.28282 21.0441 1.40929 20.1594C0.510059 19.2504 -0.00378526 17.9128 2.0997e-05 16.4925C-0.00283371 15.3055 0.436789 14.1041 1.2066 13.2067C1.90029 12.3961 3.22201 11.406 5.57905 11.2883H5.59331C6.79039 11.2391 7.91038 11.3047 8.9885 11.4755L11.473 3.06287C12.0335 1.32784 13.5065 0.206543 15.2251 0.206543C16.9436 0.206543 18.4165 1.32784 18.9771 3.06287L18.9847 3.08796L24.0519 19.6778L21.6253 21.5392V21.5383ZM5.73035 14.5644C3.47419 14.6802 3.23344 15.9501 3.23533 16.4914C3.23438 17.0453 3.40568 17.5461 3.69495 17.8395C3.94996 18.098 4.31821 18.2264 4.81779 18.2322C6.10336 18.1406 7.17578 16.9391 8.01219 14.6551C7.27664 14.5634 6.52205 14.5325 5.73035 14.5644ZM12.123 12.2948C14.5095 13.1594 16.8665 14.5422 19.6422 16.3312L15.8988 4.07319C15.7236 3.54439 15.3697 3.48745 15.2251 3.48745C15.0804 3.48745 14.7283 3.54439 14.5532 4.0674L12.1239 12.2948H12.123Z'
              fill='black'
            />
            <path
              d='M56.5496 18.6374C55.9244 18.6374 55.4316 18.4435 55.0709 18.0577C54.7092 17.6716 54.5294 17.1119 54.5294 16.3785V0.245117H51.3359V16.557C51.3359 18.1995 51.7804 19.4384 52.6682 20.2701C53.5558 21.1021 54.7339 21.5179 56.1975 21.5179C56.8169 21.5179 57.3963 21.4552 57.9359 21.3316L57.7065 18.5044C57.365 18.593 56.9797 18.6374 56.5505 18.6374H56.5496Z'
              fill='black'
            />
            <path
              d='M61.9268 29.6606C61.4481 29.6606 61.0225 29.4603 60.8326 29.1521V29.5991H59.875V24.1431H60.8326V26.1466C61.0529 25.8539 61.4633 25.6535 61.9421 25.6535C63.0743 25.6535 63.7506 26.4703 63.7506 27.657C63.7506 28.8438 63.0743 29.6606 61.9268 29.6606ZM60.7717 27.657C60.7717 28.3583 61.1746 28.8206 61.79 28.8206C62.3979 28.8206 62.8008 28.3583 62.8008 27.657C62.8008 26.9558 62.3979 26.4934 61.79 26.4934C61.1746 26.4934 60.7717 26.9558 60.7717 27.657ZM66.8205 25.7151H67.8007L65.7795 31.0016H64.8067L65.3995 29.4448L63.9101 25.7151H64.9512L65.8706 28.2119L66.8205 25.7151ZM70.1912 29.5991V24.2046H73.7932V25.1063H71.2248V26.3547H73.2993V27.264H71.2248V29.5991H70.1912ZM74.525 29.5991V24.2046H75.5584V29.5991H74.525ZM78.3062 29.6606C77.2576 29.6606 76.3836 28.944 76.2545 27.9653L77.1739 27.6802C77.2576 28.3274 77.7515 28.7668 78.3442 28.7668C78.899 28.7668 79.2638 28.497 79.2638 28.0885C79.2638 26.9249 76.4445 27.58 76.4445 25.6226C76.4445 24.7595 77.2424 24.1431 78.291 24.1431C79.3018 24.1431 80.0997 24.6901 80.2668 25.5071L79.3397 25.8924C79.2334 25.3684 78.8458 25.0369 78.291 25.0369C77.7819 25.0369 77.4703 25.2605 77.4703 25.6226C77.4703 26.6013 80.3124 25.8769 80.3124 28.0655C80.3124 29.021 79.507 29.6606 78.3062 29.6606Z'
              fill='black'
            />
          </g>
        </g>
        <defs>
          <clipPath id='clip0_287_59676'>
            <rect width='81' height='31' fill='white' />
          </clipPath>
        </defs>
      </svg>
      <span className='sr-only'>Atelio</span>
    </div>
  )
}
