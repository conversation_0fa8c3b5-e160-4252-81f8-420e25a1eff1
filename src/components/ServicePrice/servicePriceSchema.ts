import {
  balanceType,
  code,
  costType,
  disposition,
  int,
  number,
  priceType,
} from '@/api/zodSchemas'
import { match } from '@/lib/unions/match'
import { variant } from '@/lib/unions/Union'
import { z } from 'zod'

// From https://bitbucket.fis.dev/projects/AFIN/repos/af_fpb/browse/fpb/src/main/java/com/fisglobal/af/fpb/services/catalog/serviceprice/ServicePriceEntity.java
export const servicePriceSchema = z.object({
  code: code(),
  effectiveDate: z.string().date(),
  serviceCode: code(),
  pricingHierarchyEntryType: z.enum(['STANDARD', 'PRICE_LIST', 'OVERRIDE']),
  pricingHierarchyEntryCode: code(),
  tierNumber: int,

  // Properties which apply to all service prices. These are only optional because
  // service pricing from price lists and overrides do not need to provide them.
  // But default pricing (and lead price lists) must set these.
  priceType: priceType.nullable(),
  priceValue: number.nullable(),
  disposition: disposition.nullable(),

  // Properties that may apply to any service price.
  minimumFee: number.nullable(),
  maximumFee: number.nullable(),
  costType: costType.nullable(),
  costValue: number.nullable(),

  // Properties valid for balance-based pricing (including balance-based tiers
  // within tiered pricing)
  balanceType: balanceType.nullable(),

  // Properties valid for any service price with a currency.
  currency: z.literal('USD'), //.nullable(),

  // Properties valid for recurring service pricing.
  units: int.nullable(),
  // TODO: Right now we just hardcode some string values for the cycle definition,
  // but in the future we'll need to load cycle definitions from an API, each one
  // will have a code, and the selected code will be stored here.
  cycleDefinitionCode: z.string().nullable(),

  // Properties valid for tiered service pricing.
  tierPriceType: z.enum(['UNIT_PRICED', 'FLAT_FEE', 'INDEXED']).nullable(),
  tierMinVolumeInclusive: int.nullable(),
  tierMaxVolumeExclusive: int.nullable(),
  tierMinBalanceInclusive: number.nullable(),
  tierMaxBalanceExclusive: number.nullable(),

  // Properties valid for tiered service pricing, where the tier uses balanced-based
  // pricing (e.g. indexed, percentage).
  // TODO: indexRateCode doesn't exist in the ServicePriceEntity, but it probably should?
  //       And right now we're just hardcoding the values this can take, but in the future
  //       we'll need to load the values from an API, each value has a code, and the code
  //       will be stored here.
  indexRateCode: z.string().nullable(),
  basisDays: z.enum(['365', '366']).nullable(),
  indexMultiplier: number.nullable(),

  // Properties valid for a subset of service prices (these could technically
  // be applied to all service prices, but by convention they're only populated
  // for certain serviceType + priceType combos).
  baseFee: number.nullable(),
})

export const pricingHierarchyEntryTypeLabel = (
  value: z.infer<typeof servicePriceSchema>['pricingHierarchyEntryType'],
) =>
  match(variant(value), {
    OVERRIDE: () => 'Account',
    PRICE_LIST: () => 'Price list',
    STANDARD: () => 'Default',
  })

export type ServicePrice = z.input<typeof servicePriceSchema>

export const servicePriceDefaultValues: ServicePrice = {
  code: '000',
  effectiveDate: '',
  serviceCode: 'ABC',
  pricingHierarchyEntryCode: 'ABC',
  pricingHierarchyEntryType: 'STANDARD',
  tierNumber: 0,
  priceType: null,
  priceValue: null,
  disposition: null,
  minimumFee: null,
  maximumFee: null,
  costType: 'NO_COST',
  costValue: null,
  balanceType: null,
  currency: 'USD',
  units: 0,
  cycleDefinitionCode: null,
  tierPriceType: null,
  tierMinVolumeInclusive: null,
  tierMaxVolumeExclusive: null,
  tierMinBalanceInclusive: null,
  tierMaxBalanceExclusive: null,
  indexRateCode: null,
  basisDays: '365',
  indexMultiplier: null,
  baseFee: null,
}
