import {
  ReactFormExtended<PERSON><PERSON>,
  useField,
  useForm,
  useStore,
} from '@tanstack/react-form'
import { If } from '../If'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
} from '../Table/Table'
import { SelectField, SelectOption } from '../Input/Select'
import clsx from 'clsx'
import { CheckIcon } from '@heroicons/react/24/outline'
import { TextInputField } from '../Input/TextInput'
import { PropsWithChildren, ReactNode, useEffect, useMemo } from 'react'
import { requiredWhen } from '@/lib/validation/requiredWhen'
import { PRICE_TYPES_BY_SERVICE_TYPE } from '@/app/types'
import { Toggle, ToggledOff, ToggledOn, ToggleSwitch } from '../Toggle'
import { formToApiSchemas, Service, ServiceForm } from '@/api/formToApiSchema'
import {
  costTypeLabel,
  dispositionLabel,
  priceTypeLabel,
  pricingHierarchyEntryTypeLabel,
} from '@/strings/enums'
import { enumOptions } from '@/lib/validation/enumOptions'
import { formatCurrency } from '@/strings/currency'
import { useQuery } from '@tanstack/react-query'
import { query } from '@/app/[bankId]/services/queries'
import { ApiToFromServicePriceFormExt } from '@/app/[bankId]/pricing/add-price-list/step-3/page'
import { includes } from '@/lib/functional/includes'
import { PricingTiersModal } from '@/app/[bankId]/pricing/add-price-list/step-3/PricingTiersModal'
import { ModalOpenButton } from '../Modal'
import { preventDefault } from '@/lib/preventDefault'
import { getSupportedSubPriceTypes } from '@/app/[bankId]/services/_components/ServicePrice/servicePriceSchema'
import { ServicePriceTier } from '@/app/[bankId]/services/[effectiveDate]/(service)/UpdateServiceForm'
import { useAddPriceListContext } from '@/app/[bankId]/pricing/add-price-list/context'

export type ServicePriceProps = React.ComponentPropsWithoutRef<'div'> & {
  compareTo: ApiToFromServicePriceFormExt
  form: ReactFormExtendedApi<ApiToFromServicePriceFormExt>
  serviceType: ServiceForm['serviceType']
  compareToService: Service | null
  isEdit: boolean
}

// We have to provide the compareToPrice as a prop -- because the page needs
// to be able to set the form value to === the compareToPrice.
export function ServicePrice({
  compareTo,
  compareToService,
  form,
  serviceType,
  isEdit,
  ...props
}: ServicePriceProps) {
  const [
    costType,
    pricingHierarchyEntryType,
    priceType,
    indexRateCode,
    tierPriceType,
    pricingHierarchyEntryCode,
  ] = useStore(form.store, (state) => [
    state.values.costType,
    state.values.pricingHierarchyEntryType,
    state.values.priceType,
    state.values.indexRateCode,
    state.values.tierPriceType,
    state.values.pricingHierarchyEntryCode,
  ])

  const priceListForm = useAddPriceListContext()!

  const [pricingTiers] = useStore(priceListForm.store, (s) => [
    s.values.pricingTiers,
  ])

  const allowedPriceTypes = useMemo(
    () => (serviceType ? PRICE_TYPES_BY_SERVICE_TYPE[serviceType] : []),
    [serviceType],
  )

  const effectiveDate = useStore(
    form.store,
    (state) => state.values.effectiveDate,
  )
  const cycleDefinition = useQuery({
    ...query('/getCycleDefinitions', {
      effectiveDate: effectiveDate,
    }),
    enabled: effectiveDate != undefined,
  }).data

  const FeeCycles = useMemo(() => {
    let obj: Record<string, Record<string, string>> = {}
    cycleDefinition?.forEach((cycle) => {
      obj[`${cycle.code}`] = {
        description: cycle.description!,
        code: cycle?.code,
        type: cycle?.cycleType,
      }
    })
    return obj
  }, [cycleDefinition])

  const indexRateDefinition = useQuery({
    ...query('/listIndexRatesByEffectiveDate', {
      effectiveDate: form?.state?.values?.effectiveDate,
    }),
    enabled: form?.state?.values?.effectiveDate != undefined,
  }).data

  const IndexRates = useMemo(() => {
    let obj: Record<
      string,
      { description: string; code: string; indexRate: number | null }
    > = {}
    indexRateDefinition?.forEach((indexRate) => {
      obj[`${indexRate.code}`] = {
        description: indexRate.name!,
        code: indexRate.code,
        indexRate: indexRate?.indexRate,
      }
    })
    return obj
  }, [indexRateDefinition])

  const selectedIndexRate = useMemo(() => {
    if (indexRateCode == null) {
      return null
    }

    return IndexRates[indexRateCode]
  }, [IndexRates, indexRateCode])

  const activeIndexRate = useMemo(() => {
    if (compareTo.indexRateCode == null) {
      return null
    }

    return IndexRates[compareTo.indexRateCode]
  }, [IndexRates, compareTo.indexRateCode])

  // TODO: do we really need this "apply service to" thing? We'll use a form
  // to store its value for now, so that we can re-use our `<Select>` component.
  const chargeServiceToForm = useForm({
    defaultValues: {
      chargeServiceTo:
        compareTo.units === null || Number(compareTo.units) === 0 ?
          'Only certain deposit accounts'
        : 'All deposit accounts',
    },
  })

  const chargeServiceTo = useStore(
    chargeServiceToForm.store,
    (state) => state.values.chargeServiceTo,
  )

  const activePricingTiers = [
    ...compareTo.pricingTiers
      .filter((p) =>
        includes(p.priceType, ['PARTITIONED_TIER', 'THRESHOLD_TIER']),
      )
      .filter((p) =>
        isEdit ?
          p.pricingHierarchyEntryCode === pricingHierarchyEntryCode
        : p.pricingHierarchyEntryType === 'STANDARD',
      ),
  ].sort((a, b) => a.tierNumber - b.tierNumber)

  // The user may have set an allowed price type, but then changed the service
  // type such that the current price type is not allowed -- in this case we
  // reset the price type.
  useEffect(() => {
    if (priceType && !allowedPriceTypes.includes(priceType)) {
      form.setFieldValue('priceType', null)
    }
  }, [form, allowedPriceTypes, priceType])

  const pricingTiersField = useField<
    ApiToFromServicePriceFormExt,
    'pricingTiers'
  >({ form, name: 'pricingTiers' })

  return (
    <Table {...props}>
      <TableHead>
        <TableRow className='rounded-t-lg bg-zinc-100 text-sm text-zinc-500'>
          <TableCell className={'border-r'}>Change</TableCell>
          <TableCell>Pricing attribute</TableCell>
          <TableCell>
            {pricingHierarchyEntryTypeLabel(pricingHierarchyEntryType)} value
          </TableCell>
          <TableCell>
            {pricingHierarchyEntryTypeLabel(
              compareTo.pricingHierarchyEntryType,
            )}{' '}
            value
          </TableCell>
        </TableRow>
      </TableHead>
      <TableBody className='overflow-y-scroll'>
        <If true={allowedPriceTypes.length > 0}>
          <form.Field name='priceType'>
            {(field) => (
              <ServicePriceRow
                label='Price type'
                checked={field.state.value !== compareTo.priceType}
                defaultValue={compareTo.priceType}
                renderDefaultValue={priceTypeLabel}
                onReset={field.handleChange}
              >
                <SelectField
                  className='w-full'
                  field={field}
                  options={allowedPriceTypes}
                  renderOption={priceTypeLabel}
                  renderSelected={priceTypeLabel}
                  renderErrors={false}
                />
              </ServicePriceRow>
            )}
          </form.Field>
        </If>
        <If
          true={includes(priceType, [
            'FLAT_FEE',
            'UNIT_PRICED',
            'INDEXED',
            'PERCENTAGE',
          ])}
        >
          <form.Field name='priceValue'>
            {(field) => (
              <ServicePriceRow
                label='Price'
                checked={field.state.value !== compareTo.priceValue}
                defaultValue={compareTo.priceValue}
                renderDefaultValue={formatCurrency}
                onReset={field.handleChange}
              >
                <TextInputField
                  className='w-full'
                  field={field}
                  type='number'
                  prefix={priceType === 'PERCENTAGE' ? undefined : '$'}
                  suffix={priceType === 'PERCENTAGE' ? '%' : undefined}
                  renderErrors={false}
                />
              </ServicePriceRow>
            )}
          </form.Field>
        </If>
        <If true={priceType === 'UNIT_PRICED'}>
          <form.Field name='baseFee'>
            {(field) => (
              <ServicePriceRow
                label='Base fee'
                checked={field.state.value !== compareTo.baseFee}
                defaultValue={compareTo.baseFee}
                renderDefaultValue={formatCurrency}
                onReset={field.handleChange}
              >
                <TextInputField
                  className='w-full'
                  field={field}
                  type='number'
                  prefix='$'
                  renderErrors={false}
                />
              </ServicePriceRow>
            )}
          </form.Field>
        </If>
        <If true={includes(priceType, ['PARTITIONED_TIER', 'THRESHOLD_TIER'])}>
          <form.Field name='tierPriceType'>
            {(field) => {
              return (
                <>
                  <Toggle
                    defaultChecked={
                      field.state.value !== 'INDEXED' ||
                      JSON.stringify(form.state.values.pricingTiers) !==
                        JSON.stringify(compareTo.pricingTiers) ||
                      form.state.values.indexRateCode !==
                        compareTo.indexRateCode
                    }
                  >
                    <ServicePriceAttribute
                      rowspan={field.state.value === 'INDEXED' ? 3 : 2}
                      label='Price subtype'
                      defaultValue={compareTo.tierPriceType}
                      onReset={field.handleChange}
                      renderDefaultValue={priceTypeLabel}
                    >
                      <SelectField
                        className='mb-1 w-full'
                        field={field}
                        options={getSupportedSubPriceTypes(serviceType)}
                        renderOption={priceTypeLabel}
                        renderSelected={priceTypeLabel}
                        renderErrors={false}
                      />
                    </ServicePriceAttribute>
                    <ServicePriceAttribute
                      withToggle={false}
                      label='Price'
                      defaultValue={compareTo.pricingTiers.length}
                      renderDefaultValue={() => {
                        return (
                          <PricingTiersModal
                            confirmText='Close'
                            title='Pricing tiers'
                            subtitle=''
                            viewOnly
                            viewOnlyTiers={
                              activePricingTiers as ServicePriceTier[]
                            }
                            indexRate={
                              activeIndexRate?.indexRate?.toString() ?? '0'
                            }
                            serviceType={compareToService?.serviceType!}
                            subPriceType={compareTo.tierPriceType}
                          >
                            <div className='flex flex-row items-baseline'>
                              {activePricingTiers.length}
                              <ModalOpenButton
                                className='ml-2 flex items-center gap-1 border-none p-3 hover:bg-app-color-bg-secondary'
                                onClick={preventDefault()}
                              >
                                View
                              </ModalOpenButton>
                            </div>
                          </PricingTiersModal>
                        )
                      }}
                      onReset={() => {}}
                    >
                      <PricingTiersModal
                        confirmText='Save'
                        title='Configure pricing tiers'
                        subtitle=''
                        indexRate={
                          selectedIndexRate?.indexRate?.toString() ?? '0'
                        }
                        serviceType={serviceType}
                        subPriceType={field.state.value}
                      >
                        <div className='flex flex-row items-baseline'>
                          {pricingTiers.length} tiers
                          <ModalOpenButton
                            className='ml-2 flex items-center gap-1 border-none p-3 hover:bg-app-color-bg-secondary'
                            onClick={preventDefault()}
                          >
                            Configure
                          </ModalOpenButton>
                        </div>
                      </PricingTiersModal>
                    </ServicePriceAttribute>
                    <If true={field.state.value === 'INDEXED'}>
                      <form.Field name='indexRateCode'>
                        {(field) => (
                          <ServicePriceAttribute
                            withToggle={false}
                            label='Index rate'
                            defaultValue={compareTo.indexRateCode}
                            renderDefaultValue={(indexRateCode) =>
                              indexRateCode ?
                                <div>
                                  <span>{indexRateCode} </span>
                                  <span className='text-app-color-text-secondary'>
                                    {selectedIndexRate?.indexRate}%
                                  </span>
                                </div>
                              : '-'
                            }
                            onReset={field.handleChange}
                          >
                            <SelectField
                              className='w-full'
                              field={field}
                              renderSelected={(key) => (
                                <>
                                  <span>{key} </span>
                                  <span className='text-app-color-text-secondary'>
                                    {IndexRates[key]?.indexRate}
                                  </span>
                                </>
                              )}
                              renderErrors={false}
                            >
                              {Object.entries(IndexRates).map(
                                ([key, value]) => (
                                  <SelectOption
                                    key={key}
                                    value={key}
                                    className='relative flex items-center'
                                  >
                                    <span className='flex-1 truncate'>
                                      {key}
                                    </span>
                                    <span className='text-app-color-text-secondary'>
                                      {(value.indexRate as number).toFixed(2)}%
                                    </span>
                                    <span
                                      className={clsx(
                                        'ml-2 flex-shrink items-center text-indigo-600',
                                        'group-[&:not([data-selected])]:invisible',
                                      )}
                                    >
                                      <CheckIcon
                                        aria-hidden='true'
                                        className='size-5'
                                      />
                                    </span>
                                  </SelectOption>
                                ),
                              )}
                            </SelectField>
                          </ServicePriceAttribute>
                        )}
                      </form.Field>
                    </If>
                  </Toggle>
                </>
              )
            }}
          </form.Field>
        </If>

        <If true={serviceType === 'RECURRING'}>
          <chargeServiceToForm.Field name='chargeServiceTo'>
            {(field) => (
              <ServicePriceRow
                label='Charge this service to'
                defaultValue={
                  Number(compareTo.units ?? 0) > 0 ?
                    'All deposit accounts'
                  : 'Only certain deposit accounts'
                }
                onReset={field.handleChange}
              >
                <SelectField
                  className='w-full'
                  field={field}
                  options={[
                    'Only certain deposit accounts',
                    'All deposit accounts',
                  ]}
                  renderErrors={false}
                />
              </ServicePriceRow>
            )}
          </chargeServiceToForm.Field>
          <If true={chargeServiceTo === 'All deposit accounts'}>
            <form.Field
              name='units'
              validators={requiredWhen(
                () => chargeServiceTo === 'All deposit accounts',
              )}
            >
              {(field) => (
                <ServicePriceRow
                  label='Units'
                  defaultValue={compareTo.units}
                  onReset={field.handleChange}
                >
                  <TextInputField
                    className='w-full'
                    field={field}
                    type='number'
                    renderErrors={false}
                  />
                </ServicePriceRow>
              )}
            </form.Field>
          </If>
        </If>

        <If true={priceType === 'INDEXED'}>
          <form.Field name='indexRateCode'>
            {(field) => (
              <ServicePriceRow
                label='Index rate'
                defaultValue={compareTo.indexRateCode}
                renderDefaultValue={(indexRateCode) =>
                  indexRateCode ?
                    <div>
                      <span>{indexRateCode} </span>
                      <span className='text-app-color-text-secondary'>
                        {IndexRates[indexRateCode].indexRate}%
                      </span>
                    </div>
                  : '-'
                }
                onReset={field.handleChange}
              >
                <SelectField
                  className='w-full'
                  field={field}
                  renderSelected={(key) => (
                    <>
                      <span>{key} </span>
                      <span className='text-app-color-text-secondary'>
                        {IndexRates[key].indexRate}
                      </span>
                    </>
                  )}
                  renderErrors={false}
                >
                  {Object.entries(IndexRates).map(([key, value]) => (
                    <SelectOption
                      key={key}
                      value={key}
                      className='relative flex items-center'
                    >
                      <span className='flex-1 truncate'>{key}</span>
                      <span className='text-app-color-text-secondary'>
                        {(value.indexRate as number).toFixed(2)}%
                      </span>
                      <span
                        className={clsx(
                          'ml-2 flex-shrink items-center text-indigo-600',
                          'group-[&:not([data-selected])]:invisible',
                        )}
                      >
                        <CheckIcon aria-hidden='true' className='size-5' />
                      </span>
                    </SelectOption>
                  ))}
                </SelectField>
              </ServicePriceRow>
            )}
          </form.Field>
        </If>
        <If
          true={
            serviceType === 'BALANCE_BASED' &&
            includes(priceType, ['PARTITIONED_TIER', 'THRESHOLD_TIER']) &&
            tierPriceType === 'UNIT_PRICED'
          }
        >
          <form.Field name='balanceDivisor'>
            {(field) => (
              <ServicePriceRow
                label='Balance divisor'
                defaultValue={compareTo.balanceDivisor}
                onReset={field.handleChange}
              >
                <TextInputField
                  className='w-full'
                  field={field}
                  type='number'
                  renderErrors={false}
                />
              </ServicePriceRow>
            )}
          </form.Field>
        </If>
        <If
          true={serviceType === 'RECURRING' || serviceType === 'BALANCE_BASED'}
        >
          <form.Field name='cycleDefinitionCode'>
            {(field) => (
              <ServicePriceRow
                label='Fee cycle'
                checked={field.state.value !== compareTo.cycleDefinitionCode}
                defaultValue={compareTo.cycleDefinitionCode}
                renderDefaultValue={(cycleDefinitionCode) =>
                  cycleDefinitionCode ?
                    <div className='flex-1'>
                      <p className='truncate'>{cycleDefinitionCode}</p>
                      <p className='truncate text-sm text-app-color-text-secondary'>
                        {FeeCycles[cycleDefinitionCode]?.description}
                      </p>
                    </div>
                  : '-'
                }
                onReset={field.handleChange}
              >
                <SelectField
                  className='w-full'
                  field={field}
                  renderErrors={false}
                >
                  {Object.entries(FeeCycles).map(
                    ([cycleDefinitionCode, value]) => (
                      <SelectOption
                        key={cycleDefinitionCode}
                        value={cycleDefinitionCode}
                        className='relative flex items-center'
                      >
                        <div className='flex-1'>
                          <p className='truncate'>
                            {FeeCycles[cycleDefinitionCode]?.code +
                              '-' +
                              FeeCycles[cycleDefinitionCode]?.type}
                          </p>
                          <p className='truncate text-sm text-app-color-text-secondary'>
                            {FeeCycles[cycleDefinitionCode]?.description}
                          </p>
                        </div>
                        <span
                          className={clsx(
                            'flex-shrink items-center text-indigo-600',
                            'group-[&:not([data-selected])]:invisible group-data-[focus]:text-white',
                          )}
                        >
                          <CheckIcon aria-hidden='true' className='size-5' />
                        </span>
                      </SelectOption>
                    ),
                  )}
                </SelectField>
              </ServicePriceRow>
            )}
          </form.Field>
        </If>

        <form.Field name='disposition'>
          {(field) => (
            <ServicePriceRow
              label='Disposition'
              checked={field.state.value !== compareTo.disposition}
              defaultValue={compareTo.disposition}
              renderDefaultValue={dispositionLabel}
              onReset={field.handleChange}
            >
              <SelectField
                className='w-full'
                field={field}
                options={enumOptions(
                  formToApiSchemas.servicePrice.shape.disposition,
                )}
                renderOption={dispositionLabel}
                renderSelected={dispositionLabel}
                renderErrors={false}
              />
            </ServicePriceRow>
          )}
        </form.Field>
        <If true={!!priceType && priceType !== 'NOT_PRICED'}>
          <form.Field name='minimumFee'>
            {(field) => (
              <ServicePriceRow
                label='Minimum fee'
                checked={field.state.value !== compareTo.minimumFee}
                defaultValue={compareTo.minimumFee}
                renderDefaultValue={formatCurrency}
                onReset={field.handleChange}
              >
                <TextInputField
                  className='w-full'
                  field={field}
                  type='number'
                  prefix='$'
                  renderErrors={false}
                />
              </ServicePriceRow>
            )}
          </form.Field>
          <form.Field name='maximumFee'>
            {(field) => (
              <ServicePriceRow
                label='Maximum fee'
                checked={field.state.value !== compareTo.maximumFee}
                defaultValue={compareTo.maximumFee}
                renderDefaultValue={formatCurrency}
                onReset={field.handleChange}
              >
                <TextInputField
                  className='w-full'
                  field={field}
                  type='number'
                  prefix='$'
                  renderErrors={false}
                />
              </ServicePriceRow>
            )}
          </form.Field>
        </If>

        <form.Field name='costType'>
          {(field) => (
            <ServicePriceRow
              label='Cost type'
              checked={field.state.value !== compareTo.costType}
              defaultValue={compareTo.costType}
              renderDefaultValue={costTypeLabel}
              onReset={field.handleChange}
            >
              <SelectField
                className='w-full'
                field={field}
                options={enumOptions(
                  formToApiSchemas.servicePrice.shape.costType,
                )}
                renderOption={costTypeLabel}
                renderSelected={costTypeLabel}
                renderErrors={false}
              />
            </ServicePriceRow>
          )}
        </form.Field>
        <If true={costType === 'FLAT_COST' || costType === 'UNIT_COST'}>
          <form.Field name='costValue'>
            {(field) => (
              <ServicePriceRow
                label='Cost'
                checked={field.state.value !== compareTo.costValue}
                defaultValue={compareTo.costValue}
                renderDefaultValue={formatCurrency}
                onReset={field.handleChange}
              >
                <TextInputField
                  className='w-full'
                  field={field}
                  type='number'
                  prefix='$'
                  renderErrors={false}
                />
              </ServicePriceRow>
            )}
          </form.Field>
        </If>
      </TableBody>
    </Table>
  )
}

function ServicePriceAttribute<Value extends ReactNode>({
  children,
  checked,
  defaultValue,
  label: name,
  onReset,
  renderDefaultValue,
  required,
  rowspan,
  withToggle = true,
}: PropsWithChildren<{
  checked?: boolean
  defaultValue: Value
  label: string
  onReset: (value: Value) => void
  required?: boolean
  rowspan?: number
  withToggle?: boolean
  renderDefaultValue?: (value: Value) => ReactNode
}>) {
  return (
    <TableRow className='border-b-[1px]'>
      {withToggle && (
        <TableCell className='w-24 border-r' rowSpan={rowspan}>
          <ToggleSwitch
            defaultChecked={checked}
            label={`Change ${name.toLowerCase()}`}
            onChange={(checked) => {
              if (checked) return
              onReset(defaultValue)
            }}
          />
        </TableCell>
      )}
      <TableCell>
        {name}
        {required && <span className='pl-1'>*</span>}
      </TableCell>
      <TableCell>
        <ToggledOn>{children}</ToggledOn>
        <DoesNotChangeWhenToggledOff />
      </TableCell>
      <TableCell>
        <span>
          {renderDefaultValue ? renderDefaultValue(defaultValue) : defaultValue}
        </span>
      </TableCell>
    </TableRow>
  )
}

function ServicePriceRow<Value extends ReactNode>({
  children,
  ...props
}: PropsWithChildren<{
  checked?: boolean
  defaultValue: Value
  label: string
  onReset: (value: Value) => void
  required?: boolean
  rowspan?: number
  renderDefaultValue?: (value: Value) => ReactNode
}>) {
  return (
    <Toggle>
      <ServicePriceAttribute {...props}>{children}</ServicePriceAttribute>
    </Toggle>
  )
}

const DoesNotChangeWhenToggledOff = () => (
  <ToggledOff>
    <span className='py-2 text-app-color-secondary'>Does not change</span>
  </ToggledOff>
)
