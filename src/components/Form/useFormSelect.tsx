import {
  Deep<PERSON>eys,
  DeepValue,
  FieldValidators,
  FormApi,
  ReactFormExtendedApi,
  Validator,
} from '@tanstack/react-form'
import { SelectField } from '@/components/Input/Select'
import clsx from 'clsx'
import { useMemo } from 'react'

interface FormSelectProps<
  TState,
  TName,
  TFormValidator extends Validator<TState, unknown> | undefined,
  TFieldValidators,
> {
  name: TName
  label: string
  validators?: TFieldValidators
  options?: DeepValue<TState, TName>[]
  onChange?: (
    value: DeepValue<TState, TName>,
    form: FormApi<TState, TFormValidator>,
  ) => void
  disabled?: boolean
  className?: string
  required?: boolean
}

export function useFormSelect<
  TState,
  TFormValidator extends Validator<TState, unknown> | undefined = undefined,
>({ form }: { form: ReactFormExtendedApi<TState, TFormValidator> }) {
  return useMemo(() => {
    return function FormSelect<
      TName extends DeepKeys<TState>,
      TFieldValidators extends FieldValidators<
        TState,
        TName,
        undefined,
        TFormValidator,
        DeepValue<TState, TName>
      >,
    >({
      name,
      validators,
      label,
      className,
      onChange,
      required,
      disabled,
      options,
      ...props
    }: FormSelectProps<TState, TName, TFormValidator, TFieldValidators> &
      Omit<
        React.ComponentPropsWithoutRef<
          typeof SelectField<DeepValue<TState, TName>>
        >,
        'field' | 'onChange' | 'value'
      >) {
      return (
        <form.Field name={name} validators={validators}>
          {(field) => (
            <SelectField
              className={clsx(className, 'flex-1')}
              field={field}
              required={required}
              disabled={disabled}
              options={options}
              label={label}
              onChange={(value) => onChange?.(value, form)}
              {...props}
            />
          )}
        </form.Field>
      )
    }
  }, [form])
}
