import { TextInputField } from '@/components/Input/TextInput'
import {
  Deep<PERSON>eys,
  useField,
  FormApi,
  FieldValidators,
  DeepValue,
  Validator,
  FieldListeners,
} from '@tanstack/react-form'
import { useEffect, useMemo } from 'react'
import { defaultMeta } from './defaultMeta'

interface FormTextInputProps<
  TName extends DeepKeys<TState>,
  TFieldValidators,
  TState,
> {
  name: TName
  validators?: TFieldValidators
  listeners?: FieldListeners<
    TState,
    TName,
    undefined,
    undefined,
    DeepValue<TState, TName>
  >
}

export function useFormTextInput<
  TState,
  TFromValidator extends Validator<TState, unknown> | undefined = undefined,
>({ form }: { form: FormApi<TState, TFromValidator> }) {
  return useMemo(() => {
    return function FormInput<
      TName extends DeepKeys<TState>,
      TFieldValidators extends FieldValidators<
        TState,
        TName,
        undefined,
        TFromValidator,
        DeepValue<TState, TName>
      >,
    >({
      name,
      validators,
      readonly,
      listeners,
      ...props
    }: FormTextInputProps<TName, TFieldValidators, TState> &
      Omit<
        React.ComponentPropsWithoutRef<
          typeof TextInputField<DeepValue<TState, TName>>
        >,
        'field'
      >) {
      const field = useField({ name, form, validators, listeners })

      useEffect(() => {
        // reset the validation state for a field if component is unmounted
        return () => field.setMeta(defaultMeta)
      }, [field])

      return (
        <TextInputField
          className='flex-1'
          field={field}
          readonly={readonly}
          {...props}
        />
      )
    }
  }, [form])
}
