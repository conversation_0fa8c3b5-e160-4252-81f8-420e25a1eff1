import { useEffect, useMemo } from 'react'

import ColorPickerInput from '../Input/ColorPickerInput'
import {
  DeepKeys,
  useField,
  FormApi,
  FieldValidators,
  DeepValue,
  Validator,
  FieldListeners,
} from '@tanstack/react-form'
import { defaultMeta } from './defaultMeta'

interface FormColorPickerProps<TName, TFieldValidators> {
  name: TName
  label: string
  validators?: TFieldValidators
  required?: boolean
}
export function useFormColorPicker<
  TState,
  TFormValidator extends Validator<TState, unknown> | undefined = undefined,
>({ form }: { form: FormApi<TState, TFormValidator> }) {
  return useMemo(() => {
    return function FormColorPicker<
      TName extends DeepKeys<TState>,
      TFieldValidators extends FieldValidators<
        TState,
        TName,
        undefined,
        TFormValidator,
        DeepValue<TState, TName>
      >,
    >({
      name,
      label,
      validators,
      required,
      ...props
    }: FormColorPickerProps<TName, TFieldValidators>) {
      const {
        state: { meta, value },
        handleChange,
        setMeta,
      } = useField<TState, TName, undefined, TFormValidator>({
        name,
        form,
        validators,
      })

      useEffect(() => {
        // reset the validation state for a field if component is unmounted
        return () => setMeta(defaultMeta)
      }, [setMeta])

      const errors = meta.errors.filter((e) => typeof e === 'string')
      const showErrors = meta.isTouched && errors.length > 0

      return (
        <div className='flex-1'>
          <label
            htmlFor={`color-picker-${name}`}
            className='text-sm font-medium'
          >
            {label}
            {required && <span className='pl-1'>*</span>}
          </label>
          <ColorPickerInput
            onChange={(value) =>
              handleChange(value as DeepValue<TState, TName>)
            }
            value={value as string}
            name={name as string}
            errors={errors}
            showErrors={showErrors}
            {...props}
          />
        </div>
      )
    }
  }, [form])
}
