import {
  <PERSON><PERSON><PERSON><PERSON>,
  use<PERSON>ield,
  FormApi,
  FieldValidators,
  DeepValue,
  Validator,
} from '@tanstack/react-form'
import { useEffect, useMemo } from 'react'
import { MonthPicker } from '../Filter/MonthPicker'
import clsx from 'clsx'

import { defaultMeta } from './defaultMeta'
import { formatToServerString } from '@/lib/date'

interface FormMonthPickerProps<TName, TFieldValidators> {
  name: TName
  label: string
  validators?: TFieldValidators
  required?: boolean
  onChange?: (value: string) => void
}

export function useFormMonthPicker<
  TState,
  TFormValidator extends Validator<TState, unknown> | undefined = undefined,
>({ form }: { form: FormApi<TState, TFormValidator> }) {
  return useMemo(() => {
    return function FormMonthPicker<
      TName extends DeepKeys<TState>,
      TFieldValidators extends FieldValidators<
        TState,
        TName,
        undefined,
        TFormValidator,
        DeepValue<TState, TName>
      >,
    >({
      name,
      validators,
      label,
      required,
      onChange,
    }: FormMonthPickerProps<TName, TFieldValidators>) {
      const {
        state: { meta, value },
        handleChange,
        setMeta,
      } = useField<TState, TName, undefined, TFormValidator>({
        name,
        form,
        validators,
      })
      useEffect(() => {
        return () => setMeta(defaultMeta)
      }, [setMeta])

      return (
        <>
          <label
            htmlFor={`month-picker-${name}`}
            className='mb-1 text-sm font-medium'
          >
            {label}
            {required && <span className='pl-1'>*</span>}
          </label>
          <MonthPicker
            id={`month-picker-${name}`}
            className='h-10 ring-1 ring-inset ring-gray-300'
            initialDate={value as Date | string}
            onDateChange={(value) => {
              const tempValue = formatToServerString(value)
              handleChange(tempValue as DeepValue<TState, TName>)
              onChange?.(tempValue)
            }}
          />
          {meta.errors.length > 0 && (
            <div
              className={clsx(
                'min-h-5 text-sm',
                'text-app-color-button-primary-error-bg',
              )}
            >
              {meta.errors.join('')}
            </div>
          )}
        </>
      )
    }
  }, [form])
}
