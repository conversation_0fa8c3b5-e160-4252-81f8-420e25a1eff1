import {
  <PERSON><PERSON><PERSON><PERSON>,
  use<PERSON>ield,
  FormApi,
  FieldValidators,
  DeepValue,
  Validator,
} from '@tanstack/react-form'
import { useEffect, useMemo } from 'react'
import { DateFilter } from '../Filter/DateFilter'
import clsx from 'clsx'
import { isDate } from '@/lib/guards/isDate'
import { defaultMeta } from './defaultMeta'
import { parseServerFormat, toServerFormat } from '@/lib/date'

interface FormDatePickerProps<TName, TFieldValidators> {
  name: TName
  label: string
  validators?: TFieldValidators
}

export function useFormDatePicker<
  TState,
  TFormValidator extends Validator<TState, unknown> | undefined = undefined,
>({ form }: { form: FormApi<TState, TFormValidator> }) {
  return useMemo(() => {
    return function FormDatePicker<
      TName extends DeepKeys<TState>,
      TFieldValidators extends FieldValidators<
        TState,
        TName,
        undefined,
        TFormValidator,
        DeepValue<TState, TName>
      >,
    >({
      name,
      validators,
      label,
    }: FormDatePickerProps<TName, TFieldValidators>) {
      const {
        state: { meta, value },
        handleChange,
        setMeta,
      } = useField<TState, TName, undefined, TFormValidator>({
        name,
        form,
        validators,
      })

      useEffect(() => {
        return () => setMeta(defaultMeta)
      }, [setMeta])

      const representAsString = typeof value === 'string'

      const initialDate =
        isDate(value) ? value
        : typeof value === 'string' ? parseServerFormat(value)
        : undefined

      return (
        <>
          <label
            htmlFor={`date-picker-${name}`}
            className='text-sm font-medium'
          >
            {label}
          </label>
          <DateFilter
            id={`date-picker-${name}`}
            className='max-h-9 min-h-9 flex-1 ring-1 ring-inset ring-gray-300'
            initialDate={initialDate}
            onDateChange={(value) =>
              handleChange(
                (representAsString ?
                  toServerFormat(value)
                : value) as DeepValue<TState, TName>,
              )
            }
          />
          {meta.errors.length > 0 && (
            <div
              className={clsx(
                'min-h-5 text-sm',
                'text-app-color-button-primary-error-bg',
              )}
            >
              {meta.errors.join('')}
            </div>
          )}
        </>
      )
    }
  }, [form])
}
