import {
  Button as HeadlessButt<PERSON>,
  <PERSON>over,
  PopoverButton,
  PopoverPanel,
} from '@headlessui/react'
import { ChevronDownIcon, XMarkIcon } from '@heroicons/react/24/outline'
import { clsx } from 'clsx'
import React, { ReactNode } from 'react'

export type FilterProps = {
  id?: string
  label: React.ReactNode
  icon?: ReactNode
  inUse?: boolean
  onClear?: () => void
  classNameButton?: string
  disabled?: boolean
}

export function Filter({
  children,
  id,
  label,
  icon,
  inUse,
  onClear,
  className,
  disabled,
  ...props
}: FilterProps & React.ComponentPropsWithoutRef<typeof PopoverPanel>) {
  return (
    <Popover
      className={clsx(
        'group flex rounded-md text-sm shadow-sm focus-visible:outline',
        className,
      )}
    >
      <PopoverButton id={id} type='button' className='flex w-full'>
        <span className='inline-flex h-full w-full items-center justify-between gap-x-1.5 px-2.5 py-1'>
          {icon && (
            <div aria-hidden='true' className='-mr-0.5 h-5 w-5'>
              {icon}
            </div>
          )}
          {label}
          <ChevronDownIcon aria-hidden='true' className='-mr-0.5 h-5 w-5' />
        </span>
        {onClear && inUse && (
          <HeadlessButton
            as='span'
            className='border-l border-indigo-300 px-2.5 py-1 hover:bg-indigo-300'
            onClick={(e: React.MouseEvent) => {
              e.preventDefault()
              onClear?.()
            }}
          >
            <XMarkIcon className='h-5 w-5' />
          </HeadlessButton>
        )}
      </PopoverButton>
      {!disabled && (
        <PopoverPanel
          anchor='bottom start'
          className={clsx(
            'z-10 mt-1 rounded-md border border-zinc-300 bg-white p-4 pb-2',
          )}
          {...props}
        >
          {children}
        </PopoverPanel>
      )}
    </Popover>
  )
}
