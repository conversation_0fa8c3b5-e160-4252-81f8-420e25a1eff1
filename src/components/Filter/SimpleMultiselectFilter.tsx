import { Checkbox, Field, Label } from '@headlessui/react'
import { Filter } from './Filter'
import { Button } from '../Button'
import clsx from 'clsx'

export type SimpleMultiselectFilterProps<Options extends readonly string[]> = {
  defaultLabel: string
  name: string
  options: Options
  value: Options[number][] | null
  onValueChange: (value: Options[number][] | null) => void
  renderLabel?: (value: Options[number]) => React.ReactNode
}

export function SimpleMultiselectFilter<Options extends readonly string[]>({
  defaultLabel,
  name,
  options,
  value,
  onValueChange,
  renderLabel,
}: SimpleMultiselectFilterProps<Options>) {
  const formatLabel = (value: Options[number][] | null) => {
    if (!value || value.length === 0) return defaultLabel
    return (
      value.length === 1 ?
        renderLabel ? renderLabel(value[0])
        : value[0]
      : `${defaultLabel} (${value.length})`
    )
  }
  const label = formatLabel(value)

  const inUse = !!value && value.length > 0

  return (
    <Filter
      label={label}
      inUse={inUse}
      className={clsx(
        inUse ?
          'border border-indigo-300 bg-indigo-100 hover:bg-indigo-200 focus-visible:outline-indigo-300'
        : 'border border-zinc-300 bg-zinc-50 hover:bg-zinc-200 focus-visible:outline-zinc-300',
      )}
      onClear={() => onValueChange(null)}
    >
      {({ close }) => (
        <form
          onSubmit={(e) => {
            e.preventDefault()
            onValueChange(
              new FormData(e.currentTarget).getAll(name) as Options[number][],
            )
            close()
          }}
          onReset={() => {
            onValueChange(null)
            close()
          }}
          className='flex flex-col gap-2'
        >
          {options.map((option) => (
            <Field key={option} className='flex items-center gap-2'>
              <Checkbox
                name={name}
                value={option}
                defaultChecked={!!value && value.includes(option)}
                className='group block size-4 rounded border bg-white data-[checked]:bg-indigo-600'
              >
                <svg
                  className='stroke-white opacity-0 group-data-[checked]:opacity-100'
                  viewBox='0 0 14 14'
                  fill='none'
                >
                  <path
                    d='M3 8L6 11L11 3.5'
                    strokeWidth={2}
                    strokeLinecap='round'
                    strokeLinejoin='round'
                  />
                </svg>
              </Checkbox>
              <Label>
                {renderLabel ? renderLabel(option as Options[number]) : option}
              </Label>
            </Field>
          ))}
          <div className='mt-2 flex gap-2 border-t pt-4'>
            <Button type='reset' className='flex-auto'>
              Clear
            </Button>
            <Button type='submit' className='btn-primary flex-auto text-white'>
              Apply
            </Button>
          </div>
        </form>
      )}
    </Filter>
  )
}
