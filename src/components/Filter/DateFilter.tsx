import { useState } from 'react'
import { Filter } from './Filter'
import { DayPicker } from 'react-day-picker'
import { Button } from '../Button'
import { preventDefault } from '@/lib/preventDefault'
import { toUIFormat } from '@/lib/date'

export type DateFilterProps = {
  id?: string | undefined
  label?: string
  initialDate?: Date
  onDateChange: (date: Date) => void
  title?: string
  className?: string
}

export function DateFilter({
  id,
  label,
  initialDate,
  onDateChange,
  title,
  className,
}: DateFilterProps) {
  const [internalDate, setInternalDate] = useState<Date>(
    initialDate ?? new Date(),
  )
  const [externalDate, setExternalDate] = useState<Date>(internalDate)

  const fullLabel =
    label ? `${label}: ${toUIFormat(externalDate)}` : toUIFormat(externalDate)
  return (
    <Filter id={id} label={fullLabel} className={`${className} h-10`} inUse>
      {({ close }) => (
        <div className='flex flex-col gap-4'>
          {title && (
            <div className='text-center text-lg font-semibold'>
              <span>{title}</span>
            </div>
          )}
          <DayPicker
            mode='single'
            required
            selected={internalDate}
            defaultMonth={internalDate}
            onSelect={setInternalDate}
            classNames={{
              day: 'w-12 h-12 text-center',
              months: 'flex relative',
              nav: 'flex items-center absolute w-full',
              button_next: 'btn rounded-md ml-auto',
              button_previous: 'btn rounded-md',
              month: 'flex flex-col gap-6',
              month_caption: 'text-center font-semibold',
              outside: 'opacity-25',
              selected: 'btn-primary rounded-full text-white',
            }}
          />
          <form
            onSubmit={preventDefault((event) => {
              event.stopPropagation()
              onDateChange(internalDate)
              setExternalDate(internalDate)
              close()
            })}
            onReset={() => {
              setInternalDate(externalDate)
              close()
            }}
            className='mt-2 flex gap-2 border-t pt-4'
          >
            <Button type='reset' className='btn flex-auto'>
              Cancel
            </Button>
            <Button type='submit' className='btn-primary flex-auto text-white'>
              Apply
            </Button>
          </form>
        </div>
      )}
    </Filter>
  )
}
