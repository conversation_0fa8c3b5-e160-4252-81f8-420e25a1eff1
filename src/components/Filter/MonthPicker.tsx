import { useState } from 'react'
import {
  ChevronLeftIcon,
  ChevronRightIcon,
  CalendarIcon,
} from '@heroicons/react/24/outline'

import {
  formatToMonthYearFromDate,
  formatToMonthYearFromInts,
} from '@/lib/date'
import { Filter } from './Filter'
import { Button } from '../Button'

export type MonthPickerProps = {
  id?: string | undefined
  prefix?: string
  initialDate?: Date | string
  onDateChange: (date: string) => void
  disableFutureDates?: boolean
  showIcon?: boolean
  title?: string
  className?: string
}

const MONTHS = [
  'Jan',
  'Feb',
  'Mar',
  'Apr',
  'May',
  'Jun',
  'Jul',
  'Aug',
  'Sep',
  'Oct',
  'Nov',
  'Dec',
]
const MonthClassNames = {
  default:
    'w-1/4 h-1/3 flex justify-center items-center select-none cursor-pointer rounded',
  active: 'bg-black text-white',
  disabled: 'text-zinc-400 cursor-not-allowed',
}

const FilterClassName =
  'outline outline-1 -outline-offset-1 outline-zinc-300 hover:bg-zinc-100 focus-visible:outline-indigo-600'

export function MonthPicker({
  id,
  prefix,
  initialDate,
  onDateChange,
  disableFutureDates,
  showIcon,
  title,
  className,
}: MonthPickerProps) {
  const currentYearMonth = formatToMonthYearFromDate(new Date())
  const initialYearMonth =
    initialDate ? formatToMonthYearFromDate(initialDate) : currentYearMonth

  const [currentYear, currentMonth] = currentYearMonth
    .split('-')
    .map((el) => parseInt(el))
  const [initialYearValue, initialMonthValue] = initialYearMonth
    .split('-')
    .map((el) => parseInt(el))

  const [activeMonthValue, setActiveMonthValue] =
    useState<number>(initialMonthValue)
  const [activeYearValue, setActiveYearValue] =
    useState<number>(initialYearValue)

  const currentValue = formatToMonthYearFromInts(
    activeMonthValue,
    activeYearValue,
  )

  const reset = () => {
    setActiveYearValue(initialYearValue)
    setActiveMonthValue(initialMonthValue)
  }

  const monthIsDisabled = (monthNumber: number) => {
    if (!disableFutureDates) {
      return false
    }
    if (
      activeYearValue > currentYear ||
      (activeYearValue === currentYear && monthNumber > currentMonth)
    ) {
      return true
    }
    return false
  }

  const getMonthClassName = (monthNumber: number) => {
    if (monthIsDisabled(monthNumber)) {
      return `${MonthClassNames.default} ${MonthClassNames.disabled}`
    } else if (monthNumber === activeMonthValue) {
      return `${MonthClassNames.default} ${MonthClassNames.active}`
    } else {
      return MonthClassNames.default
    }
  }

  return (
    <Filter
      id={id}
      icon={showIcon && <CalendarIcon />}
      label={`${prefix ? prefix + ':' : ''} ${currentValue}`}
      className={`${FilterClassName} ${className} h-10`}
      inUse
    >
      {({ close }) => (
        <div className='flex flex-col gap-4'>
          {title && (
            <div className='text-center text-lg font-semibold'>
              <span>{title}</span>
            </div>
          )}
          <div>
            {/* Year Nav */}
            <div className='m-3 flex select-none flex-wrap items-center justify-between'>
              <ChevronLeftIcon
                className='h-[16px] w-[16px] cursor-pointer'
                onClick={() => setActiveYearValue(activeYearValue - 1)}
              />
              <div className='font-medium'>{activeYearValue}</div>
              <ChevronRightIcon
                className='h-[16px] w-[16px] cursor-pointer'
                onClick={() =>
                  (!disableFutureDates || activeYearValue + 1 <= currentYear) &&
                  setActiveYearValue(activeYearValue + 1)
                }
              />
            </div>

            {/* Month Picker */}
            <div className='flex h-[120px] w-[345px] flex-wrap items-center justify-center'>
              {MONTHS.map((month: string, i: number) => (
                <div
                  className={getMonthClassName(i + 1)}
                  key={month}
                  onClick={() =>
                    !monthIsDisabled(i + 1) && setActiveMonthValue(i + 1)
                  }
                >
                  {month}
                </div>
              ))}
            </div>
          </div>

          {/* Controls */}
          <div className='mt-2 flex gap-2 border-t pt-4'>
            <Button
              type='button'
              className='btn flex-auto'
              onClick={() => {
                reset()
                close()
              }}
            >
              Cancel
            </Button>
            <Button
              type='button'
              className='btn-primary flex-auto text-white'
              onClick={() => {
                onDateChange(currentValue)
                close()
              }}
            >
              Apply
            </Button>
          </div>
        </div>
      )}
    </Filter>
  )
}
