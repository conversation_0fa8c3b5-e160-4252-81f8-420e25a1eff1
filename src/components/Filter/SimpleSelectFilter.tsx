import {
  Listbox,
  ListboxButton,
  ListboxOption,
  ListboxOptions,
} from '@headlessui/react'
import { ChevronDownIcon } from '@heroicons/react/24/outline'
import { clsx } from 'clsx'

export type SimpleSelectFilterProps<Options extends readonly string[]> = {
  defaultLabel?: string
  name?: string
  options: Options
  value: Options[number]
  onValueChange: (value: Options[number]) => void
}

export function SimpleSelectFilter<Options extends readonly string[]>({
  defaultLabel,
  name,
  options,
  value,
  onValueChange,
}: SimpleSelectFilterProps<Options>) {
  const label = value !== null ? value : defaultLabel || ''

  return (
    <Listbox name={name} value={value} onChange={onValueChange}>
      <ListboxButton
        className={clsx(
          'flex rounded-md border text-sm shadow-sm focus-visible:outline',
          'border-indigo-300 bg-indigo-100 hover:bg-indigo-200 focus-visible:outline-indigo-300',
        )}
      >
        <span className='inline-flex items-center gap-x-1.5 px-2.5 py-1'>
          {label}
          <ChevronDownIcon aria-hidden='true' className='-mr-0.5 h-5 w-5' />
        </span>
      </ListboxButton>
      <ListboxOptions
        anchor='bottom start'
        className='mt-1 flex flex-col rounded-md border border-zinc-300 bg-white p-2'
      >
        {options.map((option) => (
          <ListboxOption
            key={option}
            value={option}
            className='cursor-pointer rounded-md p-2 hover:bg-indigo-200'
          >
            {option}
          </ListboxOption>
        ))}
      </ListboxOptions>
    </Listbox>
  )
}
