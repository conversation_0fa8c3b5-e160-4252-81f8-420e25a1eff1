import Link from 'next/link'
import { OrderedTabState, useOrderedTabsContext } from './OrderedTabs'

export type OnTabProps = {
  children: React.ReactNode | ((state: OrderedTabState) => React.ReactNode)
  select: (state: OrderedTabState) => boolean
}

/**
 * Conditionally renders its children, given the result of passing the current
 * OrderedTabState to a given `select` function.
 *
 * @param param0
 * @returns
 */
export function OnTab({ select, children }: OnTabProps) {
  const state = useOrderedTabsContext()
  const shouldRender = select(state)
  if (!shouldRender) return
  return typeof children === 'function' ? children(state) : children
}

type TabLinkProps = React.PropsWithChildren<
  Omit<React.ComponentPropsWithoutRef<typeof Link>, 'href'>
>

/**
 * Render a `<Link>` to the next tab -- only renders if next tab exists (i.e.
 * will render nothing if the current tab is the last tab in the list).
 *
 * @param param0
 * @returns
 */
export function NextTabLink({ children, ...props }: TabLinkProps) {
  return (
    <OnTab select={(state) => !!state.nextTab}>
      {({ nextTab }) => (
        <Link href={nextTab!.href} {...props}>
          {children}
        </Link>
      )}
    </OnTab>
  )
}

/**
 * Render a `<Link>` to the previous tab -- only renders if previous tab exists
 * (i.e. will render nothing if the current tab is the first tab in the list).
 *
 * @param param0
 * @returns
 */
export function PrevTabLink({ children, ...props }: TabLinkProps) {
  return (
    <OnTab select={(state) => !!state.prevTab}>
      {({ prevTab }) => (
        <Link href={prevTab!.href} {...props}>
          {children}
        </Link>
      )}
    </OnTab>
  )
}
