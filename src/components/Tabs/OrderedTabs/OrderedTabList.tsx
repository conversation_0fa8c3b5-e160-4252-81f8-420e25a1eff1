import { useEffect } from 'react'
import { setActiveTab, useOrderedTabsDispatchContext } from './OrderedTabs'

export type OrderedTabListProps = React.ComponentPropsWithoutRef<'div'> & {
  activeTab: string
}

export function OrderedTabList({
  activeTab,
  children,
  ...props
}: OrderedTabListProps) {
  const dispatch = useOrderedTabsDispatchContext()
  useEffect(() => dispatch(setActiveTab(activeTab)), [dispatch, activeTab])

  return (
    <div {...props}>
      <nav className='flex'>{children}</nav>
    </div>
  )
}
