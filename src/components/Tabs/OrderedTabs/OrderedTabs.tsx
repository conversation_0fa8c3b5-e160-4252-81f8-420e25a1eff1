import { defineContext, defineContextMutation } from '@/lib/state/defineContext'

type OrderedTab = { href: string; index: number }
export type OrderedTabState = {
  activeTab: string
  nextTab?: OrderedTab
  prevTab?: OrderedTab
  tabs: OrderedTab[]
}

const initialState: OrderedTabState = { activeTab: '', tabs: [] }

export const addTab = defineContextMutation('addTab')<OrderedTab>
export const setActiveTab = defineContextMutation('setActiveTab')<string>

export const [
  useOrderedTabsContext,
  useOrderedTabsDispatchContext,
  OrderedTabs,
] = defineContext('OrderedTabs', initialState, [addTab, setActiveTab], {
  addTab: ([state, tab]) => {
    if (state.tabs.includes(tab)) return state

    const tabs = state.tabs.concat(tab).sort((a, b) => a.index - b.index)

    if (state.activeTab === tab.href) {
      const index = tabs.findIndex(({ href }) => href === state.activeTab)
      const nextTab = state.tabs[index + 1]
      const prevTab = state.tabs[index - 1]
      return { ...state, nextTab, prevTab }
    }
    return {
      ...state,
      tabs,
    }
  },
  setActiveTab: ([state, activeTab]) => {
    if (state.activeTab === activeTab) return state
    const index = state.tabs.findIndex(({ href }) => href === activeTab)
    const nextTab = state.tabs[index + 1]
    const prevTab = state.tabs[index - 1]
    return { ...state, activeTab, nextTab, prevTab }
  },
})
