import Link from 'next/link'
import {
  addTab,
  OrderedTabState,
  useOrderedTabsContext,
  useOrderedTabsDispatchContext,
} from './OrderedTabs'
import { useEffect, useMemo } from 'react'
import { clsx } from 'clsx'

export type OrderedTabProps = Omit<
  React.ComponentPropsWithoutRef<typeof Link>,
  'children'
> & {
  children: React.ReactNode | ((state: OrderedTabState) => React.ReactNode)
  index: number
}

export function OrderedTab({
  children,
  className,
  href,
  index,
  ...props
}: OrderedTabProps) {
  const state = useOrderedTabsContext()
  const dispatch = useOrderedTabsDispatchContext()
  const tab = useMemo(() => ({ href: href.toString(), index }), [href, index])
  const active = tab.href === state.activeTab

  useEffect(() => dispatch(addTab(tab)), [dispatch, tab])

  return (
    <Link
      href={href}
      className={clsx(
        'group',
        active ? 'text-indigo-600' : 'text-zinc-500 hover:text-zinc-700',
        'whitespace-nowrap text-sm font-semibold',
        className,
      )}
      data-active={active}
      aria-current={active ? 'step' : undefined}
      {...props}
    >
      {typeof children === 'function' ? children(state) : children}
    </Link>
  )
}
