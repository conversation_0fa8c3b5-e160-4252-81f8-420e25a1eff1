'use client'

import { clsx } from 'clsx'
import Link from 'next/link'

export type TabProps = React.PropsWithChildren<
  React.ComponentPropsWithoutRef<typeof Link> & {
    active: boolean
  }
>

export function Tab({ active, children, className, ...props }: TabProps) {
  return (
    <Link
      className={clsx(
        active ?
          'border-indigo-500 text-indigo-600'
        : 'border-transparent text-zinc-500 hover:border-zinc-300 hover:text-zinc-700',
        'whitespace-nowrap border-b-2 px-1 py-2 text-sm font-semibold',
        className,
      )}
      {...props}
    >
      {children}
    </Link>
  )
}
