import { isDefined } from '@/lib/guards/isDefined'
import { Popover, PopoverButton, PopoverPanel } from '@headlessui/react'
import {
  ChevronRightIcon,
  EllipsisHorizontalIcon,
} from '@heroicons/react/24/outline'
import { clsx } from 'clsx'
import Link from 'next/link'
import React, { Children } from 'react'

export interface BreadcrumbsProps {
  truncate?: number
}

/**
 * Breadcrumbs for navigation. Supports truncating the number of breadcrumb segments.
 *
 * No truncation:
 * A > B > C > D
 *
 * With truncation:
 *
 * ... > C > D
 */

export interface Breadcrumb {
  title: string
  key: string
  href: React.ComponentPropsWithoutRef<typeof Link>['href']
}

export function Breadcrumbs({
  children,
  className,
  truncate,
  ...props
}: BreadcrumbsProps & React.ComponentPropsWithoutRef<'ol'>) {
  const definedChildren = Children.toArray(children).filter(isDefined)
  const isTruncated =
    truncate !== undefined && truncate < definedChildren.length
  const visibleChildren =
    isTruncated ? definedChildren.slice(-truncate) : children

  return (
    <nav className={clsx(className, 'flex')} aria-label='Breadcrumbs'>
      <ol className='flex items-center space-x-2' {...props}>
        {isTruncated && (
          <li>
            <Popover className='group relative flex items-center'>
              <PopoverButton aria-label='More'>
                <EllipsisHorizontalIcon className='h-5 w-5 flex-shrink-0 text-zinc-500 hover:text-zinc-700' />
                <span className='sr-only'>More</span>
              </PopoverButton>
              <PopoverPanel
                anchor='right'
                as='ol'
                className={clsx(
                  className,
                  'flex items-center space-x-2 rounded-lg bg-white p-2 shadow-md shadow-zinc-400/20 [--anchor-gap:8px]',
                )}
                {...props}
              >
                {children}
              </PopoverPanel>
            </Popover>
          </li>
        )}
        {visibleChildren}
      </ol>
    </nav>
  )
}

export function BreadcrumbSegment({
  children,
  className,
  ...props
}: React.ComponentPropsWithoutRef<typeof Link>) {
  return (
    <li className={clsx(className, 'group')}>
      <div className='flex items-center'>
        <ChevronRightIcon className='h-5 w-5 flex-shrink-0 text-zinc-400 group-first:hidden' />
        <Link
          className='ml-2 font-medium text-zinc-500 hover:text-zinc-700 group-first:ml-0 group-last:mr-2 group-last:text-lg group-last:text-black'
          {...props}
        >
          {children}
        </Link>
      </div>
    </li>
  )
}
