import { But<PERSON> } from '@headlessui/react'
import { QuestionMarkCircleIcon, XMarkIcon } from '@heroicons/react/24/outline'
import { useState } from 'react'

export function WarningRibbon({ message }: { message: string }) {
  const [showWarning, setShowWarning] = useState(true)

  return (
    <>
      {showWarning && (
        <div
          role='alert'
          className='pointer-events-auto mb-4 flex w-full items-center rounded-lg border border-app-color-fg-warning-secondary bg-app-color-bg-warning-primary p-4'
        >
          <QuestionMarkCircleIcon className='size-6 text-app-color-fg-warning-secondary' />
          <div className='ml-2 mr-auto font-medium'>{message}</div>
          <Button className='border-0' onClick={() => setShowWarning(false)}>
            <XMarkIcon className='size-5' />
          </Button>
        </div>
      )}
    </>
  )
}
