import { useMemo } from 'react'
import { useConfigurations } from '@/app/[bankId]/configuration/_hooks/useConfigurations'
import { SettlementOptionItem } from '@/app/[bankId]/accounts/types'

export interface UseSettlementOptionsDataProps {
  effectiveDate: string
}

export interface SettlementOptionsData {
  analysisResultOptionsMap: Map<string, string>
  settlementCycleOptionsMap: Map<string, string>
  allOptionsMaps: Map<SettlementOptionItem['field'], Map<string, string>>
  formatPlanDisplay: (
    planCode: string | null | undefined,
    field: string,
  ) => string
  isLoading: boolean
}

export function useSettlementOptionsData({
  effectiveDate,
}: UseSettlementOptionsDataProps): SettlementOptionsData {
  // Get configuration data
  const {
    analysisResultOptions: {
      data: analysisResultOptions,
      isLoading: isLoadingAnalysisOptions,
    },
    cycleDefinitions: {
      data: cycleDefinitions,
      isLoading: isLoadingCycleDefinitions,
    },
  } = useConfigurations({ effectiveDate })

  // Create option maps for plan name formatting
  const analysisResultOptionsMap = useMemo<Map<string, string>>(() => {
    const optionsMap: Map<string, string> = new Map()
    if (analysisResultOptions) {
      analysisResultOptions.forEach((option) => {
        const code = `${option.code}`
        const name = `${option.name}`
        optionsMap.set(code, name)
      })
    }
    return optionsMap
  }, [analysisResultOptions])

  const settlementCycleOptionsMap = useMemo<Map<string, string>>(() => {
    const optionsMap: Map<string, string> = new Map()
    if (cycleDefinitions) {
      cycleDefinitions.forEach((option) => {
        const code = `${option.code}`
        const name = `${option.description}`
        if (option.cycleType === 'SETTLEMENT') {
          optionsMap.set(code, name)
        }
      })
    }
    return optionsMap
  }, [cycleDefinitions])

  const allOptionsMaps = useMemo<
    Map<SettlementOptionItem['field'], Map<string, string>>
  >(() => {
    const optionsMap: Map<
      SettlementOptionItem['field'],
      Map<string, string>
    > = new Map()
    optionsMap.set('analysisResultOptionsPlanCode', analysisResultOptionsMap)
    optionsMap.set('settlementCyclePlanCode', settlementCycleOptionsMap)
    return optionsMap
  }, [analysisResultOptionsMap, settlementCycleOptionsMap])

  // Helper function to format plan display
  const formatPlanDisplay = useMemo(() => {
    return (planCode: string | null | undefined, field: string) => {
      if (!planCode) return 'Inherited from account type'

      let optionsMap: Map<string, string> | undefined
      if (field === 'analysisResultOptionsPlanCode') {
        optionsMap = analysisResultOptionsMap
      } else if (field === 'settlementCyclePlanCode') {
        optionsMap = settlementCycleOptionsMap
      }

      if (optionsMap && optionsMap.has(planCode)) {
        const planName = optionsMap.get(planCode)
        return `${planName} ${planCode}`
      }

      return planCode
    }
  }, [analysisResultOptionsMap, settlementCycleOptionsMap])

  return {
    analysisResultOptionsMap,
    settlementCycleOptionsMap,
    allOptionsMaps,
    formatPlanDisplay,
    isLoading: isLoadingAnalysisOptions || isLoadingCycleDefinitions,
  }
}
