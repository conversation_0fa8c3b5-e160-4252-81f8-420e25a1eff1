import clsx from 'clsx'
import React from 'react'
import { TabList, Tab } from '@headlessui/react'

export function SegmentedTabList({
  className,
  ...props
}: React.ComponentPropsWithoutRef<typeof TabList>) {
  return (
    <TabList
      className={clsx(
        className,
        'mb-4 overflow-auto rounded-md border border-solid border-gray-200 bg-app-color-bg-secondary px-2 py-0.5',
      )}
      {...props}
    />
  )
}

export function SegmentedTab({
  className,
  ...props
}: React.ComponentPropsWithoutRef<typeof Tab>) {
  return (
    <Tab
      className={clsx(
        className,
        'min-w-36 rounded-lg font-semibold text-app-color-secondary data-[selected]:bg-white data-[selected]:text-app-color-primary data-[hover]:underline',
      )}
      {...props}
    />
  )
}
