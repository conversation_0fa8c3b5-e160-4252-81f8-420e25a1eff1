import { clsx } from 'clsx'
import Link from 'next/link'
import React, { forwardRef } from 'react'

export function Columns({
  className,
  ...props
}: React.ComponentPropsWithoutRef<'div'>) {
  return (
    <div
      className={clsx(
        className,
        'flex flex-col rounded-lg border border-zinc-300 bg-white shadow-md shadow-zinc-400/20',
      )}
      {...props}
    />
  )
}

export function ColumnsHeader({
  className,
  ...props
}: React.ComponentPropsWithoutRef<'header'>) {
  return (
    <header
      className={clsx(
        className,
        'flex flex-auto flex-grow-0 items-center rounded-t-lg bg-zinc-200 px-6 py-3',
      )}
      {...props}
    />
  )
}

export function ColumnsBody({
  className,
  ...props
}: React.ComponentPropsWithoutRef<'div'>) {
  return (
    <div
      className={clsx(className, 'flex min-h-0 flex-auto divide-x')}
      {...props}
    />
  )
}

export function Column({
  className,
  ...props
}: React.ComponentPropsWithoutRef<'div'>) {
  return (
    <div
      className={clsx(className, 'flex flex-1 flex-col overflow-auto')}
      {...props}
      data-testid='column'
    />
  )
}

export function ColumnSection({
  className,
  ...props
}: React.ComponentPropsWithoutRef<'ul'>) {
  return <ul className={clsx(className, 'flex-1 p-2')} {...props} />
}

export function ColumnSectionDivider({
  className,
  ...props
}: React.ComponentPropsWithoutRef<'hr'>) {
  return <hr className={clsx(className, 'mx-4')} {...props} />
}

export function ColumnFooter({
  className,
  ...props
}: React.ComponentPropsWithoutRef<'div'>) {
  return (
    <div
      className={clsx(
        className,
        'sticky bottom-0 rounded-lg bg-white p-2 text-center text-zinc-400',
      )}
      {...props}
    />
  )
}

export const ColumnItem = forwardRef(function ColumnItem(
  {
    selected,
    className,
    children,
    href,
    ...props
  }: { selected?: boolean } & React.ComponentPropsWithoutRef<typeof Link>,
  ref: React.ForwardedRef<HTMLAnchorElement>,
) {
  const classes = clsx(
    // Base
    'flex flex-1 items-center gap-3 rounded-lg px-4 py-2.5 text-left text-base/6 font-medium',

    // Leading icon
    'data-[slot=icon]:*:size-6 data-[slot=icon]:*:shrink-0',

    // Selected
    'data-[selected]:bg-indigo-200 data-[selected]:text-indigo-800',

    // Hover
    'hover:bg-indigo-100 hover:text-indigo-800',

    // Active
    'active:bg-indigo-200 active:text-indigo-800',
  )

  return (
    <li className={clsx(className, 'flex-1')}>
      <Link
        href={href}
        className={classes}
        ref={ref}
        shallow={true}
        replace={true}
        {...props}
        data-selected={selected ? 'true' : undefined}
      >
        {children}
      </Link>
    </li>
  )
})
