import { Popover, PopoverButton, PopoverPanel } from '@headlessui/react'

import { EllipsisVerticalIcon } from '@heroicons/react/24/outline'
import React from 'react'
import { But<PERSON> } from '../Button'
import clsx from 'clsx'
import { ActionInterface } from './type'

// We'll extend the PopoverButton props, because we want to allow users of our component to provide
// props to modify the style and behavior of the button.
type VerticalEllipsisMenuProps = React.ComponentPropsWithoutRef<
  typeof PopoverButton
> & {
  actions: ActionInterface[]
}

export default function VerticalEllipsisMenu({
  actions,
  children,
  ...props
}: VerticalEllipsisMenuProps) {
  return (
    <Popover className='inline-flex'>
      <PopoverButton role='button' {...props}>
        {children ? children : <EllipsisVerticalIcon className='size-5' />}
      </PopoverButton>
      <PopoverPanel
        anchor='bottom start'
        className='flex min-w-44 flex-col rounded-md border bg-white text-sm'
      >
        {actions.map((action, index) => (
          <Button
            key={index}
            onClick={action.onClick}
            disabled={action.disabled || false}
            className={clsx(
              action.disabled ? 'cursor-not-allowed text-zinc-400' : '',
              'flex items-center gap-1 border-none p-3 hover:bg-app-color-bg-secondary',
              action.classNames,
            )}
          >
            {action.icon}
            {action.label}
          </Button>
        ))}
      </PopoverPanel>
    </Popover>
  )
}
