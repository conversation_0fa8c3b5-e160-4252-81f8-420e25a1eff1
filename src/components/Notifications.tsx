'use client'

import { Transition } from '@headlessui/react'
import React, { useEffect, useState, useSyncExternalStore } from 'react'
import { Button } from '@/components/Button'
import {
  CheckCircleIcon,
  ExclamationTriangleIcon,
  XMarkIcon,
} from '@heroicons/react/24/outline'
import { Data, Tag, Union } from '@/lib/unions/Union'
import { AnyMutation } from '@/lib/state/defineMutation'
import {
  combineMiddleware,
  outputMiddleware,
} from '@/lib/middleware/withMiddleware'
import { isoMap } from '@/lib/unions/isoMap'
import { mapObject } from '@/lib/functional/mapObject'
import { isoMapObject, ObjectIsoMapper } from '@/lib/functional/isoMapObject'

let messageUniqueKey = 0

type notificationTypes = 'SUCCESS' | 'ERROR'

export const notificationTypes = {
  SUCCESS: 'SUCCESS',
  ERROR: 'ERROR',
} as const

export type NotificationType = keyof typeof notificationTypes

interface NotificationMessage {
  key?: string
  message: React.ReactNode
  type: NotificationType
}

type NotificationData = Required<NotificationMessage>

let notifications: NotificationData[] = []
const listeners = new Set<() => void>()

function subscribe(notify: () => void) {
  listeners.add(notify)
  return () => listeners.delete(notify)
}

function dismiss(key: string) {
  const newNotifications = [
    ...notifications.filter((message) => message.key !== key),
  ]
  if (notifications.length !== newNotifications.length) {
    notifications = newNotifications
    listeners.forEach((cb) => cb())
  }
}

export function notify(newMessage: NotificationMessage) {
  const newMessageData: NotificationData = {
    ...newMessage,
    key: newMessage.key ?? (messageUniqueKey++).toString(),
  }

  const sameMessage = notifications.find(
    (notification) => notification.key === newMessageData.key,
  )

  if (sameMessage) {
    notifications = [
      ...notifications.filter((notification) => notification !== sameMessage),
      newMessageData,
    ]
  } else {
    notifications = [...notifications, newMessageData]
  }

  listeners.forEach((cb) => cb())
}

const notificationOnScreenTimeout = 4000
const animationTimeout = 300

const transitionClasses = {
  enterFrom: 'opacity-0 -translate-y-5',
  enterTo: 'opacity-100 translate-y-0',
  leave: `transition-all ease-in duration-[${animationTimeout}ms]`,
  leaveFrom: 'opacity-100 h-14',
  leaveTo: 'opacity-0 h-0',
}

const Notification = React.memo(function Notification({
  message: { key, message, type },
}: {
  message: NotificationData
}) {
  const [show, setShow] = useState(true)

  useEffect(() => {
    let mounted = true

    setTimeout(() => {
      if (mounted) {
        setShow(false)
      }
    }, notificationOnScreenTimeout)

    setTimeout(() => {
      dismiss(key)
    }, animationTimeout + notificationOnScreenTimeout)

    return () => {
      mounted = false
    }
  }, [key])

  const handleDelete = () => {
    setShow(false)

    setTimeout(() => {
      dismiss(key)
    }, animationTimeout)
  }
  if (type === notificationTypes.SUCCESS) {
    return (
      <Transition {...transitionClasses} show={show} appear={true}>
        <div
          role='alert'
          className='pointer-events-auto flex w-full max-w-sm items-center overflow-hidden rounded-lg border border-app-color-fg-success-secondary bg-app-color-bg-success-primary'
        >
          <CheckCircleIcon className='ml-4 size-6 text-app-color-fg-success-secondary' />
          <div className='mr-auto p-4 font-medium'>{message}</div>
          <Button className='border-0' onClick={handleDelete}>
            <XMarkIcon className='size-5' />
          </Button>
        </div>
      </Transition>
    )
  } else if (type === notificationTypes.ERROR) {
    return (
      <Transition {...transitionClasses} show={show} appear={true}>
        <div
          role='alert'
          className='pointer-events-auto flex w-full max-w-sm items-center overflow-hidden rounded-lg border border-app-color-fg-error-primary bg-app-color-bg-error-primary'
        >
          <div className='ml-4 size-6'>
            <ExclamationTriangleIcon className='size-6 text-app-color-fg-error-primary' />
          </div>
          <div className='mr-auto p-4 font-medium'>{message}</div>
          <Button className='border-0' onClick={handleDelete}>
            <XMarkIcon className='size-5' />
          </Button>
        </div>
      </Transition>
    )
  }
})

export function NotificationsContainer() {
  const messages = useSyncExternalStore(
    subscribe,
    () => notifications,
    () => notifications,
  )
  return (
    <div
      aria-live='assertive'
      className='pointer-events-none fixed inset-0 flex items-end px-4 py-6 sm:items-start sm:p-6'
      data-testid='notifications'
    >
      <div className='flex w-full flex-col items-center space-y-4 sm:items-center'>
        {messages.map((message) => (
          <Notification key={message.key} message={message} />
        ))}
      </div>
    </div>
  )
}

/**
 * Mutation middleware which will call `notify` in the mutation's `onSuccess`
 * handler.
 *
 * Callers must pass an object with keys corresponding to each of the possible
 * mutations handled by this middleware, and values are message strings to display
 * in the notification.
 *
 * @param messages
 * @returns
 */
export const withNotifications = <U extends Union<any, AnyMutation>>(messages: {
  [T in Tag<U>]: string | undefined
}) =>
  combineMiddleware<U>(
    outputMiddleware((options) => {
      const notifyOnSuccess = mapObject(messages, ([key, message]) => {
        return [
          key,
          (options: ReturnType<Data<U>>) =>
            isoMapObject(options, {
              onSuccess: (onSuccess) => (response, request, context) => {
                console.debug('withNotifications onSuccess:', key, message)
                if (message)
                  notify({ key, message, type: notificationTypes.SUCCESS })
                return onSuccess?.(response, request, context)
              },
              onError: (onError) => (response, request, context) => {
                console.debug(
                  'withNotifications onError:',
                  response.cause,
                  response.message,
                )
                if (message)
                  notify({
                    message: `${response.cause}`,
                    type: notificationTypes.ERROR,
                  })
                return onError?.(response, request, context)
              },
              // SAFETY: unclear why the type-checker is unable to decide that this
              // object is assignable to ObjectIsoMapper<ReturnType<Data<U>>> -- it's shape
              // does match that type.
            } as ObjectIsoMapper<ReturnType<Data<U>>>),
        ]
      })

      return isoMap(options, notifyOnSuccess)
    }),
  )
