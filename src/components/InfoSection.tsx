import clsx from 'clsx'
import React from 'react'

export function InfoSection({
  children,
  className,
  ...props
}: React.PropsWithChildren<React.JSX.IntrinsicElements['div']>) {
  return (
    <div
      className={clsx(
        'flex flex-col gap-4 rounded-xl border bg-white px-6 py-4',
        className,
      )}
      {...props}
    >
      {children}
    </div>
  )
}

export const InfoSectionTitle = ({
  children,
  className,
  ...props
}: React.PropsWithChildren<React.JSX.IntrinsicElements['h3']>) => (
  <h3
    className={clsx(
      'flex gap-2 text-xl font-bold text-app-color-primary',
      className,
    )}
    {...props}
  >
    {children}
  </h3>
)

export const InfoSubSectionTitle = ({
  children,
  className,
  ...props
}: React.PropsWithChildren<React.JSX.IntrinsicElements['h3']>) => (
  <h3
    className={clsx(
      'flex gap-2 text-lg font-bold text-app-color-primary',
      className,
    )}
    {...props}
  >
    {children}
  </h3>
)

export const InfoSectionDescription = ({
  children,
  className,
  ...props
}: React.PropsWithChildren<React.JSX.IntrinsicElements['p']>) => (
  <p className={clsx('text-app-color-secondary', className)} {...props}>
    {children}
  </p>
)

export const InfoSectionSeparator = ({ className }: { className?: string }) => {
  return (
    <div
      className={clsx(
        className,
        'my-1 w-full border-t-[1px] border-solid border-gray-200',
      )}
    />
  )
}
