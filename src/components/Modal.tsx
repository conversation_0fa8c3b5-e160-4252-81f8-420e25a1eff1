import {
  Dialog,
  DialogBackdrop,
  DialogPanel,
  DialogTitle,
} from '@headlessui/react'
import { XMarkIcon } from '@heroicons/react/24/outline'
import React, {
  createContext,
  Dispatch,
  SetStateAction,
  useCallback,
  useContext,
  useMemo,
  useState,
} from 'react'
import { Button } from '@/components/Button'
import { clsx } from 'clsx'

type ModalContextState = {
  open: boolean
  show: () => void
  close: () => void
}

type UseModalOptions = {
  initiallyOpen?: boolean
  onOpen?: () => void
  onClose?: () => void
}

export function useModal({
  initiallyOpen,
  onOpen,
  onClose,
}: UseModalOptions = {}): ModalContextState {
  const [open, setOpen] = useState(initiallyOpen ?? false)

  return useMemo(
    () => ({
      open,
      show: () => {
        setOpen(true)
        onOpen && onOpen()
      },
      close: () => {
        setOpen(false)
        onClose && onClose()
      },
    }),
    [open, onOpen, onClose],
  )
}

export function useExternalModalStateAndProps<P>({
  initiallyOpen,
  onOpen,
  onClose,
}: UseModalOptions): [
  ModalContextState,
  P | undefined,
  Dispatch<SetStateAction<P | undefined>>,
] {
  const [modalProps, setModalProps] = useState<P | undefined>()

  const onModalClose = useCallback(() => {
    // need to make sure to setModalProps to undefined to unmount the modal component,
    // which will unmount any component, forms, etc., within the modal component
    setModalProps(undefined)
    onClose && onClose()
  }, [setModalProps, onClose])

  const modalState = useModal({
    initiallyOpen,
    onOpen,
    onClose: onModalClose,
  })

  return [modalState, modalProps, setModalProps]
}

const ModalContext = createContext<ModalContextState>({
  open: false,
  show: () => {},
  close: () => {},
})

function useModalContext(componentName: string) {
  const context = useContext(ModalContext)

  if (!context) {
    throw new Error(
      `${componentName} must be nested within the <Modal> component.`,
    )
  }

  return context
}

export interface ExternalModalContextState {
  modalState?: ModalContextState
}

export function Modal({
  children,
  initiallyOpen,
  onOpen,
  onClose,
  modalState,
}: React.PropsWithChildren<UseModalOptions & ExternalModalContextState>) {
  const fallbackState = useModal({ initiallyOpen, onOpen, onClose })
  return (
    <ModalContext.Provider value={modalState ?? fallbackState}>
      {children}
    </ModalContext.Provider>
  )
}

export function ModalOpenButton({
  children,
  onClick,
  ...props
}: React.ComponentPropsWithoutRef<typeof Button>) {
  const modalContext = useModalContext('ModalOpen')
  return (
    <Button
      onClick={(e) => {
        if (onClick) onClick(e)
        modalContext.show()
      }}
      {...props}
    >
      {children}
    </Button>
  )
}

export type ModalWindowProps = React.PropsWithChildren<{
  dataTestId?: string
  zIndexStart?: number
}>

export function ModalDialog({ children }: { children: React.ReactNode }) {
  const modalContext = useModalContext('ModalWindow')
  return (
    <Dialog open={modalContext.open} onClose={modalContext.close}>
      {children}
    </Dialog>
  )
}

export function ModalWindow({
  children,
  className,
  dataTestId,
  zIndexStart = 0,
  ...props
}: ModalWindowProps &
  Omit<React.ComponentProps<typeof DialogPanel>, 'transition'>) {
  return (
    <ModalDialog>
      <DialogBackdrop
        transition
        className={clsx(
          'fixed inset-0 bg-zinc-500 bg-opacity-75 transition-opacity data-[closed]:opacity-0 data-[enter]:duration-300 data-[leave]:duration-200 data-[enter]:ease-out data-[leave]:ease-in',
          `z-${zIndexStart}`,
        )}
      />
      <div
        className={clsx(
          'fixed inset-0 w-screen overflow-y-auto',
          `z-${zIndexStart + 10}`,
        )}
      >
        <div className='flex min-h-full items-center justify-center p-4'>
          <DialogPanel
            transition
            className={clsx(
              'data-[closed]:transform-[scale(95%)] rounded-xl bg-white p-6 pt-4 backdrop-blur-2xl duration-300 ease-out data-[closed]:opacity-0',
              className,
            )}
            {...props}
            data-testid={dataTestId}
          >
            {children}
          </DialogPanel>
        </div>
      </div>
    </ModalDialog>
  )
}

export function ModalTitle({
  children,
  className,
  ...props
}: React.PropsWithChildren<
  Omit<React.ComponentPropsWithoutRef<typeof DialogTitle>, 'children'>
>) {
  const modalContext = useModalContext('ModalTitle')

  return (
    <DialogTitle
      as='h2'
      className={clsx(className, 'mb-2 flex items-center text-lg font-medium')}
      {...props}
    >
      <span className='flex-1'>{children}</span>
      <button className='ml-3' onClick={modalContext.close}>
        <XMarkIcon className='size-5' />
      </button>
    </DialogTitle>
  )
}

export function ModalFooter({
  children,
  className,
  ...props
}: React.ComponentPropsWithoutRef<'div'>) {
  return (
    <div className={clsx(className, 'mt-4 flex gap-4')} {...props}>
      {children}
    </div>
  )
}

interface ModalConfirmButtonProps {
  onClick?: (
    event: React.MouseEvent<HTMLButtonElement, MouseEvent>,
    ctx: ModalContextState,
  ) => void
}

export function ModalConfirmButton({
  children,
  className,
  onClick,
  ...props
}: Omit<React.ComponentPropsWithoutRef<typeof Button>, 'onClick'> &
  ModalConfirmButtonProps) {
  const modalContext = useModalContext('ModalConfirmButton')
  return (
    <Button
      className={clsx(className, 'flex-auto text-white')}
      onClick={async (event) => {
        if (onClick) {
          onClick(event, modalContext)
        }
      }}
      {...props}
    >
      {children}
    </Button>
  )
}

export function ModalCancelButton({
  children,
  className,
  onClick,
  ...props
}: React.ComponentPropsWithoutRef<typeof Button>) {
  const modalContext = useModalContext('ModalCancelButton')
  return (
    <Button
      className={clsx(className, 'btn flex-auto')}
      onClick={(e) => {
        modalContext.close()
        if (onClick) onClick(e)
      }}
      {...props}
    >
      {children}
    </Button>
  )
}
