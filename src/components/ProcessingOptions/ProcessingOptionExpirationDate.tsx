'use client'

import { Checkbox } from '@/components/Checkbox'
import { MonthPicker } from '@/components/Filter/MonthPicker'
import { SettlementAndProcessingOptionExpirationDateProps } from '@/app/[bankId]/accounts/types'

export function ProcessingOptionExpirationDate({
  expiry,
  isVisible,
  disabled = false,
  onCheckboxChange,
  onMonthPickerChange,
}: SettlementAndProcessingOptionExpirationDateProps) {
  return (
    isVisible && (
      <div className='flex flex-row gap-3'>
        <Checkbox
          checked={!expiry}
          label='No expiration'
          disabled={disabled}
          onChange={onCheckboxChange}
        />
        {expiry && !disabled && (
          <MonthPicker
            className='my-1 h-9 min-w-32 bg-white ring-1 ring-inset ring-gray-300'
            initialDate={expiry}
            onDateChange={onMonthPickerChange}
          />
        )}
        {expiry && disabled && (
          <div className='my-1 flex h-9 min-w-32 items-center rounded border border-gray-300 bg-gray-100 px-3 text-gray-500'>
            {expiry}
          </div>
        )}
      </div>
    )
  )
}
