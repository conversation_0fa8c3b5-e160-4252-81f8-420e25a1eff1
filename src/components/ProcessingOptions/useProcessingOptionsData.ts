import { useMemo } from 'react'
import { useConfigurations } from '@/app/[bankId]/configuration/_hooks/useConfigurations'
import { ProcessingOptionItem } from '@/app/[bankId]/accounts/types'

export interface UseProcessingOptionsDataProps {
  effectiveDate: string
}

export interface ProcessingOptionsData {
  earningsCreditOptionsMap: Map<string, string>
  investableBalanceOptionsMap: Map<string, string>
  requiredBalanceOptionsMap: Map<string, string>
  statementCycleOptionsMap: Map<string, string>
  statementFormatOptionsMap: Map<string, string>
  allOptionsMaps: Map<ProcessingOptionItem['field'], Map<string, string>>
  formatPlanDisplay: (
    planCode: string | null | undefined,
    field: string,
  ) => string
  isLoading: boolean
}

export function useProcessingOptionsData({
  effectiveDate,
}: UseProcessingOptionsDataProps): ProcessingOptionsData {
  // Get configuration data
  const {
    earningsCreditDefinitions: { data: earningsCreditDefinitions },
    investableBalanceDefinitions: { data: investableBalanceDefinitions },
    requiredBalanceDefinitions: { data: requiredBalanceDefinitions },
    cycleDefinitions: { data: cycleDefinitions },
    statementFormatPlans: { data: statementFormatPlans },
  } = useConfigurations({ effectiveDate })

  // Create option maps for plan name formatting (matching EditProcessingOptions)
  const earningsCreditOptionsMap = useMemo<Map<string, string>>(() => {
    const optionsMap: Map<string, string> = new Map()
    if (earningsCreditDefinitions) {
      earningsCreditDefinitions.forEach((option) => {
        const code = `${option.code}`
        const name = `${option.description}`
        optionsMap.set(code, name)
      })
    }
    return optionsMap
  }, [earningsCreditDefinitions])

  const investableBalanceOptionsMap = useMemo<Map<string, string>>(() => {
    const optionsMap: Map<string, string> = new Map()
    if (investableBalanceDefinitions) {
      investableBalanceDefinitions.forEach((option) => {
        const code = `${option.code}`
        const name = `${option.name}`
        optionsMap.set(code, name)
      })
    }
    return optionsMap
  }, [investableBalanceDefinitions])

  const requiredBalanceOptionsMap = useMemo<Map<string, string>>(() => {
    const optionsMap: Map<string, string> = new Map()
    if (requiredBalanceDefinitions) {
      requiredBalanceDefinitions.forEach((option) => {
        const code = `${option.code}`
        const name = `${option.name}`
        optionsMap.set(code, name)
      })
    }
    return optionsMap
  }, [requiredBalanceDefinitions])

  const statementCycleOptionsMap = useMemo<Map<string, string>>(() => {
    const optionsMap: Map<string, string> = new Map()
    if (cycleDefinitions) {
      cycleDefinitions.forEach((option) => {
        const code = `${option.code}`
        const name = `${option.description}`
        if (option.cycleType === 'STATEMENT') {
          optionsMap.set(code, name)
        }
      })
    }
    return optionsMap
  }, [cycleDefinitions])

  const statementFormatOptionsMap = useMemo<Map<string, string>>(() => {
    const optionsMap: Map<string, string> = new Map()
    if (statementFormatPlans) {
      statementFormatPlans.forEach((option) => {
        const code = `${option.code}`
        const name = `${option.description}`
        optionsMap.set(code, name)
      })
    }
    return optionsMap
  }, [statementFormatPlans])

  const allOptionsMaps = useMemo<
    Map<ProcessingOptionItem['field'], Map<string, string>>
  >(() => {
    const optionsMap: Map<
      ProcessingOptionItem['field'],
      Map<string, string>
    > = new Map()
    optionsMap.set('earningsCreditDefinitionCode', earningsCreditOptionsMap)
    optionsMap.set(
      'investableBalanceDefinitionCode',
      investableBalanceOptionsMap,
    )
    optionsMap.set(
      'balanceRequirementDefinitionCode',
      requiredBalanceOptionsMap,
    )
    optionsMap.set('statementCyclePlanCode', statementCycleOptionsMap)
    optionsMap.set('statementFormatPlanCode', statementFormatOptionsMap)
    return optionsMap
  }, [
    earningsCreditOptionsMap,
    investableBalanceOptionsMap,
    requiredBalanceOptionsMap,
    statementCycleOptionsMap,
    statementFormatOptionsMap,
  ])

  // Helper function to format plan display
  const formatPlanDisplay = useMemo(() => {
    return (planCode: string | null | undefined, field: string) => {
      if (!planCode) return 'Inherited from account type'

      let optionsMap: Map<string, string> | undefined
      if (field === 'earningsCreditDefinitionCode') {
        optionsMap = earningsCreditOptionsMap
      } else if (field === 'investableBalanceDefinitionCode') {
        optionsMap = investableBalanceOptionsMap
      } else if (field === 'balanceRequirementDefinitionCode') {
        optionsMap = requiredBalanceOptionsMap
      } else if (field === 'statementCyclePlanCode') {
        optionsMap = statementCycleOptionsMap
      } else if (field === 'statementFormatPlanCode') {
        optionsMap = statementFormatOptionsMap
      }

      if (optionsMap && optionsMap.has(planCode)) {
        const planName = optionsMap.get(planCode)
        return `${planName} ${planCode}`
      }

      return planCode
    }
  }, [
    earningsCreditOptionsMap,
    investableBalanceOptionsMap,
    requiredBalanceOptionsMap,
    statementCycleOptionsMap,
    statementFormatOptionsMap,
  ])

  return {
    earningsCreditOptionsMap,
    investableBalanceOptionsMap,
    requiredBalanceOptionsMap,
    statementCycleOptionsMap,
    statementFormatOptionsMap,
    allOptionsMaps,
    formatPlanDisplay,
    isLoading: false, // We can add loading states if needed
  }
}
