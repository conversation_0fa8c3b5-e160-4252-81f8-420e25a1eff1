import clsx from 'clsx'
import { PropsWithChildren } from 'react'

interface BadgeProps {
  type: 'error'
  className?: string
}

export function Badge({
  type: variant,
  className,
  children,
}: PropsWithChildren<BadgeProps>) {
  return (
    <div
      className={clsx(className, 'w-fit rounded-md px-2 py-1', {
        'bg-app-color-bg-error-primary text-app-color-fg-error-primary':
          variant === 'error',
      })}
    >
      {children}
    </div>
  )
}
