import React from 'react'
import clsx from 'clsx'
interface DrawerProps {
  isOpen: boolean
  onClose: () => void
  children: React.ReactNode
  className?: string
  closeOnOutsideClick?: boolean
}
export function Drawer({
  isOpen,
  onClose,
  children,
  closeOnOutsideClick,
  className,
}: DrawerProps) {
  if (!isOpen) return null
  return (
    <div
      className='fixed inset-0 z-20'
      onClick={() => closeOnOutsideClick && onClose()}
    >
      <div
        className={clsx(
          'fixed right-0 top-0 flex h-full flex-col',
          'min-w-[510px] bg-white shadow-lg',
          className,
        )}
        onClick={(e) => e.stopPropagation()}
      >
        {children}
      </div>
    </div>
  )
}

export function DrawerContent({
  children,
  className,
}: {
  children: React.ReactNode
  className?: string
}) {
  return (
    <div className={clsx('flex-grow overflow-y-auto p-6', className)}>
      {children}
    </div>
  )
}

export function DrawerFooter({
  children,
  className,
}: {
  children: React.ReactNode
  className?: string
}) {
  return (
    <div
      className={clsx(
        'bottom-0 flex items-center justify-between border-t bg-white p-6',
        className,
      )}
    >
      {children}
    </div>
  )
}
