'use client'

import { useEffect, useRef, useState } from 'react'
import { delay } from '@/lib/delay'
import { clsx } from 'clsx'

interface TooltipProps {
  content: React.ReactNode
  disabled?: boolean
  className?: string
  bgClassName?: string
  topOffset?: number
}

const delayTooltipClose = delay(100)

// Only allow a single tooltip to be open at a time. The currently-opened tooltip
// will store its close function here, and any other tooltip that opens will
// call this prior to opening.
let closePriorTooltip: (() => void) | undefined = undefined

interface Position {
  top: string
  left: string
}

export function Tooltip({
  children,
  content,
  className,
  bgClassName,
  disabled = false,
  topOffset,
}: React.PropsWithChildren<TooltipProps>) {
  const arrowSize = 12

  const popoverRef = useRef<HTMLDivElement>(null)

  const [show, setShow] = useState(false)
  const triggerRef = useRef<HTMLDivElement>(null)
  const [position, setPosition] = useState<Position>({
    top: '0px',
    left: '0px',
  })

  useEffect(() => {
    if (show && triggerRef.current && popoverRef.current) {
      const triggerRect = triggerRef.current.getBoundingClientRect()
      const tooltipContentRect = popoverRef.current.getBoundingClientRect()

      const tooltipTop = Math.round(triggerRect.top - tooltipContentRect.height)

      const tooltipLeft = Math.floor(
        triggerRect.left + (triggerRect.width - tooltipContentRect.width) / 2,
      )

      setPosition({
        left: `${tooltipLeft}px`,
        top: `${tooltipTop - (topOffset ?? 0)}px`,
      })
    }
  }, [show, topOffset])

  useEffect(() => {
    if (show) {
      popoverRef.current?.showPopover()
    } else {
      popoverRef.current?.hidePopover()
    }
  }, [show])
  const showTooltip = () => {
    if (closePriorTooltip) closePriorTooltip()
    hideTooltip.cancel()
    closePriorTooltip = () => setShow(false)
    setShow(true)
  }

  const hideTooltip = delayTooltipClose(() => setShow(false))

  // Cancel any pending state changes when the component unmounts (otherwise
  // React may complain about updating the state of an unmounted component).
  useEffect(() => () => hideTooltip.cancel(), [hideTooltip])

  return (
    <>
      <div
        ref={triggerRef}
        className={clsx(className)}
        onMouseEnter={showTooltip}
        onMouseLeave={hideTooltip}
      >
        {children}
      </div>
      {show && !disabled && (
        <div
          ref={popoverRef}
          role='tooltip'
          id='mypopover'
          popover='manual'
          style={position}
          className={'flex flex-col'}
          onMouseEnter={showTooltip}
          onMouseLeave={hideTooltip}
        >
          <div
            className={clsx(
              bgClassName ?? 'bg-white',
              'rounded-md border px-3 py-2 text-sm',
            )}
          >
            {content}
          </div>
          <div
            className='left-5 -mt-[1px] self-center overflow-hidden'
            style={{ width: `${arrowSize * 2}px` }}
          >
            <div
              className='origin-top-left -rotate-45 transform border bg-white'
              style={{ width: `${arrowSize}px`, height: `${arrowSize}px` }}
            ></div>
          </div>
        </div>
      )}
    </>
  )
}
