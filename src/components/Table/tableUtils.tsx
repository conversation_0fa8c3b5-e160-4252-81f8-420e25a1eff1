import { ColumnDef } from '@tanstack/react-table'

// TypeSafety: column definition data type doesn't matter for selectable column
export const selectableColumn: ColumnDef<any> = {
  id: 'select',
  header: ({ table }) => (
    <div className='flex items-center justify-center'>
      <input
        type='checkbox'
        className='form-checkbox h-4 w-4 cursor-pointer rounded border-zinc-500 text-indigo-600'
        checked={table.getIsAllRowsSelected()}
        onChange={table.getToggleAllRowsSelectedHandler()}
      />
    </div>
  ),
  cell: ({ row }) => (
    <div className='flex items-center justify-center'>
      <input
        type='checkbox'
        className='form-checkbox h-4 w-4 cursor-pointer rounded border-zinc-500 text-indigo-600'
        checked={row.getIsSelected()}
        onChange={row.getToggleSelectedHandler()}
      />
    </div>
  ),
}
