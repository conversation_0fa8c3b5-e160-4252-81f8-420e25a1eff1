import { clsx } from 'clsx'

export function Table({
  children,
  className,
  ...props
}: React.ComponentPropsWithoutRef<'table'>) {
  return (
    <table
      role='table'
      className={clsx(
        'flex-col divide-y rounded-lg border border-zinc-200 bg-white',
        className,
      )}
      {...props}
    >
      {children}
    </table>
  )
}

export function TableHead({
  children,
  className,
  ...props
}: React.ComponentPropsWithoutRef<'tbody'>) {
  return <tbody {...props}>{children}</tbody>
}

export const TableBody = TableHead

export function TableRow({
  children,
  className,
  ...props
}: React.ComponentPropsWithoutRef<'tr'>) {
  return (
    <tr
      role='row'
      className={clsx('h-16 min-h-16 border-zinc-200', className)}
      data-row
      {...props}
    >
      {children}
    </tr>
  )
}

export function TableCell({
  children,
  className,
  ...props
}: React.ComponentPropsWithoutRef<'td'>) {
  const hasBasis = className?.includes('basis-') ?? false
  return (
    <td
      role='cell'
      className={clsx(
        'items-center px-6 py-2',
        !hasBasis && 'basis-full',
        className,
      )}
      data-cell
      {...props}
    >
      {children}
    </td>
  )
}
