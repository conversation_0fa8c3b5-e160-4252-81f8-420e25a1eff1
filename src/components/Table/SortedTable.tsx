import React, { useEffect, useRef, useState } from 'react'
import { clsx } from 'clsx'
import {
  useReactTable,
  getCoreRowModel,
  getSortedRowModel,
  ColumnDef,
  flexRender,
  SortingState,
  Column,
  Row,
  getFilteredRowModel,
  ColumnFiltersState,
  RowSelectionState,
  CellContext,
} from '@tanstack/react-table'
import { ArrowLongUpIcon, ArrowLongDownIcon } from '@heroicons/react/24/outline'
import { useVirtualizer, VirtualItem } from '@tanstack/react-virtual'
import Link from 'next/link'

export interface ColumnMeta {
  className?: string
}

export interface TableProps<T, V> {
  data: T[]
  columns: ColumnDef<T, V>[]
  columnFilters?: ColumnFiltersState
  handleRowDoubleClick?: (row: Row<T>) => void
  onRowClick?: (row: Row<T>) => void
  onVisibleRowsChanged?: (visibleRows: Row<T>[]) => void
  onSelectionChange?: (selected: T[]) => void
  rowLink?: (row: Row<T>) => string
  initialSortingState?: SortingState
  estimatedRowHeight?: number
}

export interface RowProps<T, V> {
  row: Row<T>
  virtualRow: VirtualItem
  handleRowDoubleClick?: (row: Row<T>) => void
  onRowClick?: (row: Row<T>) => void
  getColumnMeta: (columnDef: ColumnDef<T, unknown>) => ColumnMeta
  flexRender: (
    renderable: ColumnDef<T, unknown>['cell'],
    props: CellContext<T, unknown>,
  ) => React.ReactNode
}

export function SortedTable<T, V>({
  data,
  columns,
  columnFilters,
  handleRowDoubleClick,
  onVisibleRowsChanged,
  onSelectionChange,
  onRowClick,
  rowLink,
  initialSortingState,
  estimatedRowHeight,
}: TableProps<T, V>) {
  const [sorting, setSorting] = useState<SortingState>(
    initialSortingState ?? [],
  )

  const [rowSelection, setRowSelection] = useState<RowSelectionState>({})

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onSortingChange: setSorting,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      rowSelection,
    },
  })

  const getDirectionArrow = (column: Column<any>) => {
    if (column.getCanSort()) {
      if (column.getIsSorted() === 'desc') {
        return <ArrowLongUpIcon className='mx-2 w-5' />
      } else if (column.getIsSorted() === 'asc') {
        return <ArrowLongDownIcon className='mx-2 w-5' />
      }
    }
  }

  const { rows } = table.getRowModel()

  useEffect(() => {
    if (onVisibleRowsChanged) onVisibleRowsChanged(table.getRowModel().rows)
  }, [rows.length, onVisibleRowsChanged, table])

  useEffect(() => {
    if (onSelectionChange) {
      const selectedRows = table
        .getFilteredSelectedRowModel()
        .rows.map((row) => row.original)

      onSelectionChange(selectedRows)
    }
  }, [rowSelection, table, onSelectionChange, rows.length])

  const tbodyRef = useRef<HTMLTableSectionElement>(null)
  const virtualizer = useVirtualizer({
    count: rows.length,
    getScrollElement: () => tbodyRef.current,
    estimateSize: () => estimatedRowHeight ?? 51,
    overscan: 100,
  })

  const [headerGroup] = table.getHeaderGroups()
  const [footerGroup] = table.getFooterGroups()

  const getColumnMeta = <T,>(columnDef: ColumnDef<T, unknown>): ColumnMeta => {
    return (columnDef.meta as ColumnMeta | undefined) ?? {}
  }

  return (
    <div className='flex min-h-0 rounded-lg border border-zinc-300 bg-white shadow-md shadow-zinc-400/20'>
      <div
        className='flex min-h-0 w-full flex-col overflow-y-scroll'
        role='table'
      >
        <div
          className='sticky top-0 z-10 flex min-h-10 w-full items-center bg-zinc-100 text-sm text-zinc-500'
          role='row'
        >
          {headerGroup.headers.map((header) => {
            const { className } = getColumnMeta(header.column.columnDef)
            return (
              <div
                key={header.id}
                className={clsx(
                  className ?? 'basis-full px-6',
                  'flex text-left font-normal',
                  header.column.getCanSort() && 'cursor-pointer',
                )}
                role='columnheader'
                onClick={header.column.getToggleSortingHandler()}
              >
                <span>
                  {flexRender(
                    header.column.columnDef.header,
                    header.getContext(),
                  )}
                </span>
                {getDirectionArrow(header.column)}
              </div>
            )
          })}
        </div>
        <div className='flex w-full' ref={tbodyRef}>
          <div
            className='flex w-full flex-col'
            style={{ height: `${virtualizer.getTotalSize()}px` }}
          >
            {virtualizer.getVirtualItems().map((virtualRow, index) => {
              const row = rows[virtualRow.index]
              const rowLinkHref = rowLink?.(row)
              const rowContentProps = {
                row,
                virtualRow,
                handleRowDoubleClick,
                onRowClick,
                getColumnMeta,
                flexRender,
              }

              return rowLinkHref ?
                  <Link key={row.id} href={rowLinkHref} prefetch={true}>
                    <RowContent {...rowContentProps} />
                  </Link>
                : <RowContent key={row.id} {...rowContentProps} />
            })}
          </div>
        </div>
        <div>
          {footerGroup.headers.map((group) => (
            <div key={group.id}>
              {flexRender(group.column.columnDef.footer, group.getContext())}
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

function RowContent<T, V>({
  row,
  virtualRow,
  handleRowDoubleClick,
  onRowClick,
  getColumnMeta,
  flexRender,
}: RowProps<T, V>) {
  return (
    <div
      className={clsx(
        !!handleRowDoubleClick && 'cursor-pointer',
        'flex items-center border-b hover:bg-indigo-200 active:bg-indigo-300',
      )}
      style={{
        height: `${virtualRow.size}px`,
        transform: `translateY(${virtualRow.start - virtualRow.index * virtualRow.size}px)`, // Use virtualRow.index here
      }}
      onDoubleClick={() => handleRowDoubleClick?.(row)}
      onClick={() => onRowClick?.(row)}
      role='row'
    >
      {row.getVisibleCells().map((cell) => {
        const { className } = getColumnMeta(cell.column.columnDef)
        return (
          <div
            key={cell.id}
            className={clsx(className ?? 'basis-full px-6', 'flex text-left')}
            role='cell'
          >
            {flexRender(cell.column.columnDef.cell, cell.getContext())}
          </div>
        )
      })}
    </div>
  )
}
