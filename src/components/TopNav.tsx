'use client'

import { usePathname } from 'next/navigation'
import Link from 'next/link'

import { Menu, MenuButton, MenuItems, MenuItem } from '@headlessui/react'
import { Cog6ToothIcon } from '@heroicons/react/24/outline'
import { useEffectiveDate } from '@/app/[bankId]/services/_hooks/useEffectiveDate'
import { formatToMonthYearFromDate } from '@/lib/date'
import { FISLogo } from './FISLogo'
import { configLinks } from '@/app/constants'

export function TopNav() {
  const bankId = usePathname().split('/')[1]
  const effectiveDate = useEffectiveDate()

  /*
   * index-rates, branches & officer require a full effective-date
   * user-fields is not effective-dated
   * remaining configs have an effective-month
   */
  const generateHref = (route: string) => {
    if (
      route === 'index-rates' ||
      route === 'branches' ||
      route === 'officer'
    ) {
      return `/${bankId}/configuration/${route}/${effectiveDate}`
    } else if (route === 'user-fields') {
      return `/${bankId}/configuration/${route}`
    } else if (route === 'bank-options') {
      return `/${bankId}/configuration/${route}/${effectiveDate}/view`
    } else {
      return `/${bankId}/configuration/${route}/${formatToMonthYearFromDate(effectiveDate)}`
    }
  }

  return (
    <nav className='fixed left-0 top-0 z-50 w-full border-b border-gray-200 bg-white'>
      <div className='flex h-[59px] items-center justify-between pl-10 pr-6'>
        <div className='flex flex-shrink-0'>
          <Link className='flex items-end px-4 py-2.5' href={`/${bankId}#`}>
            <FISLogo />
            <div className='ml-3 text-sm font-semibold'>Revenue Connect</div>
          </Link>
        </div>

        <div className='flex items-center'>
          {/* 
          todo: information
          <QuestionMarkCircleIcon className='size-6 stroke-2' /> 
          */}
          <Menu>
            <MenuButton name='Product configuration' className=''>
              <Cog6ToothIcon className='ml-6 size-6 stroke-2 text-gray-600' />
            </MenuButton>
            <MenuItems
              anchor='bottom'
              className='mt-4 flex flex-col whitespace-nowrap rounded border-2 border-gray-200 bg-white px-4 py-3'
            >
              <div className='py-2 text-xs font-semibold uppercase text-gray-500'>
                PRODUCT CONFIGURATION
              </div>
              {configLinks.map((link) => {
                return (
                  <MenuItem key={`${link.label}`}>
                    <Link
                      className='py-1 text-sm hover:underline'
                      href={generateHref(link.route)}
                    >
                      {link.label}
                    </Link>
                  </MenuItem>
                )
              })}
            </MenuItems>
          </Menu>
        </div>
      </div>
    </nav>
  )
}
