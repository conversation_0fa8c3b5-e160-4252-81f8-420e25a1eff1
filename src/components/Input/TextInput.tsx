import { Field<PERSON>pi, FieldState, ValidationError } from '@tanstack/react-form'
import { InputHTMLAttributes, JSX, ReactNode } from 'react'
import { Tooltip } from '../Tooltip'
import { QuestionMarkCircleIcon } from '@heroicons/react/24/outline'
import clsx from 'clsx'
import { ExclamationCircleIcon } from '@heroicons/react/24/solid'
import { delay } from '@/lib/delay'
import { isString } from '@/lib/guards/isString'
import { isNumber } from '@/lib/guards/isNumber'

export type TextInputProps = Omit<
  React.ComponentPropsWithoutRef<'div'>,
  'onChange' | 'defaultValue'
> & {
  /** @deprecated Use `label` instead. */
  'aria-label'?: string
  disabled?: boolean
  errors?: ValidationError[]
  invalidChars?: string[]
  isTouched?: boolean
  label?: ReactNode
  name: string
  onChange?: (value: string) => void
  placeholder?: string
  prefix?: ReactNode
  readonly?: boolean
  inputClassName?: string
  required?: boolean
  renderErrors?: ((errors: string[]) => ReactNode) | false
  suffix?: ReactNode
  tooltip?: ReactNode
  type?: JSX.IntrinsicElements['input']['type']
  value?: InputHTMLAttributes<HTMLInputElement>['value']
}

export function TextInput({
  disabled,
  inputClassName,
  errors: maybeErrors,
  invalidChars,
  isTouched,
  label,
  name,
  onChange,
  placeholder,
  prefix,
  readonly,
  required,
  renderErrors,
  suffix,
  tooltip,
  type,
  value,
  ...props
}: TextInputProps) {
  const errors = maybeErrors?.filter((e) => typeof e === 'string') ?? []
  const backwardsCompatibleLabel = label ?? props['aria-label']
  const showErrors = isTouched && errors.length > 0 && !readonly

  return (
    <div {...props}>
      {backwardsCompatibleLabel && (
        <label
          htmlFor={name}
          className='mb-1 flex items-center text-sm font-medium'
        >
          {backwardsCompatibleLabel}
          {required && <span className='pl-1'>*</span>}
          {tooltip && (
            <Tooltip content={tooltip} className='ml-1 size-4'>
              <QuestionMarkCircleIcon />
            </Tooltip>
          )}
        </label>
      )}
      <div
        className={clsx(
          'flex w-full items-center rounded-md',
          'outline outline-1 -outline-offset-1 outline-zinc-300',
          'focus-within:outline focus-within:outline-2 focus-within:-outline-offset-2 focus-within:outline-indigo-600',
          disabled || readonly ? 'bg-zinc-100 text-zinc-500' : 'bg-white',
        )}
      >
        {prefix && (
          <div className='shrink-0 select-none pl-3 text-zinc-500'>
            {prefix}
          </div>
        )}
        <input
          className={clsx(
            'min-w-0 grow px-3',
            'rounded-md border-0 bg-transparent px-0',
            'placeholder:text-zinc-400',
            'focus:outline-none focus:ring-0',
            inputClassName,
          )}
          id={name}
          name={name}
          value={value}
          type={type ?? 'text'}
          placeholder={placeholder}
          onKeyDown={(event) => {
            if (invalidChars && invalidChars.includes(event.key)) {
              event.preventDefault()
            }
          }}
          onChange={({ currentTarget: { value } }) => {
            onChange?.(value)
          }}
          onWheel={(event) => {
            // This prevents TextInput[type=number] from changing the value when user scrolls through the view.
            if (type === 'number') {
              event.currentTarget.blur()
              focusAfter100ms.cancel()
              // Return focus back to the element if scroll event is over.
              focusAfter100ms(event.currentTarget)
            }
          }}
          disabled={disabled}
          readOnly={readonly}
        />
        {suffix && (
          <div className='shrink-0 select-none pr-3 text-zinc-500'>
            {suffix}
          </div>
        )}
        {showErrors && (
          <Tooltip
            className='pr-3'
            content={
              <p className='mt-1 text-sm text-red-600'>{errors.join(', ')}</p>
            }
          >
            <ExclamationCircleIcon
              aria-hidden='true'
              className='size-5 shrink-0 text-red-500'
            />
          </Tooltip>
        )}
      </div>
      {/* If renderErrors === false, we do nothing here -- otherwise we reserve
      space with an empty <p> until showErrors is true, then we either call the
      custom renderErrors function or render a simple <p> with the error strings. */}
      {renderErrors !== false ?
        showErrors ?
          typeof renderErrors === 'function' ?
            renderErrors(errors)
          : <p className='mt-1 text-sm text-app-color-fg-error-primary'>
              {errors.join(', ')}
            </p>

        : <p className='mt-1 text-sm'>&nbsp;</p>
      : undefined}
    </div>
  )
}

export type TextInputFieldProps<Value> = Omit<
  TextInputProps,
  'errors' | 'isTouched' | 'name' | 'onChange' | 'readonly' | 'value'
> & {
  field: FieldApi<any, any, any, any, Value>
  readonly?: boolean | ((fieldState: FieldState<Value>) => boolean)
}

export function TextInputField<Value>({
  field,
  readonly,
  ...props
}: TextInputFieldProps<Value>) {
  const name = field.name
  const value = field.state.value
  const meta = field.getMeta()
  const computedReadonly =
    typeof readonly === 'function' ? readonly(field.state) : readonly

  const onChange = (value: string) => {
    const castValue = (value?.length === 0 ? null : value) as Value
    field.handleChange(castValue)
  }

  const definedValue = value ?? ''
  if (!isStringOrNumber(definedValue)) {
    throw new Error(
      `Only string or number type is supported. Got "${value}" for input field "${name}"`,
    )
  }

  return (
    <TextInput
      errors={meta.errors}
      isTouched={meta.isTouched}
      name={name}
      onChange={onChange}
      readonly={computedReadonly}
      value={definedValue}
      {...props}
    />
  )
}

const focusAfter100ms = delay(100)((input: HTMLInputElement) => input.focus())
const isStringOrNumber = (value: unknown): value is string | number => {
  return isString(value) || isNumber(value)
}
