import {
  Label,
  Listbox,
  ListboxButton,
  ListboxOption,
  ListboxOptions,
} from '@headlessui/react'
import { Toolt<PERSON> } from '../Tooltip'
import {
  CheckIcon,
  ChevronDownIcon,
  QuestionMarkCircleIcon,
} from '@heroicons/react/24/outline'
import { <PERSON><PERSON><PERSON>, ValidationError } from '@tanstack/react-form'
import { ReactNode } from 'react'
import { clsx } from 'clsx'
import { ExclamationCircleIcon } from '@heroicons/react/24/solid'
import { isNonNull } from '@/lib/guards/isNonNull'

export type SelectProps<Value> = Omit<
  React.ComponentPropsWithoutRef<typeof Listbox<'div', Value>>,
  'as' | 'children' | 'defaultValue' | 'onChange' | 'name'
> & {
  children?: ReactNode
  disabled?: boolean
  errors?: ValidationError[]
  isTouched?: boolean
  label?: string | ReactNode
  name: string
  onChange?: (value: Value) => void
  options?: Value[]
  placeholder?: string
  required?: boolean
  renderErrors?: ((errors: string[]) => ReactNode) | false
  renderOption?: (value: NonNullable<Value>) => ReactNode
  renderSelected?: (value: NonNullable<Value>) => ReactNode
  tooltip?: string
  value: Value
}

export function Select<Value>({
  children,
  disabled,
  errors: maybeErrors,
  isTouched,
  label,
  name,
  onChange,
  options,
  placeholder,
  required,
  renderErrors,
  renderOption,
  renderSelected,
  tooltip,
  value,
  ...props
}: SelectProps<Value>) {
  const errors = maybeErrors?.filter((e) => typeof e === 'string') ?? []
  const showErrors = isTouched && errors.length > 0

  return (
    <Listbox as='div' value={value} onChange={onChange} {...props}>
      {label && (
        <Label
          htmlFor={name}
          className='mb-1 flex items-center text-sm font-medium'
        >
          {label}
          {required && <span className='pl-1'>*</span>}
          {tooltip && (
            <Tooltip content={tooltip} className='ml-1 size-4'>
              <QuestionMarkCircleIcon />
            </Tooltip>
          )}
        </Label>
      )}
      <div className='relative w-full'>
        <ListboxButton
          id={name}
          name={name}
          className={clsx(
            'flex w-full cursor-default items-center rounded-md bg-white py-2 pl-3 pr-2',
            'text-left outline outline-1 -outline-offset-1 outline-zinc-300',
            'focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600',
            'disabled:bg-zinc-100 disabled:text-zinc-500',
          )}
          disabled={disabled}
        >
          <span className='flex-1 truncate pr-6'>
            {value != undefined && value !== '' ?
              renderSelected ?
                renderSelected(value)
              : `${value}`
            : placeholder ?
              <div className='text-zinc-400'>{placeholder}</div>
            : <div className='text-zinc-400'>Select</div>}
          </span>
          {showErrors && (
            <Tooltip
              content={
                <p className='mt-1 text-sm text-red-600'>{errors.join(', ')}</p>
              }
            >
              <ExclamationCircleIcon
                aria-hidden='true'
                className='mr-1 size-5 shrink-0 text-red-500'
              />
            </Tooltip>
          )}
          <ChevronDownIcon aria-hidden='true' className='size-5' />
        </ListboxButton>
        <ListboxOptions
          anchor='bottom start'
          transition
          className={clsx(
            'absolute z-50 mt-1 max-h-80 w-[var(--button-width)] overflow-auto',
            'rounded-md bg-white py-1 shadow-lg ring-1 ring-black/5 focus:outline-none',
            'data-[closed]:data-[leave]:opacity-0',
            'data-[leave]:transition data-[leave]:duration-100 data-[leave]:ease-in',
          )}
        >
          {options &&
            options.map((option) => (
              <SelectOption
                key={`${option}`}
                value={option}
                className='relative'
              >
                <span className='block truncate'>
                  {renderOption && isNonNull(option) ?
                    renderOption(option)
                  : `${option}`}
                </span>
                <span
                  className={clsx(
                    'absolute inset-y-0 right-0 flex items-center pr-4 text-indigo-600',
                    'group-[&:not([data-selected])]:hidden',
                  )}
                >
                  <CheckIcon aria-hidden='true' className='size-5' />
                </span>
              </SelectOption>
            ))}
          {children}
        </ListboxOptions>
        {/* If renderErrors === false, we do nothing here -- otherwise we reserve
        space with an empty <p> until showErrors is true, then we either call the
        custom renderErrors function or render a simple <p> with the error strings. */}
        {renderErrors !== false ?
          showErrors ?
            typeof renderErrors === 'function' ?
              renderErrors(errors)
            : <p className='mt-1 text-sm text-app-color-button-primary-error-bg'>
                {errors.join(', ')}
              </p>

          : <p className='mt-1 text-sm'>&nbsp;</p>
        : undefined}
      </div>
    </Listbox>
  )
}

export type SelectFieldProps<Value> = Omit<
  SelectProps<Value>,
  'errors' | 'isTouched' | 'name' | 'value'
> & {
  field: FieldApi<any, any, any, any, Value>
  onChangeDeferred?: (value: Value, handleChange: () => void) => void
}

export function SelectField<Value>({
  field,
  onChange: maybeOnChange,
  onChangeDeferred,
  ...props
}: SelectFieldProps<Value>) {
  const name = field.name
  const value = field.state.value
  const meta = field.getMeta()

  const onChange = (value: Value) => {
    if (onChangeDeferred) {
      // use onChangeDeferred when you don't want to change the field value right away (e.g., await modal confirmation)
      onChangeDeferred(value, () => field.handleChange(value))
    } else {
      maybeOnChange?.(value)
      field.handleChange(value)
    }
  }

  return (
    <Select
      name={name}
      value={value}
      errors={meta.errors}
      isTouched={meta.isTouched}
      onChange={onChange}
      {...props}
    />
  )
}

export type SelectOptionProps<Value> = Omit<
  React.ComponentPropsWithoutRef<typeof ListboxOption>,
  'value'
> & { value: Value; focusClassNames?: string }

export function SelectOption<Value>({
  children,
  className,
  value,
  focusClassNames,
  ...props
}: SelectOptionProps<Value>) {
  return (
    <ListboxOption
      className={clsx(
        'group select-none px-3 py-2',
        'data-[focus]:bg-indigo-100 data-[focus]:outline-none',
        className,
      )}
      value={value}
      {...props}
    >
      {children}
    </ListboxOption>
  )
}
