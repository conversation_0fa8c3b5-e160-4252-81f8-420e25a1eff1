import {
  Combobox,
  ComboboxButton,
  ComboboxInput,
  ComboboxOption,
  ComboboxOptions,
} from '@headlessui/react'
import {
  ChevronUpDownIcon,
  MagnifyingGlassIcon,
} from '@heroicons/react/24/outline'
import { clsx } from 'clsx'
import { ComponentPropsWithoutRef, ReactNode, useState } from 'react'

export type TypeaheadProps<T, Multiple extends boolean | undefined> = Omit<
  ComponentPropsWithoutRef<typeof Combobox<T, Multiple, 'div'>>,
  'as' | 'children'
> & {
  children: (results: T[]) => ReactNode
  filter: (query: string) => T[]
  placeholder?: string
  renderSelected?: (
    value: ComponentPropsWithoutRef<
      typeof Combobox<T, Multiple, 'div'>
    >['value'],
  ) => string
}

export function Typeahead<T, Multiple extends boolean | undefined>({
  children,
  filter,
  onChange,
  placeholder,
  renderSelected,
  value,
  ...props
}: TypeaheadProps<T, Multiple>) {
  const [query, setQuery] = useState('')

  const results = filter(query)

  return (
    <Combobox
      as='div'
      value={value}
      onChange={onChange}
      onClose={() => setQuery('')}
      {...props}
    >
      <div
        className={clsx(
          'relative flex w-full items-center rounded-md',
          'outline outline-1 -outline-offset-1 outline-zinc-300',
          'focus-within:outline focus-within:outline-2 focus-within:-outline-offset-2 focus-within:outline-indigo-600',
          'bg-white',
        )}
      >
        <div className='absolute inset-y-0 left-0 flex select-none items-center pl-3 text-zinc-500'>
          <MagnifyingGlassIcon className='size-5' />
        </div>
        <ComboboxInput
          className={clsx(
            'grow px-10',
            'rounded-md border-0 bg-transparent px-0',
            'placeholder:text-zinc-400',
            'focus:outline-none focus:ring-0',
          )}
          displayValue={renderSelected}
          placeholder={placeholder}
          onChange={(e) => setQuery(e.currentTarget.value)}
          onBlur={() => setQuery('')}
        />
        <ComboboxButton className='absolute inset-y-0 right-0 flex items-center pr-2 text-zinc-400 focus:outline-none'>
          <ChevronUpDownIcon className='size-5' aria-hidden='true' />
        </ComboboxButton>
      </div>
      <ComboboxOptions
        anchor='bottom'
        transition
        className={clsx(
          'z-10 mt-1 max-h-80 w-[var(--input-width)] overflow-auto',
          'rounded-md bg-white py-1 shadow-lg ring-1 ring-black/5 focus:outline-none',
          'data-[closed]:data-[leave]:opacity-0',
          'data-[leave]:transition data-[leave]:duration-100 data-[leave]:ease-in',
        )}
      >
        {children(results)}
      </ComboboxOptions>
    </Combobox>
  )
}

export type TypeaheadOptionProps<T> = Omit<
  ComponentPropsWithoutRef<typeof ComboboxOption<'div', T>>,
  'as'
>

export function TypeaheadOption<T>({
  className,
  ...props
}: TypeaheadOptionProps<T>) {
  return (
    <ComboboxOption
      as='div'
      className={clsx(
        'group cursor-default select-none py-2 pl-3 pr-9',
        'data-[focus]:bg-indigo-100 data-[focus]:outline-none',
        className,
      )}
      {...props}
    />
  )
}
