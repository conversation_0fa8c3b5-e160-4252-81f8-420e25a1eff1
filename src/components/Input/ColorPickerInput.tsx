'use client'

import { useEffect, useRef, ReactNode } from 'react'
import '@melloware/coloris/dist/coloris.css'
import clsx from 'clsx'

import { Tooltip } from '../Tooltip'
import { ExclamationCircleIcon } from '@heroicons/react/24/solid'

const swatches = [
  '#846301',
  '#BC3F01',
  '#D70412',
  '#7A1E9F',
  '#045CD7',
  '#006B8F',
  '#027935',
  '#427613',
  '#0E0F10',
  '#FFFFFF',
  '#E2E4E9',
]

export type ColorPickerInputProps<Value> = {
  name: string
  value: string
  label?: string | ReactNode
  onChange: (newColor: string) => void
  className?: string
  required?: boolean
  showErrors?: boolean
  errors: string[]
  renderErrors?: ((errors: string[]) => ReactNode) | false
}
export default function ColorPickerInput<Value>({
  name,
  value,
  label,
  onChange,
  className,
  required,
  errors,
  renderErrors,
  showErrors,
}: ColorPickerInputProps<Value>) {
  const inputRef = useRef<HTMLInputElement>(null)

  useEffect(() => {
    if (inputRef.current) {
      // import Coloris only on the client side
      const Coloris = require('@melloware/coloris')
      Coloris.init()
      Coloris({
        el: inputRef.current,
        theme: 'default',
        format: 'hex',
        alpha: true,
        swatches,
      })

      const handleChange = (e: Event) => {
        const newColor = (e.target as HTMLInputElement).value
        onChange(newColor.toUpperCase())
      }

      // close Coloris color picker on scroll/wheel
      const handleScroll = () => {
        if (Coloris && Coloris.close) {
          Coloris.close()
        }
      }
      const scrollOptions = { capture: true, passive: true }
      document.addEventListener('scroll', handleScroll, scrollOptions)
      document.addEventListener('wheel', handleScroll, scrollOptions)

      // remove Coloris' injected swatch button
      // it is automatically added within input on right side
      const removeColorisButton = () => {
        const parent = inputRef.current?.parentElement
        if (parent) {
          const injectedButton = parent.querySelector('button[type="button"')
          if (injectedButton) {
            injectedButton.remove()
          }
        }
      }
      const timeoutId = setTimeout(removeColorisButton, 0)

      inputRef.current.addEventListener('change', handleChange)
      inputRef.current.addEventListener('input', handleChange)

      // clean up eventlisteners on unmount
      return () => {
        clearTimeout(timeoutId)
        inputRef.current?.removeEventListener('change', handleChange)
        inputRef.current?.removeEventListener('input', handleChange)
        document.removeEventListener('scroll', handleScroll, scrollOptions)
        document.removeEventListener('wheel', handleScroll, scrollOptions)
      }
    }
  }, [])

  return (
    <>
      <label htmlFor={`color-picker-${name}`} className='text-sm font-medium'>
        {label}
        {required && <span className='pl-1'>*</span>}
      </label>

      <div
        className={clsx('relative w-full items-center rounded-md', className)}
      >
        {/* color swatch within input */}
        <div
          className={clsx(
            'pointer-events-none absolute inset-y-0',
            'left-0 z-10 flex items-center pl-2',
            { hidden: !value },
          )}
        >
          <div
            className='h-5 w-5 border border-gray-400'
            style={{ backgroundColor: value }}
          />
        </div>

        <input
          id={`color-picker-${name}`}
          ref={inputRef}
          type='text'
          value={value}
          readOnly
          className='max-h-10 w-full rounded-md border-zinc-300 pl-9 uppercase text-black'
        />

        {showErrors && (
          <Tooltip
            content={
              <p className='mt-1 text-sm text-red-600'>{errors.join(', ')}</p>
            }
            className='pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2'
          >
            <ExclamationCircleIcon
              aria-hidden='true'
              className='mr-1 size-5 shrink-0 text-red-500'
            />
          </Tooltip>
        )}
      </div>

      {/* If renderErrors === false, we do nothing here -- otherwise we reserve
        space with an empty <p> until showErrors is true, then we either call the
        custom renderErrors function or render a simple <p> with the error strings. */}
      {renderErrors !== false ?
        showErrors ?
          typeof renderErrors === 'function' ?
            renderErrors(errors)
          : <p className='mt-1 text-sm text-app-color-button-primary-error-bg'>
              {errors.join(', ')}
            </p>

        : <p className='mt-1 text-sm'>&nbsp;</p>
      : undefined}
    </>
  )
}
