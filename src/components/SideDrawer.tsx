'use client'

import { Dialog, DialogBackdrop, DialogPanel } from '@headlessui/react'

export default function Drawer({
  open,
  closeDrawer,
  children,
}: {
  open: boolean
  closeDrawer: (x: boolean) => void
  children: React.ReactNode
}) {
  return (
    <Dialog open={open} onClose={closeDrawer} className='relative z-10'>
      <DialogBackdrop
        transition
        className='data-closed:opacity-0 fixed inset-0 transition-opacity duration-500 ease-in-out'
      />
      <div className='fixed inset-0 overflow-hidden'>
        <div className='absolute inset-0 overflow-hidden'>
          <div className='pointer-events-none fixed inset-y-0 right-0 flex max-w-full pl-10'>
            <DialogPanel
              transition
              className='data-closed:translate-x-full pointer-events-auto relative w-screen max-w-[800px] transform transition duration-500 ease-in-out sm:duration-700'
            >
              <div className='flex h-full flex-col overflow-y-scroll bg-white py-6 shadow-xl'>
                <div className='relative flex-1 px-4 sm:px-6'>{children}</div>
              </div>
            </DialogPanel>
          </div>
        </div>
      </div>
    </Dialog>
  )
}
