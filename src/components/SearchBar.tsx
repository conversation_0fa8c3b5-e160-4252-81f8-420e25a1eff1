import { TextInput } from '@/components/Input/TextInput'
import { InputGroup } from '@/components/Input/InputGroup'
import { MagnifyingGlassIcon } from '@heroicons/react/24/outline'

interface SearchBarProps {
  className?: string
  defaultLabel: string
  value?: string
  onValueChange: (searchTerm: string) => void
}

export function SearchBar({
  className,
  defaultLabel,
  value,
  onValueChange,
}: SearchBarProps) {
  return (
    <div className={className}>
      <InputGroup>
        <MagnifyingGlassIcon className='min-h-3 min-w-3' />
        <TextInput
          className={'flex max-h-10'}
          value={value}
          onChange={(value) => {
            onValueChange(value ?? '')
          }}
          placeholder={defaultLabel}
          name='searchBar'
          renderErrors={false}
        />
      </InputGroup>
    </div>
  )
}
