import clsx from 'clsx'
import { Tooltip } from '@/components/Tooltip'
import { InformationCircleIcon } from '@heroicons/react/24/outline'
import { ReactNode } from 'react'

export function DetailsSection({
  children,
  className,
}: React.PropsWithChildren<React.JSX.IntrinsicElements['div']>) {
  return (
    <div
      className={clsx(
        'flex flex-col rounded-lg border bg-white p-6',
        className,
      )}
    >
      {children}
    </div>
  )
}

export function DetailsSectionTitle({
  children,
}: React.PropsWithChildren<React.JSX.IntrinsicElements['div']>) {
  return <div className='font-semibold'>{children}</div>
}

export function DetailsSectionSubtitle({
  children,
}: React.PropsWithChildren<React.JSX.IntrinsicElements['div']>) {
  return <div className='text-app-color-secondary'>{children}</div>
}

export function DetailsSectionItemsRow({
  children,
  className,
}: React.PropsWithChildren<React.JSX.IntrinsicElements['div']>) {
  return <div className={clsx('mt-4 flex', className)}>{children}</div>
}

export function DetailsSectionItem({
  label,
  info,
  className,
  tooltip,
}: {
  label: string
  info?: ReactNode
  className?: string
  tooltip?: string
}) {
  return (
    <div className={clsx('flex flex-1 flex-col text-sm', className)}>
      <div className='flex text-zinc-500'>
        {label}{' '}
        {tooltip && (
          <Tooltip className='ml-1 size-4 self-center' content={tooltip}>
            <InformationCircleIcon />
          </Tooltip>
        )}
      </div>
      <div className='flex-1 whitespace-pre-line font-semibold'>
        {info ? info : '--'}
      </div>
    </div>
  )
}
