import { Button } from '@headlessui/react'
import { CheckIcon } from '@heroicons/react/20/solid'
import clsx from 'clsx'

export interface Step {
  name: string
  onClick?: () => void
}

interface StepperProps {
  steps: Step[]
  currentStepIndex: number
}

export function Stepper({ steps, currentStepIndex }: StepperProps) {
  return (
    <nav aria-label='Progress'>
      <ol role='list' className='flex flex-wrap'>
        {steps.map((step, stepIndex) => {
          return (
            <li key={step.name} className='flex flex-1 flex-col text-center'>
              <StepperStep
                step={step}
                stepIndex={stepIndex}
                totalSteps={steps.length}
                currentStepIndex={currentStepIndex}
              />
            </li>
          )
        })}
      </ol>
    </nav>
  )
}

interface StepComponentProps {
  step: Step
  stepIndex: number
  totalSteps: number
  currentStepIndex: number
}

function StepperStep({
  step,
  stepIndex,
  totalSteps,
  currentStepIndex,
}: StepComponentProps) {
  const isComplete = stepIndex < currentStepIndex
  const isCurrent = stepIndex === currentStepIndex
  const isUpcoming = !isCurrent && !isComplete
  const isFinal = stepIndex === totalSteps - 1
  const isClickable = step.onClick ? true : false

  return (
    <div className='flex flex-col items-center'>
      {!isFinal && (
        <div
          className={clsx(
            'relative left-1/2 top-4 w-full border-t-2 border-dotted transition-[border-color] duration-300',
            isComplete ? 'border-indigo-600' : 'border-gray-300',
          )}
          aria-hidden='true'
        />
      )}

      <Button
        className={clsx(
          'relative flex h-8 w-8 items-center justify-center rounded-full',
          {
            'cursor-default': !isClickable,
            'bg-indigo-600': isComplete,
            'border-2 border-indigo-600 bg-white': isCurrent,
            'border-2 border-gray-300 bg-white': isUpcoming,
            'hover:bg-indigo-900': isComplete && isClickable,
            'hover:border-gray-400': isUpcoming && isClickable,
          },
        )}
        aria-current={isCurrent ? 'step' : undefined}
        onClick={() => {
          step.onClick && step.onClick()
        }}
      >
        {isComplete ?
          <CheckIcon aria-hidden='true' className='h-5 w-5 text-white' />
        : <div
            className={clsx(
              'font-semibold',
              isCurrent ?
                'text-app-color-fg-brand-primary'
              : 'text-app-color-secondary',
            )}
          >
            {stepIndex + 1}
          </div>
        }
      </Button>

      <div
        className={clsx(
          isComplete || isCurrent ?
            'text-app-color-fg-brand-primary'
          : 'text-app-color-secondary',
        )}
      >
        {step.name}
      </div>
    </div>
  )
}
