interface DoubleTickButtonProps {
  onClick: React.MouseEventHandler
  className?: string
}

export const DoubleTickButton = ({
  onClick,
  className,
}: DoubleTickButtonProps) => (
  <svg
    className={className}
    onClick={onClick}
    width='36'
    height='36'
    viewBox='0 0 36 36'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
  >
    <g filter='url(#filter0_d_3932_267038)'>
      <path
        d='M10.0835 19.5278L13.0002 22.5834L13.8535 21.6894M21.7502 13.4167L16.3335 19.0913'
        stroke='#475467'
        strokeWidth='1.5'
      />
      <path
        d='M14.25 19.5278L17.1667 22.5834L25.9167 13.4167'
        stroke='#475467'
        strokeWidth='1.5'
      />
    </g>
    <defs>
      <filter
        id='filter0_d_3932_267038'
        x='-2'
        y='-1'
        width='40'
        height='40'
        filterUnits='userSpaceOnUse'
        colorInterpolationFilters='sRGB'
      >
        <feFlood floodOpacity='0' result='BackgroundImageFix' />
        <feColorMatrix
          in='SourceAlpha'
          type='matrix'
          values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0'
          result='hardAlpha'
        />
        <feOffset dy='1' />
        <feGaussianBlur stdDeviation='1' />
        <feComposite in2='hardAlpha' operator='out' />
        <feColorMatrix
          type='matrix'
          values='0 0 0 0 0.0627451 0 0 0 0 0.0941176 0 0 0 0 0.156863 0 0 0 0.04 0'
        />
        <feBlend
          mode='normal'
          in2='BackgroundImageFix'
          result='effect1_dropShadow_3932_267038'
        />
        <feBlend
          mode='normal'
          in='SourceGraphic'
          in2='effect1_dropShadow_3932_267038'
          result='shape'
        />
      </filter>
    </defs>
  </svg>
)
