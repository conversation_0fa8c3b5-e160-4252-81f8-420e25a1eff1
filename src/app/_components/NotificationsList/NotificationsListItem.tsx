'use client'
import { CheckCircleIcon } from '@heroicons/react/24/outline'
import { DoubleTickButton } from '../svgComponents/DoubleTickButton'

export interface NotificationsListItemProps {
  id: number
  message: string
  fileName: string
  date: string
  read: boolean
  setReadStatus: (id: number, readStatus: boolean) => void
}

export function NotificationsListItem({
  id,
  message,
  fileName,
  date,
  read,
  setReadStatus,
}: NotificationsListItemProps) {
  const handleCLick = (isRead: boolean) => {
    setReadStatus(id, isRead)
  }
  return (
    <div className='flex'>
      <div className='flex min-w-full items-center px-3'>
        <div className='basis-1/20 mx-3'>
          <div
            className={`h-2 w-2 rounded-full ${!read ? 'bg-blue-500' : 'bg-white'}`}
          ></div>
        </div>
        <div className='flex basis-full items-center justify-between border-b'>
          <div className='flex-col justify-center py-2 text-xs'>
            <div>{message}</div>
            <div>{fileName}</div>
          </div>
          <div className='flex items-center'>
            <div className='mx-3 text-xs text-zinc-500'>{date}</div>
            {!read ?
              <CheckCircleIcon
                className='h-8 w-8 cursor-pointer text-zinc-500 hover:text-indigo-500'
                onClick={() => handleCLick(true)}
              />
            : <DoubleTickButton
                className='mx-1 h-6 w-6 cursor-pointer text-zinc-500 hover:text-indigo-500'
                onClick={() => handleCLick(false)}
              />
            }
          </div>
        </div>
      </div>
    </div>
  )
}
