'use client'
import { useState } from 'react'
import { NotificationsListHeader } from './NotificationsListHeader'
import {
  NotificationsListItem,
  NotificationsListItemProps,
} from './NotificationsListItem'
import { EmptyState } from '../EmptyState'

interface NotificationItem {
  id: number
  message: string
  fileName: string
  date: string
  read: boolean
}

interface NotificationListProps {
  notifications: NotificationItem[]
}

export function NotificationList({ notifications }: NotificationListProps) {
  const [updatedNotifications, setUpdatedNotifications] =
    useState(notifications)

  const setReadStatus = (id: number, updatedStatus: boolean) => {
    const updated = [...updatedNotifications].map((notification) => {
      if (notification.id === id) {
        notification.read = updatedStatus
      }
      return notification
    })
    setUpdatedNotifications(updated)
  }

  const setAllAsRead = () => {
    const updated = [...updatedNotifications].map((notification) => {
      notification.read = true
      return notification
    })
    setUpdatedNotifications(updated)
  }

  return (
    <div className='min-h-0 min-w-full flex-col rounded-lg border border-zinc-300 bg-white p-0 shadow-md'>
      <NotificationsListHeader handleClick={setAllAsRead} />
      <div className='h-full max-h-[250px] w-full overflow-auto p-0'>
        {updatedNotifications.length > 0 ?
          updatedNotifications.map((notification) => (
            <NotificationsListItem
              key={notification.id}
              setReadStatus={setReadStatus}
              {...notification}
            />
          ))
        : <EmptyState />}
      </div>
    </div>
  )
}
