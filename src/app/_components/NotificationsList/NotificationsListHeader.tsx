'use client'
import { Filter } from '@/components/Filter/Filter'
import {
  CalendarDateRangeIcon,
  EnvelopeOpenIcon,
} from '@heroicons/react/24/outline'

interface NotificationsListHeaderProps {
  handleClick: () => void
}

export function NotificationsListHeader({
  handleClick,
}: NotificationsListHeaderProps) {
  return (
    <div className='flex w-full justify-between border border-zinc-300 px-2 py-3'>
      <Filter
        className='bg-white-100 border border-indigo-300 hover:bg-indigo-200 focus-visible:outline-indigo-300'
        label='Jan 1, 2025 - February 1, 2025'
        icon={<CalendarDateRangeIcon />}
        disabled
        inUse
      ></Filter>

      <div
        className='flex items-center justify-center rounded-lg px-3 hover:cursor-pointer hover:bg-indigo-100'
        onClick={handleClick}
      >
        <EnvelopeOpenIcon className='flex h-5 w-5' />
        <span className='px-1 text-xs'>{'Mark all as resolved'}</span>
      </div>
    </div>
  )
}
