'use client'
import React from 'react'
import dynamic from 'next/dynamic'
const Chart = dynamic(() => import('react-apexcharts'), { ssr: false })
import { ApexOptions } from 'apexcharts'

import { colorBase, generateColors, brandColors } from './colors'

const generateOptions = (
  categories: string[],
  seriesLength: number,
): ApexOptions => ({
  chart: {
    type: 'bar',
    stacked: true,
    toolbar: {
      show: true,
      offsetY: -24,
    },
    zoom: {
      enabled: false,
    },
  },
  colors: [colorBase, '#348F2B', '#A5E69F'],
  plotOptions: {
    bar: {
      borderRadius: 4,
      columnWidth: '40px',
    },
  },

  stroke: {
    curve: 'straight',
    width: [3, 1, 1],
    colors: [colorBase, '#fff', '#fff'],
  },
  markers: {
    size: 5,
    hover: {
      size: 5,
    },
    colors: [colorBase],
  },
  grid: {
    show: true,
    xaxis: {
      lines: { show: false },
    },
    yaxis: {
      lines: { show: true },
    },
    padding: {
      top: 20,
      bottom: 15,
      left: 20,
      right: 20,
    },
    borderColor: '#fff',
  },
  dataLabels: {
    enabled: false,
  },
  xaxis: {
    categories,
  },
  yaxis: [
    {
      seriesName: 'Percentage',
      opposite: true,
      title: {
        text: 'Percentage',
      },
      min: 0,
      max: 100,
      labels: {
        formatter: (value: number) => value + '%',
      },
    },
    {
      seriesName: 'Value',
      title: {
        text: 'Dollars (K)',
      },
      labels: {
        formatter: (value: number) => value + 'K',
      },
    },
  ],
  legend: {
    position: 'top',
    horizontalAlign: 'right',
    offsetX: 0,
  },
})

interface CombinationBarAndLineProps {
  categories: string[]
  series: ApexAxisChartSeries
}
export const CombinationBarAndLineChart = ({
  categories,
  series,
}: CombinationBarAndLineProps) => {
  const options = generateOptions(categories, series?.length)
  return <Chart type='bar' options={options} series={series} height={400} />
}
