'use client'
import React from 'react'
import dynamic from 'next/dynamic'
const Chart = dynamic(() => import('react-apexcharts'), { ssr: false })
import { ApexOptions } from 'apexcharts'

import { colorBase, generateColors } from './colors'

const generateOptions = (
  categories: string[],
  seriesLength: number,
): ApexOptions => ({
  chart: {
    type: 'line',
    toolbar: {
      show: true,
      offsetY: -24,
    },
    zoom: {
      enabled: false,
    },
  },
  colors: [colorBase],
  stroke: {
    curve: 'straight',
    width: 3,
  },
  markers: {
    size: 3,
    hover: {
      size: 5,
    },
  },
  grid: {
    show: true,
    xaxis: {
      lines: { show: false },
    },
    yaxis: {
      lines: { show: true },
    },
    padding: {
      top: 20,
      bottom: 15,
      left: 20,
      right: 20,
    },
  },
  dataLabels: {
    enabled: false,
  },
  xaxis: {
    categories,
    axisBorder: {
      show: false,
    },
    axisTicks: {
      show: false,
    },
  },
  yaxis: {
    labels: {
      formatter: (value: number) => value / 1000 + 'M',
    },
  },
})

interface BasicLineChartSeriesData {
  name: string
  data: number[]
}

interface BasicLineChartProps {
  categories: string[]
  series: BasicLineChartSeriesData[]
}

export const BasicLineChart = ({ categories, series }: BasicLineChartProps) => {
  const options = generateOptions(categories, series.length)
  return <Chart options={options} series={series} width='100%' height={330} />
}
