'use client'
import React from 'react'
import dynamic from 'next/dynamic'
const Chart = dynamic(() => import('react-apexcharts'), { ssr: false })
import { ApexOptions } from 'apexcharts'

import { colorBase, generateColors } from './colors'

const generateOptions = (
  categories: string[],
  seriesLength: number,
): ApexOptions => ({
  chart: {
    type: 'line',
    stacked: true,
    toolbar: {
      show: true,
      offsetY: -24,
    },
  },
  colors: generateColors(colorBase, seriesLength),
  plotOptions: {
    bar: {
      horizontal: true,
      barHeight: '40px',
      columnWidth: '100%',
      borderRadius: 4,
    },
  },
  grid: {
    show: false,
    xaxis: {
      lines: { show: true },
    },
    yaxis: {
      lines: { show: false },
    },
  },
  dataLabels: {
    enabled: false,
  },
  xaxis: {
    categories,
    labels: {
      formatter: (value: string) => value + 'K',
    },
  },
  yaxis: {},
  legend: {
    position: 'right',
    horizontalAlign: 'left',
    offsetX: 0,
  },
  stroke: {
    curve: 'straight',
    width: [1],
    colors: ['#fff'],
  },
  states: {
    hover: {
      filter: {
        type: 'darken',
        value: 0.8,
      },
    },
  },
})

interface HorizontalBarStackedProps {
  categories: string[]
  series: ApexAxisChartSeries
}

export const HorizontalBarStackedChart = ({
  categories,
  series,
}: HorizontalBarStackedProps) => {
  const options = generateOptions(categories, series?.length)
  return (
    <Chart
      options={options}
      series={series}
      type='bar'
      width='100%'
      height={350}
    />
  )
}
