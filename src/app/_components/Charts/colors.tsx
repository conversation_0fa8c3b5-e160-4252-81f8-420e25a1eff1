// export const colorBase = '#4F46E5'
export const colorBase = '#4BCD3E'

export const hexToRgb = (hex: string) => ({
  r: parseInt(hex.slice(1, 3), 16),
  g: parseInt(hex.slice(3, 5), 16),
  b: parseInt(hex.slice(5, 7), 16),
})

export const rgbToHex = ({ r, g, b }: { r: number; g: number; b: number }) => {
  const mappedToHex = [r, g, b].map((x) =>
    Math.round(x).toString(16).padStart(2, '0'),
  )
  return `#${mappedToHex.join('')}`
}

export const brandColors = [
  '#4BCD3E',
  '#009775',
  '#015B7E',
  '#3BCFF0',
  '#285BC5',
  '#1B1B6F',
  '#A18CDE',
  '#4C12A1',
  '#FD8D62',
  '#FF1F3E',
  '#FFED8C',
  '#FFCD00',
]

export const generateSpectrum = (hexBase: string, n: number): string[] => {
  const { r, g, b } = hexToRgb(hexBase)

  const lightener = (_: string, i: number) => {
    const ratio = (i * 1) / n
    const lighten = (x: number) => x + (255 - x) * ratio
    return rgbToHex({
      r: lighten(r),
      g: lighten(g),
      b: lighten(b),
    })
  }

  return Array.from({ length: n }, lightener)
}

const alternateDarkAndLight = (range: string[]): string[] => {
  let result = []
  let i = 0
  let j = range.length - 1
  while (i < j) {
    result.push(range[i])
    result.push(range[j])
    i++
    j--
  }
  return result
}

export const generateColors = (hexBase: string, n: number): string[] => {
  // // Spectrum based on baseColor
  // return generateSpectrum(hexBase, n)

  // // Spectrum based on baseColor, alternating from each end
  // return alternateDarkAndLight(generateSpectrum(hexBase, n))

  return brandColors
}
