'use client'
import React, { useState } from 'react'
import dynamic from 'next/dynamic'
const Chart = dynamic(() => import('react-apexcharts'), { ssr: false })
import { ApexOptions } from 'apexcharts'

import { colorBase, generateColors } from './colors'

const generateOptions = (
  categories: string[],
  seriesLength: number,
): ApexOptions => ({
  chart: {
    type: 'bar',
    stacked: true,
    toolbar: {
      show: true,
      offsetY: -92,
    },
  },
  colors: [colorBase],
  plotOptions: {
    bar: {
      borderRadius: 4,
      columnWidth: '40px',
    },
  },
  grid: {
    show: false,
    xaxis: {
      lines: { show: false },
    },
    yaxis: {
      lines: { show: false },
    },
    padding: {
      top: 0,
      bottom: 0,
    },
  },
  dataLabels: {
    enabled: false,
  },
  xaxis: {
    categories,
    axisBorder: {
      show: false,
    },
    axisTicks: {
      show: false,
    },
  },
  yaxis: {
    labels: {
      formatter: (value: number) => value / 1000 + 'K',
    },
  },
})

interface VerticalBarChartSeriesData {
  name: string
  data: number[]
}

interface VerticalBarChartProps {
  categories: string[]
  series: VerticalBarChartSeriesData[]
}

export const VerticalBarTabbedChart = ({
  categories,
  series,
}: VerticalBarChartProps) => {
  const [activeSeriesIndex, setActiveSeriesIndex] = useState(0)

  const options = generateOptions(categories, series?.length)
  const data = series[activeSeriesIndex]
  return (
    <>
      <div className='mb-0 mt-8 flex justify-between gap-[20px] px-3'>
        {series.map((tab, i) => (
          <div
            key={i}
            className={`flex-1 cursor-pointer items-center justify-center whitespace-nowrap rounded-lg p-2 text-center text-sm ${i === activeSeriesIndex ? 'text-black-500 bg-[#E9F9E8]' : ''}`}
            onClick={() => setActiveSeriesIndex(i)}
          >
            {tab.name}
          </div>
        ))}
      </div>
      <Chart
        options={options}
        series={[data]}
        type='bar'
        width='100%'
        height={250}
      />
    </>
  )
}
