import { MagnifyingGlassIcon } from '@heroicons/react/20/solid'
import clsx from 'clsx'

export const EmptyState = ({ classNames }: { classNames?: string }) => (
  <div
    className={clsx(
      'flex h-64 w-full flex-col items-center justify-center',
      classNames,
    )}
  >
    <MagnifyingGlassIcon className='mb-2 h-6 w-6 text-zinc-400' />
    <span className='text-sm text-zinc-400'>No Data Available</span>
  </div>
)
