import mockData from '@/app/api/v1/service-price-lists/servicePriceListsMockData.json'

class ServicePriceLists {
  static readonly instance = new ServicePriceLists()

  // Deep copy -- we don't care about performance here, and we know the data
  // can be stringified because it's from a json file.
  private data = JSON.parse(JSON.stringify(mockData))

  getData(id: string) {
    return this.data[id]?.priceListsContents
  }
}

// Create a single instance of the ServicePriceLists data store
export default ServicePriceLists.instance
