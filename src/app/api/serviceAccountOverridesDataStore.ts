import mockData from '@/app/api/v1/service-account-overrides/serviceAccountOverridesMockData.json'

class ServiceAccountOverrides {
  static readonly instance = new ServiceAccountOverrides()

  // Deep copy -- we don't care about performance here, and we know the data
  // can be stringified because it's from a json file.
  private data = JSON.parse(JSON.stringify(mockData))

  getData(id: string) {
    return this.data[id]?.accountOverridesContents
  }
}

// Create a single instance of the ServiceAccountOverrides data store
export default ServiceAccountOverrides.instance
