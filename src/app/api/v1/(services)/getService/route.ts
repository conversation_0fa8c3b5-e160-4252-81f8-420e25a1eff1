import { Service } from '@/app/[bankId]/services/types'
import { MOCK_DATA } from '../../mocks/MockData'

export interface GetServiceResponse {
  versions: Service[]
}

export async function POST(request: Request) {
  const serviceCode = await request.json()
  const versions = MOCK_DATA.get('ServiceStore').getService(serviceCode)

  if (!versions) {
    return Response.json({ reason: 'Service not found' }, { status: 404 })
  }

  return Response.json({
    versions: Object.values(versions),
  } as GetServiceResponse)
}
