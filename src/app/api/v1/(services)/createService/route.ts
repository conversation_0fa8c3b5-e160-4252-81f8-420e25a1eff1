import { NextRequest } from 'next/server'
import { Service } from '@/app/[bankId]/services/types'
import { MOCK_DATA } from '../../mocks/MockData'

export async function POST(request: NextRequest) {
  const newService = (await request.json()) as Service

  MOCK_DATA.get('ServiceStore').setService(newService)

  MOCK_DATA.get('ServiceCatalog').addService({
    code: newService.code,
    type: 'service',
    name: newService.name,
    serviceType: newService.serviceType,
    priceType: newService.price.type,
    parentCode: newService.parentCode ?? 'root',
    effectiveDate: newService.effectiveDate,
    isExpired: newService.isExpired,
  })

  return Response.json(newService)
}
