import { NextRequest } from 'next/server'
import { MOCK_DATA } from '../../mocks/MockData'

export interface DeleteServiceRequest {
  code: string
  effectiveDate: string
}

export type DeleteServiceResponse = DeleteServiceRequest

export async function POST(request: NextRequest) {
  const deleteData = (await request.json()) as DeleteServiceRequest

  MOCK_DATA.get('ServiceStore').deleteService(
    deleteData.code,
    deleteData.effectiveDate,
  )

  MOCK_DATA.get('ServiceCatalog').deleteService(deleteData.code)

  return Response.json(deleteData)
}
