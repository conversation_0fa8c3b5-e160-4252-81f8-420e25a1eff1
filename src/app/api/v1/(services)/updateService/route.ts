import { NextRequest } from 'next/server'
import { MOCK_DATA } from '../../mocks/MockData'
import { Service } from '@/app/[bankId]/services/types'

export interface UpdateServiceRequest {
  previousCode: string
  service: Service
}

export interface UpdateServiceResponse {
  versions: Service[]
}

export async function POST(request: NextRequest) {
  const { service, previousCode } =
    (await request.json()) as UpdateServiceRequest

  const versions = MOCK_DATA.get('ServiceStore').updateService(
    previousCode,
    service,
  )

  if (!versions) {
    return Response.json({ reason: 'previous is not found' }, { status: 404 })
  }

  MOCK_DATA.get('ServiceCatalog').updateService(previousCode, {
    code: service.code,
    type: 'service',
    name: service.name,
    serviceType: service.serviceType,
    priceType: service.price.type,
    parentCode: service.parentCode ?? 'root',
    effectiveDate: service.effectiveDate,
    isExpired: service.isExpired,
  })

  return Response.json({
    versions: Object.values(versions),
  } as UpdateServiceResponse)
}
