import { NextRequest, NextResponse } from 'next/server'
import { MOCK_DATA } from '../../mocks/MockData'
import { Category } from '@/app/[bankId]/services/types'

export interface UpdateCategoryRequest {
  previousCode: string
  newValues: Partial<Category>
}

export async function POST(request: NextRequest) {
  const category = (await request.json()) as UpdateCategoryRequest

  MOCK_DATA.get('ServiceCatalog').updateCategory(
    category.previousCode,
    category.newValues,
  )

  if (category.newValues.code) {
    MOCK_DATA.get('ServiceStore').updateCategory(
      category.previousCode,
      category.newValues.code,
    )
  }

  return NextResponse.json(category)
}
