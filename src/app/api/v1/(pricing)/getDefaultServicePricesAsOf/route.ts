import { paths } from '@/api/schema'
import { toServerFormat } from '@/lib/date'

type GetServicesResponse =
  paths['/getServicePricesAsOf']['post']['responses']['200']['content']['application/json']

export function POST() {
  const res: GetServicesResponse = [
    {
      applyServiceTo: 'ALL_DEPOSIT_ACCOUNTS',
      balanceDivisor: 11,
      balanceType: 'AVERAGE_COLLECTED',
      baseFee: 1111,
      basisDays: '365',
      costType: 'NO_COST',
      costValue: 111,
      currency: 'USD$$',
      cycleDefinitionCode: 'SOME VAL',
      disposition: 'HARD_CHARGE',
      effectiveDate: '2024-07-07',
      includeReferenceInformationOnStatements: false,
      indexAdjustment: 111111,
      indexMultiplier: 11,
      indexRateCode: 'TEST11',
      maximumFee: 111111,
      minimumFee: 11,
      priceType: 'FLAT_FEE',
      priceValue: 222,
      pricingHierarchyEntryCode: '1111',
      pricingHierarchyEntryType: 'PRICE_LIST',
      serviceCode: 'serv2',
      subjectToDiscountOrPremium: false,
      tierMaxBalanceExclusive: 111,
      tierMaxVolumeExclusive: 111,
      tierMinBalanceInclusive: 11,
      tierMinVolumeInclusive: 111,
      tierNumber: 111,
      tierPriceType: 'UNIT_PRICED',
      units: 1,
    },
  ]

  return Response.json(res)
}
