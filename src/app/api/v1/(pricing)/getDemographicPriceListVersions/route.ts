import { paths } from '@/api/schema'
import { toServerFormat } from '@/lib/date'

type GetDemographicPriceListVersionsResponse =
  paths['/getDemographicPriceListVersions']['post']['responses']['200']['content']['application/json']

const generateRandomDate = (start: Date, end: Date) => {
  return new Date(
    start.getTime() + Math.random() * (end.getTime() - start.getTime()),
  )
}

export function POST() {
  const res: GetDemographicPriceListVersionsResponse = new Array(5)
    .fill(0)
    .map((_, i): string => {
      const randomDate = generateRandomDate(
        new Date(2023, 0, 1 + i),
        new Date(2025, 11, 31),
      )
      return toServerFormat(randomDate)
    })
  return Response.json(res)
}
