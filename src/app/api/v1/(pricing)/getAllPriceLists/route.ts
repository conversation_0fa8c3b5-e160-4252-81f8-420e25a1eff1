import { DemographicPriceList } from '@/api/formToApiSchema'

// export function POST() {
//   return Response.json(
//     new Array(50).fill(0).map((_, i): DemographicPriceList => {
//       return {
//         // services: `${Math.round(Math.random() * 20)}`,
//         ranking: `${i + 1}`,
//         code: `${i + 1}`,
//         effectiveDate: '2024-12-01',
//         name: `DPL ${i + 1}`,
//         isLeadPriceList: Math.random() > 0.5,
//         currency: 'USD',
//       }
//     }),
//   )
// }
