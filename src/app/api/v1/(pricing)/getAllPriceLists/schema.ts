// import { servicePriceSchema } from '@/api/runtimeSchema'
// import { code, int, name } from '@/api/zodSchemas'
// // import { servicePriceSchema } from '@/components/ServicePrice/servicePriceSchema'
// import { z } from 'zod'

// export const priceListSchema = z.object({
//   code: code(1, 20).nullable(),
//   effectiveDate: z.string().date(),
//   name: name.nullable(),
//   priceListType: z.enum(['DEMOGRAPHIC', 'PROMOTIONAL']),
//   isLeadPriceList: z.boolean(),
//   term: int.nullable(),
//   currency: z.literal('USD'),
// })

// export type PriceList = z.infer<typeof priceListSchema>

// const demographicPriceListSchema = priceListSchema
//   .transform((value) => {
//     return { ...value, priceListType: 'DEMOGRAPHIC', term: null }
//   })
//   .pipe(
//     z.object({
//       ...priceListSchema.shape,
//       code: code(1, 20),
//       name,
//       priceListType: z.literal('DEMOGRAPHIC'),
//       term: z.null(),
//     }),
//   )

// export const demographicCriteriaSchema = z.object({
//   priceListCode: z.string(),
//   criteriaCode: z.string(),
//   keyType: z.enum(['USER_FIELD', 'ACCOUNT_FIELD']),
//   stringKey: z.string().nullable(),
//   codeKey: z.string().nullable(),
//   valueCode: z.string(),
//   booleanValue: z.boolean().nullable(),
//   stringValue: z.string().nullable(),
//   codeValue: z.number().nullable(),
// })

// export type DemographicCriteria = z.infer<typeof demographicCriteriaSchema>

// export const hydratedDemographicPriceListSchema = z.object({
//   priceList: demographicPriceListSchema,
//   criteria: z.array(demographicCriteriaSchema),
//   services: z.array(servicePriceSchema),
// })

// export type HydratedDemographicPriceList = z.input<
//   typeof hydratedDemographicPriceListSchema
// >

// export const hydratedDemographicPriceListDefaultValues = (
//   effectiveDate: string,
// ): HydratedDemographicPriceList => ({
//   priceList: {
//     effectiveDate,
//     code: null,
//     name: null,
//     priceListType: 'DEMOGRAPHIC',
//     isLeadPriceList: false,
//     term: null,
//     currency: 'USD',
//   },
//   criteria: [],
//   services: [],
// })
