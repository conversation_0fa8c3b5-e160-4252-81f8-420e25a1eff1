import { ServiceCatalogStore } from './ServiceCatalogStore'
import { ServiceStore } from './ServiceStore'
declare global {
  var mockDataRef: MockData<{
    ServiceStore: ServiceStore
    ServiceCatalog: ServiceCatalogStore
  }>
}

export interface MockSource {
  reset(): void
}

class MockData<T extends Record<string, MockSource>> implements MockSource {
  constructor(private mocks: T) {}

  get<TKey extends keyof T>(type: TKey): T[TKey] {
    return this.mocks[type]
  }

  reset(): void {
    Object.values(this.mocks).forEach((mock) => mock.reset())
  }
}

// Need to keep the mock data reference inside of the global object,
// this helps to prevent mocked state being reset each time NextJS triggers partial compilation in developer mode.
export const MOCK_DATA =
  globalThis.mockDataRef ??
  (globalThis.mockDataRef = new MockData({
    ServiceStore: new ServiceStore(),
    ServiceCatalog: new ServiceCatalogStore(),
  }))
