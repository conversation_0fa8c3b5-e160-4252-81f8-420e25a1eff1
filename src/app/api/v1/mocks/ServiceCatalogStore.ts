import {
  Category,
  GetCatalogResponse,
  ServiceSummary,
} from '@/app/[bankId]/services/types'
import mockData from './mockData.json'
import { PriceType, ServiceType } from '@/api/zodSchemas'

type Data = typeof mockData

function transformToNormalizedState(mockData: Data): GetCatalogResponse {
  const result: GetCatalogResponse = {
    serviceByCode: {},
    categoryByCode: {},
  }

  forEachCatalogItem(mockData, (item, parent) => {
    if (item.type === 'category') {
      const categoryCodes = item.contents
        .filter((item) => item.type == 'category')
        .map((category) => category.code)

      const serviceCodes = item.contents
        .filter((item) => item.type == 'service')
        .map((service) => service.code)

      const category: Category = {
        name: item.name,
        type: 'category',
        code: item.code,
        parentCode: parent?.code,
        categoryCodes,
        serviceCodes,
      }

      result.categoryByCode[category.code] = category
    } else {
      const service: ServiceSummary = {
        code: item.code,
        name: item.name,
        parentCode: parent?.code!,
        priceType:
          'priceType' in item ?
            (item.priceType as string)
          : ('NOT_PRICED' as PriceType),
        serviceType:
          'productType' in item ?
            (item.productType as string)
          : ('volumeBased' as ServiceType),
        type: 'service',
        effectiveDate: '2024-10-25',
        isExpired: false,
      }
      result.serviceByCode[item.code] = service
    }
  })

  return result
}

export class ServiceCatalogStore {
  // Deep copy -- we don't care about performance here, and we know the data
  // can be stringified because it's from a json file.
  private data: GetCatalogResponse = JSON.parse(
    JSON.stringify(transformToNormalizedState(mockData)),
  )

  getAll(): GetCatalogResponse {
    return this.data
  }

  addCategory(category: Category) {
    this.data.categoryByCode[category.code] = category
    if (category.parentCode) {
      const parent = this.data.categoryByCode[category.parentCode]
      parent.categoryCodes.push(category.code)
    }
  }

  deleteService(serviceCode: string) {
    const service = this.data.serviceByCode[serviceCode]
    const category = this.data.categoryByCode[service.parentCode]

    category.serviceCodes = category.serviceCodes.filter(
      (code) => code !== service.code,
    )

    delete this.data.serviceByCode[service.code]
  }

  updateService(previousCode: string, service: ServiceSummary) {
    const previousService = this.data.serviceByCode[previousCode]

    if (previousService.parentCode != service.parentCode) {
      this.deleteService(previousCode)
      this.addService(service)
    } else {
      this.data.serviceByCode[service.code] = service
      const category = this.data.categoryByCode[service.parentCode]

      const serviceIndex = category.serviceCodes.findIndex(
        (code) => code === previousCode,
      )

      category.serviceCodes[serviceIndex] = service.code
    }
  }

  updateCategory(previousCode: string, newValues: Partial<Category>) {
    const previousVersion = this.data.categoryByCode[previousCode]

    const newVersion = {
      ...previousVersion,
      ...newValues,
    }

    this.data.categoryByCode[newVersion.code] = newVersion

    if (previousVersion.parentCode) {
      const parentCategory =
        this.data.categoryByCode[previousVersion.parentCode]

      if (previousCode !== newVersion.code) {
        const index = parentCategory.categoryCodes.findIndex(
          (code) => code === previousCode,
        )

        previousVersion.serviceCodes.forEach((serviceCode) => {
          this.data.serviceByCode[serviceCode].parentCode = newVersion.code
        })

        parentCategory.categoryCodes[index] = newVersion.code

        delete this.data.categoryByCode[previousCode]
      }
    }
  }

  deleteCategory(category: Category) {
    if (category.parentCode) {
      const parent = this.data.categoryByCode[category.parentCode]
      parent.categoryCodes = parent.categoryCodes.filter(
        (item) => item !== category.code,
      )
    }

    delete this.data.categoryByCode[category.code]
  }

  addService(service: ServiceSummary) {
    this.data.serviceByCode[service.code] = service
    this.data.categoryByCode[service.parentCode].serviceCodes.push(service.code)
  }

  reset(): void {
    this.data = new ServiceCatalogStore().data
  }
}

export function forEachCatalogItem(
  category: Data,
  fn: (item: Data, parent?: Data) => boolean | void,
): void {
  fn(category)
  const stack = [category]
  while (stack.length > 0) {
    const category = stack.pop()!
    for (const item of category.contents) {
      fn(item as Data, category)
      if (item.type === 'category') stack.push(item as Data)
    }
  }
}
