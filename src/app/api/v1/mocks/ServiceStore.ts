import { Service, ServiceSummary } from '@/app/[bankId]/services/types'
import { MOCK_DATA, MockSource } from './MockData'
import { CostTypes } from '@/app/[bankId]/services/_components/costTypeOptions'

const uniqueServiceId = (code: string, effectiveDate: string) =>
  `${code}+${effectiveDate}`

function toService(service: ServiceSummary): Service {
  return {
    ...service,
    id: crypto.randomUUID(),
    price: {
      cost: { cost: '1', type: CostTypes.unitCost },
      type: 'NOT_PRICED',
      disposition: 'ANALYSED',
      baseFee: null,
      maxFee: null,
      minFee: null,
      price: null,
    },
    serviceType: 'VOLUME_BASED',
    //ToDo: for purpose of the demo, should be removed in scope of: https://jira.fis.dev/browse/AFIN-253
    hasAcceptedTransactions: service.code === 'B25',
    balanceType: null,
    serviceSet: [],
    priceTiers: [],
    applyServiceTo: null,
    basisDays: null,
    feeCycle: null,
    indexRate: null,
    subPriceType: null,
    units: null,
    balanceDivisor: '0',
  }
}
export class ServiceStore implements MockSource {
  private versionsByCode: Record<string, Service>[] = []

  reset(): void {
    this.versionsByCode = []
  }

  updateCategory(previousCode: string, newCode: string) {
    this.versionsByCode.forEach((versions) => {
      Object.values(versions).forEach((version) => {
        if (version.parentCode === previousCode) {
          version.parentCode = newCode
        }
      })
    })
  }

  private createNew(service: Service) {
    this.versionsByCode.push({
      [uniqueServiceId(service.code, service.effectiveDate)]: service,
    })
  }

  private getAllVersions(serviceCode: string) {
    return this.versionsByCode.find((codes) =>
      Object.keys(codes).some((key) => key.startsWith(serviceCode)),
    )
  }

  private updateByCode(previousCode: string, updatedService: Service) {
    let serviceRecords = this.getAllVersions(previousCode)

    if (!serviceRecords) {
      return
    }

    serviceRecords[
      uniqueServiceId(updatedService.code, updatedService.effectiveDate)
    ] = updatedService
  }

  updateService(previousCode: string, service: Service) {
    this.updateByCode(previousCode, service)
    return this.getAllVersions(service.code)
  }

  getService(code: string) {
    const versions = this.getAllVersions(code)
    const versionInCatalog =
      MOCK_DATA.get('ServiceCatalog').getAll().serviceByCode[code]

    if (!versions && !versionInCatalog) {
      return undefined
    }

    if (versionInCatalog) {
      this.createNew(toService(versionInCatalog))
    }

    return this.getAllVersions(code)
  }

  setService(service: Service) {
    this.createNew(service)
  }

  deleteService(code: string, effectiveDate: string) {
    const versions = this.getAllVersions(code)

    if (!versions) {
      return undefined
    }

    delete versions[uniqueServiceId(code, effectiveDate)]
  }
}
