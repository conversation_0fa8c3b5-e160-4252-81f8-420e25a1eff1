import mockData from '@/app/api/v1/service-promotions/servicePromotionsMockData.json'

class ServicePromotions {
  static readonly instance = new ServicePromotions()

  // Deep copy -- we don't care about performance here, and we know the data
  // can be stringified because it's from a json file.
  private data = JSON.parse(JSON.stringify(mockData))

  getData(id: string) {
    return this.data[id]?.promotionsContents
  }
}

// Create a single instance of the ServicePromotions data store
export default ServicePromotions.instance
