import { Row } from '@tanstack/react-table'
import { useMemo, useState } from 'react'

type State = Record<string, any>
type FilterOptions<T extends State> = {
  values: T
  onStateUpdate: (newState: T) => void
}

export function defineFilters<T extends State>({
  defaultValues,
}: {
  defaultValues: T
}) {
  return function filtersState({ values, onStateUpdate }: FilterOptions<T>) {
    return {
      values,
      update<Key extends keyof T>(key: Key) {
        return (value: T[Key] | null | undefined) => {
          onStateUpdate({ ...values, [key]: value ?? defaultValues[key] })
        }
      },
      columnFilters: Object.entries(values).map(([id, value]) => {
        return { id, value }
      }),
    }
  }
}

export function useFilterState<T extends State>({
  defaultValues,
}: {
  defaultValues: T
}) {
  const [filtersState, setFiltersState] = useState(defaultValues)

  return useMemo(() => {
    return defineFilters({ defaultValues })({
      values: filtersState,
      onStateUpdate: setFiltersState,
    })
  }, [filtersState, defaultValues])
}

export function arrayFilterFn<Value>(
  row: Row<Value>,
  columnId: string,
  filterValue: unknown[],
) {
  const columnValue = row.getValue(columnId)
  if (columnValue === undefined || filterValue.length === 0) return true
  return filterValue.includes(row.getValue(columnId))
}
