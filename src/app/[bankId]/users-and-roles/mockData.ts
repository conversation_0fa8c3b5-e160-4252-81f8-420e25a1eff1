export const mockUsers = [
  {
    id: '1',
    name: '<PERSON>',
    roles: ['1', '2', '4'],
    email: 'joh<PERSON><PERSON>@xzy.com',
    created: '2023-08-31',
    lastUpdated: '2023-08-31',
    lastSignIn: '2023-08-31',
  },
  {
    id: '2',
    name: '<PERSON>',
    roles: ['1', '2'],
    email: 'j<PERSON><PERSON>@sxasd.com',
    created: '2023-08-31',
    lastUpdated: '2023-08-31',
    lastSignIn: '2023-08-31',
  },
  {
    id: '3',
    name: '<PERSON>',
    roles: ['3'],
    email: '<EMAIL>',
    created: '2023-08-31',
    lastUpdated: '2023-08-31',
    lastSignIn: '2023-08-31',
  },
  {
    id: '4',
    name: '<PERSON>',
    roles: ['3'],
    email: '<EMAIL>',
    created: '2023-08-31',
    lastUpdated: '2023-08-31',
    lastSignIn: '2023-08-31',
  },
]
export const mockRoles = [
  {
    id: '1',
    name: 'Admin',
    description: 'description - Admin role',
    numberOfUsers: 1,
    permissions: [
      {
        name: 'Bank options',
        view: true,
        add: true,
        edit: true,
        delete: true,
      },
      {
        name: 'User fields',
        view: true,
        add: true,
        edit: true,
        delete: true,
      },
      {
        name: 'System options',
        view: true,
        add: true,
        edit: true,
        delete: true,
      },
      {
        name: 'Branch',
        view: true,
        add: true,
        edit: true,
        delete: true,
      },
      {
        name: 'Index rates',
        view: true,
        add: true,
        edit: true,
        delete: true,
      },
      {
        name: 'Officer',
        view: true,
        add: true,
        edit: true,
        delete: true,
      },
      {
        name: 'Reports',
        view: true,
        add: true,
        edit: true,
        delete: true,
      },
    ],
  },
  {
    id: '2',
    name: 'Bank Operations',
    description: 'description - Bank Operations role',
    numberOfUsers: 2,
    permissions: [
      {
        name: 'Bank options',
        view: true,
        add: false,
        edit: false,
        delete: false,
      },
      {
        name: 'User fields',
        view: true,
        add: false,
        edit: false,
        delete: false,
      },
      {
        name: 'System options',
        view: true,
        add: false,
        edit: false,
        delete: false,
      },
      {
        name: 'Branch',
        view: true,
        add: false,
        edit: false,
        delete: false,
      },
      {
        name: 'Index rates',
        view: true,
        add: false,
        edit: false,
        delete: false,
      },
      {
        name: 'Officer',
        view: true,
        add: false,
        edit: false,
        delete: false,
      },
      {
        name: 'Reports',
        view: true,
        add: false,
        edit: false,
        delete: false,
      },
    ],
  },
  {
    id: '3',
    name: 'Consulting Manager',
    description: 'description - CM role',
    numberOfUsers: 1,
    permissions: [
      {
        name: 'Bank options',
        view: true,
        add: true,
        edit: true,
        delete: true,
      },
      {
        name: 'User fields',
        view: true,
        add: true,
        edit: true,
        delete: true,
      },
      {
        name: 'Index rates',
        view: true,
        add: true,
        edit: true,
        delete: true,
      },
      {
        name: 'Officer',
        view: true,
        add: true,
        edit: true,
        delete: true,
      },
      {
        name: 'Reports',
        view: true,
        add: true,
        edit: true,
        delete: true,
      },
    ],
  },
  {
    id: '4',
    name: 'Product Manager',
    description: 'description - PM role',
    numberOfUsers: 1,
    permissions: [
      {
        name: 'Bank options',
        view: true,
        add: true,
        edit: true,
        delete: true,
      },
      {
        name: 'User fields',
        view: true,
        add: true,
        edit: true,
        delete: true,
      },
      {
        name: 'Index rates',
        view: true,
        add: true,
        edit: true,
        delete: true,
      },
      {
        name: 'Officer',
        view: true,
        add: true,
        edit: true,
        delete: true,
      },
      {
        name: 'Reports',
        view: true,
        add: true,
        edit: true,
        delete: true,
      },
    ],
  },
]
export const mockPermissionNames = [
  'Bank Options',
  'User Fields',
  'System Options',
  'Branch',
  'Index Rates',
  'Officer',
  'Reports',
  'Account Types',
  'Cycle',
  'Investable Balance',
  'Required Balance',
  'Reserve Requirement',
  'Earnigns Credit',
  'Interest',
  'Results Options',
  'Statement Format',
  'Statement Message',
  'Demographic Price List',
  'Pricing Hierarchy',
  'Promotional Price List',
  'Service Category',
  'Service',
  'Pricing Difference',
  'Trends - Volume and Fees for all Accounts',
  'Demographics and Pricing Options',
  'Name and Address',
  'Settlement and Processing',
  'Trends',
  'Statements',
  'Statement Package',
  'Relationship',
  'Overrides',
  'Price List',
  'Promotional Price List',
  'Pricing Hierarchy',
  'Composite Account Workflow',
  'Volume, Fees and Pricing for Services',
  'Daily Balances',
  'Balance Adjustments',
  'Service Adjustments',
  'Re-analysis',
  'Services',
  'Related Accounts',
  'Relationship Components',
  'Service Transaction File',
  'Exception Pricing',
  'Demographics',
  'User Fields',
  'Roles and Entitlements',
  'Users',
]
