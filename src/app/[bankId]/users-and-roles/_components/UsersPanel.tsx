import { useState, useMemo } from 'react'
import { selectableColumn } from '@/components/Table/tableUtils'
import { Button } from '@/components/Button'
import { SearchBar } from '@/components/SearchBar'
import { SimpleMultiselectFilter } from '@/components/Filter/SimpleMultiselectFilter'
import { SortedTable } from '@/components/Table/SortedTable'
import { User, Role } from '../types'
import { ColumnDef, ColumnFilter } from '@tanstack/react-table'
import { toUIFormat } from '@/lib/date'
import { AssignRolesDrawer } from './AssignRolesDrawer'

type UsersListColumnDef = ColumnDef<User>
type RoleValues = string[] | null
interface UsersPanelProps {
  users: User[]
  roles: Role[]
  updateUsers: (userIds: string[], roleIds: string[]) => void
  permissionNames: string[]
}
export function UsersPanel({
  users,
  roles,
  updateUsers,
  permissionNames,
}: UsersPanelProps) {
  const [selectedUsers, setSelectedUsers] = useState<User[]>([])
  const [searchFilter, setSearchFilter] = useState<ColumnFilter>({
    id: 'name',
    value: '',
  })
  const [selectedRolesFilter, setSelectedRolesFilter] = useState<ColumnFilter>({
    id: 'roles',
    value: [],
  })
  const [selectedRoleValues, setSelectedRoleValues] = useState<RoleValues>([])
  const [isDrawerOpen, setDrawerOpen] = useState(false)

  const handleSearchChange = (searchVal: string) => {
    setSearchFilter({ id: 'name', value: searchVal })
  }

  const handleRoleFilterChange = (selectedRoleNames: RoleValues) => {
    const selectedRoleNamesSet = new Set(selectedRoleNames)
    const selectedRoleIds = roles
      .filter((role) => selectedRoleNamesSet.has(role.name))
      .map((role) => role.id)
    setSelectedRolesFilter({ id: 'roles', value: selectedRoleIds })
    setSelectedRoleValues(selectedRoleNames)
  }

  const rolesMap = useMemo(() => {
    return Object.fromEntries(roles.map(({ id, name }) => [id, name]))
  }, [roles])

  const columns = useMemo(() => {
    return [
      selectableColumn,
      {
        header: 'Name',
        accessorKey: 'name',
        cell: ({ getValue }) => (
          <div className='text-sm font-medium'>{getValue<string>()}</div>
        ),
      },
      {
        header: 'Roles',
        accessorKey: 'roles',
        cell: ({ getValue }) => {
          const roleNames = getValue<string[]>().map(
            (roleId) => rolesMap[roleId],
          )
          return (
            <div className='flex flex-wrap gap-2'>
              {roleNames.map((roleName) => (
                <span
                  key={roleName}
                  className='flex items-center rounded-md bg-gray-100 px-[8px] py-[2px] text-xs font-medium text-app-color-text-secondary'
                >
                  {roleName}
                </span>
              ))}
            </div>
          )
        },
        filterFn: (row, columnId, filterValue) => {
          if (!filterValue || !filterValue.length) return true
          const rowRoles = row.getValue<string[]>(columnId)
          return filterValue.some((role: string) => rowRoles.includes(role))
        },
      },
      {
        header: 'Email',
        accessorKey: 'email',
        cell: ({ getValue }) => (
          <div
            title={getValue<string>()}
            className='truncate text-sm font-medium text-app-color-secondary'
          >
            {getValue<string>()}
          </div>
        ),
      },
      {
        header: 'Created',
        accessorKey: 'created',
        cell: ({ getValue }) => (
          <div className='text-sm font-medium text-app-color-secondary'>
            {toUIFormat(getValue<string>())}
          </div>
        ),
      },
      {
        header: 'Last updated',
        accessorKey: 'lastUpdated',
        cell: ({ getValue }) => (
          <div className='text-sm font-medium text-app-color-secondary'>
            {toUIFormat(getValue<string>())}
          </div>
        ),
      },
      {
        header: 'Last sign-in',
        accessorKey: 'lastSignIn',
        cell: ({ getValue }) => (
          <div className='text-sm font-medium text-app-color-secondary'>
            {toUIFormat(getValue<string>())}
          </div>
        ),
      },
    ] as UsersListColumnDef[]
  }, [rolesMap])
  return (
    <>
      <div className='mb-5 flex w-full items-center justify-between leading-[40px]'>
        <div className='text-lg font-semibold'>Users</div>
        <Button
          className='btn-primary text-white disabled:bg-indigo-200'
          disabled={!selectedUsers.length}
          onClick={() => setDrawerOpen(true)}
        >
          Assign role
        </Button>
      </div>
      <div className='mb-5 flex h-7 gap-2'>
        <SearchBar
          className='w-[385px]'
          defaultLabel='Search users'
          onValueChange={handleSearchChange}
        />
        <div className='mt-1'>
          <SimpleMultiselectFilter
            defaultLabel='Roles'
            name='rolesFilter'
            options={roles.map((role) => role.name)}
            value={selectedRoleValues}
            onValueChange={handleRoleFilterChange}
          />
        </div>
      </div>
      <SortedTable
        data={users}
        columns={columns}
        onSelectionChange={setSelectedUsers}
        columnFilters={[searchFilter, selectedRolesFilter]}
      />
      <AssignRolesDrawer
        selectedUsers={selectedUsers}
        isOpen={isDrawerOpen}
        onClose={() => setDrawerOpen(false)}
      />
    </>
  )
}
