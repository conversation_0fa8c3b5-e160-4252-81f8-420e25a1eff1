import Link from 'next/link'
import { But<PERSON> } from '@/components/Button'
import { SortedTable } from '@/components/Table/SortedTable'
import { Role } from '../types'
interface RolesPanelProps {
  roles: Role[]
  addRole: (newRole: Role) => void
  updateRole: (updatedRole: Role) => void
  permissionNames: string[]
}

export function RolesPanel({
  roles,
  addRole,
  updateRole,
  permissionNames,
}: RolesPanelProps) {
  const columns = [
    {
      header: 'Role',
      accessorKey: 'name',
    },
    {
      header: 'Description',
      accessorKey: 'description',
    },
    {
      header: 'Number of Users',
      accessorKey: 'numberOfUsers',
    },
  ]
  const columnFilters: any[] = []

  return (
    <div className='flex-grow overflow-y-auto'>
      <div className='mb-5 flex w-full items-center justify-between leading-9'>
        <div className='text-lg font-semibold'>Roles</div>

        {/* TODO: Role(s) Side Panel - AFIN-295 */}
        <Link href='/'>
          <Button className='btn-primary text-white' disabled>
            Add role
          </Button>
        </Link>
      </div>
      {/* TODO: Roles Table - AFIN-294 */}
      <SortedTable
        data={roles}
        columns={columns}
        columnFilters={columnFilters}
      />
    </div>
  )
}
