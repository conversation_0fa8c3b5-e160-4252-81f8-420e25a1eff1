import React from 'react'
import { <PERSON>er, Drawer<PERSON>ontent, DrawerFooter } from '@/components/Drawer'
import { XMarkIcon } from '@heroicons/react/24/outline'
import { User, Role } from '../types'
import { Button } from '@/components/Button'
import { SearchBar } from '@/components/SearchBar'
interface AssignRolesDrawerProps {
  isOpen: boolean
  selectedUsers: User[]
  onClose: () => void
}
export function AssignRolesDrawer({
  selectedUsers,
  isOpen,
  onClose,
}: AssignRolesDrawerProps) {
  return (
    <Drawer isOpen={isOpen} onClose={onClose}>
      <DrawerContent>
        <div className='flex items-center justify-between'>
          <div className='text-2xl font-medium'>
            {selectedUsers.length === 1 ?
              selectedUsers[0].name
            : `Assign roles to ${selectedUsers.length} users`}
          </div>
          <Button className='border-0 px-0' onClick={onClose}>
            <XMarkIcon className='h-5 w-5' />
          </Button>
        </div>
        <div className='mb-2 mt-6 text-lg'>Roles</div>

        {/* TODO: AFIN-291 - search and add roles*/}
        <SearchBar
          defaultLabel='Search and add roles'
          onValueChange={() => {}}
        />
        <div className='border-b-[1px] border-gray-300 pb-6 text-sm'>
          todo: Roles to add
        </div>

        <div className='mt-6 text-lg'>Permissions</div>

        {/* TODO: AFIN-292 & AFIN-293 - Permissions Table */}
        <div className='mt-3 text-sm font-semibold'>Configuration</div>
      </DrawerContent>

      <DrawerFooter>
        <Button onClick={onClose}>Cancel</Button>
        <Button onClick={() => {}} className='btn-primary text-white'>
          Save
        </Button>
      </DrawerFooter>
    </Drawer>
  )
}
