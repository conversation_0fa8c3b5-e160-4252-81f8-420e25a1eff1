'use client'
import { useState } from 'react'
import { UsersPanel } from './_components/UsersPanel'
import { RolesPanel } from './_components/RolesPanel'
import { TabGroup, TabList, TabPanel, TabPanels, Tab } from '@headlessui/react'
import { User, Role } from './types'

// Mock Data
import { mockUsers, mockRoles, mockPermissionNames } from './mockData'

export default function UsersAndRoles() {
  // TODO: replace mock data with api calls and Tanstack Query
  const [users, setUsers] = useState<User[]>(mockUsers)
  const [roles, setRoles] = useState<Role[]>(mockRoles)

  // update multiple users, which only adds role(s)
  // update single user, which adds or removes role(s)
  const updateUsers = (userIds: string[], roleIds: string[]): void => {
    setUsers((prevUsers) =>
      prevUsers.map((user) =>
        userIds.includes(user.id) ?
          userIds.length > 1 ?
            { ...user, roles: [...new Set([...user.roles, ...roleIds])] }
          : { ...user, roles: roleIds }
        : user,
      ),
    )
  }
  // create new role
  const addRole = (newRole: Role): void => {
    setRoles([...roles, newRole])
  }

  // edit existing role
  const updateRole = (updatedRole: Role): void => {
    setRoles(
      roles.map((role) =>
        role.id === updatedRole.id ? { ...role, ...updatedRole } : role,
      ),
    )
  }
  return (
    <div className='flex w-full flex-auto flex-col p-8'>
      <div className='text-lg font-semibold leading-[30px]'>
        Users and roles
      </div>
      <div className='my-1.5 text-sm leading-7 text-zinc-500'>
        Manage users, roles, and permissions.
      </div>

      <TabGroup className='flex min-h-0 flex-auto flex-col'>
        <TabList className='mb-6 flex flex-row gap-4 border-b-[1px] border-gray-300'>
          {['Users', 'Roles'].map((tabLabel) => (
            <Tab
              key={tabLabel}
              className='rounded-sm text-sm font-semibold leading-[45px] text-app-color-secondary decoration-2 underline-offset-[17px] focus:outline-none data-[selected]:text-app-color-fg-brand-primary data-[hover]:underline data-[selected]:underline data-[focus]:outline-2 data-[focus]:outline-black'
            >
              {tabLabel}
            </Tab>
          ))}
        </TabList>
        <TabPanels className='flex min-h-0 w-full flex-auto flex-grow overflow-hidden overflow-y-scroll rounded-[10px] border bg-white p-6'>
          <TabPanel className='flex-auto'>
            <UsersPanel
              users={users}
              roles={roles}
              updateUsers={updateUsers}
              permissionNames={mockPermissionNames}
            />
          </TabPanel>
          <TabPanel className='flex-auto'>
            <RolesPanel
              roles={roles}
              addRole={addRole}
              updateRole={updateRole}
              permissionNames={mockPermissionNames}
            />
          </TabPanel>
        </TabPanels>
      </TabGroup>
    </div>
  )
}
