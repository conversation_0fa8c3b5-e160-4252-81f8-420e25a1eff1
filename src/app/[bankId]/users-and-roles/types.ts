export type PermissionActions = {
  view: boolean
  add: boolean
  edit: boolean
  delete: boolean
}

export interface PermissionDefinition extends PermissionActions {
  name: string
}

export interface User {
  id: string
  name: string
  roles: string[]
  email: string
  created: string
  lastUpdated: string
  lastSignIn: string
}
export interface Role {
  id: string
  name: string
  description: string
  numberOfUsers: number
  permissions: PermissionDefinition[]
}
