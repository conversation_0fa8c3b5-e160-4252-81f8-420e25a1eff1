import { CostTypes } from './_components/costTypeOptions'
import { ServiceWithCategory } from './_components/SelectServiceSetModal'
import { ServicePriceSchema } from './_components/ServicePrice/servicePriceSchema'
import { ApplyServiceTo } from './_components/ApplyServiceTo'
import {
  Disposition,
  PriceType,
  ServiceType,
  SubPriceType,
} from '@/api/zodSchemas'
import { CatalogCategory, CatalogService } from './_hooks/useServiceCatalog'

/**
 * Services catalog
 */

export interface Category {
  code: string
  type: 'category'
  name: string
  serviceCodes: string[]
  categoryCodes: string[]
  parentCode?: string
}

export interface CategoryWithContents extends CatalogCategory {
  services: CatalogService[]
  categories: CategoryWithContents[]
}

export interface ServiceSummary {
  code: string
  type: 'service'
  name: string
  priceType: string
  serviceType: string
  parentCode: string
  effectiveDate: string
  isExpired: boolean
}

export type CatalogItem = Category | ServiceSummary

export type GetCatalogResponse = {
  serviceByCode: Record<string, ServiceSummary>
  categoryByCode: Record<string, Category>
}

/**
 * Services
 */

export interface Service {
  id: string
  code: string
  type: 'service'
  name: string
  parentCode?: string
  effectiveDate: string
  expirationDate?: string
  serviceType: ServiceType
  subjectToDiscountOrPremium?: boolean
  displayAddendaInformation?: boolean
  price: PriceDetails
  afpCode?: { domesticCode: string; globalCode: string }
  internalNote?: string
  hasAcceptedTransactions?: boolean
  isExpired: boolean
  balanceType: string | null
  serviceSet: ServiceWithCategory[]
  priceTiers: ServicePriceSchema[]
  applyServiceTo: ApplyServiceTo | null
  units: string | null
  feeCycle: string | null
  indexRate: string | null
  basisDays: number | null
  subPriceType: SubPriceType | null
  balanceDivisor: string | null
}

export type CostDetails = { type: CostTypes; cost: string | null }

export type PriceDetails = {
  type: PriceType
  price: string | null
  baseFee: string | null
  disposition: Disposition
  cost: CostDetails
  minFee: string | null
  maxFee: string | null
}

export interface TimelinePeriod {
  code: string
  effectiveDate: string
  expirationDate?: string
  isExpired: boolean
}

export type PriceListsItem = {
  name: string
  id: string
  lead: string
  type: string
  price: string
  disposition: string
}

export type PromotionsItem = {
  name: string
  id: string
  expirationDate: string
  numberOfAccounts: string
  priceType: string
  price: string
  disposition: string
}

export type AccountOverridesItem = {
  name: string
  number: string
  expirationDate: string
  priceType: string
  price: string
  disposition: string
}
