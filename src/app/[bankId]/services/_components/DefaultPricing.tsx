import { useFormTextInput } from '@/components/Form/useFormTextInput'
import { UpdateServiceFormState } from '../[effectiveDate]/(service)/UpdateServiceForm'
import { PriceType } from './PriceType'
import { useFormSelect } from '@/components/Form/useFormSelect'
import { ReactFormExtendedApi, useStore } from '@tanstack/react-form'
import { If } from '@/components/If'
import { CostType } from './CostType'
import { ApplyService } from './ApplyService'
import { FeeCycleSelect } from './FeeCycleSelect'
import {
  InfoSection,
  InfoSectionTitle,
  InfoSectionDescription,
} from '@/components/InfoSection'
import { IndexRateSelect } from './IndexRateSelect'
import { EditPricingTiers, TiersFormState } from './EditPricingTiers'
import {
  getSupportedSubPriceTypes,
  servicePriceSchema,
} from './ServicePrice/servicePriceSchema'
import {
  disposition,
  dispositionLabel,
  percentage,
  priceTypeLabel,
} from '@/api/zodSchemas'
import { includes } from '@/lib/functional/includes'
import { nullableRequired } from '@/lib/validation/nullableRequiredNativeEnum'
import { TextInput } from '@/components/Input/TextInput'
import { useQuery } from '@tanstack/react-query'
import { query } from '../queries'

interface DefaultPricingProps {
  form: ReactFormExtendedApi<UpdateServiceFormState>
}

export function DefaultPricing({ form }: DefaultPricingProps) {
  const TextInputForm = useFormTextInput<UpdateServiceFormState>({ form })
  const Select = useFormSelect<UpdateServiceFormState>({ form })

  const [
    serviceType,
    priceType,
    subPriceType,
    indexRate,
    basisDays,
    effectiveDate,
  ] = useStore(form.store, ({ values }) => [
    values.service.serviceType,
    values.servicePrice.priceType,
    values.servicePrice.tierPriceType,
    values.servicePrice.indexRateCode,
    values.servicePrice.basisDays,
    values.service.effectiveDate,
  ])

  const indexRateData = useQuery({
    ...query('/listIndexRatesByEffectiveDate', { effectiveDate }),
    select(data) {
      return data.find((rate) => rate.code === indexRate)
    },
  })

  return (
    <InfoSection>
      <InfoSectionTitle>Default pricing</InfoSectionTitle>
      <InfoSectionDescription>
        Set the default pricing for this service.
      </InfoSectionDescription>
      <div className='flex gap-9'>
        <PriceType form={form} />
        <If
          true={includes(priceType, ['UNIT_PRICED', 'FLAT_FEE', 'PERCENTAGE'])}
        >
          <TextInputForm
            invalidChars={['e', 'E']}
            type='number'
            label='Price'
            name='servicePrice.priceValue'
            prefix={priceType !== 'PERCENTAGE' ? '$' : undefined}
            suffix={priceType === 'PERCENTAGE' ? '%' : undefined}
            required
            validators={{
              onChange: nullableRequired(
                priceType === 'PERCENTAGE' ? percentage : (
                  servicePriceSchema.shape.priceValue
                ),
              ),
            }}
          />
        </If>

        <If
          true={
            includes(serviceType, [
              'BALANCE_BASED',
              'SERVICE_SET',
              'VOLUME_BASED',
            ]) && includes(priceType, ['PARTITIONED_TIER', 'THRESHOLD_TIER'])
          }
        >
          <Select
            renderSelected={priceTypeLabel}
            renderOption={priceTypeLabel}
            options={getSupportedSubPriceTypes(serviceType)}
            label='Price subtype'
            required
            name='servicePrice.tierPriceType'
          />
        </If>
        <If
          true={
            includes(priceType, ['NOT_PRICED', 'INDEXED']) || priceType == null
          }
        >
          <div className='flex-1'></div>
        </If>
      </div>
      <If
        true={
          serviceType === 'BALANCE_BASED' &&
          subPriceType === 'UNIT_PRICED' &&
          includes(priceType, ['PARTITIONED_TIER', 'THRESHOLD_TIER'])
        }
      >
        <div className='flex gap-9'>
          <TextInputForm
            className='flex-1'
            invalidChars={['e', 'E']}
            type='number'
            aria-label='Balance divisor'
            name='servicePrice.balanceDivisor'
            required
            validators={{
              onChange: nullableRequired(
                servicePriceSchema.shape.balanceDivisor,
              ),
            }}
          />
          <div className='flex-1'></div>
        </div>
      </If>
      <If true={priceType != undefined}>
        <If true={serviceType === 'RECURRING'}>
          <div className='flex gap-9'>
            <ApplyService form={form} />
          </div>
        </If>
        <If true={priceType === 'INDEXED' || subPriceType === 'INDEXED'}>
          <div className='flex gap-9'>
            <IndexRateSelect form={form} />
            <TextInput
              className='flex-1'
              label='Days in year'
              value={basisDays!}
              disabled={true}
              name='Days in year'
            />
          </div>
        </If>
        <If true={includes(priceType, ['PARTITIONED_TIER', 'THRESHOLD_TIER'])}>
          <div className='flex-1'>
            <EditPricingTiers
              // SAFETY: UpdateServiceFormState is explictly extended with TiersFormState
              // minimum required state for EditPricingTiers component is TiersFormState.
              form={form as unknown as ReactFormExtendedApi<TiersFormState>}
              serviceType={serviceType}
              subPriceType={subPriceType}
              indexRate={(indexRateData.data?.indexRate ?? 0).toString()}
            />
          </div>
        </If>
        <div className='flex gap-9'>
          <If true={includes(serviceType, ['VOLUME_BASED', 'SERVICE_SET'])}>
            <Select
              label='Disposition'
              name='servicePrice.disposition'
              required
              options={disposition.options}
              renderOption={dispositionLabel}
              renderSelected={dispositionLabel}
              tooltip='Disposition'
              validators={{
                onChange: nullableRequired(
                  servicePriceSchema.shape.disposition,
                ),
              }}
            />
            <div className='flex-1'>
              <If
                true={
                  priceType === 'UNIT_PRICED' ||
                  (includes(priceType, [
                    'PARTITIONED_TIER',
                    'THRESHOLD_TIER',
                  ]) &&
                    subPriceType === 'UNIT_PRICED')
                }
              >
                <TextInputForm
                  prefix='$'
                  aria-label='Base Fee'
                  name='servicePrice.baseFee'
                  invalidChars={['e', 'E']}
                  type='number'
                  validators={{
                    onChange: servicePriceSchema.shape.baseFee,
                  }}
                />
              </If>
            </div>
          </If>
          <If true={includes(serviceType, ['RECURRING', 'BALANCE_BASED'])}>
            <FeeCycleSelect form={form} />
            <Select
              label='Disposition'
              name='servicePrice.disposition'
              required
              options={disposition.options}
              renderSelected={dispositionLabel}
              renderOption={dispositionLabel}
              tooltip='Disposition'
              validators={{
                onChange: nullableRequired(
                  servicePriceSchema.shape.disposition,
                ),
              }}
            />
          </If>
        </div>
        <div className='flex gap-9'>
          <CostType form={form} />
        </div>
        <If true={priceType !== 'NOT_PRICED'}>
          <div className='flex gap-9'>
            <TextInputForm
              label='Minimum fee'
              prefix='$'
              name='servicePrice.minimumFee'
              invalidChars={['e', 'E']}
              type='number'
              validators={{
                onChange: servicePriceSchema.shape.minimumFee,
              }}
            />
            <TextInputForm
              label='Maximum fee'
              prefix='$'
              name='servicePrice.maximumFee'
              invalidChars={['e', 'E']}
              type='number'
              validators={{
                onChangeListenTo: ['servicePrice.minimumFee'],
                onChange: servicePriceSchema.shape.maximumFee
                  .nullable()
                  .refine(() => {
                    const { maximumFee, minimumFee } =
                      form.state.values.servicePrice

                    if (maximumFee != null && minimumFee != null) {
                      return parseFloat(minimumFee) < parseFloat(maximumFee)
                    }

                    return true
                  }, 'Minimum fee should be less than Maximum fee.'),
              }}
            />
          </div>
        </If>
      </If>
    </InfoSection>
  )
}
