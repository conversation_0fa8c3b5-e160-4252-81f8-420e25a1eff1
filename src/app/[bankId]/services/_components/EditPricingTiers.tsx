import { useFormTextInput } from '@/components/Form/useFormTextInput'
import { ReactFormExtendedApi, useField } from '@tanstack/react-form'
import {
  servicePriceDefaultValues,
  servicePriceSchema,
} from './ServicePrice/servicePriceSchema'
import { ColumnDef } from '@tanstack/react-table'
import { useMemo } from 'react'
import { z } from 'zod'
import { defaultMeta } from '@/components/Form/defaultMeta'
import { Button } from '@/components/Button'
import PlusIcon from '@heroicons/react/24/outline/PlusIcon'
import { SortedTable } from '@/components/Table/SortedTable'
import TrashIcon from '@heroicons/react/24/outline/TrashIcon'
import { percentage, ServiceType, SubPriceType } from '@/api/zodSchemas'
import { nullableRequired } from '@/lib/validation/nullableRequiredNativeEnum'
import { ServicePriceTier } from '../[effectiveDate]/(service)/UpdateServiceForm'

export type TiersFormState = {
  pricingTiers: ServicePriceTier[]
}

export interface EditPricingTiersProps {
  form: ReactFormExtendedApi<TiersFormState>
  serviceType: ServiceType
  subPriceType: SubPriceType | 'INDEX_RATE' | 'MANUAL' | null
  indexRate: string | null
}

const maxTierValue = '999999999'
const minTierValue = '0'

export const tiersSchema = z
  .array(z.any())
  .min(1, 'Must contain at least one tier')

export function EditPricingTiers({
  form,
  serviceType,
  subPriceType,
  indexRate,
}: EditPricingTiersProps) {
  const TextInput = useFormTextInput<TiersFormState>({ form })

  const {
    state: { value: priceTiers, meta },
  } = useField<TiersFormState, 'pricingTiers'>({
    form,
    name: 'pricingTiers',
    validators: { onChange: tiersSchema },
  })

  const tierColumns = useMemo(() => {
    const columns: ColumnDef<ServicePriceTier>[] = []

    const rangeType = serviceType === 'BALANCE_BASED' ? 'Balance' : 'Volume'

    columns.push({
      id: 'range',
      cell: (context) => (
        <div className='mt-2 flex flex-1 items-baseline justify-end'>
          <span className='mr-2 min-w-32 text-right text-sm'>
            {context.row.original[`tierMin${rangeType}Inclusive`]} and
          </span>
          <TextInput
            inputClassName='text-right'
            name={`pricingTiers[${context.row.index}].tierMax${rangeType}Exclusive`}
            type='number'
            disabled={context.table.getRowCount() - 1 === context.row.index}
            validators={{
              onChange: z.string({ message: 'Required' }).refine((maxValue) => {
                const minValue = form.getFieldValue(
                  `pricingTiers[${context.row.index}].tierMin${rangeType}Inclusive`,
                )
                const maxValueNum = parseFloat(maxValue)
                const minValueNum = parseFloat(minValue ?? '0')

                return isNaN(minValueNum) || maxValueNum > minValueNum
              }, 'Should be greater than min tier value'),
            }}
            listeners={{
              onChange: ({ value }) => {
                form.setFieldValue(
                  `pricingTiers[${context.row.index + 1}].tierMin${rangeType}Inclusive`,
                  z.coerce
                    .number()
                    .transform((n) => n + 1)
                    .pipe(z.coerce.string())
                    .nullable()
                    .safeParse(value).data ?? null,
                )
              },
            }}
          />
        </div>
      ),
      header: `${rangeType} tier`,
      footer: () => (
        <Button
          className='m-2 flex items-center border-none text-sm font-semibold text-app-color-fg-brand-primary'
          onClick={() => {
            const tiers = [...form.getFieldValue('pricingTiers')]

            if (tiers.length > 0) {
              tiers[tiers.length - 1] = {
                ...tiers[tiers.length - 1],
                tierMaxBalanceExclusive: null,
                tierMaxVolumeExclusive: null,
              }
            }

            form.setFieldValue(
              'pricingTiers',
              setMinMaxEdgeValues(...tiers, servicePriceDefaultValues),
            )
          }}
        >
          <PlusIcon className='size-4' aria-disabled={true} /> Add tier
        </Button>
      ),
    })

    if (subPriceType === 'INDEXED' || subPriceType === 'INDEX_RATE') {
      columns.push(
        {
          id: 'indexAdjustments',
          cell: (context) => {
            return (
              <div className='mt-2 flex items-baseline'>
                <TextInput
                  className='mr-1'
                  inputClassName='text-right'
                  validators={{
                    onChange: servicePriceSchema.shape.indexAdjustment,
                  }}
                  name={`pricingTiers[${context.row.index}].indexAdjustment`}
                />
                %
              </div>
            )
          },
          header: 'Index adjustment',
        },
        {
          id: 'effectiveRate',
          cell: (ctx) => {
            return (
              <div className='flex h-10 text-sm'>
                {(
                  (tryParseFloat(indexRate) ?? 0) +
                  tryParseFloat(ctx.row.original.indexAdjustment)
                ).toFixed(2)}
                %
              </div>
            )
          },
          header: 'Effective rate',
        },
      )
    } else if (subPriceType === 'MANUAL') {
      columns.push({
        id: 'indexAdjustments',
        cell: (context) => {
          return (
            <div className='mt-2 flex items-baseline'>
              <TextInput
                className='mr-1'
                inputClassName='text-right'
                validators={{
                  onChange: servicePriceSchema.shape.indexAdjustment,
                }}
                name={`pricingTiers[${context.row.index}].priceValue`}
              />
              %
            </div>
          )
        },
        header: 'Index rate',
      })
    } else {
      columns.push({
        id: 'priceValue',
        cell: (context) => {
          return (
            <div className='mt-2 flex items-baseline'>
              <TextInput
                prefix={subPriceType === 'PERCENTAGE' ? '' : '$'}
                suffix={subPriceType === 'PERCENTAGE' ? '%' : ''}
                className='min-w-60 max-w-96'
                name={`pricingTiers[${context.row.index}].priceValue`}
                type='number'
                invalidChars={['E', 'e']}
                validators={{
                  onChange: nullableRequired(
                    subPriceType === 'PERCENTAGE' ? percentage : (
                      servicePriceSchema.shape.priceValue
                    ),
                  ),
                }}
              />
              <span className='ml-2 text-sm'>
                {subPriceType === 'UNIT_PRICED' ? 'per unit' : 'flat fee'}
              </span>
            </div>
          )
        },
        header: 'Price',
      })
    }

    columns.push({
      id: 'remove',
      cell: (context) => (
        <div
          className='mr-5 p-2'
          onClick={() => {
            const tiers = form.getFieldValue('pricingTiers')

            form.setFieldMeta(
              `pricingTiers[${context.row.index}].priceValue`,
              defaultMeta,
            )

            const newTiers = [...tiers]
            newTiers.splice(context.row.index, 1)

            form.setFieldValue('pricingTiers', setMinMaxEdgeValues(...newTiers))
          }}
        >
          <TrashIcon className='size-4' aria-label='Remove tier' />
        </div>
      ),
      enableSorting: false,
      meta: {
        className: `min-w-[60px] flex justify-end`,
      },
    })

    return columns
  }, [TextInput, form, indexRate, serviceType, subPriceType])

  return (
    <>
      <SortedTable
        estimatedRowHeight={90}
        data={priceTiers}
        columns={tierColumns}
        columnFilters={[]}
      />
      <span className='min-h-5 text-sm text-app-color-button-primary-error-bg'>
        {meta.errors.join(',')}
      </span>
    </>
  )
}

function setMinMaxEdgeValues(...tiers: ServicePriceTier[]) {
  if (tiers.length < 1) {
    return tiers
  }

  const updatedTiers = [...tiers]

  updatedTiers[0] = {
    ...updatedTiers[0],
    tierMinBalanceInclusive: minTierValue,
    tierMinVolumeInclusive: minTierValue,
  }

  updatedTiers[updatedTiers.length - 1] = {
    ...updatedTiers[updatedTiers.length - 1],
    tierMaxBalanceExclusive: maxTierValue,
    tierMaxVolumeExclusive: maxTierValue,
  }

  return updatedTiers
}

export function tryParseFloat(
  value: string | null,
  defaultValue: number = 0,
): number {
  if (value == null) {
    return defaultValue
  }

  const parsedValue = parseFloat(value)

  return isNaN(parsedValue) ? defaultValue : parsedValue
}
