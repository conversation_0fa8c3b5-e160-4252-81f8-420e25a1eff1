import { useFormSelect } from '../../../../components/Form/useFormSelect'
import { useFormTextInput } from '@/components/Form/useFormTextInput'
import {
  UpdateServiceFormApi,
  UpdateServiceFormState,
} from '../[effectiveDate]/(service)/UpdateServiceForm'
import { costType, costTypeLabel } from '@/api/zodSchemas'
import { servicePriceSchema } from './ServicePrice/servicePriceSchema'
import { includes } from '@/lib/functional/includes'
import { nullableRequired } from '@/lib/validation/nullableRequiredNativeEnum'

export function CostType({ form }: { form: UpdateServiceFormApi }) {
  const FormInput = useFormTextInput<UpdateServiceFormState>({ form })
  const Select = useFormSelect<UpdateServiceFormState>({ form })

  return (
    <>
      <Select
        label='Cost Type'
        name='servicePrice.costType'
        renderSelected={costTypeLabel}
        renderOption={costTypeLabel}
        options={costType.options}
        validators={{
          onChange: servicePriceSchema.shape.costType,
        }}
      />
      <form.Subscribe selector={(state) => state.values.servicePrice.costType}>
        {(costType) =>
          includes(costType, ['FLAT_COST', 'UNIT_COST']) ?
            <FormInput
              label='Cost'
              prefix='$'
              name={'servicePrice.costValue'}
              invalidChars={['e', 'E']}
              type='number'
              required
              validators={{
                onChange: nullableRequired(servicePriceSchema.shape.costValue),
              }}
            />
          : <div className='flex-1'></div>
        }
      </form.Subscribe>
    </>
  )
}
