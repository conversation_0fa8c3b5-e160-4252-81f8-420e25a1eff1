import { useFormSelect } from '@/components/Form/useFormSelect'
import {
  UpdateServiceFormApi,
  UpdateServiceFormState,
} from '../[effectiveDate]/(service)/UpdateServiceForm'
import { SelectOption } from '@/components/Input/Select'
import { servicePriceSchema } from './ServicePrice/servicePriceSchema'
import { nullableRequired } from '@/lib/validation/nullableRequiredNativeEnum'
import { query } from '../queries'
import { useQuery } from '@tanstack/react-query'
import { useStore } from '@tanstack/react-form'
import { monthNameToShort } from '@/lib/date'
import { unary } from '@/lib/functional/unary'

interface FeeCycleProps {
  form: UpdateServiceFormApi
  className?: string
}

export function FeeCycleSelect({ form, className }: FeeCycleProps) {
  const Select = useFormSelect<UpdateServiceFormState>({ form })

  const [effectiveDate, cycleDefinitionCode] = useStore(form.store, (state) => [
    state.values.service.effectiveDate,
    state.values.servicePrice.cycleDefinitionCode,
  ])

  const cycleDefinitionResults = useQuery(
    query('/getCycleDefinitions', { effectiveDate }),
  )

  if (cycleDefinitionResults.isError) {
    throw cycleDefinitionResults.error
  }

  if (!cycleDefinitionResults.isSuccess) {
    return 'Loading...'
  }

  const selectedCycle = cycleDefinitionResults.data.find(
    (cycle) => cycle.code === cycleDefinitionCode,
  )

  return (
    <Select
      className={className}
      label='Fee cycle'
      name='servicePrice.cycleDefinitionCode'
      required
      renderSelected={() =>
        selectedCycle ?
          <>
            <div>
              {selectedCycle.code} - {selectedCycle.description}
            </div>
            <div className='text-xs text-app-color-text-secondary'>
              {selectedCycle.includedMonths
                ?.map(unary(monthNameToShort))
                .join(', ')}
            </div>
          </>
        : null
      }
      validators={{
        onChange: nullableRequired(
          servicePriceSchema.shape.cycleDefinitionCode,
        ),
      }}
    >
      {cycleDefinitionResults.data.map((cycle) => (
        <SelectOption key={cycle.code} value={cycle.code}>
          <div>
            {cycle.code} - {cycle.description}
          </div>
          <div className='text-xs text-app-color-text-secondary'>
            {cycle.includedMonths?.map(unary(monthNameToShort)).join(', ')}
          </div>
        </SelectOption>
      ))}
    </Select>
  )
}
