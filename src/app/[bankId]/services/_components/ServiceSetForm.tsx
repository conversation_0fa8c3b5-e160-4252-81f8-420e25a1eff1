import { z } from 'zod'
import {
  UpdateServiceFormApi,
  UpdateServiceFormState,
} from '../[effectiveDate]/(service)/UpdateServiceForm'
import { useField, useStore } from '@tanstack/react-form'
import { ServiceSet } from './ServiceSet'
import { If } from '@/components/If'
import { useEffect } from 'react'

interface ServiceSetFormProps {
  form: UpdateServiceFormApi
}

const serviceSetFormSchema = z
  .array(z.any())
  .nonempty('Must contain at least 1 element')

export function ServiceSetForm({ form }: ServiceSetFormProps) {
  const {
    values: {
      service: { effectiveDate, serviceType },
    },
  } = useStore(form.store)

  const {
    state: { value: selectedServices, meta },
    setValue,
    setMeta,
  } = useField<UpdateServiceFormState, 'servicesInServiceSet'>({
    form,
    name: 'servicesInServiceSet',
    validators: {
      onChange:
        serviceType === 'SERVICE_SET' ? serviceSetFormSchema : undefined,
    },
  })

  useEffect(() => {
    return () => {
      setMeta((prev) => ({ ...prev, errors: [], errorMap: {} }))
    }
  }, [setMeta])

  return (
    <If true={serviceType === 'SERVICE_SET'}>
      <ServiceSet
        selectedServices={selectedServices}
        effectiveDate={effectiveDate}
        addService={(selected) => {
          const uniqueServices = new Map([
            ...selectedServices.map((s) => [s.code, s] as const),
            ...selected.map((s) => [s.code, s] as const),
          ])
          setValue([...uniqueServices.values()])
        }}
        removeService={(serviceToRemove) => {
          setValue(
            selectedServices.filter((s) => s.code != serviceToRemove.code),
          )
        }}
        error={meta.errors.join(',')}
      />
    </If>
  )
}
