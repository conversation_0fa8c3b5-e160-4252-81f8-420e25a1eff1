'use client'

import { validPriceTypeOrNull } from '@/app/types'
import { Checkbox } from '@/components/Checkbox'
import { SelectOption } from '@/components/Input/Select'
import { CheckIcon, QuestionMarkCircleIcon } from '@heroicons/react/24/outline'
import { Tooltip } from '@/components/Tooltip'
import { useFormSelect } from '../../../../components/Form/useFormSelect'
import { useFormTextInput } from '@/components/Form/useFormTextInput'
import { InfoSection, InfoSectionTitle } from '@/components/InfoSection'
import { CategoryWithContents } from '../types'
import {
  serviceSchema,
  UpdateServiceFormApi,
  UpdateServiceFormState,
} from '../[effectiveDate]/(service)/UpdateServiceForm'
import { z } from 'zod'
import {
  balanceType,
  baseBalanceTypeLabel,
  code,
  name,
  serviceTypeLabel,
  serviceType as serviceTypeSchema,
} from '@/api/zodSchemas'
import { useStore } from '@tanstack/react-form'
import {
  servicePriceSchema,
  supportedPriceSubTypeOrNull,
} from './ServicePrice/servicePriceSchema'
import { If } from '@/components/If'
import { nullableRequired } from '@/lib/validation/nullableRequiredNativeEnum'
import { clsx } from 'clsx'
import { includes } from '@/lib/functional/includes'
import { CatalogCategory, useServiceCatalog } from '../_hooks/useServiceCatalog'
import { formatToServerString } from '@/lib/date'
import { MonthPicker } from '@/components/Filter/MonthPicker'
import { query } from '../queries'
import { useQuery } from '@tanstack/react-query'
import { isNonNull } from '@/lib/guards/isNonNull'
import { useRoute } from '../routing'
import { unwrap } from '@/lib/unions/unwrap'

interface ServiceInformationProps {
  serviceCategories: CategoryWithContents[]
  form: UpdateServiceFormApi
}

const serviceInformationSchema = z.object({
  effectiveDate: z.date(),
  expirationDate: z.date(),
  hasExpirationDate: z.boolean(),
  serviceCategory: nullableRequired(z.custom<CatalogCategory>()),
  name,
  code: code(),
  serviceType: serviceTypeSchema,
  subjectToDiscountOrPremium: z.boolean(),
  displayAddendaInformation: z.boolean(),
  hasAcceptedTransactions: z.boolean(),
  balanceType: nullableRequired(z.string()),
})

export type ServiceInformationState = z.infer<typeof serviceInformationSchema>

function renderServiceCategoryOption(category: CategoryWithContents) {
  return (
    <div key={category.current.code}>
      <SelectOption value={category.current.code} className='relative text-sm'>
        <span className='block truncate'>{category.current.name}</span>
        <span
          className={clsx(
            'absolute inset-y-0 right-0 flex items-center pr-4 text-indigo-600',
            'group-[&:not([data-selected])]:hidden group-data-[focus]:text-white',
          )}
        >
          <CheckIcon aria-hidden='true' className='size-5' />
        </span>
      </SelectOption>
      <div className='pl-8'>
        {category.categories.map(renderServiceCategoryOption)}
      </div>
    </div>
  )
}

export function ServiceInformation({
  serviceCategories,
  form,
}: ServiceInformationProps) {
  const FormInput = useFormTextInput<UpdateServiceFormState>({ form })
  const Select = useFormSelect<UpdateServiceFormState>({ form })

  const isDuplicateMode = isNonNull(
    unwrap(useRoute(), '/services/[effectiveDate]/duplicate/[serviceCode]'),
  )

  const isEditMode = isNonNull(
    unwrap(useRoute(), '/services/[effectiveDate]/edit/[serviceCode]'),
  )

  const [serviceType, effectiveDate, code] = useStore(
    form.store,
    ({ values: { service } }) => [
      service.serviceType,
      service.effectiveDate,
      service.code,
    ],
  )

  const verifyCode = useQuery({
    ...query('/getServiceTimeline', { code: code! }),
    enabled: false,
    //no need to keep verification results in a cache
    gcTime: 0,
  })

  const catalog = useServiceCatalog({
    effectiveDate: effectiveDate!,
  })
  return (
    <InfoSection>
      <InfoSectionTitle>System Information</InfoSectionTitle>
      <div className='flex gap-9'>
        <div className='flex flex-1 flex-col gap-1'>
          <label
            htmlFor={`date-picker-${name}`}
            className='text-sm font-medium'
          >
            Effective date
          </label>
          <MonthPicker
            className='max-h-10 min-h-10 flex-1 bg-white ring-1 ring-inset ring-gray-300 hover:bg-white'
            showIcon
            onDateChange={(date) => {
              form.setFieldValue(
                'service.effectiveDate',
                formatToServerString(date),
              )
            }}
          />
        </div>
        <div className='flex flex-1 flex-col gap-1'></div>
      </div>
      <div className='flex gap-9'>
        <Select
          label='Service category'
          name='serviceCategoryCode'
          renderSelected={(value) =>
            catalog.data?.categoryByCode[value]?.current.name ?? 'Not selected'
          }
        >
          <SelectOption
            className='text-sm text-app-color-secondary'
            value={null}
          >
            No category
          </SelectOption>
          {serviceCategories.map(renderServiceCategoryOption)}
        </Select>
        <div className='flex-1'></div>
      </div>
      <div className='flex gap-9'>
        <FormInput
          name='service.description'
          label='Service name'
          placeholder='3 to 50 characters'
          required
          validators={{
            onChange: serviceSchema.shape.description,
          }}
        />
        <FormInput
          tooltip='A bank-specific internal code unique to each service.'
          name='service.code'
          label='Service code'
          placeholder='3 to 10 characters'
          required
          readonly={isEditMode}
          validators={{
            onChangeAsyncDebounceMs: 500,
            onChangeAsync: async () => {
              if (isEditMode) {
                return null
              }

              const result = await verifyCode.refetch()

              if (result.isSuccess) {
                return result.data.length > 0 ? 'Code is already exists.' : null
              } else {
                return 'Cannot verify the code.'
              }
            },
            onChange: serviceSchema.shape.code,
          }}
        />
      </div>
      <div className='flex gap-9'>
        <Select
          className='disabled:text-cyan-300'
          name='service.serviceType'
          label='Service type'
          renderSelected={serviceTypeLabel}
          renderOption={serviceTypeLabel}
          disabled={isEditMode || isDuplicateMode}
          // PRE_PRICED service type is not supported
          options={serviceTypeSchema.options.filter(
            (type) => type !== 'PRE_PRICED',
          )}
          onChange={(value, form) => {
            const currentPriceType = form.getFieldValue(
              'servicePrice.priceType',
            )
            const currentSubPriceType = form.getFieldValue(
              'servicePrice.tierPriceType',
            )
            if (value) {
              form.setFieldValue(
                'servicePrice.priceType',
                validPriceTypeOrNull(value, currentPriceType),
              )
              form.setFieldValue(
                'servicePrice.tierPriceType',
                supportedPriceSubTypeOrNull(value, currentSubPriceType),
              )
            }
          }}
          required
          tooltip='Service type'
        />
        <div className='flex-1'>
          <If true={serviceType === 'BALANCE_BASED'}>
            <Select
              name='servicePrice.balanceType'
              label='Balance type'
              required
              renderSelected={baseBalanceTypeLabel}
              renderOption={baseBalanceTypeLabel}
              options={balanceType.options}
              validators={{
                onChange: nullableRequired(
                  servicePriceSchema.shape.balanceType,
                ),
              }}
            />
          </If>
        </div>
      </div>
      <form.Subscribe selector={(state) => state.values.service.serviceType}>
        {(value) => (
          <>
            <If true={includes(value, ['BALANCE_BASED', 'SERVICE_SET'])}>
              <div className='flex gap-9'>
                <div className='flex flex-1 gap-2'>
                  <form.Field name='servicePrice.subjectToDiscountOrPremium'>
                    {(field) => (
                      <Checkbox
                        label='Subject to discount or premium'
                        checked={field.state.value ?? undefined}
                        onChange={field.handleChange}
                      />
                    )}
                  </form.Field>
                  <Tooltip
                    className='size-4 self-center'
                    topOffset={8}
                    content='Subject to discount or premium'
                  >
                    <QuestionMarkCircleIcon />
                  </Tooltip>
                </div>
                <div className='flex flex-1 gap-2'>
                  <If true={includes(serviceType, ['VOLUME_BASED'])}>
                    <form.Field name='servicePrice.includeReferenceInformationOnStatements'>
                      {(field) => (
                        <Checkbox
                          label='Display addenda information on analysis statements'
                          checked={field.state.value ?? undefined}
                          onChange={field.handleChange}
                        />
                      )}
                    </form.Field>
                    <Tooltip
                      className='size-4 self-center'
                      topOffset={8}
                      content='Display addenda information on analysis statements'
                    >
                      <QuestionMarkCircleIcon />
                    </Tooltip>
                  </If>
                </div>
              </div>
            </If>
          </>
        )}
      </form.Subscribe>
    </InfoSection>
  )
}
