// ToDo: fetch the balance types from the configuration endpoint
export const BalanceType: Record<string, string> = {
  AverageCollected: 'Average Collected',
  AverageLedger: 'Average Ledger',
  AverageNegative_Collected: 'Average Negative Collected',
  AverageNegative_Ledger: 'Average Negative Ledger',
  AveragePositive_Collected: 'Average Positive Collected',
  AveragePositive_Ledger: 'Average Positive Ledger',
  AverageUncollected_Funds: 'Average Uncollected Funds',
  CompensatingBalance: 'Compensating Balance',
  EndOf_Month_Ledger: 'End of Month Ledger',
  InvestableBalance: 'Investable Balance',
  AverageFloat: 'Average Float',
  AverageClearinghouseFloat: 'Average Clearinghouse Float',
}
