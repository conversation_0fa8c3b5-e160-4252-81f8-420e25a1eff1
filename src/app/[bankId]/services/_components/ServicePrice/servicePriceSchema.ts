import { components } from '@/api/schema'
import {
  applyServiceTo,
  balanceType,
  basisDays,
  code,
  costType,
  currency,
  disposition,
  fee,
  int,
  priceType,
  pricingHierarchyEntryType,
  ServiceType,
  stringToNumber,
  subPriceType,
  SubPriceType,
} from '@/api/zodSchemas'
import { startOfTheMonth, toServerFormat } from '@/lib/date'
import { hasMaxDecimalPlaces } from '@/lib/validation/hasMaxDecimalPlaces'
import { nullableRequired } from '@/lib/validation/nullableRequiredNativeEnum'
import { z } from 'zod'

export function getSupportedSubPriceTypes(
  serviceType: ServiceType,
): SubPriceType[] {
  switch (serviceType) {
    case 'SERVICE_SET':
    case 'VOLUME_BASED': {
      return ['UNIT_PRICED', 'FLAT_FEE']
    }
    case 'BALANCE_BASED': {
      return subPriceType.options
    }
    default: {
      return []
    }
  }
}

export function supportedPriceSubTypeOrNull(
  serviceType: ServiceType,
  priceType: SubPriceType | null,
) {
  const supportedSubTypes = getSupportedSubPriceTypes(serviceType)

  if (priceType && supportedSubTypes.includes(priceType)) {
    return priceType
  }

  return supportedSubTypes.length > 0 ? supportedSubTypes[0] : null
}

/**
 * From https://bitbucket.fis.dev/projects/AFIN/repos/af_fpb/browse/fpb/src/main/java/com/fisglobal/af/fpb/services/catalog/serviceprice/ServicePriceEntity.java
 * @deprecated TODO use `formValidators.servicePrice` instead
 */
export const servicePriceSchema = z.object({
  serviceCode: code().nullable(),
  applyServiceTo: applyServiceTo.nullable(),
  balanceType: balanceType.nullable(),
  baseFee: fee.pipe(z.coerce.number().nullable()),
  basisDays: basisDays.nullable(),
  costType: costType.nullable(),
  costValue: stringToNumber.nullable(),
  currency: currency,
  cycleDefinitionCode: z.string().nullable(),
  disposition: disposition.nullable(),
  effectiveDate: z.string().nullable(),
  includeReferenceInformationOnStatements: z.boolean().nullable(),
  indexAdjustment: z
    .string()
    .min(1)
    .superRefine(hasMaxDecimalPlaces(4))
    .pipe(z.coerce.number().min(0).max(100))
    .nullable(),
  indexMultiplier: stringToNumber.nullable(),
  indexRateCode: z.string().nullable(),
  maximumFee: fee,
  minimumFee: fee,
  priceType: nullableRequired(priceType),
  priceValue: z
    .string()
    .nullable()
    .superRefine(hasMaxDecimalPlaces(4))
    .pipe(z.coerce.number().gt(0).max(1_000_000_000).nullable()),
  pricingHierarchyEntryType: pricingHierarchyEntryType,
  pricingHierarchyEntryCode: z.string(),
  subjectToDiscountOrPremium: z.boolean().nullable(),
  tierMinVolumeInclusive: z
    .string()
    .nullable()
    .pipe(z.coerce.number().int().nullable()),
  tierMaxVolumeExclusive: z
    .string()
    .nullable()
    .pipe(z.coerce.number().int().nullable()),
  tierMinBalanceInclusive: z
    .string()
    .nullable()
    .pipe(z.coerce.number().nullable()),
  tierMaxBalanceExclusive: z
    .string()
    .nullable()
    .pipe(z.coerce.number().nullable()),
  tierNumber: z.number().int(),
  units: z.string().nullable().pipe(z.coerce.number().nullable()),
  tierPriceType: subPriceType.nullable().transform(
    // ToDo: could be removed when server `tierPriceType` will support PERCENTAGE value
    (v) => v as components['schemas']['ServicePrice']['tierPriceType'],
  ),
  balanceDivisor: int.min(1).max(1_000_000_000).nullable(),
})

export type ServicePriceSchema = z.input<typeof servicePriceSchema>
export type ServicePriceSchemaOutput = z.output<typeof servicePriceSchema>

export const servicePriceDefaultValues: ServicePriceSchema = {
  balanceDivisor: null,
  subjectToDiscountOrPremium: false,
  pricingHierarchyEntryCode: '',
  includeReferenceInformationOnStatements: false,
  effectiveDate: toServerFormat(startOfTheMonth()),
  disposition: null,
  costType: 'NO_COST',
  balanceType: null,
  applyServiceTo: null,
  priceType: null,
  serviceCode: null,
  pricingHierarchyEntryType: 'STANDARD',
  tierNumber: 0,
  priceValue: null,
  minimumFee: null,
  maximumFee: null,
  costValue: null,
  currency: 'USD',
  units: '1',
  cycleDefinitionCode: null,
  tierPriceType: null,
  tierMinVolumeInclusive: null,
  tierMaxVolumeExclusive: null,
  tierMinBalanceInclusive: null,
  tierMaxBalanceExclusive: null,
  indexRateCode: null,
  basisDays: '365',
  indexMultiplier: null,
  baseFee: null,
  indexAdjustment: null,
}
