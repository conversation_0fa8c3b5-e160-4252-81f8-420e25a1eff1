import {
  InfoSection,
  InfoSectionDescription,
  InfoSectionTitle,
} from '@/components/InfoSection'
import { ColumnDef } from '@tanstack/react-table'
import { SortedTable } from '@/components/Table/SortedTable'
import TrashIcon from '@heroicons/react/24/outline/TrashIcon'
import { SelectServiceSetModal } from './SelectServiceSetModal'
import { If } from '@/components/If'
import { startOfToday } from 'date-fns'
import { useMemo } from 'react'
import DocumentTextIcon from '@heroicons/react/24/outline/DocumentTextIcon'
import PlusIcon from '@heroicons/react/24/outline/PlusIcon'
import { ServiceType, serviceTypeLabel } from '@/api/zodSchemas'
import { components } from '@/api/schema'
import { startOfTheMonth, toServerFormat } from '@/lib/date'

interface ServiceSetProps {
  effectiveDate?: string
  selectedServices: components['schemas']['Service'][]
  removeService?: (serviceToRemove: components['schemas']['Service']) => void
  addService?: (serviceToAdd: components['schemas']['Service'][]) => void
  readonly?: boolean
  error?: string
}

export function ServiceSet({
  effectiveDate,
  selectedServices,
  removeService,
  addService,
  readonly,
  error,
}: ServiceSetProps) {
  const serviceSetColumns = useMemo(() => {
    const columns = [
      {
        id: 'name',
        accessorFn: (originalRow) => originalRow.description,
        header: () => 'Name',
        enableSorting: false,
        cell: ({ getValue }) => (
          <>
            <DocumentTextIcon className='mr-1 h-5 w-5 text-indigo-500' />
            <span>{getValue<string>() ?? '-'}</span>
          </>
        ),
      },
      {
        id: 'code',
        accessorFn: (originalRow) => originalRow.code,
        header: () => 'Code',
        enableSorting: false,
        meta: {
          className: 'min-w-[100px]',
        },
      },
      {
        id: 'serviceType',
        accessorFn: (originalRow) => originalRow.serviceType,
        header: () => 'Service type',
        cell: ({ getValue }) => serviceTypeLabel(getValue<ServiceType>()),
        enableSorting: false,
        meta: {
          className: 'min-w-[180px]',
        },
      },
      {
        id: 'categoryName',
        accessorFn: (rowData) => '--',
        header: () => 'Service category',
        enableSorting: false,
      },
      ,
    ] as ColumnDef<components['schemas']['Service']>[]

    if (!readonly) {
      columns.push({
        id: 'remove',
        cell: (context) => (
          <div
            className='cursor-pointer p-2'
            onClick={() => {
              if (removeService) {
                removeService(context.row.original)
              }
            }}
          >
            <TrashIcon className='size-4' />
          </div>
        ),
        enableSorting: false,
        meta: {
          className: `min-w-[100px] flex justify-center`,
        },
      })
    }

    return columns
  }, [readonly, removeService])

  return (
    <InfoSection className='max-h-[60vh]'>
      <InfoSectionTitle>Service set</InfoSectionTitle>
      <InfoSectionDescription className='flex items-center justify-between'>
        {selectedServices.length} service(s) selected.
        <If true={readonly !== true}>
          <SelectServiceSetModal
            effectiveDate={effectiveDate ?? toServerFormat(startOfTheMonth())}
            onServiceSetSelected={(selected) => {
              if (addService) {
                addService(selected.map((s) => s.current))
              }
            }}
          >
            <span className='inline-flex cursor-pointer items-center border-none text-sm font-semibold text-app-color-bg-brand-solid hover:underline'>
              <PlusIcon className='mr-1 inline size-4' aria-hidden='true' />
              Add service
            </span>
          </SelectServiceSetModal>
        </If>
      </InfoSectionDescription>
      <SortedTable
        data={selectedServices}
        columnFilters={[]}
        columns={serviceSetColumns}
      />
      <span className='min-h-5 text-sm text-app-color-button-primary-error-bg'>
        {error}
      </span>
    </InfoSection>
  )
}
