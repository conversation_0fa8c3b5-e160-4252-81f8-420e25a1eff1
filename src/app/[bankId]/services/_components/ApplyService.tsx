import { useFormSelect } from '../../../../components/Form/useFormSelect'
import { useFormTextInput } from '@/components/Form/useFormTextInput'
import {
  UpdateServiceFormApi,
  UpdateServiceFormState,
} from '../[effectiveDate]/(service)/UpdateServiceForm'
import { applyServiceTo, units } from '@/api/zodSchemas'
import { servicePriceSchema } from './ServicePrice/servicePriceSchema'
import { nullableRequired } from '@/lib/validation/nullableRequiredNativeEnum'

export function ApplyService({ form }: { form: UpdateServiceFormApi }) {
  const FormInput = useFormTextInput<UpdateServiceFormState>({ form })
  const Select = useFormSelect<UpdateServiceFormState>({ form })

  return (
    <>
      <Select
        label='Apply service to'
        name='servicePrice.applyServiceTo'
        options={applyServiceTo.options}
        required
        onChange={(value, formApi) => {
          formApi.setFieldValue(
            'servicePrice.units',
            value === 'SELECT_DEPOSIT_ACCOUNTS' ? '0' : '1',
          )
        }}
        validators={{
          onChange: nullableRequired(servicePriceSchema.shape.applyServiceTo),
        }}
      />
      <form.Subscribe
        selector={(state) => state.values.servicePrice.applyServiceTo}
      >
        {(applyServiceTo) =>
          applyServiceTo === 'ALL_DEPOSIT_ACCOUNTS' ?
            <FormInput
              label='Units'
              name={'servicePrice.units'}
              invalidChars={['e', 'E', '.', ',']}
              type='number'
              required
              validators={{
                onChange: units(
                  applyServiceTo === 'ALL_DEPOSIT_ACCOUNTS' ? 1 : 0,
                ),
              }}
            />
          : <div className='flex-1'></div>
        }
      </form.Subscribe>
    </>
  )
}
