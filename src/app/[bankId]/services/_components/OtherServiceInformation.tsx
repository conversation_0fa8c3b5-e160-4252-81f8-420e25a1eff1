import { QuestionMarkCircleIcon } from '@heroicons/react/24/outline'
import { Tooltip } from '@/components/Tooltip'
import { Checkbox } from '@/components/Checkbox'
import { InfoSection, InfoSectionTitle } from '@/components/InfoSection'
import { useFormTextInput } from '@/components/Form/useFormTextInput'
import React from 'react'
import {
  serviceSchema,
  UpdateServiceFormApi,
  UpdateServiceFormState,
} from '../[effectiveDate]/(service)/UpdateServiceForm'
import { z } from 'zod'
import { alphanumericString } from '@/api/zodSchemas'

const otherInformationSchema = z.object({
  addAfpCode: z.boolean(),
  addInternalNote: z.boolean(),
  domesticCode: alphanumericString.length(6),
  globalCode: alphanumericString.length(8),
  internalNote: z.string().min(1),
  isExpired: z.boolean(),
})

export type OtherInformationState = z.infer<typeof otherInformationSchema>

export function OtherServiceInfo({ form }: { form: UpdateServiceFormApi }) {
  const FormInput = useFormTextInput<UpdateServiceFormState>({ form })

  return (
    <InfoSection>
      <InfoSectionTitle>Other service information</InfoSectionTitle>
      <div className='flex gap-9'>
        <div className='flex flex-1 gap-2'>
          <form.Field name='service.addAfpCode'>
            {(field) => (
              <Checkbox
                label='Add AFP Code'
                checked={field.state.value}
                onChange={field.handleChange}
              />
            )}
          </form.Field>
          <Tooltip
            className='size-4 self-center'
            topOffset={8}
            content='Add AFP Code'
          >
            <QuestionMarkCircleIcon />
          </Tooltip>
        </div>
        <div className='flex-1'></div>
      </div>
      <form.Subscribe selector={(state) => state.values.service.addAfpCode}>
        {(addAfpCode) =>
          addAfpCode && (
            <div className='flex gap-9'>
              <div className='flex-1'>
                <FormInput
                  name='service.domesticAfpCode'
                  label='Domestic code'
                  placeholder='Input'
                  required
                  validators={{
                    onChange: serviceSchema.shape.domesticAfpCode,
                  }}
                />
              </div>
              <div className='flex-1'>
                <FormInput
                  name='service.globalAfpCode'
                  label='Global code'
                  placeholder='Input'
                  required
                  validators={{
                    onChange: serviceSchema.shape.globalAfpCode,
                  }}
                />
              </div>
            </div>
          )
        }
      </form.Subscribe>
      <div className='flex gap-9'>
        <div className='flex flex-1 gap-2'>
          <form.Field name='service.addInternalNote'>
            {(field) => (
              <Checkbox
                label='Add an internal note about this service'
                checked={field.state.value}
                onChange={field.handleChange}
              />
            )}
          </form.Field>
        </div>
        <div className='flex-1'></div>
      </div>
      <form.Subscribe
        selector={(state) => state.values.service.addInternalNote}
      >
        {(addInternalNote) =>
          addInternalNote && (
            <div className='flex gap-9'>
              <FormInput
                required
                name='service.internalNote'
                validators={{
                  onChange: serviceSchema.shape.internalNote,
                }}
              />
            </div>
          )
        }
      </form.Subscribe>
    </InfoSection>
  )
}
