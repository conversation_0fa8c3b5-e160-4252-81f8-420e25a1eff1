'use client'

import {
  B<PERSON><PERSON>rumb,
  Breadcrum<PERSON>,
  BreadcrumbSegment,
} from '@/components/Breadcrumbs/Breadcrumbs'
import { clsx } from 'clsx'
import { routeTo, useRoute } from '@/app/[bankId]/services/routing'
import { useCategoryPath } from '../_hooks/useCategoryPath'
import { match } from '@/lib/unions/match'

type ServicesBreadcrumbsProps = {
  categoryCode?: string
  showRoot?: boolean
}

export function ServicesBreadcrumbs({
  categoryCode,
  children,
  className,
  showRoot = true,
  ...props
}: React.ComponentPropsWithoutRef<typeof Breadcrumbs> &
  ServicesBreadcrumbsProps) {
  const { bankId, effectiveDate, view } = match(useRoute()!, {
    '/services/[effectiveDate]/catalog/[categoryCode]': ({
      params,
      query,
    }) => ({
      ...params,
      view: query.view,
    }),
    _: ({ params }) => ({
      ...params,
      view: 'column' as const,
    }),
  })

  const path = useCategoryPath(categoryCode, effectiveDate)

  const breadcrumbs: Breadcrumb[] = path
    .map((category, i) => {
      return {
        title:
          category.current.name === 'root' ?
            'All services'
          : category.current.name!,
        key: category.current.code!,
        href: routeTo(
          '/services/[effectiveDate]/catalog/[categoryCode]',
          { bankId, effectiveDate, categoryCode: category.current.code! },
          { view, column: Math.min(i, 2) },
        ),
      }
    })
    .filter(({ key }) => showRoot || key !== 'root')

  return (
    <Breadcrumbs className={clsx(className, 'h-11')} {...props}>
      {breadcrumbs.map((breadcrumb) => (
        <BreadcrumbSegment key={breadcrumb.key} href={breadcrumb.href}>
          {breadcrumb.title}
        </BreadcrumbSegment>
      ))}
      {children}
    </Breadcrumbs>
  )
}
