import {
  <PERSON>dal,
  <PERSON>dal<PERSON><PERSON><PERSON>utton,
  ModalConfirm<PERSON>utton,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ModalOpenButton,
  Modal<PERSON><PERSON>le,
  ModalWindow,
} from '@/components/Modal'
import { ColumnDef } from '@tanstack/react-table'
import { useMemo, useState } from 'react'
import { SortedTable } from '@/components/Table/SortedTable'
import { SearchBar } from '@/components/SearchBar'
import { selectableColumn } from '@/components/Table/tableUtils'
import { SimpleMultiselectFilter } from '@/components/Filter/SimpleMultiselectFilter'
import { preventDefault } from '@/lib/preventDefault'
import { stopPropagation } from '@/lib/stopPropagation'
import DocumentTextIcon from '@heroicons/react/24/outline/DocumentTextIcon'
import { arrayFilterFn, useFilterState } from '../../hooks/useFilterState'
import {
  priceType,
  PriceType,
  priceTypeLabel,
  ServiceType,
  serviceTypeLabel,
} from '@/api/zodSchemas'
import {
  CatalogCategory,
  CatalogService,
  useServiceCatalog,
} from '../_hooks/useServiceCatalog'
import { formatToServerString } from '@/lib/date'
import { isDefined } from '@/lib/guards/isDefined'
import { useServicePrices } from '../_hooks/useServicePrices'
import { MonthPicker } from '@/components/Filter/MonthPicker'
import { If } from '@/components/If'

export type ServiceWithCategory = CatalogService & {
  category: CatalogCategory | null
}
interface SelectServiceSetModalProps {
  effectiveDate: string
  onServiceSetSelected: (selected: ServiceWithCategory[]) => void
}

type FiltersState = {
  categoryName: string[]
  priceType: PriceType[]
  name: string
}

const filterDefaultValues: FiltersState = {
  categoryName: [],
  priceType: [],
  name: '',
}

export function SelectServiceSetModal({
  effectiveDate,
  onServiceSetSelected: onSeviceSetSelected,
  children,
}: React.PropsWithChildren<SelectServiceSetModalProps>) {
  const [selectedServices, setSelectedServices] = useState<
    ServiceWithCategory[]
  >([])
  const [selectedDate, setSelectedDate] = useState(effectiveDate)

  const catalogQueryResult = useServiceCatalog({
    effectiveDate: selectedDate,
  })

  const pricesQueryResult = useServicePrices({
    effectiveDate: selectedDate,
  })

  const catalog = useMemo(() => {
    if (!catalogQueryResult.data) {
      return {
        categoriesWithVolumeBased: [],
        volumeBasedServices: [],
      }
    }

    const volumeBasedServices = Object.values(
      catalogQueryResult.data.serviceByCode,
    )
      .filter((s) => s.current.serviceType === 'VOLUME_BASED')
      .map((service) => ({
        ...service,
        category: catalogQueryResult.data.categoryByCode[service.parent?.code!],
      }))

    const categoriesWithVolumeBased = [
      ...new Set(
        volumeBasedServices
          .map((s) => s.category?.current.name)
          .filter(isDefined),
      ),
    ]

    return { volumeBasedServices, categoriesWithVolumeBased }
  }, [catalogQueryResult])

  const serviceSetFilters = useFilterState<FiltersState>({
    defaultValues: filterDefaultValues,
  })

  const [visibleServices, setVisibleServices] = useState(0)

  type CatalogColumnDef = ColumnDef<ServiceWithCategory, ServiceWithCategory>

  const columns = useMemo(() => {
    return [
      {
        ...selectableColumn,
        meta: {
          className: 'min-w-[50px] flex justify-center',
        },
      },
      {
        header: 'Name',
        id: 'name',
        accessorFn: (originalRow) => originalRow.current.description,
        cell: ({ getValue }) => (
          <>
            <DocumentTextIcon className='mr-1 h-5 w-5 text-indigo-500' />
            <span>{getValue<string>() ?? '-'}</span>
          </>
        ),
      },
      {
        header: 'Code',
        id: 'code',
        accessorFn: (originalRow) => originalRow.current.code,
        meta: {
          className: 'min-w-[100px]',
        },
      },
      {
        header: 'Service type',
        id: 'serviceType',
        accessorFn: (originalRow) => originalRow.current.serviceType,
        cell: ({ getValue }) => {
          return serviceTypeLabel(getValue<ServiceType>()) ?? '-'
        },
        meta: {
          className: 'min-w-[180px]',
        },
      },
      {
        id: 'categoryName',
        header: 'Service category',
        cell: ({ getValue }) => getValue() ?? '-',
        filterFn: arrayFilterFn,
        accessorFn: (row) => row.category?.current.name,
      },
      {
        header: 'Price type',
        id: 'priceType',
        accessorFn: (originalRow) =>
          pricesQueryResult.data == undefined ?
            '-'
          : pricesQueryResult.data[originalRow.current.code!]?.priceType,
        cell: ({ getValue }) =>
          getValue<PriceType>() ? priceTypeLabel(getValue<PriceType>()) : '-',
        filterFn: arrayFilterFn,
        meta: {
          className: 'min-w-[180px]',
        },
      },
    ] as CatalogColumnDef[]
  }, [pricesQueryResult.data])

  return (
    <Modal>
      <ModalOpenButton className='border-none'>{children}</ModalOpenButton>
      <ModalWindow className='flex h-[90vh] w-[80vw] flex-col gap-4'>
        <ModalTitle>
          <h4 className='text-xl font-medium text-app-color-text-primary-800'>
            Add services to this set
          </h4>
        </ModalTitle>
        <form
          className='flex flex-1 flex-col gap-4 overflow-hidden'
          onSubmit={preventDefault(stopPropagation())}
        >
          <SearchBar
            className='max-w-96'
            defaultLabel='Search service catalog'
            value={serviceSetFilters.values.name}
            onValueChange={serviceSetFilters.update('name')}
          />
          <div className='flex items-center justify-between gap-4'>
            <div className='flex gap-4'>
              <MonthPicker
                showIcon
                prefix='View data effective on'
                initialDate={selectedDate}
                onDateChange={(date) =>
                  setSelectedDate(formatToServerString(date))
                }
              />
              <SimpleMultiselectFilter
                defaultLabel='Price type'
                name='priceType'
                renderLabel={priceTypeLabel}
                options={priceType.options}
                value={serviceSetFilters.values.priceType}
                onValueChange={serviceSetFilters.update('priceType')}
              />
              <SimpleMultiselectFilter
                defaultLabel='Service category'
                name='categoryName'
                options={catalog.categoriesWithVolumeBased}
                value={serviceSetFilters.values.categoryName}
                onValueChange={serviceSetFilters.update('categoryName')}
              />
            </div>
            <div>{visibleServices} service(s)</div>
          </div>
          <div className='flex flex-1 flex-col justify-between overflow-hidden'>
            <If
              true={
                catalogQueryResult.fetchStatus !== 'fetching' &&
                pricesQueryResult.fetchStatus !== 'fetching'
              }
            >
              <SortedTable
                columnFilters={serviceSetFilters.columnFilters}
                columns={columns}
                data={catalog.volumeBasedServices}
                onSelectionChange={setSelectedServices}
                onVisibleRowsChanged={(rows) => {
                  setVisibleServices(rows.length)
                }}
              />
            </If>
            <ModalFooter className='mt-6 flex items-center justify-between'>
              <div>{selectedServices.length} service(s) selected</div>
              <div className='flex gap-4'>
                <ModalCancelButton className='min-w-60'>
                  Cancel
                </ModalCancelButton>
                <ModalConfirmButton
                  className='btn-primary min-w-60'
                  disabled={selectedServices.length === 0}
                  onClick={(_, modal) => {
                    onSeviceSetSelected(selectedServices)
                    modal.close()
                  }}
                >
                  Select
                </ModalConfirmButton>
              </div>
            </ModalFooter>
          </div>
        </form>
      </ModalWindow>
    </Modal>
  )
}
