'use client'

import { ServicesBreadcrumbs } from './ServicesBreadcrumbs'
import { clsx } from 'clsx'
import { useSelectedCategory } from '../_hooks/useSelectedCategory'
import { useGeneralRouteParams } from '../routing'

export function ServicesHeader({
  children,
  className,
  ...props
}: React.ComponentPropsWithoutRef<'header'>) {
  const { params } = useGeneralRouteParams()
  const { selectedCategory } = useSelectedCategory({
    effectiveDate: params.effectiveDate,
  })

  return (
    <header className={clsx(className, 'flex')} {...props}>
      <ServicesBreadcrumbs
        categoryCode={selectedCategory?.current.code}
        truncate={2}
      />
      {children}
    </header>
  )
}
