import { useFormSelect } from '@/components/Form/useFormSelect'
import {
  UpdateServiceFormApi,
  UpdateServiceFormState,
} from '../[effectiveDate]/(service)/UpdateServiceForm'
import { SelectOption } from '@/components/Input/Select'
import clsx from 'clsx'
import { servicePriceSchema } from './ServicePrice/servicePriceSchema'
import { useStore } from '@tanstack/react-form'
import { useQuery } from '@tanstack/react-query'
import { query } from '../queries'
import { components } from '@/api/schema'

// ToDo: should be consumed from the configuration API endpoint

interface FeeCycleProps {
  form: UpdateServiceFormApi
  className?: string
}

interface IndexRateOptionProps {
  indexRate: components['schemas']['IndexRate']
  selected?: boolean
}

function IndexRateOption({ indexRate, selected }: IndexRateOptionProps) {
  return (
    <div>
      <span
        className={clsx('inline-block', {
          'mr-2': selected,
          'min-w-64': !selected,
        })}
      >
        {indexRate.code} - {indexRate.name}
      </span>
      <span className='text-sm text-app-color-text-secondary'>
        {indexRate.indexRate.toFixed(2)}%
      </span>
    </div>
  )
}

export function IndexRateSelect({ form, className }: FeeCycleProps) {
  const Select = useFormSelect<UpdateServiceFormState>({ form })

  const [effectiveDate, indexRateCode] = useStore(form.store, (state) => [
    state.values.service.effectiveDate,
    state.values.servicePrice.indexRateCode,
  ])

  const indexRateList = useQuery(
    query('/listIndexRatesByEffectiveDate', { effectiveDate }),
  )

  if (indexRateList.isError) {
    throw indexRateList.error
  }

  if (!indexRateList.isSuccess) {
    return 'Loading...'
  }

  const selectedIndexRate = indexRateList.data.find(
    (rate) => rate.code === indexRateCode,
  )

  return (
    <Select
      className={className}
      label='Index rate'
      name='servicePrice.indexRateCode'
      required
      renderSelected={() =>
        selectedIndexRate ?
          <IndexRateOption selected indexRate={selectedIndexRate} />
        : null
      }
      validators={{
        onChange: servicePriceSchema.shape.indexRateCode,
      }}
    >
      {indexRateList.data.map((indexRateOptionData) => (
        <SelectOption
          key={indexRateOptionData.code}
          value={indexRateOptionData.code}
        >
          <IndexRateOption indexRate={indexRateOptionData} />
        </SelectOption>
      ))}
    </Select>
  )
}
