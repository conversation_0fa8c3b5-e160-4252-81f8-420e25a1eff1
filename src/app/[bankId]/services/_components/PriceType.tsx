import { useFormSelect } from '../../../../components/Form/useFormSelect'
import { PRICE_TYPES_BY_SERVICE_TYPE } from '@/app/types'
import {
  UpdateServiceFormApi,
  UpdateServiceFormState,
} from '../[effectiveDate]/(service)/UpdateServiceForm'
import { priceTypeLabel } from '@/api/zodSchemas'
import { servicePriceSchema } from './ServicePrice/servicePriceSchema'

export function PriceType({ form, ...props }: { form: UpdateServiceFormApi }) {
  const Select = useFormSelect<UpdateServiceFormState>({ form })
  return (
    <form.Subscribe selector={(state) => state.values.service.serviceType}>
      {(serviceType) => (
        <Select
          label='Price type'
          renderSelected={priceTypeLabel}
          renderOption={priceTypeLabel}
          required
          options={PRICE_TYPES_BY_SERVICE_TYPE[serviceType]}
          name='servicePrice.priceType'
          tooltip='Price type'
          validators={{
            onChange: servicePriceSchema.shape.priceType,
          }}
          {...props}
        />
      )}
    </form.Subscribe>
  )
}
