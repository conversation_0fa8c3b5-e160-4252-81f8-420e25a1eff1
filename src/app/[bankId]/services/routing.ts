import { defineRoutes } from '@/lib/defineRoute'
import { servicesCatalogRoute } from './[effectiveDate]/catalog/[categoryCode]/routing'
import { servicesAddRoute } from './[effectiveDate]/(service)/(create)/add/[parentCategoryCode]/routing'
import { servicesEditRoute } from './[effectiveDate]/(service)/edit/[serviceCode]/routing'
import { servicesViewRoute } from './[effectiveDate]/(service)/view/[serviceCode]/routing'
import { servicesDuplicateRoute } from './[effectiveDate]/(service)/(create)/duplicate/[serviceCode]/routing'
import { match } from '@/lib/unions/match'

export const [useRoute, routeTo] = defineRoutes(
  servicesAddRoute,
  servicesCatalogRoute,
  servicesEditRoute,
  servicesViewRoute,
  servicesDuplicateRoute,
)

export const useGeneralRouteParams = () => {
  const route = useRoute()

  return match(route!, {
    _: (data) => data,
  })
}
