'use client'
import { components } from '@/api/schema'
import { useQuery } from '@tanstack/react-query'
import { query } from '../queries'

export interface CatalogCategory {
  current: components['schemas']['ServiceCategory']
  parent: components['schemas']['ServiceCategory'] | undefined
  categoryCodes: string[]
  serviceCodes: string[]
}

export type CatalogItem = CatalogCategory | CatalogService

export function isCategory(value: any): value is CatalogCategory {
  return Array.isArray(value.categoryCodes) && Array.isArray(value.serviceCodes)
}

export interface CatalogService {
  current: components['schemas']['Service']
  parent: components['schemas']['ServiceCategory'] | null
}

export interface Catalog {
  categoryByCode: Record<string, CatalogCategory>
  serviceByCode: Record<string, CatalogService>
}

export function useServiceCatalog({
  effectiveDate,
}: {
  effectiveDate: string
}) {
  return useQuery({
    ...query('/getServiceCatalogMapping', {
      effectiveDate,
    }),
    select(data) {
      const rootCategory = {
        code: 'root',
        effectiveDate: '1970-01-01',
        name: 'root',
      }

      const parentToChildCategory = data.categoryToCategoryMappings!.reduce<
        Record<string, string[]>
      >((prev, curr) => {
        const parentCode = curr.parent?.code ?? 'root'
        return {
          ...prev,
          [parentCode!]: [...(prev[parentCode!] ?? []), curr.child?.code!],
        }
      }, {})

      const parentToChildServiceCodes = data.serviceToCategoryMappings!.reduce<
        Record<string, string[]>
      >((prev, curr) => {
        const parentCode = curr.parent?.code ?? 'root'
        return {
          ...prev,
          [parentCode!]: [...(prev[parentCode!] ?? []), curr.child?.code!],
        }
      }, {})

      return {
        categoryByCode: data.categoryToCategoryMappings!.reduce<
          Record<string, CatalogCategory>
        >(
          (previous, categoryMapping) => ({
            ...previous,
            [categoryMapping.child?.code!]: {
              current: categoryMapping.child,
              parent: categoryMapping.parent ?? rootCategory,
              categoryCodes:
                parentToChildCategory[categoryMapping.child?.code!] ?? [],
              serviceCodes:
                parentToChildServiceCodes[categoryMapping.child?.code!] ?? [],
            } as CatalogCategory,
          }),
          {
            root: {
              categoryCodes: parentToChildCategory['root'] ?? [],
              serviceCodes: parentToChildServiceCodes['root'] ?? [],
              current: rootCategory,
              parent: undefined,
            } as CatalogCategory,
          },
        ),
        serviceByCode: data.serviceToCategoryMappings!.reduce<
          Record<string, CatalogService>
        >(
          (pervious, serviceMapping) =>
            ({
              ...pervious,
              [serviceMapping.child?.code!]: {
                current: serviceMapping.child,
                parent: serviceMapping.parent,
              },
            }) as Record<string, CatalogService>,
          {},
        ),
      } as Catalog
    },
  })
}
