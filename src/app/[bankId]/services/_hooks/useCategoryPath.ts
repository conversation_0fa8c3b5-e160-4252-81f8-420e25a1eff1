import { useMemo } from 'react'
import { CatalogCategory, useServiceCatalog } from './useServiceCatalog'

export function useCategoryPath(
  categoryCode: string | undefined,
  effectiveDate: string,
) {
  const catalog = useServiceCatalog({ effectiveDate })

  return useMemo(() => {
    if (!catalog.data || !categoryCode) {
      return []
    }

    const path: CatalogCategory[] = [catalog.data.categoryByCode[categoryCode]]

    let parentCode = path[0].parent?.code

    while (parentCode) {
      path.push(catalog.data.categoryByCode[parentCode])
      parentCode = path.at(-1)?.parent?.code!
    }

    return path.reverse()
  }, [catalog, categoryCode])
}
