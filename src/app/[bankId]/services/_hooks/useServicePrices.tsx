import { components } from '@/api/schema'
import { useQuery } from '@tanstack/react-query'
import { query } from '../queries'

export function useServicePrices({ effectiveDate }: { effectiveDate: string }) {
  return useQuery({
    ...query('/getServicePricesAsOf', {
      asOfDate: effectiveDate,
      pricingHierarchyEntryType: 'STANDARD',
    }),
    select: (data) => {
      return data.reduce<Record<string, components['schemas']['ServicePrice']>>(
        (previous, current) => ({
          ...previous,
          [current.serviceCode!]: current,
        }),
        {},
      )
    },
  })
}
