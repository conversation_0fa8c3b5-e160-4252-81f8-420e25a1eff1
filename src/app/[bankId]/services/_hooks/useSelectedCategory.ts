import { useMemo } from 'react'
import { useRoute } from '@/app/[bankId]/services/routing'
import { match } from '@/lib/unions/match'
import { useServiceCatalog } from './useServiceCatalog'

export function useSelectedCategory({
  effectiveDate,
}: {
  effectiveDate: string
}) {
  const catalog = useServiceCatalog({ effectiveDate })
  const route = useRoute()

  return useMemo(() => {
    return {
      selectedCategory:
        (route &&
          match(route, {
            '/services/[effectiveDate]/add/[parentCategoryCode]': ({
              params: { parentCategoryCode },
            }) => catalog.data?.categoryByCode[parentCategoryCode],

            '/services/[effectiveDate]/catalog/[categoryCode]': ({
              params: { categoryCode },
            }) => catalog.data?.categoryByCode[categoryCode],

            '/services/[effectiveDate]/edit/[serviceCode]': ({
              params: { serviceCode },
            }) =>
              catalog.data?.categoryByCode[
                catalog.data.serviceByCode[serviceCode].parent?.code!
              ],

            '/services/[effectiveDate]/view/[serviceCode]': ({
              params: { serviceCode },
            }) =>
              catalog.data?.categoryByCode[
                catalog.data.serviceByCode[serviceCode].parent?.code!
              ],
            '/services/[effectiveDate]/duplicate/[serviceCode]': ({
              params: { serviceCode },
            }) =>
              catalog.data?.categoryByCode[
                catalog.data.serviceByCode[serviceCode].parent?.code!
              ],
          })) ??
        catalog.data?.categoryByCode['root'],
    }
  }, [catalog, route])
}
