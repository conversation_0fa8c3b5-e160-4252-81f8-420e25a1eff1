import { defineQueries, defineQuery } from '@/lib/state/defineQuery'
import { AccountOverridesItem, PriceListsItem, PromotionsItem } from './types'
import { withMetrics } from '@/lib/middleware/withMetrics'
import { GetServiceResponse } from '../../api/v1/(services)/getService/route'
import { createAdHocClient } from '@/lib/openapi/client'
import { revenueConnectClient } from '@/api/revenueConnectClient'

const serviceCatalogMapping = defineQuery(
  revenueConnectClient,
  '/getServiceCatalogMapping',
  (payload) => (payload ? [payload] : []),
)

const servicePricesAsOf = defineQuery(
  revenueConnectClient,
  '/getServicePricesAsOf',
  (payload) => (payload ? [payload] : []),
)

const getDemographicPriceLists = defineQuery(
  revenueConnectClient,
  '/getDemographicPriceLists',
  (payload) => (payload ? [payload] : []),
)

/**
 * Service details
 */
const serviceQuery = defineQuery(
  createAdHocClient<string, GetServiceResponse>(),
  '/api/v1/getService',
  (serviceCode) => [serviceCode],
)

const getServiceTimeline = defineQuery(
  revenueConnectClient,
  '/getServiceTimeline',
  (payload) => (payload ? [payload] : []),
)

const getServiceDetailsAsOf = defineQuery(
  revenueConnectClient,
  '/getServiceDetailsAsOf',
  (payload) => (payload ? [payload] : []),
)

/**
 * Service pricing differences
 */
const priceListQuery = defineQuery(
  createAdHocClient<string, PriceListsItem[]>(),
  '/api/v1/service-price-lists',
  (serviceCode) => [serviceCode],
)

const pricingPromotionsQuery = defineQuery(
  createAdHocClient<string, PromotionsItem[]>(),
  '/api/v1/service-promotions',
  (serviceCode) => [serviceCode],
)

const pricingAccountOverridesQuery = defineQuery(
  createAdHocClient<string, AccountOverridesItem[]>(),
  '/api/v1/service-account-overrides',
  (serviceCode) => [serviceCode],
)

/**
 * All service queries
 */

export const query = defineQueries(
  [
    serviceQuery,
    priceListQuery,
    pricingPromotionsQuery,
    pricingAccountOverridesQuery,
    serviceCatalogMapping,
    servicePricesAsOf,
    getServiceTimeline,
    getServiceDetailsAsOf,
    defineQuery(
      revenueConnectClient,
      '/listIndexRatesByEffectiveDate',
      (payload) => (payload ? [payload] : []),
    ),
    defineQuery(revenueConnectClient, '/getCycleDefinitions', (payload) =>
      payload ? [payload] : [],
    ),
    defineQuery(
      revenueConnectClient,
      '/listBankOptionsByEffectiveDate',
      (payload) => (payload ? [payload] : []),
    ),
    getDemographicPriceLists,
  ],
  withMetrics(),
)
