import { <PERSON><PERSON><PERSON>, React<PERSON>orm<PERSON><PERSON>, useForm } from '@tanstack/react-form'
import { OtherServiceInfo } from '../../_components/OtherServiceInformation'
import { ServiceInformation } from '../../_components/ServiceInformation'
import { useMemo } from 'react'
import { getCategoryNested } from '../catalog/catalog'
import { DefaultPricing } from '../../_components/DefaultPricing'
import { ServiceSetForm } from '../../_components/ServiceSetForm'
import { TiersFormState } from '../../_components/EditPricingTiers'
import { useServiceCatalog } from '../../_hooks/useServiceCatalog'
import { useGeneralRouteParams } from '../../routing'
import { z } from 'zod'
import {
  alphanumericString,
  code,
  serviceType,
  stringToNumber,
} from '@/api/zodSchemas'
import { nullableRequired } from '@/lib/validation/nullableRequiredNativeEnum'
import {
  ServicePriceSchema,
  servicePriceSchema,
} from '../../_components/ServicePrice/servicePriceSchema'
import { startOfTheMonth, toServerFormat } from '@/lib/date'
import { unary } from '@/lib/functional/unary'
import { replaceNullWithUndefined } from '@/lib/functional/replaceNullWithUndefined'
import { components } from '@/api/schema'
import { includes } from '@/lib/functional/includes'
import { isNumber } from '@/lib/guards/isNumber'

interface UpdateServiceFormProps {
  defaultValues: UpdateServiceFormState
  onSubmit: (state: UpdateServiceFormState) => void
}

// TODO can use `formValidators.service.extend(...)` instead
export const serviceSchema = z.object({
  code: code(),
  description: nullableRequired(z.string().min(3).max(50)),
  domesticAfpCode: alphanumericString.length(6).nullable(),
  effectiveDate: z.string(),
  globalAfpCode: alphanumericString.length(8).nullable(),
  internalNote: z.string().min(1).nullable(),
  serviceType,
})

const serviceFormStateSchema = serviceSchema.extend({
  addAfpCode: z.boolean(),
  addInternalNote: z.boolean(),
})

// Accoring to design we only operate with inclusive numbers,
// convert to exclusive number for the server.
const toExclusive = (v: number | null) => (isNumber(v) ? v + 1 : v)

// TODO can use `formValidators.servicePrice.pick(...)`
export const tierPriceSchema = z.object({
  tierMaxBalanceExclusive: stringToNumber
    .nullable()
    .transform(unary(toExclusive)),
  tierMaxVolumeExclusive: stringToNumber
    .nullable()
    .transform(unary(toExclusive)),
  tierMinBalanceInclusive: stringToNumber.nullable(),
  tierMinVolumeInclusive: stringToNumber.nullable(),
  priceValue: stringToNumber.nullable(),
  indexAdjustment: stringToNumber.nullable(),
})

export type ServicePriceTier = z.input<typeof tierPriceSchema>

function createPricing(
  service: components['schemas']['Service'],
  commonServicePriceData: components['schemas']['ServicePrice'],
  pricingTiers: Partial<components['schemas']['ServicePrice']>[],
): components['schemas']['ServicePrice'][] {
  if (
    includes(commonServicePriceData.priceType, [
      'PARTITIONED_TIER',
      'THRESHOLD_TIER',
    ])
  ) {
    return pricingTiers.map((tier, index) => ({
      ...commonServicePriceData,
      ...tier,
      tierNumber: index,
      serviceCode: service.code,
      pricingHierarchyEntryCode: '',
    }))
  } else {
    return [commonServicePriceData].map((price) => {
      return {
        ...price,
        serviceCode: service.code,
        pricingHierarchyEntryCode: '',
      }
    })
  }
}

export const serviceFormToServerDataSchema = z
  .object({
    service: serviceSchema.transform(unary(replaceNullWithUndefined)),
    servicePrice: servicePriceSchema.transform(unary(replaceNullWithUndefined)),
    servicesInServiceSet: z
      .array(z.any())
      .nullable()
      .transform((v) => v ?? undefined),
    serviceCategoryCode: z
      .string()
      .nullable()
      .transform((v) => v ?? undefined),
    pricingTiers: z
      .array(tierPriceSchema)
      .transform((p) => p.map(unary(replaceNullWithUndefined))),
  })
  .transform((data) => {
    return {
      service: data.service,
      serviceCategoryCode: data.serviceCategoryCode,
      servicePrice: createPricing(
        data.service,
        {
          ...data.servicePrice,
          serviceCode: data.service.code,
          effectiveDate: data.service.effectiveDate,
        },
        data.pricingTiers,
      ),
      serviceCodes:
        data.servicesInServiceSet ?
          (data.servicesInServiceSet as components['schemas']['Service'][]).map(
            (s) => s.code!,
          )
        : undefined,
    }
  })

export type ServiceFormState = z.input<typeof serviceFormStateSchema>

export const serviceFormStateDefaultValues: ServiceFormState = {
  addAfpCode: false,
  addInternalNote: false,
  code: '',
  description: null,
  domesticAfpCode: null,
  effectiveDate: toServerFormat(startOfTheMonth()),
  globalAfpCode: null,
  internalNote: null,
  serviceType: 'VOLUME_BASED',
}

export type UpdateServiceFormState = {
  service: ServiceFormState
  serviceCategoryCode: string | null
  servicesInServiceSet: components['schemas']['Service'][]
  servicePrice: ServicePriceSchema
} & TiersFormState

export type UpdateServiceFormApi = ReactFormApi<UpdateServiceFormState> &
  FormApi<UpdateServiceFormState>

export function UpdateServiceForm({
  defaultValues,
  onSubmit,
  children,
}: React.PropsWithChildren<UpdateServiceFormProps>) {
  const {
    params: { effectiveDate },
  } = useGeneralRouteParams()

  const categories = useAllCategories({ effectiveDate })

  const form = useForm<UpdateServiceFormState>({
    // When updating a Service, we want the default effectiveDate to be today.
    defaultValues: {
      ...defaultValues,
      service: {
        ...defaultValues.service,
        effectiveDate: toServerFormat(startOfTheMonth()),
      },
    },
    onSubmit: ({ value }) => {
      onSubmit(value)
    },
  })

  return (
    <form
      className='flex max-h-full flex-auto flex-col'
      onSubmit={async (event) => {
        form.reset(form.state.values)
        event.preventDefault()
        event.stopPropagation()
        await form.handleSubmit()
      }}
    >
      <div className='mx-8 mt-8 flex flex-auto flex-col gap-2 overflow-y-auto pb-8'>
        <ServiceInformation serviceCategories={categories} form={form} />
        <ServiceSetForm form={form} />
        <DefaultPricing form={form} />
        <OtherServiceInfo form={form} />
      </div>
      <div className='flex justify-end gap-4 border-t bg-white px-12 py-10'>
        {children}
      </div>
    </form>
  )
}

function useAllCategories({ effectiveDate }: { effectiveDate: string }) {
  const catalog = useServiceCatalog({
    effectiveDate,
  })

  return useMemo(() => {
    if (!catalog.data) {
      return []
    }

    return getCategoryNested(catalog.data, 'root', 'Infinity')?.categories ?? []
  }, [catalog])
}
