'use client'

import { toFormState } from '@/app/[bankId]/services/ServiceFormState'
import { Button } from '@/components/Button'
import Link from 'next/link'
import { routeTo, useRoute } from '@/app/[bankId]/services/routing'
import { unwrap } from '@/lib/unions/unwrap'
import { useMutation, useQuery } from '@tanstack/react-query'
import { query } from '@/app/[bankId]/services/queries'
import {
  serviceFormToServerDataSchema,
  UpdateServiceForm,
} from '../../../UpdateServiceForm'
import { useRouter } from 'next/navigation'
import { serviceMutation } from '../../../mutations'

export default function DuplicateService() {
  const route = unwrap(
    useRoute(),
    '/services/[effectiveDate]/duplicate/[serviceCode]',
  )!
  const router = useRouter()

  const { mutate: createService } = useMutation(
    serviceMutation('/addService', {
      onSuccess: (service) => {
        router.push(
          routeTo(
            '/services/[effectiveDate]/view/[serviceCode]',
            {
              ...route.params,
              serviceCode: service.code!,
            },
            {
              ...route.query,
              version: service.effectiveDate,
            },
          ),
        )
      },
    }),
  )
  const {
    data: serviceData,
    error,
    isLoading,
  } = useQuery(
    query('/getServiceDetailsAsOf', {
      code: route.params.serviceCode,
      effectiveDate: route.params.effectiveDate,
    }),
  )

  if (error) {
    throw error
  }

  if (isLoading) {
    return 'Loading...'
  }

  if (!route.query.version) {
    return 'A Service version must be specified via the "version" query parameter.'
  }

  if (!serviceData) {
    return 'Service not found.'
  }

  const defaultValues = toFormState({
    serverService: {
      ...serviceData.service!,
      code: '',
      description: undefined,
    },
    serviceCategoryCode: serviceData.serviceCategory?.code,
    servicesInServiceSet: serviceData.servicesInServiceSet ?? [],
    servicePrices: serviceData.servicePrice?.map((p) => p.servicePrice!)!,
  })

  return (
    <main className='flex min-h-0 flex-auto'>
      <UpdateServiceForm
        defaultValues={defaultValues}
        onSubmit={(state) => {
          const result = serviceFormToServerDataSchema.safeParse(state)

          if (result.success) {
            createService(result.data)
          } else {
            console.error(result.error)
          }
        }}
      >
        <Link
          href={routeTo(
            '/services/[effectiveDate]/view/[serviceCode]',
            route.params,
            route.query,
          )}
        >
          <Button className='btn w-60'>Cancel</Button>
        </Link>
        <Button type='submit' className='btn-primary w-60'>
          Create
        </Button>
      </UpdateServiceForm>
    </main>
  )
}
