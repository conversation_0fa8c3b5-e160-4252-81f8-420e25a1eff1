'use client'

import { But<PERSON> } from '@/components/Button'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { routeTo, useRoute } from '@/app/[bankId]/services/routing'
import { useSelectedCategory } from '@/app/[bankId]/services/_hooks/useSelectedCategory'
import { unwrap } from '@/lib/unions/unwrap'
import { useMutation, useQuery } from '@tanstack/react-query'
import { serviceMutation } from '../../../mutations'
import {
  serviceFormToServerDataSchema,
  serviceFormStateDefaultValues,
  UpdateServiceForm,
  UpdateServiceFormState,
} from '../../../UpdateServiceForm'
import { servicePriceDefaultValues } from '@/app/[bankId]/services/_components/ServicePrice/servicePriceSchema'
import { query } from '@/app/[bankId]/services/queries'

export default function AddService() {
  const route = unwrap(
    useRoute(),
    '/services/[effectiveDate]/add/[parentCategoryCode]',
  )!

  const { selectedCategory } = useSelectedCategory({
    effectiveDate: route.params.effectiveDate,
  })

  const { data: bankOptions } = useQuery({
    ...query('/listBankOptionsByEffectiveDate', {
      effectiveDate: route.params.effectiveDate,
    }),
    select(data) {
      if (data.length > 0) {
        return data[0]
      }

      return null
    },
  })

  const router = useRouter()
  const { mutate: createService } = useMutation(
    serviceMutation('/addService', {
      onSuccess: (service) => {
        router.push(
          routeTo(
            '/services/[effectiveDate]/view/[serviceCode]',
            {
              ...route.params,
              serviceCode: service.code!,
            },
            {
              ...route.query,
              version: service.effectiveDate,
            },
          ),
        )
      },
    }),
  )

  const categoryCode = selectedCategory?.current.code ?? null

  const defaultValues: UpdateServiceFormState = {
    servicePrice: {
      ...servicePriceDefaultValues,
      basisDays: bankOptions?.calculatingBalanceFeeBasis ?? '365',
    },
    servicesInServiceSet: [],
    serviceCategoryCode:
      categoryCode == null || categoryCode === 'root' ? null : categoryCode,
    service: serviceFormStateDefaultValues,
    pricingTiers: [],
  }

  return (
    <UpdateServiceForm
      defaultValues={defaultValues}
      onSubmit={(state) => {
        const result = serviceFormToServerDataSchema.safeParse(state)

        if (result.success) {
          createService(result.data)
        } else {
          console.error(result.error)
        }
      }}
    >
      <Link
        href={routeTo(
          '/services/[effectiveDate]/catalog/[categoryCode]',
          {
            ...route.params,
            categoryCode: selectedCategory?.current.code ?? 'root',
          },
          route.query,
        )}
      >
        <Button className='btn w-60'>Cancel</Button>
      </Link>
      <Button type='submit' className='btn-primary w-60'>
        Create
      </Button>
    </UpdateServiceForm>
  )
}
