import { parseServerFormat, toServerFormat } from '@/lib/date'
import { compareDesc, isBefore, isSameDay, subDays } from 'date-fns'

/**
 * Given an effective date and a list of service versions, return the version
 * effective on that date -- if none of the verisons were effective on the given
 * date, return undefined.
 *
 * This function may add an `expirationDate` field to the result, if the effective
 * version is not the also the most recent version.
 *
 * @param effectiveDate
 * @param services
 * @returns
 */

export function getEffectiveServiceVersion(
  effectiveDate: string,
  versions: string[],
): string {
  // Sort versions from most recent to least recent. The first version with an
  // effectiveDate on or before the target date is the effective version.
  let expirationDate: string | undefined = undefined
  const service = versions
    .slice()
    .sort((a, b) => compareDesc(parseServerFormat(a), parseServerFormat(b)))
    .find((version, i, services) => {
      expirationDate =
        services[i - 1] ?
          toServerFormat(subDays(parseServerFormat(services[i - 1]), 1))
        : undefined
      return (
        isSameDay(
          parseServerFormat(version),
          parseServerFormat(effectiveDate),
        ) ||
        isBefore(parseServerFormat(version), parseServerFormat(effectiveDate))
      )
    })

  return service ?? versions[0]
}
