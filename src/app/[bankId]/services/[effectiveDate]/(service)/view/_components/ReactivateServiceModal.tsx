import { Service } from '@/app/[bankId]/services/types'
import { useFormDatePicker } from '@/components/Form/useFormDatePicker'
import {
  Modal,
  ModalCancelButton,
  ModalConfirmButton,
  ModalFooter,
  ModalOpenButton,
  ModalTitle,
  ModalWindow,
} from '@/components/Modal'
import { useForm } from '@tanstack/react-form'
import { isAfter, isBefore } from 'date-fns'
import { useMutation } from '@tanstack/react-query'
import { z } from 'zod'
import {
  startOfTheMonth,
  toUIFormat,
  toServerFormat,
  parseServerFormat,
} from '@/lib/date'
import { Checkbox } from '@/components/Checkbox'
import { customErrorParams } from '@/components/Form/customErrorParams'
import { useRouter } from 'next/navigation'
import { routeTo, useRoute } from '@/app/[bankId]/services/routing'
import { unwrap } from '@/lib/unions/unwrap'
import { serviceMutation } from '../../mutations'
import { preventDefault } from '@/lib/preventDefault'

interface ReactivateServiceModalProps {
  service: Service
}

type ReactivateFormState = z.infer<typeof reactivateFormSchema>

const reactivateFormSchema = z
  .object({
    expirationDate: z.date(),
    previousExpirationDate: z.date(),
    effectiveDate: z.date(),
    hasExpirationDate: z.boolean(),
  })
  .refine(
    (state) => isBefore(state.previousExpirationDate, state.effectiveDate),
    customErrorParams(({ effectiveDate, previousExpirationDate }) => ({
      message: `The effective date (${toUIFormat(effectiveDate)}) should be after previous expiration date(${toUIFormat(previousExpirationDate)}).`,
      path: ['effectiveDate'],
    })),
  )
  .refine(
    ({ hasExpirationDate, effectiveDate, expirationDate }) =>
      !hasExpirationDate || isAfter(expirationDate, effectiveDate),
    customErrorParams(({ expirationDate, effectiveDate }) => ({
      message: `The expiration date (${toUIFormat(expirationDate)}) should be after the effective date(${toUIFormat(effectiveDate)}).`,
      path: ['expirationDate'],
    })),
  )

export function ReactivateServiceModal({
  service,
}: ReactivateServiceModalProps) {
  const router = useRouter()
  const route = unwrap(
    useRoute(),
    '/services/[effectiveDate]/view/[serviceCode]',
  )!

  const form = useForm({
    defaultValues: {
      expirationDate: startOfTheMonth(),
      effectiveDate: startOfTheMonth(),
      hasExpirationDate: false,
      previousExpirationDate: parseServerFormat(service.expirationDate!),
    } as ReactivateFormState,
    validators: {
      onChange: reactivateFormSchema,
    },
    onSubmit(_) {
      // ToDo: update the service
    },
  })

  const FromDatePicker = useFormDatePicker<ReactivateFormState>({ form })

  return (
    <Modal
      onOpen={async () => {
        form.reset()
        await form.validate('mount')
        await form.validateAllFields('mount')
      }}
    >
      <ModalOpenButton className='btn h-10'>Reactivate</ModalOpenButton>
      <ModalWindow className='w-[450px]'>
        <form onSubmit={preventDefault()}>
          <ModalTitle>
            <h4 className='text-xl font-medium text-app-color-text-primary-800'>
              Reactivate this service?
            </h4>
          </ModalTitle>
          <p className='mb-6 mt-2 text-base font-normal text-app-color-text-secondary-hover'>
            You are reactivating the version of this service that was effective
            from {toUIFormat(parseServerFormat(service.effectiveDate))} –
            {service.expirationDate ?
              toUIFormat(parseServerFormat(service.expirationDate))
            : 'ongoing'}
            .
          </p>
          <div className='flex flex-col gap-1'>
            <FromDatePicker
              name='effectiveDate'
              label='Select the service effective date'
            />
            <form.Field name='hasExpirationDate'>
              {({ handleChange, state }) => (
                <Checkbox
                  label='This service has an expiration date'
                  checked={state.value}
                  onChange={handleChange}
                />
              )}
            </form.Field>
            <form.Subscribe
              selector={(state) => state.values.hasExpirationDate}
            >
              {(hasExpirationDate) =>
                hasExpirationDate && (
                  <FromDatePicker
                    name='expirationDate'
                    label='Expiration date'
                  />
                )
              }
            </form.Subscribe>
          </div>
          <ModalFooter className='mt-6'>
            <ModalCancelButton>Cancel</ModalCancelButton>
            <ModalConfirmButton
              type='submit'
              className='btn-primary'
              onClick={async (_, ctx) => {
                await form.handleSubmit()
                await form.validateAllFields('submit')

                if (form.state.canSubmit) {
                  ctx.close()
                  form.reset()
                }
              }}
            >
              Activate service
            </ModalConfirmButton>
          </ModalFooter>
        </form>
      </ModalWindow>
    </Modal>
  )
}
