import { Tooltip } from '@/components/Tooltip'
import { QuestionMarkCircleIcon } from '@heroicons/react/24/outline'
import clsx from 'clsx'

interface ServiceValueProps {
  label: string
  tooltip?: string
  className?: string
}

export function ServiceValue({
  label,
  tooltip,
  className,
  children,
}: React.PropsWithChildren<ServiceValueProps>) {
  if (children == undefined) {
    return
  }

  return (
    <div className={clsx(`my-3 flex flex-col`, className)}>
      <span className='inline-flex text-sm text-app-color-secondary'>
        {label}
        {tooltip && (
          <Tooltip
            className='ml-1 size-4 self-center'
            topOffset={8}
            content={tooltip}
          >
            <QuestionMarkCircleIcon />
          </Tooltip>
        )}
      </span>
      <span className='inline-flex items-center text-[15px]/[22px] font-medium'>
        {children}
      </span>
    </div>
  )
}
