import { ServiceType, SubPriceType } from '@/api/zodSchemas'
import { SortedTable } from '@/components/Table/SortedTable'
import { formatUSD, formatVolume } from '@/lib/intlFormatters'
import { ColumnDef } from '@tanstack/react-table'
import { useMemo } from 'react'
import { ServicePriceTier } from '../../UpdateServiceForm'

interface PricingTiersListProps {
  serviceType: ServiceType
  tiers: ServicePriceTier[]
  columns: ColumnDef<ServicePriceTier>[]
}

export function getColumns({
  tierPriceType,
  indexRate,
}: {
  tierPriceType: SubPriceType | null
  indexRate: number | null
}) {
  switch (tierPriceType as SubPriceType) {
    case 'FLAT_FEE':
    case 'UNIT_PRICED': {
      return [
        {
          id: 'price',
          header: 'Price',
          cell: ({ row: { original } }) => formatUSD(original.priceValue),
          meta: {
            className: 'basis-2/12',
          },
        },
        {
          id: 'sub-type',
          cell: () =>
            tierPriceType === 'UNIT_PRICED' ? 'per unit' : 'flat fee',
          meta: {
            className: 'basis-2/12',
          },
        },
      ] as ColumnDef<ServicePriceTier>[]
    }
    case 'INDEXED': {
      return [
        {
          id: 'price',
          header: 'Index adjustment',
          cell: ({ row: { original } }) => `${original.indexAdjustment}%`,
          meta: {
            className: 'basis-2/12',
          },
        },
        {
          id: 'sub-type',
          header: 'Effective rate',
          cell: ({ row: { original } }) =>
            `${(parseFloat(original.indexAdjustment ?? '0') + (indexRate ?? 0)).toFixed(2)}%`,
          meta: {
            className: 'basis-2/12',
          },
        },
      ] as ColumnDef<ServicePriceTier>[]
    }
    case 'PERCENTAGE': {
      return [
        {
          id: 'percentage',
          header: 'Percentage',
          cell: ({ row: { original } }) =>
            `${parseFloat(original.priceValue ?? '0').toFixed(2)}%`,
          meta: {
            className: 'basis-2/12',
          },
        },
      ] as ColumnDef<ServicePriceTier>[]
    }
    default: {
      return []
    }
  }
}

export function PricingTiersList({
  tiers,
  serviceType,
  columns,
}: PricingTiersListProps) {
  const tierType = serviceType === 'BALANCE_BASED' ? 'Balance' : 'Volume'

  const resultColumns = useMemo((): ColumnDef<ServicePriceTier>[] => {
    return [
      {
        id: 'min',
        header: `${tierType} tier`,
        cell: ({ row: { original } }) =>
          formatVolume(original[`tierMin${tierType}Inclusive`]),
        meta: {
          className: 'basis-2/12 justify-end flex',
        },
      },
      {
        id: 'to',
        cell: 'to',
        meta: {
          className: 'max-w-10 justify-center basis-1/12',
        },
      },
      {
        id: 'max',
        cell: ({ row: { original } }) =>
          formatVolume(original[`tierMax${tierType}Exclusive`]),
        meta: {
          className: 'basis-2/12',
        },
      },
      ...columns,
    ]
  }, [columns, tierType])

  return <SortedTable data={tiers} columns={resultColumns} />
}
