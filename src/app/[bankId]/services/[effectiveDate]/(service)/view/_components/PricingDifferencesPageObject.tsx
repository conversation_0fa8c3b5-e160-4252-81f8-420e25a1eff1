import { routeTo } from '@/app/[bankId]/services/routing'
import { Page } from '@playwright/test'
import { createPageObjectProvider } from '../../../../../../../../test/e2e/createPageObjectProvider'

export class PricingDifferencesPage {
  constructor(readonly page: Page) {}

  static get provider() {
    return createPageObjectProvider((page) => new PricingDifferencesPage(page))
  }

  async goto(serviceCode: string, effectiveDate: string) {
    await this.page.goto('/api/v1/resetCatalog')
    await this.page.goto(
      routeTo(
        '/services/[effectiveDate]/view/[serviceCode]',
        { bankId: '999', effectiveDate, serviceCode },
        { view: 'column', column: 0, version: effectiveDate },
      ),
    )
    await this.page.getByText('Pricing differences').click()
  }

  async switchTab(tabName: 'Price lists' | 'Promotions' | 'Account overrides') {
    await this.page.getByText(tabName).click()
  }

  getColumnHeader(name: string) {
    return this.page.getByRole('columnheader', { name, exact: true })
  }

  getRowByIndex(index: number) {
    // Add one to the index to skip the header.
    return this.page.getByRole('row').nth(index + 1)
  }

  getRowAndCellByIndex(row: number, cell: number) {
    return this.getRowByIndex(row).getByRole('cell').nth(cell)
  }
}
