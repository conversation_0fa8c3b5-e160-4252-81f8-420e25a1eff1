import { Service } from '@/app/[bankId]/services/types'
import { useFormDatePicker } from '@/components/Form/useFormDatePicker'
import {
  Modal,
  ModalCancelButton,
  ModalConfirmButton,
  ModalFooter,
  ModalOpenButton,
  ModalTitle,
  ModalWindow,
} from '@/components/Modal'
import { useForm } from '@tanstack/react-form'
import { isBefore } from 'date-fns'
import { z } from 'zod'
import { startOfTheMonth, parseServerFormat, toUIFormat } from '@/lib/date'
import { customErrorParams } from '@/components/Form/customErrorParams'

interface ExpireServiceModalProps {
  service: Service
}

const expireServiceFormSchema = z
  .object({
    expireDate: z.date(),
    effectiveDate: z.date(),
  })
  .refine(
    ({ effectiveDate, expireDate }) => isBefore(effectiveDate, expireDate),
    customErrorParams(({ expireDate, effectiveDate }) => ({
      message: `The expiration date (${toUIFormat(expireDate)}) should be after the effective date (${toUIFormat(effectiveDate)}).`,
      path: ['expireDate'],
    })),
  )

type ExpireServiceFormState = z.infer<typeof expireServiceFormSchema>

export function ExpireServiceModal({ service }: ExpireServiceModalProps) {
  const form = useForm({
    defaultValues: {
      expireDate: startOfTheMonth(),
      effectiveDate: parseServerFormat(service.effectiveDate),
    } as ExpireServiceFormState,
    validators: {
      onChange: expireServiceFormSchema,
    },
    onSubmit(_) {
      // ToDo: update the service
    },
  })

  const FromDatePicker = useFormDatePicker<ExpireServiceFormState>({
    form,
  })

  return (
    <Modal
      onOpen={async () => {
        form.reset()
        await form.validate('mount')
        await form.validateAllFields('mount')
      }}
    >
      <ModalOpenButton className='btn h-10'>Expire</ModalOpenButton>
      <ModalWindow className='w-[450px]'>
        <ModalTitle>
          <h4 className='text-xl font-medium text-app-color-text-primary-800'>
            Expire this service?
          </h4>
        </ModalTitle>
        <p className='mb-6 mt-2 text-base font-normal text-app-color-text-secondary-hover'>
          This service will become inactive as of its expiration date. You can
          still duplicate or reactivate it at any time.
        </p>
        <FromDatePicker
          name='expireDate'
          label='Select the service expire date'
        />
        <ModalFooter className='mt-6'>
          <ModalCancelButton>Cancel</ModalCancelButton>
          <ModalConfirmButton
            className='btn-primary'
            onClick={async (_, ctx) => {
              await form.handleSubmit()
              await form.validateAllFields('submit')
              if (form.state.canSubmit) {
                ctx.close()
                form.reset()
              }
            }}
          >
            Expire service
          </ModalConfirmButton>
        </ModalFooter>
      </ModalWindow>
    </Modal>
  )
}
