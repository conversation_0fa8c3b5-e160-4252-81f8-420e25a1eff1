import { useRoute } from '@/app/[bankId]/services/routing'
import {
  Modal,
  ModalOpenButton,
  ModalWindow,
  ModalT<PERSON>le,
  Modal<PERSON>ooter,
  ModalCancelButton,
  ModalConfirmButton,
} from '@/components/Modal'
import { ExclamationTriangleIcon } from '@heroicons/react/24/outline'
import { useRouter } from 'next/navigation'
import { Service } from '@/app/[bankId]/services/types'
import { unwrap } from '@/lib/unions/unwrap'
import { Tooltip } from '@/components/Tooltip'

interface DeleteServiceModalProps {
  service: Service
}

export function DeleteServiceModal({ service }: DeleteServiceModalProps) {
  return (
    <Modal>
      <Tooltip
        disabled={!service.hasAcceptedTransactions}
        content='Service has processed transactions'
      >
        <ModalOpenButton
          aria-disabled={service.hasAcceptedTransactions}
          disabled={service.hasAcceptedTransactions}
          className='btn h-10 aria-disabled:text-app-color-bg-disabled aria-disabled:hover:bg-white'
        >
          Delete
        </ModalOpenButton>
      </Tooltip>
      <ModalWindow className={'border-2 border-app-color-border-error'}>
        <ModalTitle>
          <ExclamationTriangleIcon className='size-8 text-app-color-fg-error-primary' />
        </ModalTitle>
        <h4 className='text-xl font-medium text-app-color-text-primary-800'>
          Delete this Service?
        </h4>
        <p className='mt-2 text-base font-normal text-app-color-text-secondary-hover'>
          Permanently delete this service? You cannot undo this action.
        </p>
        <ModalFooter>
          <ModalCancelButton>Cancel</ModalCancelButton>
          <ModalConfirmButton
            className='btn-alert'
            onClick={() => {
              //ToDo: call delete service API
            }}
          >
            Delete service
          </ModalConfirmButton>
        </ModalFooter>
      </ModalWindow>
    </Modal>
  )
}
