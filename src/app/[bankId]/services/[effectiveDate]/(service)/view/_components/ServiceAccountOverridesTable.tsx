import { query } from '@/app/[bankId]/services/queries'
import { AccountOverridesItem } from '@/app/[bankId]/services/types'
import { SortedTable } from '@/components/Table/SortedTable'
import { useQuery } from '@tanstack/react-query'
import { ColumnDef } from '@tanstack/react-table'
import { useMemo } from 'react'

type AccountOverridesColumnDef = ColumnDef<AccountOverridesItem>

export function AccountOverridesTable({
  serviceCode,
}: {
  serviceCode: string
}) {
  const { data } = useQuery(
    query('/api/v1/service-account-overrides', serviceCode),
  )
  const columns = useMemo<AccountOverridesColumnDef[]>(() => {
    return [
      {
        header: 'Account name',
        accessorKey: 'name',
      },
      {
        header: 'Account number',
        accessorKey: 'number',
      },
      {
        header: 'Expiration date',
        accessorKey: 'expirationDate',
      },
      {
        header: 'Price Type',
        accessorKey: 'priceType',
      },
      {
        header: 'Price',
        accessorKey: 'price',
      },
      {
        header: 'Disposition',
        accessorKey: 'disposition',
      },
    ]
  }, [])

  // TODO: To be used when effective date filtering is implemented (AFIN-146)
  const columnFilters = useMemo(() => [], [])

  return (
    <SortedTable
      data={data ?? []}
      columns={columns}
      columnFilters={columnFilters}
    />
  )
}
