import {
  priceTypeLabel,
  ServiceType,
  dispositionLabel,
  costTypeLabel,
  SubPriceType,
} from '@/api/zodSchemas'
import { schemaServicePriceToFromState } from '@/app/[bankId]/services/ServiceFormState'
import { If } from '@/components/If'
import { includes } from '@/lib/functional/includes'
import { formatUSD } from '@/lib/intlFormatters'
import { PricingTiersList } from './PricingTiers'
import { ServiceValue } from './ServiceValue'
import { components } from '@/api/schema'
import { useMemo } from 'react'
import { ServicePriceTier } from '../../UpdateServiceForm'
import { ColumnDef } from '@tanstack/react-table'
import { useQuery } from '@tanstack/react-query'
import { query } from '@/app/[bankId]/services/queries'
import { monthNameToShort } from '@/lib/date'
import { unary } from '@/lib/functional/unary'

interface ServicePricingDetailsProps {
  servicePrices: components['schemas']['ServicePrice'][]
  serviceType: ServiceType
}

export function ServicePricingDetails({
  servicePrices,
  serviceType,
}: ServicePricingDetailsProps) {
  const servicePrice = servicePrices[0]!

  const indexRate = useQuery({
    ...query('/listIndexRatesByEffectiveDate', {
      effectiveDate: servicePrice.effectiveDate,
    }),
    select(rates) {
      return rates.find((rate) => rate.code === servicePrice.indexRateCode)
    },
  }).data

  const cycleDefinition = useQuery({
    ...query('/getCycleDefinitions', {
      effectiveDate: servicePrice.effectiveDate,
    }),
    select(cycles) {
      return cycles.find(
        (cycle) => cycle.code === servicePrice.cycleDefinitionCode,
      )
    },
  }).data

  const tiersColumns = useMemo(() => {
    switch (servicePrice.tierPriceType as SubPriceType) {
      case 'FLAT_FEE':
      case 'UNIT_PRICED': {
        return [
          {
            id: 'price',
            header: 'Price',
            cell: ({ row: { original } }) => formatUSD(original.priceValue),
            meta: {
              className: 'basis-2/12',
            },
          },
          {
            id: 'sub-type',
            cell: () =>
              servicePrice.tierPriceType === 'UNIT_PRICED' ?
                'per unit'
              : 'flat fee',
            meta: {
              className: 'basis-2/12',
            },
          },
        ] as ColumnDef<ServicePriceTier>[]
      }
      case 'INDEXED': {
        return [
          {
            id: 'price',
            header: 'Index adjustment',
            cell: ({ row: { original } }) => `${original.indexAdjustment}%`,
            meta: {
              className: 'basis-2/12',
            },
          },
          {
            id: 'sub-type',
            header: 'Effective rate',
            cell: ({ row: { original } }) =>
              `${(parseFloat(original.indexAdjustment ?? '0') + (indexRate?.indexRate ?? 0)).toFixed(2)}%`,
            meta: {
              className: 'basis-2/12',
            },
          },
        ] as ColumnDef<ServicePriceTier>[]
      }
      case 'PERCENTAGE': {
        return [
          {
            id: 'percentage',
            header: 'Percentage',
            cell: ({ row: { original } }) =>
              `${parseFloat(original.priceValue ?? '0').toFixed(2)}%`,
            meta: {
              className: 'basis-2/12',
            },
          },
        ] as ColumnDef<ServicePriceTier>[]
      }
      default: {
        return []
      }
    }
  }, [indexRate?.indexRate, servicePrice.tierPriceType])

  return (
    <div className='flex flex-wrap'>
      <div className='flex basis-full'>
        <ServiceValue label='Price type' className='basis-1/2'>
          {priceTypeLabel(servicePrice.priceType!)}
        </ServiceValue>
        <If
          true={includes(servicePrice.priceType, [
            'FLAT_FEE',
            'PERCENTAGE',
            'UNIT_PRICED',
          ])}
        >
          <ServiceValue label='Price' className='basis-1/2'>
            {servicePrice.priceType === 'PERCENTAGE' ?
              `${servicePrice.priceValue}%`
            : formatUSD(servicePrice.priceValue)}
          </ServiceValue>
        </If>
        <If
          true={includes(servicePrice.priceType, [
            'PARTITIONED_TIER',
            'THRESHOLD_TIER',
          ])}
        >
          <ServiceValue label='Price subtype' className='basis-1/2'>
            {priceTypeLabel(servicePrice.tierPriceType ?? 'UNIT_PRICED')}
          </ServiceValue>
        </If>
      </div>
      <If
        true={
          servicePrice.priceType === 'INDEXED' ||
          servicePrice.tierPriceType === 'INDEXED'
        }
      >
        <div className='flex basis-full'>
          <ServiceValue label='Index rate' className='basis-1/2'>
            {indexRate?.code} - {indexRate?.name}
            <span className='ml-2'>{indexRate?.indexRate}%</span>
          </ServiceValue>
          <ServiceValue label='Days in year'>
            {servicePrice.basisDays}
          </ServiceValue>
        </div>
        {/* <ServiceValue label='Cycle' className='basis-1/2'>
          <div>
            {cycleDefinition?.code} - {cycleDefinition?.description}
            <div className='text-xs text-app-color-secondary'>
              {cycleDefinition?.includedMonths?.join(', ')}
            </div>
          </div>
        </ServiceValue> */}
      </If>
      <If
        true={
          serviceType === 'BALANCE_BASED' &&
          servicePrice.tierPriceType === 'UNIT_PRICED' &&
          includes(servicePrice.priceType, [
            'PARTITIONED_TIER',
            'THRESHOLD_TIER',
          ])
        }
      >
        <ServiceValue label='Balance divisor'>
          {servicePrice.balanceDivisor}
        </ServiceValue>
      </If>
      <If true={servicePrices.length > 1 && serviceType !== 'RECURRING'}>
        <ServiceValue label='Price' className='basis-full'>
          <div className='mt-2 basis-full'>
            <PricingTiersList
              columns={tiersColumns}
              serviceType={serviceType}
              tiers={servicePrices.map((price) =>
                schemaServicePriceToFromState(price!),
              )}
            />
          </div>
        </ServiceValue>
      </If>
      <If true={serviceType === 'BALANCE_BASED'}>
        <ServiceValue label='Cycle' className='basis-1/2'>
          <div className='inline-flex flex-col'>
            <div>
              {cycleDefinition?.code} - {cycleDefinition?.description}
            </div>
            <div className='text-xs'>
              {cycleDefinition?.includedMonths
                ?.map(unary(monthNameToShort))
                .join(', ')}
            </div>
          </div>
        </ServiceValue>
      </If>
      <If true={serviceType === 'RECURRING'}>
        <ServiceValue label='Apply service to' className='basis-1/2'>
          {servicePrice.applyServiceTo}
        </ServiceValue>
        <ServiceValue label='Units' className='basis-1/2'>
          {servicePrice.units}
        </ServiceValue>
        <ServiceValue label='Cycle' className='basis-1/2'>
          {cycleDefinition?.code} - {cycleDefinition?.description}
          <span className='text-sm'>
            {cycleDefinition?.includedMonths?.join(',')}
          </span>
        </ServiceValue>
      </If>
      <ServiceValue label='Disposition' className='basis-1/2'>
        {servicePrice.disposition && dispositionLabel(servicePrice.disposition)}
      </ServiceValue>
      <ServiceValue label='Base fee' className='basis-1/2'>
        {formatUSD(servicePrice.baseFee)}
      </ServiceValue>
      <div className='flex basis-full'>
        <ServiceValue label='Cost type' className='basis-1/2'>
          {servicePrice.costType && costTypeLabel(servicePrice.costType)}
        </ServiceValue>
        <If true={servicePrice.costType != 'NO_COST'}>
          <ServiceValue label='Cost' className='basis-1/2'>
            {formatUSD(servicePrice.costValue)}
          </ServiceValue>
        </If>
      </div>
      <ServiceValue label='Minimum fee' className='basis-1/2'>
        {formatUSD(servicePrice.minimumFee)}
      </ServiceValue>
      <ServiceValue label='Maximum fee' className='basis-1/2'>
        {formatUSD(servicePrice.maximumFee)}
      </ServiceValue>
    </div>
  )
}
