import { TabGroup, TabPanel, TabPanels } from '@headlessui/react'
import { PriceListsTable } from './ServicePriceListsTable'
import { PromotionsTable } from './ServicePromotionsTable'
import { AccountOverridesTable } from './ServiceAccountOverridesTable'
import { SegmentedTab, SegmentedTabList } from '@/components/SegmentedTabList'
import { useRoute } from '@/app/[bankId]/services/routing'
import { unwrap } from '@/lib/unions/unwrap'
import { components } from '@/api/schema'

export function PricingDifferences({
  serviceDetails,
}: {
  serviceDetails: components['schemas']['ServiceDetails']
}) {
  const {
    params: { serviceCode },
  } = unwrap(useRoute(), '/services/[effectiveDate]/view/[serviceCode]')!
  return (
    <div className='flex flex-col gap-4 rounded-xl border bg-white px-6 py-4'>
      <h3 className='text-xl font-bold text-app-color-primary'>
        Pricing exceptions
      </h3>

      <TabGroup className='flex flex-col gap-2'>
        <div className='flex flex-row gap-3'>
          <SegmentedTabList>
            <SegmentedTab>Price lists</SegmentedTab>
            <SegmentedTab>Promotions</SegmentedTab>
            <SegmentedTab>Account overrides</SegmentedTab>
          </SegmentedTabList>

          {/* TODO: AFIN-147 Implement Export button on Pricing differences view */}
          {/* <Link
            className='ml-auto font-semibold text-app-color-fg-brand-primary hover:underline'
            href='/#'
          >
            Export
          </Link> */}
        </div>

        <TabPanels>
          <TabPanel>
            <PriceListsTable serviceDetails={serviceDetails} />
          </TabPanel>
          <TabPanel>
            <PromotionsTable serviceCode={serviceCode ?? ''} />
          </TabPanel>
          <TabPanel>
            <AccountOverridesTable serviceCode={serviceCode ?? ''} />
          </TabPanel>
        </TabPanels>
      </TabGroup>
    </div>
  )
}
