import { components } from '@/api/schema'
import { query } from '@/app/[bankId]/services/queries'
import { useRoute } from '@/app/[bankId]/services/routing'
import { SortedTable } from '@/components/Table/SortedTable'
import { unwrap } from '@/lib/unions/unwrap'
import { useQuery } from '@tanstack/react-query'
import { ColumnDef } from '@tanstack/react-table'
import { useMemo, useState } from 'react'
import { Button } from '@/components/Button'
import { XMarkIcon } from '@heroicons/react/24/solid'
import { If } from '@/components/If'
import { ServicePricingDetails } from './ServicePricingDetails'
import {
  Disposition,
  dispositionLabel,
  PriceType,
  priceTypeLabel,
  ServiceType,
} from '@/api/zodSchemas'
import { formatUSD } from '@/lib/intlFormatters'
import { isNonNull } from '@/lib/guards/isNonNull'
import { ServiceValue } from './ServiceValue'
import { InfoSection, InfoSectionTitle } from '@/components/InfoSection'

type PriceListData = {
  priceList: components['schemas']['DemographicPriceList']
  servicePrice: components['schemas']['ServicePrice']
}

type PriceListsColumnDef = ColumnDef<PriceListData>

export function PriceListsTable({
  serviceDetails,
}: {
  serviceDetails: components['schemas']['ServiceDetails']
}) {
  const route = unwrap(
    useRoute(),
    '/services/[effectiveDate]/view/[serviceCode]',
  )!

  const [selectedRow, setSelectedRow] = useState<PriceListData | null>(null)

  const servicePrices = useQuery({
    ...query('/getServicePricesAsOf', {
      asOfDate: route.params.effectiveDate,
      serviceCodes: [serviceDetails.service.code],
      pricingHierarchyEntryType: 'PRICE_LIST',
    }),
    placeholderData: [],
  })

  const demographicPriceLists = useQuery({
    ...query('/getDemographicPriceLists', {
      effectiveDate: route.params.effectiveDate,
      codes: servicePrices.data!.map((sp) => sp.pricingHierarchyEntryCode),
    }),
    enabled: (servicePrices.data ?? []).length > 0,
    select: (data) => {
      const servicePricesByHierarchyEntryCode = (
        servicePrices.data ?? []
      ).reduce(
        (prev, curr) => ({
          ...prev,
          [curr.pricingHierarchyEntryCode]: curr,
        }),
        {} as Record<string, components['schemas']['ServicePrice']>,
      )

      return data.map<PriceListData>((priceList) => ({
        priceList,
        servicePrice: servicePricesByHierarchyEntryCode[priceList.code],
      }))
    },
  })

  const columns = useMemo<PriceListsColumnDef[]>(() => {
    return [
      {
        header: 'Price list name',
        accessorKey: 'priceList.name',
      },
      {
        header: 'Price list ID',
        accessorKey: 'priceList.code',
      },
      {
        header: 'Lead price list',
        accessorKey: 'priceList.isLeadPriceList',
        cell(props) {
          return props.getValue<boolean>() ? 'Yes' : 'No'
        },
      },
      {
        header: 'Price type',
        accessorKey: 'servicePrice.priceType',
        cell(props) {
          return priceTypeLabel(props.getValue<PriceType>())
        },
      },
      {
        header: 'Price',
        accessorKey: 'servicePrice.priceValue',
        cell(props) {
          return formatUSD(props.getValue<number | undefined>())
        },
      },
      {
        header: 'Disposition',
        accessorKey: 'servicePrice.disposition',
        cell(props) {
          return dispositionLabel(props.getValue<Disposition>())
        },
      },
    ]
  }, [])

  return (
    <>
      <div className='flex flex-row'>
        <div className='flex-1'>
          <SortedTable
            data={demographicPriceLists.data ?? []}
            columns={columns}
            onRowClick={(rowData) => {
              setSelectedRow(rowData.original)
            }}
          />
        </div>
        <If true={isNonNull(selectedRow)}>
          <div className='ml-4 w-1/3'>
            <InfoSection>
              <div className='flex flex-row justify-between'>
                <InfoSectionTitle>
                  {selectedRow?.priceList.name}
                </InfoSectionTitle>
                <Button
                  className='border-0'
                  onClick={() => {
                    setSelectedRow(null)
                  }}
                >
                  <XMarkIcon className='h-6 w-6' />
                </Button>
              </div>
              <div className='flex basis-full flex-wrap'>
                <ServiceValue label='Price list name' className='basis-1/2'>
                  {selectedRow?.priceList.name}
                </ServiceValue>
                <ServiceValue label='Price list ID' className='basis-1/2'>
                  {selectedRow?.priceList.code}
                </ServiceValue>
                <ServiceValue label='Lead price list' className='basis-1/2'>
                  {selectedRow?.priceList.isLeadPriceList ? 'Yes' : 'No'}
                </ServiceValue>
              </div>
              <ServicePricingDetails
                servicePrices={selectedRow ? [selectedRow.servicePrice] : []}
                serviceType={serviceDetails.service.serviceType as ServiceType}
              />
            </InfoSection>
          </div>
        </If>
      </div>
    </>
  )
}
