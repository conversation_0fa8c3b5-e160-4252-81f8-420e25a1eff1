import clsx from 'clsx'
import { parseServerFormat, toUIFormat } from '@/lib/date'
import { Badge } from '@/components/Badge'
import { compareAsc } from 'date-fns'

export function ServiceVersionTimeline({
  selectedVersion,
  versions,
}: {
  selectedVersion: string
  versions: string[]
}) {
  // To create the timeline:
  //
  // 1. Sort service versions from oldest to newest.
  // 2. Iterating from oldest to newest:
  //    a. Create a new TimelinePeriod object to represent the service version.
  //    b. Update the previous TimelinePeriod to give it an expiration date. This
  //    date is one day before the effectiveDate of the current TimelinePeriod.
  // 3. Sort the TimelinePeriods from newest to oldest.
  //
  const timeline: TimelinePeriod[] = versions
    .sort((v1, v2) => compareAsc(parseServerFormat(v1), parseServerFormat(v2)))
    .reduce((timeline, version, index) => {
      const previousService = timeline[timeline.length - 1]
      if (previousService) {
        previousService.expirationDate = version
      }
      return timeline.concat({
        effectiveDate: version,
        expirationDate: undefined,
      })
    }, [] as TimelinePeriod[])
    .reverse()

  return (
    <>
      {timeline.map((record, index) => (
        <TimelineItem
          key={index}
          isSelected={record.effectiveDate === selectedVersion}
          record={record}
          isLast={timeline.length - 1 === index}
          isFirst={index === 0}
          isExpired={false}
        />
      ))}
    </>
  )
}

interface TimelinePeriod {
  effectiveDate: string
  expirationDate?: string
}

function TimelineItem({
  record,
  isLast,
  isFirst,
  isSelected,
  isExpired,
}: React.PropsWithChildren<{
  record: TimelinePeriod
  isLast: boolean
  isFirst: boolean
  isSelected: boolean
  isExpired: boolean
}>) {
  return (
    <div className='flex min-h-20 gap-2'>
      <div className='relative flex min-w-10 flex-col items-center'>
        <div
          className={clsx(
            'absolute top-3 w-1 flex-auto bg-app-color-bg-brand-secondary',
            isLast ? 'h-3' : 'h-full',
            { 'top-2': isFirst },
          )}
        ></div>
        {isSelected ?
          <div className='z-10 mt-2 size-5 rounded-full border-4 border-app-color-bg-brand-secondary bg-app-color-bg-brand-solid'></div>
        : <div className='mt-3 size-3 rounded-full bg-app-color-bg-brand-secondary'></div>
        }
      </div>
      <div
        className={clsx(
          'flex flex-1 flex-col text-nowrap rounded-lg p-2 text-[15px]',
          {
            'bg-app-color-bg-brand-primary font-bold': isSelected,
          },
        )}
      >
        <div className='flex justify-between'>
          <span>Effective:</span>
          {isExpired && (
            <Badge type='error' className='text-xs font-medium'>
              Expired
            </Badge>
          )}
        </div>
        <span>
          {` ${toUIFormat(record.effectiveDate, 'yyyy-MM')} - ${
            record.expirationDate ?
              toUIFormat(record.expirationDate, 'yyyy-MM')
            : 'ongoing'
          }`}
        </span>
        <span className='text-xs font-semibold text-app-color-text-secondary'>
          {isSelected && 'Currently viewing'}
        </span>
      </div>
    </div>
  )
}
