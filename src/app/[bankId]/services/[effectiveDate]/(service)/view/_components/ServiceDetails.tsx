import {
  InfoSection,
  InfoSectionDescription,
  InfoSectionTitle,
} from '@/components/InfoSection'
import { Tooltip } from '@/components/Tooltip'
import { CheckIcon, QuestionMarkCircleIcon } from '@heroicons/react/24/outline'
import React from 'react'
import { toUIFormat } from '@/lib/date'
import clsx from 'clsx'
import { If } from '@/components/If'
import {
  baseBalanceTypeLabel,
  ServiceType,
  serviceTypeLabel,
} from '@/api/zodSchemas'
import { useServiceCatalog } from '../../../../_hooks/useServiceCatalog'
import { useRoute } from '@/app/[bankId]/services/routing'
import { unwrap } from '@/lib/unions/unwrap'
import { components } from '@/api/schema'
import { ServiceVersionTimeline } from './ServiceVersionTimeline'
import { ServicePricingDetails } from './ServicePricingDetails'
import { ServiceSet } from '@/app/[bankId]/services/_components/ServiceSet'

interface ServiceValueProps {
  label: string
  tooltip?: string
  className?: string
}

function ServiceValue({
  label,
  tooltip,
  className,
  children,
}: React.PropsWithChildren<ServiceValueProps>) {
  if (children == undefined) {
    return
  }

  return (
    <div className={clsx(`my-3 flex flex-col`, className)}>
      <span className='inline-flex text-sm text-app-color-secondary'>
        {label}
        {tooltip && (
          <Tooltip
            className='ml-1 size-4 self-center'
            topOffset={8}
            content={tooltip}
          >
            <QuestionMarkCircleIcon />
          </Tooltip>
        )}
      </span>
      <span className='inline-flex items-center text-[15px]/[22px] font-medium'>
        {children}
      </span>
    </div>
  )
}

interface ServiceDetailsProps {
  serviceDetails: components['schemas']['ServiceDetails']
  serviceVersions: string[]
}

export function ServiceDetails({
  serviceDetails,
  serviceVersions,
}: ServiceDetailsProps) {
  const route = unwrap(
    useRoute(),
    '/services/[effectiveDate]/view/[serviceCode]',
  )!

  const servicePrice = serviceDetails.servicePrice![0].servicePrice!
  const service = serviceDetails.service!
  const serviceSet = serviceDetails.servicesInServiceSet ?? []

  const catalogResult = useServiceCatalog({
    effectiveDate: route.params.effectiveDate,
  })

  if (catalogResult.status === 'error') {
    throw catalogResult.error
  }

  if (catalogResult.status !== 'success') {
    return 'Loading...'
  }

  return (
    <div className='mb-8 flex gap-2'>
      <div className='flex grow flex-col gap-3'>
        <InfoSection data-testid='serviceInformation'>
          <InfoSectionTitle>Service Information</InfoSectionTitle>
          <div className='flex flex-wrap'>
            <ServiceValue label='Effective date' className='basis-full'>
              {toUIFormat(service.effectiveDate!, 'yyyy-MM')}
            </ServiceValue>
            <ServiceValue label='Service category' className='basis-full'>
              {serviceDetails.serviceCategory?.name}
            </ServiceValue>
            <ServiceValue label='Service name' className='basis-1/2'>
              {service.description}
            </ServiceValue>
            <ServiceValue label='Service code' className='basis-1/2'>
              {service.code}
            </ServiceValue>
            <ServiceValue label='Service type' className='basis-1/2'>
              {serviceTypeLabel(service.serviceType as ServiceType)}
            </ServiceValue>
            <ServiceValue label='Balance type' className='basis-1/2'>
              {servicePrice.balanceType &&
                baseBalanceTypeLabel(servicePrice.balanceType)}
            </ServiceValue>
            <div className='flex basis-full'>
              {/* <If true={service ?? false}>
                <span className='mt-2 inline-flex basis-1/2 items-center font-medium'>
                  <CheckIcon className='size-6 pr-1' /> Subject of discount or
                  premium
                </span>
              </If> */}
              <If
                true={
                  servicePrice.includeReferenceInformationOnStatements ?? false
                }
              >
                <span className='mt-2 inline-flex basis-1/2 items-center font-medium'>
                  <CheckIcon className='size-6 pr-1' />
                  Display addenda information
                </span>
              </If>
            </div>
          </div>
        </InfoSection>
        <If true={service.serviceType === 'SERVICE_SET'}>
          <ServiceSet selectedServices={serviceSet} readonly />
        </If>
        <InfoSection data-testid='standardPricing'>
          <InfoSectionTitle>Standard pricing</InfoSectionTitle>
          <InfoSectionDescription>
            Default pricing for this service
          </InfoSectionDescription>
          <ServicePricingDetails
            servicePrices={serviceDetails.servicePrice.map(
              (price) => price.servicePrice!,
            )}
            serviceType={service.serviceType as ServiceType}
          ></ServicePricingDetails>
        </InfoSection>
        <If
          true={
            service.domesticAfpCode != undefined ||
            service.internalNote != undefined
          }
        >
          <InfoSection>
            <InfoSectionTitle>Other service information</InfoSectionTitle>
            <div className='flex flex-wrap'>
              <If true={service.domesticAfpCode != undefined}>
                <ServiceValue className='flex basis-full' label='AFP code'>
                  <div className='basis-1/2'>
                    Domestic code: {service.domesticAfpCode}
                  </div>
                  <div>Global code: {service.globalAfpCode}</div>
                </ServiceValue>
                <ServiceValue label='Internal note'>
                  {service.internalNote}
                </ServiceValue>
              </If>
            </div>
          </InfoSection>
        </If>
      </div>
      <InfoSection className='max-w-72' data-testid='serviceTimeline'>
        <div className='flex flex-col'>
          <InfoSectionTitle>Service timeline</InfoSectionTitle>
          <InfoSectionDescription>
            All changes on this services will be saved in the timeline.
          </InfoSectionDescription>
        </div>
        <div>
          <ServiceVersionTimeline
            selectedVersion={service.effectiveDate!}
            versions={serviceVersions}
          />
        </div>
      </InfoSection>
    </div>
  )
}
