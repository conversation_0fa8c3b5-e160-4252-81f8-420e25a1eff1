import { query } from '@/app/[bankId]/services/queries'
import { PromotionsItem } from '@/app/[bankId]/services/types'
import { SortedTable } from '@/components/Table/SortedTable'
import { useQuery } from '@tanstack/react-query'
import { ColumnDef } from '@tanstack/react-table'
import { useMemo } from 'react'

type PromotionsColumnDef = ColumnDef<PromotionsItem>

export function PromotionsTable({ serviceCode }: { serviceCode: string }) {
  const { data } = useQuery(query('/api/v1/service-promotions', serviceCode))
  const columns = useMemo<PromotionsColumnDef[]>(() => {
    return [
      {
        header: 'Price list name',
        accessorKey: 'name',
      },
      {
        header: 'Price list ID',
        accessorKey: 'id',
      },
      {
        header: 'Expiration date',
        accessorKey: 'expirationDate',
      },
      {
        header: 'Number of accounts',
        accessorKey: 'numberOfAccounts',
      },
      {
        header: 'Price Type',
        accessorKey: 'priceType',
      },
      {
        header: 'Price',
        accessorKey: 'price',
      },
      {
        header: 'Disposition',
        accessorKey: 'disposition',
      },
    ]
  }, [])
  const columnFilters = useMemo(() => [], [])

  return (
    <SortedTable
      data={data ?? []}
      columns={columns}
      columnFilters={columnFilters}
    />
  )
}
