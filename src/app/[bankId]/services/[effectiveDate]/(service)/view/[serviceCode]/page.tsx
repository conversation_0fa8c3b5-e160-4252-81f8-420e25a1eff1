'use client'

import { ServicesBreadcrumbs } from '@/app/[bankId]/services/_components/ServicesBreadcrumbs'
import { PricingDifferences } from '../_components/PricingDifferences'
import { ServiceDetails } from '../_components/ServiceDetails'
import { Tab, TabGroup, TabList, TabPanel, TabPanels } from '@headlessui/react'
import { Button } from '@/components/Button'
import Link from 'next/link'
import { useQuery } from '@tanstack/react-query'
import { routeTo, useRoute } from '@/app/[bankId]/services/routing'
import { query } from '@/app/[bankId]/services/queries'
import { unwrap } from '@/lib/unions/unwrap'
import { getEffectiveServiceVersion } from '../../getEffectiveServiceVersion'
import { formatToServerString } from '@/lib/date'
import { BreadcrumbSegment } from '@/components/Breadcrumbs/Breadcrumbs'
import { MonthPicker } from '@/components/Filter/MonthPicker'
import { useRouter } from 'next/navigation'

export default function ViewProduct() {
  const route = unwrap(
    useRoute(),
    '/services/[effectiveDate]/view/[serviceCode]',
  )!

  const router = useRouter()

  const serviceTimeline = useQuery(
    query('/getServiceTimeline', { code: route.params.serviceCode }),
  )

  const effectiveDateVersion = getEffectiveServiceVersion(
    route.query.version ?? route.params.effectiveDate,
    serviceTimeline.data ?? [],
  )

  const serviceDetailsAsOf = useQuery({
    ...query('/getServiceDetailsAsOf', {
      code: route.params.serviceCode,
      effectiveDate: effectiveDateVersion!,
    }),
    enabled: effectiveDateVersion != undefined,
  })

  const effectiveServiceVersion = useQuery({
    ...query('/getServiceDetailsAsOf', {
      code: route.params.serviceCode,
      effectiveDate:
        serviceTimeline.data ?
          serviceTimeline.data[serviceTimeline.data.length - 1]
        : effectiveDateVersion!,
    }),
    enabled: serviceTimeline.data != undefined,
  })

  if (!serviceDetailsAsOf.isSuccess || !effectiveServiceVersion.isSuccess) {
    return 'Loading...'
  }

  return (
    <>
      <header className='mx-8 mt-12 flex'>
        <ServicesBreadcrumbs
          categoryCode={
            effectiveServiceVersion.data.serviceCategory?.code ?? 'root'
          }
        >
          <BreadcrumbSegment href='#'>
            {effectiveServiceVersion.data.service?.description}
            <small className='ml-2 text-zinc-400'>
              {effectiveServiceVersion.data.service?.code}
            </small>
          </BreadcrumbSegment>
        </ServicesBreadcrumbs>
        <MonthPicker
          initialDate={route.params.effectiveDate}
          prefix='View data effective on'
          showIcon
          onDateChange={(pickedDate) => {
            router.push(
              routeTo(
                '/services/[effectiveDate]/view/[serviceCode]',
                {
                  ...route.params,
                  effectiveDate: formatToServerString(pickedDate),
                },
                { ...route.query, version: formatToServerString(pickedDate) },
              ),
            )
          }}
        />
        <div className='ml-auto flex gap-2'>
          <Link
            href={routeTo(
              '/services/[effectiveDate]/duplicate/[serviceCode]',
              route.params,
              {
                ...route.query,
                version: effectiveDateVersion,
              },
            )}
          >
            <Button className='btn h-10'>Duplicate</Button>
          </Link>
          {/* 

          Disabling the Delete, Archive, and Reactivate features 
          because they are not yet supported on the server side.

          <DeleteServiceModal service={service} />
          {latestServiceVersion.isExpired ?
            <ReactivateServiceModal service={latestServiceVersion} />
          : <ExpireServiceModal service={latestServiceVersion} />} */}
          <Link
            href={routeTo(
              '/services/[effectiveDate]/edit/[serviceCode]',
              route.params,
              {
                ...route.query,
                version: effectiveDateVersion,
              },
            )}
          >
            <Button className='btn-primary h-10'>Edit</Button>
          </Link>
        </div>
      </header>
      <div className='m-8 mb-0 flex flex-auto flex-col overflow-hidden'>
        <TabGroup className='flex min-h-0 flex-auto flex-col'>
          <TabList className='mb-4 flex flex-row gap-4'>
            {['Details', 'Pricing differences'].map((name) => (
              <Tab
                key={name}
                className='rounded-sm text-sm font-semibold text-app-color-secondary decoration-2 underline-offset-8 focus:outline-none data-[selected]:text-app-color-fg-brand-primary data-[hover]:underline data-[selected]:underline data-[focus]:outline-2 data-[focus]:outline-black'
              >
                {name}
              </Tab>
            ))}
          </TabList>
          <TabPanels className='flex min-h-0 flex-auto'>
            <TabPanel className='flex-auto overflow-scroll'>
              <ServiceDetails
                serviceDetails={serviceDetailsAsOf.data!}
                serviceVersions={serviceTimeline.data!}
              />
            </TabPanel>
            <TabPanel className='flex-auto overflow-scroll'>
              <PricingDifferences serviceDetails={serviceDetailsAsOf.data!} />
            </TabPanel>
          </TabPanels>
        </TabGroup>
      </div>
    </>
  )
}
