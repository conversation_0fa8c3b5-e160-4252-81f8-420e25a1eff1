'use client'

import {
  serviceFormToServerDataSchema,
  UpdateServiceForm,
} from '../../UpdateServiceForm'
import { toFormState } from '@/app/[bankId]/services/ServiceFormState'
import { useRouter } from 'next/navigation'
import { But<PERSON> } from '@/components/Button'
import Link from 'next/link'
import { query } from '@/app/[bankId]/services/queries'
import { routeTo, useRoute } from '@/app/[bankId]/services/routing'
import { unwrap } from '@/lib/unions/unwrap'
import { serviceMutation } from '../../mutations'
import { useMutation, useQuery } from '@tanstack/react-query'

export default function EditServicePage() {
  const router = useRouter()
  const route = unwrap(
    useRoute(),
    '/services/[effectiveDate]/edit/[serviceCode]',
  )!

  const updateService = useMutation(
    serviceMutation('/updateService', {
      // When the service is updated, it may create a new version with a new
      // effective date -- we must route to this new version.
      onSuccess: (_, request) => {
        router.push(
          routeTo(
            '/services/[effectiveDate]/view/[serviceCode]',
            route.params,
            {
              ...route.query,
              version: request.service?.effectiveDate,
            },
          ),
        )
      },
    }),
  )

  const serviceDetailsAsOf = useQuery({
    ...query('/getServiceDetailsAsOf', {
      code: route.params.serviceCode,
      effectiveDate: route.query.version!,
    }),
  })

  if (!route.query.version) {
    return 'A Service version must be specified via the "version" query parameter.'
  }

  if (!serviceDetailsAsOf.isSuccess) {
    return 'Loading...'
  }

  return (
    <>
      <header className='mx-8 mt-12 text-2xl font-medium'>Edit service</header>
      <main className='flex min-h-0 flex-auto'>
        <UpdateServiceForm
          defaultValues={toFormState({
            serverService: serviceDetailsAsOf.data.service!,
            serviceCategoryCode: serviceDetailsAsOf.data.serviceCategory?.code,
            servicesInServiceSet:
              serviceDetailsAsOf.data.servicesInServiceSet ?? [],
            servicePrices:
              serviceDetailsAsOf.data.servicePrice?.map(
                (s) => s.servicePrice!,
              ) ?? [],
          })}
          onSubmit={(state) => {
            const result = serviceFormToServerDataSchema.safeParse(state)

            if (result.success) {
              updateService.mutate(result.data)
            } else {
              console.error(result.error)
            }
          }}
        >
          <Link
            href={routeTo(
              '/services/[effectiveDate]/view/[serviceCode]',
              route.params,
              route.query,
            )}
          >
            <Button className='btn w-60'>Cancel</Button>
          </Link>
          <Button type='submit' className='btn-primary w-60'>
            Save
          </Button>
        </UpdateServiceForm>
      </main>
    </>
  )
}
