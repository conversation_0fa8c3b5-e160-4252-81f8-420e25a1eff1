import { DatePickerPageObject } from '@/components/_testing/DatePickerPageObject'
import { Page } from '@playwright/test'

export class ExpireServiceModalPageObject {
  constructor(readonly page: Page) {}

  async expireService() {
    const expireBtn = this.page.getByRole('button', {
      name: 'Expire service',
    })
    await expireBtn.click()
    await expireBtn.waitFor({ state: 'hidden' })
  }

  async openExpireDate() {
    await this.page.getByLabel('Select the service expire').click()
    return new DatePickerPageObject(this.page)
  }
}
