import { routeTo } from '@/app/[bankId]/services/routing'
import { Page, Locator } from '@playwright/test'
import { createPageObjectProvider } from '../../../../../../../test/e2e/createPageObjectProvider'
import { DeleteServiceModalPageObject } from './DeleteServiceModalPageObject'
import { ExpireServiceModalPageObject } from './ExpireServiceModalPageObject'
import { ReactivateModalPageObject } from './ReactivateModalPageObject'
import { UpdateServicePageObject } from './UpdateServicePageObject'

export class ServiceDetailsPageObject {
  constructor(readonly page: Page) {}

  async goto({
    serviceCode,
    effectiveDate,
    version,
    column,
    view,
  }: {
    serviceCode: string
    version: string
    effectiveDate?: string
    column?: number
    view?: 'column' | 'list'
  }) {
    await this.page.goto('/api/v1/resetCatalog')
    await this.page.goto(
      routeTo(
        '/services/[effectiveDate]/view/[serviceCode]',
        {
          bankId: '999',
          serviceCode,
          effectiveDate: effectiveDate ?? '2025-01-01',
        },
        {
          column: column ?? 0,
          view: view ?? 'column',
          version,
        },
      ),
    )
  }

  static get provider() {
    return createPageObjectProvider(
      (page) => new ServiceDetailsPageObject(page),
    )
  }

  getButton(name: 'Expire' | 'Reactivate') {
    return this.page.getByRole('button', { name })
  }

  async delete() {
    await this.page.getByRole('button', { name: 'Delete' }).click()
    return new DeleteServiceModalPageObject(this.page)
  }

  async edit() {
    await this.page.getByRole('button', { name: 'Edit' }).click()
    await this.page.waitForURL('**/edit/**')
    await this.page.getByText('Edit service').waitFor({ state: 'visible' })
    return new UpdateServicePageObject(this.page)
  }

  async duplicate() {
    await this.page.getByRole('button', { name: 'Duplicate' }).click()
    return new UpdateServicePageObject(this.page)
  }

  async expire() {
    await this.getButton('Expire').click()
    return new ExpireServiceModalPageObject(this.page)
  }

  async reactivate() {
    await this.getButton('Reactivate').click()
    return new ReactivateModalPageObject(this.page)
  }

  async waitForDetailsTab() {
    await this.page.waitForURL('**/view/**')
    await this.page.getByLabel('Details').waitFor({ state: 'visible' })
  }

  getServiceTimeline() {
    return this.page.getByTestId('serviceTimeline')
  }

  getServiceInformation(...params: Parameters<Locator['getByText']>) {
    return this.page.getByTestId('serviceInformation').getByText(...params)
  }

  getStandardPricing(...params: Parameters<Locator['getByText']>) {
    return this.page.getByTestId('standardPricing').getByText(...params)
  }
}
