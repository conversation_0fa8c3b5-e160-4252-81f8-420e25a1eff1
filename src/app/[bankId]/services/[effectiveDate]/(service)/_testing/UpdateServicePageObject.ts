import { routeTo } from '@/app/[bankId]/services/routing'
import { Union, Tag, Data } from '@/lib/unions/Union'
import { Page } from '@playwright/test'
import { createPageObjectProvider } from '../../../../../../../test/e2e/createPageObjectProvider'
import { ServiceDetailsPageObject } from './ServiceDetailsPageObject'
import { DispositionLabel, PriceTypeLabel } from '@/api/zodSchemas'

type SelectComponentVariations =
  | Union<'Price type', PriceTypeLabel>
  | Union<'Disposition', DispositionLabel>
  | Union<'Service category', string>

export class UpdateServicePageObject {
  constructor(readonly page: Page) {}

  static get provider() {
    return createPageObjectProvider((page) => new UpdateServicePageObject(page))
  }

  async goto({
    parentCategoryCode,
    effectiveDate,
    column,
    view,
  }: {
    parentCategoryCode: string
    effectiveDate?: string
    column?: number
    view?: 'column' | 'list'
  }) {
    await this.page.goto('/api/v1/resetCatalog')
    return this.page.goto(
      routeTo(
        '/services/[effectiveDate]/add/[parentCategoryCode]',
        {
          bankId: '999',
          parentCategoryCode,
          effectiveDate: effectiveDate ?? '2024-01-01',
        },
        { view: view ?? 'column', column: column ?? 0 },
      ),
    )
  }

  fill(name: 'Service name' | 'Service code', value: string) {
    return this.page.getByLabel(name).fill(value)
  }

  async selectFrom<TLabel extends Tag<SelectComponentVariations>>(
    label: TLabel,
    value: Data<SelectComponentVariations, TLabel>,
  ) {
    await this.page.getByLabel(label).click()
    await this.page.getByRole('option', { name: value }).click()
  }

  async confirm(type: 'Save' | 'Create') {
    await this.page.getByRole('button', { name: type }).click()
    await this.page.waitForURL('**/view/**')
    await this.page.getByLabel('Details').waitFor({ state: 'visible' })
    return new ServiceDetailsPageObject(this.page)
  }
}
