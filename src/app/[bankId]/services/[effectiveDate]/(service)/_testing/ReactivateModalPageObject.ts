import { DatePickerPageObject } from '@/components/_testing/DatePickerPageObject'
import { Page } from '@playwright/test'

export class ReactivateModalPageObject {
  constructor(readonly page: Page) {}

  async selectDay(
    field: 'Select the service effective date' | 'Expiration date',
    day: number,
  ) {
    const datePicker =
      field === 'Expiration date' ?
        await this.openExpirationDatePicker()
      : await this.openEffectiveDatePicker()
    await datePicker.selectDay(day)
    await datePicker.apply()
  }

  async activateService() {
    const activateBtn = this.page.getByRole('button', {
      name: 'Activate service',
    })
    await activateBtn.click()
    await activateBtn.waitFor({ state: 'hidden' })
  }

  async openEffectiveDatePicker() {
    await this.page
      .getByLabel('Select the service effective date', { exact: true })
      .click()
    return new DatePickerPageObject(this.page)
  }

  async toggleHasExpirationDate() {
    await this.page
      .getByLabel('This service has an expiration date', { exact: true })
      .click()
  }

  async openExpirationDatePicker() {
    await this.page.getByLabel('Expiration date', { exact: true }).click()
    return new DatePickerPageObject(this.page)
  }
}
