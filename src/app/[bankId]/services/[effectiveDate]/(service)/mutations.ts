import { query } from '@/app/[bankId]/services/queries'
import { compose } from '@/lib/functional/compose'
import { withMetrics } from '@/lib/middleware/withMetrics'
import { defineMutations, defineMutation } from '@/lib/state/defineMutation'
import { notifications } from './notifications'
import { withLogOnError } from '@/lib/middleware/withLogOnError'
import { revenueConnectClient } from '@/api/revenueConnectClient'

const createService = defineMutation(revenueConnectClient, '/addService', {
  invalidate: (request) => [
    { queryKey: ['/getServiceCatalogMapping'] },
    query('/getServicePricesAsOf', { asOfDate: request.service.effectiveDate }),
    { queryKey: ['/getServiceTimeline'] },
  ],
})

const updateService = defineMutation(revenueConnectClient, '/updateService', {
  invalidate: (request) => {
    return [
      { queryKey: ['/getServiceCatalogMapping'] },
      query('/getServicePricesAsOf', {
        asOfDate: request.service.effectiveDate,
      }),
      query('/getServiceDetailsAsOf', {
        code: request.service?.code,
        effectiveDate: request.service?.effectiveDate,
      }),
      { queryKey: ['/getServiceTimeline'] },
    ]
  },
})

export type ServiceMutation = typeof createService | typeof updateService

export const serviceMutation = defineMutations(
  [createService, updateService],
  compose(notifications, withMetrics(), withLogOnError()),
)
