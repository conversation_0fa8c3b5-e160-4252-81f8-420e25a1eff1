import { isDefined } from '@/lib/guards/isDefined'
import {
  CatalogItem,
  Category,
  CategoryWithContents,
  ServiceSummary,
} from '../../types'
import { Catalog, CatalogService } from '../../_hooks/useServiceCatalog'

export function isCategory(item: CatalogItem): item is Category {
  return item.type === 'category'
}

export function isService(item: CatalogItem): item is ServiceSummary {
  return item.type === 'service'
}

export function getCategoryNested(
  catalog: Catalog,
  categoryCode: string,
  depth: 1 | 'Infinity' = 1,
): CategoryWithContents | undefined {
  if (!categoryCode) {
    return undefined
  }

  const category = catalog.categoryByCode[categoryCode]

  if (!category) {
    return undefined
  }

  const nestedServices = category.serviceCodes.map(
    (code) => catalog.serviceByCode[code],
  )

  const nestedCategories = category.categoryCodes.map(
    (code) => catalog.categoryByCode[code],
  )

  return {
    ...category,
    services: nestedServices,
    categories:
      depth === 1 ?
        nestedCategories.map((cat) => ({
          ...cat,
          categories: [],
          services: [],
        }))
      : nestedCategories
          .map((cat) => getCategoryNested(catalog, cat.current.code!, depth))
          .filter(isDefined),
  }
}

export function forEachService(
  catalog: Catalog,
  code: string,
  visitService: (service: CatalogService) => void,
) {
  const categories = [code]

  while (categories.length > 0) {
    const current = catalog.categoryByCode[categories.pop()!]

    if (current) {
      current.serviceCodes.forEach((serviceCode) =>
        visitService(catalog.serviceByCode[serviceCode]),
      )
      categories.push(...current.categoryCodes)
    }
  }
}
