import { Locator, <PERSON> } from '@playwright/test'

export class CategoryMenuPageObject {
  testId = 'categoryMenu'
  private readonly locator: Locator

  constructor(private readonly page: Page) {
    this.locator = this.page.getByTestId(this.testId)
  }

  open() {
    return this.locator.getByRole('button', { name: 'Open' }).click()
  }

  edit() {
    return this.locator.getByRole('button', { name: 'Edit' }).click()
  }

  delete() {
    return this.locateDeleteButton().click()
  }

  async deleteIsDisabled() {
    return this.locateDeleteButton().isDisabled()
  }

  hoverDeleteButton() {
    return this.locateDeleteButton().hover()
  }

  private locateDeleteButton() {
    return this.locator.getByRole('button', { name: 'Delete' })
  }
}
