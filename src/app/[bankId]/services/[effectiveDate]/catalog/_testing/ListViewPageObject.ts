import { expect, Page } from '@playwright/test'
import { CategoryMenuPageObject } from './CategoryMenuPageObject'
import { DeleteCategoryModalPageObject } from './DeleteCategoryModalPageObject'

export class ListViewPageObject {
  constructor(readonly page: Page) {}

  async openCategoryMenu(categoryName: string) {
    const row = await this.page.getByRole('row', { name: categoryName })
    await row.getByRole('button').click()
    return new CategoryMenuPageObject(this.page)
  }

  async deleteCategory(categoryName: string) {
    const menu = await this.openCategoryMenu(categoryName)
    await menu.delete()
    await new DeleteCategoryModalPageObject(this.page).yesDelete()
  }

  async viewAllNestedProducts() {
    await this.page
      .getByRole('button', { name: 'Services and categories' })
      .click()
    await this.page
      .getByRole('option', { name: 'Services only (all nested)' })
      .click()
  }

  getColumnHeader(name: string) {
    return this.page.getByRole('columnheader', { name })
  }

  getRowByIndex(index: number) {
    // add one to the index to skip the header.
    return this.page.getByRole('row').nth(index + 1)
  }

  getRowAndCellByIndex(row: number, cell: number) {
    return this.getRowByIndex(row).getByRole('cell').nth(cell)
  }

  async getNumVisibleServices(): Promise<number | undefined> {
    const text = await this.numVisibleServices().textContent()
    if (text) return parseInt(text.split(' ')[0])
  }

  numVisibleServices() {
    return this.page.getByText(/[0-9]+ services/)
  }

  async selectCategory(name: string) {
    await this.page.getByRole('link', { name }).click()
    // Wait for the navigation to complete by checking for visibility of the
    // selected category in the breadcrumbs.
    await expect(
      this.page
        .getByRole('navigation')
        .filter({ has: this.page.getByRole('link', { name }) }),
    ).toBeVisible()
  }

  async filter(filter: string, values: string[]) {
    await this.page.getByRole('button', { name: filter }).click()
    for (const value of values) {
      await this.page.getByLabel(value).click()
    }
    await this.page.getByRole('button', { name: 'Apply' }).click()
  }
}
