import { routeTo } from '@/app/[bankId]/services/routing'
import { Union, Tag, Data } from '@/lib/unions/Union'
import { Page } from '@playwright/test'
import { createPageObjectProvider } from '../../../../../../../test/e2e/createPageObjectProvider'
import { ColumnViewPageObject } from './ColumnViewPageObject'
import { ListViewPageObject } from './ListViewPageObject'
import { AddEditCategoryModalPageObject } from './AddEditCategoryModalPageObject'

type ViewType =
  | Union<'list', ListViewPageObject>
  | Union<'column', ColumnViewPageObject>

export class ServiceCatalogPageObject {
  constructor(readonly page: Page) {}

  static get provider() {
    return createPageObjectProvider(
      (page) => new ServiceCatalogPageObject(page),
    )
  }

  async goto(view: Tag<ViewType>, effectiveDate = '2024-10-25') {
    await this.page.goto('/api/v1/resetCatalog')
    return this.page.goto(
      routeTo(
        '/services/[effectiveDate]/catalog/[categoryCode]',
        { bankId: '999', effectiveDate, categoryCode: 'root' },
        { view, column: undefined },
      ),
    )
  }

  getView<T extends Tag<ViewType>>(type: T): Data<ViewType, T> {
    return (
      type === 'list' ?
        new ListViewPageObject(this.page)
      : new ColumnViewPageObject(this.page)) as Data<ViewType, T>
  }

  getNotification(text: string) {
    return this.page.getByTestId('notifications').getByText(text)
  }

  async addCategory(data?: { name: string; code: string }) {
    await this.page.getByRole('button', { name: 'Add category' }).click()
    const modal = new AddEditCategoryModalPageObject(this.page)
    await modal.isVisible()
    if (!data) return modal
    await modal.categoryName().click()
    await modal.categoryName().fill(data.name)
    await modal.categoryCode().click()
    await modal.categoryCode().fill(data.code)
    await modal.add()
    return modal
  }
}
