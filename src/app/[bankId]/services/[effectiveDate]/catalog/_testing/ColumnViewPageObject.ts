import { expect, Page } from '@playwright/test'
import { CategoryMenuPageObject } from './CategoryMenuPageObject'
import { DeleteCategoryModalPageObject } from './DeleteCategoryModalPageObject'
import { AddEditCategoryModalPageObject } from './AddEditCategoryModalPageObject'
import { createPageObjectProvider } from '../../../../../../../test/e2e/createPageObjectProvider'

export class ColumnViewPageObject {
  constructor(readonly page: Page) {}

  static get provider() {
    return createPageObjectProvider((page) => new ColumnViewPageObject(page))
  }

  async openCategoryMenu(column: 0 | 1 | 2, categoryName: string) {
    await this.column(column)
      .getByRole('link', { name: categoryName })
      .getByRole('button')
      .click()

    return new CategoryMenuPageObject(this.page)
  }

  async deleteCategory(column: 0 | 1 | 2, categoryName: string) {
    const categoryMenu = await this.openCategoryMenu(column, categoryName)
    await categoryMenu.delete()

    await new DeleteCategoryModalPageObject(this.page).yesDelete()
  }

  column(index: 0 | 1 | 2) {
    return this.page.getByTestId('column').nth(index)
  }

  columnLinks(index: 0 | 1 | 2) {
    return this.column(index).getByRole('link')
  }

  columnCounts(
    index: 0 | 1 | 2,
    { categories, products }: { categories?: number; products?: number },
  ) {
    const categoryText =
      (categories ?? 0) > 0 ?
        `${categories} ${categories === 1 ? 'category' : 'categories'}`
      : ''
    const productText =
      (products ?? 0) > 0 ?
        `${products} ${products === 1 ? 'service' : 'services'}`
      : ''
    const text = [categoryText, productText].join(', ').replace(/,\s$/, '')
    return this.column(index).getByText(text)
  }

  async columnHasContents(index: 0 | 1 | 2) {
    return expect(this.columnLinks(index).first()).toBeVisible()
  }

  async gotoCategory(name: string) {
    return this.page.getByRole('link', { name }).click()
  }

  async editCategory(
    column: 0 | 1 | 2,
    categoryName: string,
    data?: { name: string; code: string },
  ) {
    await this.column(column)
      .getByRole('link', { name: categoryName })
      .getByRole('button')
      .click()
    await new CategoryMenuPageObject(this.page).edit()
    const modal = new AddEditCategoryModalPageObject(this.page)
    await modal.isVisible()
    if (!data) return modal
    await modal.categoryName().click()
    await modal.categoryName().fill(data.name)
    await modal.save()
    return modal
  }

  getNotification(text: string) {
    return this.page.getByTestId('notifications').getByText(text)
  }
}
