import { Locator, Page, expect } from '@playwright/test'

export class AddEditCategoryModalPageObject {
  testId = 'categoryModal'
  readonly locator: Locator

  constructor(page: Page) {
    this.locator = page.getByTestId(this.testId)
  }

  async isVisible() {
    await expect(this.locator).toBeVisible()
  }

  categoryName() {
    return this.locator.getByLabel('Category name')
  }

  categoryCode() {
    return this.locator.getByLabel('Category code')
  }

  x() {
    return this.locator
      .getByRole('heading', { name: 'Add a product category' })
      .getByRole('button')
      .click()
  }

  cancel() {
    return this.locator.getByRole('button', { name: 'Cancel' }).click()
  }

  add() {
    return this.locator.getByRole('button', { name: 'Add' }).click()
  }

  save() {
    return this.locator.getByRole('button', { name: 'Save' }).click()
  }
}
