import { defineMutation, defineMutations } from '@/lib/state/defineMutation'
import { compose } from '@/lib/functional/compose'
import { notifications } from './notifications'
import { withMetrics } from '@/lib/middleware/withMetrics'
import { withLogOnError } from '@/lib/middleware/withLogOnError'
import { revenueConnectClient } from '@/api/revenueConnectClient'

const createCategory = defineMutation(
  revenueConnectClient,
  '/addServiceCategory',
  {
    invalidate: [{ queryKey: ['/getServiceCatalogMapping'] }],
  },
)

const updateCategory = defineMutation(
  revenueConnectClient,
  '/updateServiceCategory',
  {
    invalidate: [{ queryKey: ['/getServiceCatalogMapping'] }],
  },
)

export type CategoryMutation = typeof createCategory | typeof updateCategory

export const categoryMutation = defineMutations(
  [updateCategory, createCategory],
  compose(notifications, withMetrics(), withLogOnError()),
)
