### Services Catalog -- Column View

`/services/catalog/[categoryId]`

Given the categoryId in the route, we can construct a path from the `root`
category to the desired category.

For example, if we navigate to `/services/catalog/D`, we may have a path:

`[root, category A, category B, category C, category D]`

We only want to render three columns -- these will display the contents of
the three "deepest" categories in the path.

It is also convenient to keep track of which column should render the contents
of the deepest-most category -- to avoid jarring visual changes, sometimes
we want to control which column displays the contents of a newly-selected
category. Columns are zero-indexed.

Navigation looks like the following:

```
              catalog/root
    ┌───────────┬────────────┬───────────┐
    │           │            │           │
    │   ROOT    │            │           │
    │           │            │           │
    │           │            │           │
    │           │            │           │
    │           │            │           │
    │           │            │           │
    │           │            │           │
    │           │            │           │
    │           │            │           │
    └───────────┴────────────┴───────────┘
```

When a category is selected, its contents are displayed in the next column to
the right. The path now looks like:

`[root, category A]`

```
            catalog/A?column=1
    ┌───────────┬────────────┬───────────┐
    │           │            │           │
    │   ROOT    │     A      │           │
    │           │            │           │
    │           │            │           │
    │           │            │           │
    │           │            │           │
    │           │            │           │
    │           │            │           │
    │           │            │           │
    │           │            │           │
    └───────────┴────────────┴───────────┘
```

Now our path looks like:

`[root, category A, category B]`

```
              catalog/B?column=2
    ┌───────────┬────────────┬───────────┐
    │           │            │           │
    │   ROOT    │      A     │     B     │
    │           │            │           │
    │           │            │           │
    │           │            │           │
    │           │            │           │
    │           │            │           │
    │           │            │           │
    │           │            │           │
    │           │            │           │
    └───────────┴────────────┴───────────┘
```

Now the path contains more than three categories, but we only render the "deepest"
three.

`[root, category A, category B, category C]`

```
              catalog/C?column=2
    ┌───────────┬────────────┬───────────┐
    │           │            │           │
    │     A     │      B     │     C     │
    │           │            │           │
    │           │            │           │
    │           │            │           │
    │           │            │           │
    │           │            │           │
    │           │            │           │
    │           │            │           │
    │           │            │           │
    └───────────┴────────────┴───────────┘

              catalog/D?column=2
    ┌───────────┬────────────┬───────────┐
    │           │            │           │
    │     B     │      C     │     D     │
    │           │            │           │
    │           │            │           │
    │           │            │           │
    │           │            │           │
    │           │            │           │
    │           │            │           │
    │           │            │           │
    │           │            │           │
    └───────────┴────────────┴───────────┘
```

In the above example, the user has selected category C, which is contained in
category B. If the user selects a different child of B, we want to render
that child in the same column which had previously rendered category C.

```
           catalog/E?column=1
    ┌───────────┬────────────┬───────────┐
    │           │            │           │
    │     B     │      E     │           │
    │           │            │           │
    │           │            │           │
    │           │            │           │
    │           │            │           │
    │           │            │           │
    │           │            │           │
    │           │            │           │
    │           │            │           │
    └───────────┴────────────┴───────────┘
```

This previous a jarring visual change which would occur if E was rendered
in the right-most column.
