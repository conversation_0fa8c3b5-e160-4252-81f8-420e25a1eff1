import { useCallback, useMemo } from 'react'
import { ColumnDef, ColumnFiltersState, Row } from '@tanstack/react-table'
import { DocumentTextIcon, FolderIcon } from '@heroicons/react/24/outline'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { forEachService, getCategoryNested } from '../catalog'
import { SortedTable } from '@/components/Table/SortedTable'
import { ToggleView } from '../_components/ToggleView'
import { ProductNestedViewOptions } from './page'
import { isDefined } from '@/lib/guards/isDefined'
import { routeTo, useRoute } from '@/app/[bankId]/services/routing'
import { ServicePopover } from '../_components/ServicePopover'
import { BreadcrumbTooltip } from '../_components/BreadcrumbsTooltip'
import { useSelectedCategory } from '../../../_hooks/useSelectedCategory'
import { unwrap } from '@/lib/unions/unwrap'
import { arrayFilterFn } from '@/app/[bankId]/hooks/useFilterState'
import { priceTypeLabel, ServiceType, serviceTypeLabel } from '@/api/zodSchemas'
import {
  CatalogCategory,
  isCategory,
  CatalogService,
  useServiceCatalog,
} from '../../../_hooks/useServiceCatalog'
import { CategoryPopover } from '../_components/CategoryPopover'
import { useServicePrices } from '../../../_hooks/useServicePrices'

export function ListView({
  columnFilters,
  productNestedView,
  onVisibleItemsChanged,
}: {
  columnFilters: ColumnFiltersState
  productNestedView: ProductNestedViewOptions
  onVisibleItemsChanged: (numVisible: {
    services: number
    categories: number
  }) => void
}) {
  const route = unwrap(
    useRoute(),
    '/services/[effectiveDate]/catalog/[categoryCode]',
  )!

  const effectiveDate = route.params.effectiveDate
  const servicePrices = useServicePrices({ effectiveDate })
  const { selectedCategory } = useSelectedCategory({ effectiveDate })

  const nestedCategory = useCategoryNested({
    code: selectedCategory?.current.code,
    effectiveDate,
  })

  const allServices = useAllServices({
    code: selectedCategory?.current.code,
    effectiveDate,
  })

  const router = useRouter()

  const href = useCallback(
    (item?: CatalogService | CatalogCategory) =>
      routeTo(
        '/services/[effectiveDate]/catalog/[categoryCode]',
        {
          ...route.params,
          categoryCode: item && isCategory(item) ? item.current.code! : 'root',
        },
        { view: 'list', column: undefined },
      ),
    [route],
  )

  // Conditional types distribute over unions -- this gets the value types of each member of a union
  // and returns a union of those value types. Simple `keyof` does not distribute over unions, so
  // it would only return the keys of the union type itself, i.e. the intersection of union member keys.
  type ValuesInUnion<T> = T extends T ? T[keyof T] : never
  type CatalogColumnDef = ColumnDef<
    CatalogService | CatalogCategory,
    ValuesInUnion<CatalogService | CatalogCategory | string | ServiceType>
  >

  const columns = useMemo<CatalogColumnDef[]>(() => {
    const columnsDef: CatalogColumnDef[] = [
      {
        header: 'Name',
        accessorKey: 'name',
        accessorFn: (original) =>
          isCategory(original) ?
            original.current.name!
          : original.current.description!,
        cell: ({ row }) => {
          const catalogItem = row.original
          const isService = !isCategory(catalogItem)

          const value =
            isService ?
              catalogItem.current.description
            : catalogItem.current.name

          return (
            <div className='flex flex-auto items-center'>
              {isService ?
                <DocumentTextIcon className='h-5 w-5 text-indigo-500' />
              : <FolderIcon className='h-5 w-5' />}
              {isService ?
                <>
                  <span className='px-3'>{value}</span>
                </>
              : <Link
                  href={href(catalogItem)}
                  className='hover:text-indigo-800 hover:underline'
                >
                  <span className='px-3'>{value}</span>
                </Link>
              }
            </div>
          )
        },
      },
      {
        header: 'Code',
        id: 'code',
        cell: ({ row }) => {
          return row.original.current.code
        },
      },
      {
        header: 'Service type',
        id: 'serviceType',
        accessorFn: (original) => {
          return isCategory(original) ? '' : original.current.serviceType!
        },
        cell: ({ row }) => {
          const value = row.original
          return value != null && !isCategory(value) ?
              serviceTypeLabel(value.current.serviceType! as ServiceType)
            : '-'
        },
        filterFn: arrayFilterFn,
      },
      {
        header: 'Price type',
        id: 'priceType',
        accessorFn: (original) =>
          isCategory(original) || servicePrices.data == null ?
            ''
          : servicePrices.data[original.current.code!]?.priceType!,
        cell: ({ row }) => {
          const value = row.original
          return (
              value != null && !isCategory(value) && servicePrices.data != null
            ) ?
              priceTypeLabel(
                servicePrices.data[value.current.code!]?.priceType!,
              )
            : '-'
        },
        filterFn: arrayFilterFn,
      },
    ]

    if (productNestedView === 'Services only (all nested)') {
      columnsDef.push({
        header: 'Category',
        accessorKey: 'category',
        cell: ({ row }) => {
          return <BreadcrumbTooltip code={row.original.parent?.code} />
        },
      })
    }

    columnsDef.push({
      header: () => (
        <div className='flex text-black'>
          <ToggleView />
        </div>
      ),
      accessorKey: 'none',
      cell: (cellData) =>
        isCategory(cellData.row.original) ?
          <CategoryPopover className='pr-4' category={cellData.row.original} />
        : <ServicePopover
            className='pr-4'
            serviceCode={cellData.row.original.current.code!}
            serviceEffectiveDate={cellData.row.original.current.effectiveDate!}
          />,
      meta: { className: 'basis-28 shrink-0 justify-end px-2' },
    })
    return columnsDef
  }, [href, productNestedView, servicePrices])

  const handleRowDoubleClick = (row: Row<CatalogService | CatalogCategory>) => {
    if (isCategory(row.original)) {
      return
    }
    router.push(href(row.original))
  }

  const onVisibleRowsChanged = (
    rows: Row<CatalogService | CatalogCategory>[],
  ) => {
    let services = 0
    let categories = 0
    for (const { original } of rows) {
      if (isCategory(original)) categories++
      else services++
    }
    onVisibleItemsChanged({ services, categories })
  }

  const dataToDisplay =
    (
      productNestedView !== 'Services only (all nested)' &&
      isDefined(nestedCategory)
    ) ?
      [...nestedCategory.categories, ...nestedCategory.services]
    : allServices

  if (!servicePrices.isSuccess) {
    return 'Loading...'
  }

  return (
    <SortedTable
      data={dataToDisplay}
      columns={columns}
      columnFilters={columnFilters}
      handleRowDoubleClick={handleRowDoubleClick}
      onVisibleRowsChanged={onVisibleRowsChanged}
    />
  )
}

function useCategoryNested({
  effectiveDate,
  code,
}: {
  effectiveDate: string
  code?: string
}) {
  const catalog = useServiceCatalog({ effectiveDate })

  return useMemo(() => {
    if (!catalog.data || !code) {
      return
    }

    return getCategoryNested(catalog.data, code)
  }, [catalog, code])
}

function useAllServices({
  effectiveDate,
  code,
}: {
  effectiveDate: string
  code?: string
}) {
  const catalog = useServiceCatalog({ effectiveDate })

  return useMemo(() => {
    if (!catalog.data || !code) {
      return []
    }

    const result: CatalogService[] = []

    forEachService(catalog.data, code, (service) => {
      result.push(service)
    })

    return result
  }, [catalog, code])
}
