'use client'

import {
  ChevronLeftIcon,
  DocumentTextIcon,
  FolderIcon,
} from '@heroicons/react/24/outline'
import { FolderOpenIcon } from '@heroicons/react/24/solid'
import Link from 'next/link'
import { getCategoryNested } from '../catalog'
import {
  Column,
  ColumnFooter,
  ColumnItem,
  Columns,
  ColumnsBody,
  ColumnSection,
  ColumnSectionDivider,
  ColumnsHeader,
} from '@/components/Columns'
import { ServicePopover } from '../_components/ServicePopover'
import { ToggleView } from '../_components/ToggleView'
import { useRoute, routeTo } from '@/app/[bankId]/services/routing'
import { useMemo } from 'react'
import { CategoryWithContents } from '../../../types'
import { useSelectedCategory } from '../../../_hooks/useSelectedCategory'
import { unwrap } from '@/lib/unions/unwrap'
import {
  CatalogCategory,
  useServiceCatalog,
} from '../../../_hooks/useServiceCatalog'
import { CategoryPopover } from '../_components/CategoryPopover'
import { useCategoryPath } from '../../../_hooks/useCategoryPath'

/**
 * See the accompanying `./ColumnView.README.md` for a detailed discussion of how
 * the ColumnView works, in particular how we use the route params to determine
 * where to place categories within the three-column layout.
 */
export function ColumnView() {
  const route = unwrap(
    useRoute(),
    '/services/[effectiveDate]/catalog/[categoryCode]',
  )!

  const effectiveDate = route.params.effectiveDate

  const { selectedCategory } = useSelectedCategory({
    effectiveDate,
  })

  const parentHref = routeTo(
    '/services/[effectiveDate]/catalog/[categoryCode]',
    { ...route.params, categoryCode: selectedCategory?.parent?.code ?? 'root' },
    { view: 'column', column: 2 },
  )

  const nestedColumns = useGetColumns(
    route.query.column ?? 0,
    effectiveDate,
    selectedCategory?.current.code,
  )

  const path = useCategoryPath(selectedCategory?.current.code, effectiveDate)

  return (
    <Columns className='min-h-0 flex-auto'>
      <ColumnsHeader className='min-h-14'>
        {path.length > 3 && (
          <Link
            href={parentHref}
            className='rounded-full bg-zinc-400 p-1 text-zinc-200 shadow-sm hover:bg-zinc-500'
          >
            <ChevronLeftIcon className='h-5 w-5' />
          </Link>
        )}
        <ToggleView className='ml-auto' />
      </ColumnsHeader>
      <ColumnsBody>
        {nestedColumns.map((category, i) => {
          const services = category?.services ?? []
          const categories = category?.categories ?? []
          const hasServices = services.length > 0
          const hasCategories = categories.length > 0

          return (
            <Column key={`category-column-${i}`}>
              {hasCategories && (
                <ColumnSection className='flex-none'>
                  {categories.map((category: CatalogCategory) => {
                    const selected = path.some(
                      (item) => item.current.code === category.current.code,
                    )
                    const href = routeTo(
                      '/services/[effectiveDate]/catalog/[categoryCode]',
                      { ...route.params, categoryCode: category.current.code! },
                      { view: 'column', column: Math.min(i + 1, 2) },
                    )

                    return (
                      <ColumnItem
                        href={href}
                        selected={selected}
                        key={category.current.code}
                      >
                        {selected ?
                          <FolderOpenIcon />
                        : <FolderIcon />}
                        <span className='flex-auto overflow-hidden text-ellipsis whitespace-nowrap'>
                          {category.current.name}
                        </span>
                        <span className='inline-flex w-12'>
                          <span className='text-app-color-secondary'>
                            {category.current.code}
                          </span>
                        </span>
                        <CategoryPopover category={category} />
                      </ColumnItem>
                    )
                  })}
                </ColumnSection>
              )}
              {hasCategories && hasServices && <ColumnSectionDivider />}
              {hasServices && (
                <ColumnSection>
                  {services.map((service) => {
                    return (
                      <ColumnItem
                        href='#'
                        key={`service-${service.current.code}`}
                      >
                        <DocumentTextIcon />
                        <span className='flex-auto'>
                          {service.current.description}
                        </span>
                        <span className='text-app-color-secondary'>
                          {service.current.code}
                        </span>
                        <ServicePopover
                          serviceCode={service.current.code!}
                          serviceEffectiveDate={service.current.effectiveDate!}
                        />
                      </ColumnItem>
                    )
                  })}
                </ColumnSection>
              )}
              {(hasCategories || hasServices) && (
                <ColumnFooter>
                  {categories.length > 0 && (
                    <span>
                      {categories.length}
                      {categories.length === 1 ? ' category' : ' categories'}
                    </span>
                  )}
                  {hasCategories && hasServices && <span>, </span>}
                  {hasServices && (
                    <span>
                      {services.length}{' '}
                      {services.length === 1 ? ' service' : ' services'}
                    </span>
                  )}
                </ColumnFooter>
              )}
            </Column>
          )
        })}
      </ColumnsBody>
    </Columns>
  )
}

function useGetColumns(
  columnIndex: number,
  effectiveDate: string,
  categoryCode?: string,
): (CategoryWithContents | undefined)[] {
  const catalog = useServiceCatalog({ effectiveDate })

  return useMemo(() => {
    const result = new Array<CategoryWithContents | undefined>(3).fill(
      undefined,
    )

    if (!catalog.data || !categoryCode) {
      return []
    }

    switch (columnIndex) {
      case 0:
        result[0] = getCategoryNested(catalog.data, categoryCode)
        break
      case 1:
        result[1] = getCategoryNested(catalog.data, categoryCode)
        result[0] = getCategoryNested(catalog.data, result[1]?.parent?.code!)
        break
      case 2:
        result[2] = getCategoryNested(catalog.data, categoryCode)
        result[1] = getCategoryNested(catalog.data, result[2]?.parent?.code!)
        result[0] = getCategoryNested(catalog.data, result[1]?.parent?.code!)
    }

    return result
  }, [catalog, categoryCode, columnIndex])
}
