'use client'

import { PriceType, ServiceType } from '@/api/zodSchemas'
import { defineContext, defineContextMutation } from '@/lib/state/defineContext'

type CatalogState = {
  filters: {
    serviceType: ServiceType[]
    priceType: PriceType[]
    name: string
  }
}

const initialState: CatalogState = {
  filters: { serviceType: [], priceType: [], name: '' },
}

export const updateFilters = defineContextMutation('updateFilters')<
  Partial<CatalogState['filters']>
>

const [
  _useCatalogContext,
  _useCatalogDispatchContext,
  _CatalogContextProvider,
] = defineContext('Catalog', initialState, [updateFilters], {
  updateFilters: ([state, filters]) => ({
    ...state,
    filters: { ...state.filters, ...filters },
  }),
})

// For some reason, NextJS's prod build doesn't like exporting a destructured tuple,
// even though this is fully supported in the dev build.
export const useCatalogContext = _useCatalogContext
export const useCatalogDispatchContext = _useCatalogDispatchContext
export const CatalogContextProvider = _CatalogContextProvider
