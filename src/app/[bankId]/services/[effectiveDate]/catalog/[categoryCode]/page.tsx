'use client'

import { SearchBar } from '@/components/SearchBar'
import { SimpleMultiselectFilter } from '@/components/Filter/SimpleMultiselectFilter'
import { useState } from 'react'
import { CatalogHeader } from '../_components/CatalogHeader'
import { ColumnView } from './ColumnView'
import { ListView } from './ListView'
import { SimpleSelectFilter } from '@/components/Filter/SimpleSelectFilter'
import { useRoute } from '@/app/[bankId]/services/routing'
import { unwrap } from '@/lib/unions/unwrap'
import {
  updateFilters,
  useCatalogContext,
  useCatalogDispatchContext,
} from './context'
import { defineFilters } from '@/app/[bankId]/hooks/useFilterState'
import {
  priceType,
  PriceType,
  priceTypeLabel,
  serviceType,
  ServiceType,
  serviceTypeLabel,
} from '@/api/zodSchemas'

const productNestedViewOptions = [
  'Services and categories',
  'Services only (all nested)',
] as const
export type ProductNestedViewOptions = (typeof productNestedViewOptions)[number]

type ListViewFilters = {
  serviceType: ServiceType[]
  priceType: PriceType[]
  name: string
}

const listViewFilterState = defineFilters<ListViewFilters>({
  defaultValues: {
    serviceType: [],
    priceType: [],
    name: '',
  },
})

export default function ServicesCatalog() {
  const route = unwrap(
    useRoute(),
    '/services/[effectiveDate]/catalog/[categoryCode]',
  )!

  const { filters } = useCatalogContext()
  const dispatch = useCatalogDispatchContext()

  const filtersState = listViewFilterState({
    values: filters,
    onStateUpdate(state) {
      dispatch(updateFilters(state))
    },
  })

  const [productNestedView, setProductNestedView] =
    useState<ProductNestedViewOptions>('Services and categories')

  const [serviceAmount, setServiceAmount] = useState(0)
  const [categoriesAmount, setCategoriesAmount] = useState(0)

  return (
    <>
      <SearchBar
        className='mb-4 max-w-96'
        defaultLabel='Search service catalog'
        value={filtersState.values.name}
        onValueChange={filtersState.update('name')}
      />
      <CatalogHeader />
      <main className='flex min-h-0 flex-auto flex-col'>
        <div className='flex gap-2 py-4'>
          {route.query.view === 'list' && (
            <>
              <div className='flex flex-auto gap-2'>
                <SimpleSelectFilter
                  options={productNestedViewOptions}
                  value={productNestedView}
                  onValueChange={setProductNestedView}
                />
                <SimpleMultiselectFilter
                  defaultLabel='Service type'
                  name='serviceType'
                  options={serviceType.options}
                  renderLabel={serviceTypeLabel}
                  value={filtersState.values.serviceType}
                  onValueChange={filtersState.update('serviceType')}
                />
                <SimpleMultiselectFilter
                  defaultLabel='Price type'
                  name='priceType'
                  options={priceType.options}
                  renderLabel={priceTypeLabel}
                  value={filtersState.values.priceType}
                  onValueChange={filtersState.update('priceType')}
                />
              </div>
              <div className='flex flex-shrink items-end gap-1 text-sm opacity-35'>
                {categoriesAmount > 0 && (
                  <span>
                    {categoriesAmount}{' '}
                    {categoriesAmount === 1 ? 'category' : 'categories'}
                    {categoriesAmount > 0 && serviceAmount > 0 && ','}
                  </span>
                )}
                {serviceAmount > 0 && (
                  <span>
                    {serviceAmount}{' '}
                    {serviceAmount === 1 ? 'service' : 'services'}
                  </span>
                )}
              </div>
            </>
          )}
        </div>
        {route.query.view === 'list' ?
          <ListView
            columnFilters={filtersState.columnFilters}
            productNestedView={productNestedView}
            onVisibleItemsChanged={({ categories, services }) => {
              setCategoriesAmount(categories)
              setServiceAmount(services)
            }}
          />
        : <ColumnView />}
      </main>
    </>
  )
}
