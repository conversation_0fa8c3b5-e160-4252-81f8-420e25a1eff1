import { CategoryPopover } from './CategoryPopover'
import { CategoryModal } from './CategoryModal'
import { ModalOpenButton } from '@/components/Modal'
import { Button } from '@/components/Button'
import Link from 'next/link'
import { ServicesHeader } from '../../../_components/ServicesHeader'
import { useSelectedCategory } from '../../../_hooks/useSelectedCategory'
import { routeTo, useRoute } from '@/app/[bankId]/services/routing'
import { unwrap } from '@/lib/unions/unwrap'
import { CatalogCategory } from '../../../_hooks/useServiceCatalog'
import { useMutation } from '@tanstack/react-query'
import { categoryMutation } from '../mutations'
import { MonthPicker } from '@/components/Filter/MonthPicker'
import { formatToServerString } from '@/lib/date'
import { useRouter } from 'next/navigation'

export function CatalogHeader() {
  const router = useRouter()
  const route = unwrap(
    useRoute(),
    '/services/[effectiveDate]/catalog/[categoryCode]',
  )!

  const { selectedCategory } = useSelectedCategory({
    effectiveDate: route.params.effectiveDate,
  })
  const addServiceHref = routeTo(
    '/services/[effectiveDate]/add/[parentCategoryCode]',
    {
      ...route.params,
      parentCategoryCode: selectedCategory?.current.code ?? 'root',
    },
    route.query,
  )

  const addServiceCategory = useMutation(
    categoryMutation('/addServiceCategory'),
  )

  const handleCreateCategory = (newCategory: CatalogCategory) => {
    addServiceCategory.mutate({
      parentServiceCategoryCode:
        selectedCategory?.current.code === 'root' ?
          undefined
        : selectedCategory?.current.code,
      serviceCategory: newCategory.current,
    })
  }

  const updateEffectiveDate = (date: string) => {
    router.push(
      routeTo(
        '/services/[effectiveDate]/catalog/[categoryCode]',
        { ...route.params, effectiveDate: formatToServerString(date) },
        route.query,
      ),
    )
  }

  return (
    <ServicesHeader className='items-center'>
      {selectedCategory?.parent?.code && (
        <div className='ml-1 flex items-center'>
          <CategoryPopover
            className='flex items-center rounded-lg border bg-white py-1 text-base'
            category={selectedCategory}
          />
        </div>
      )}
      <MonthPicker
        initialDate={route.params.effectiveDate}
        className='ml-2'
        showIcon
        prefix='View data effective on'
        onDateChange={updateEffectiveDate}
      />
      <div className='ml-auto flex'>
        <Link className='mr-2' href={addServiceHref}>
          <Button className='btn-primary text-white'>Add Service</Button>
        </Link>
        <CategoryModal
          title='Add a product category'
          subtitle='You may add a new product category.'
          confirmText='Add'
          onConfirm={handleCreateCategory}
        >
          <ModalOpenButton className='btn'>Add category</ModalOpenButton>
        </CategoryModal>
      </div>
    </ServicesHeader>
  )
}
