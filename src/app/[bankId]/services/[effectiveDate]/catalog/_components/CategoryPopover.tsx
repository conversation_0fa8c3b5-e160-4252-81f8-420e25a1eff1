import { Popover, PopoverButton, PopoverPanel } from '@headlessui/react'
import React from 'react'
import {
  EllipsisVerticalIcon,
  FolderOpenIcon,
  PencilSquareIcon,
} from '@heroicons/react/24/outline'
import { ModalOpenButton } from '@/components/Modal'
import { Button } from '@/components/Button'
import { CategoryModal } from './CategoryModal'
import { preventDefault } from '@/lib/preventDefault'
import { useRouter } from 'next/navigation'
import { useSelectedCategory } from '../../../_hooks/useSelectedCategory'
import { routeTo, useRoute } from '@/app/[bankId]/services/routing'
import { unwrap } from '@/lib/unions/unwrap'
import { useMutation } from '@tanstack/react-query'
import { categoryMutation } from '../mutations'
import { CatalogCategory } from '../../../_hooks/useServiceCatalog'

interface CategoryPopoverProps {
  onOpen?: (category: CatalogCategory) => void
  category: CatalogCategory | undefined
  className?: string
}

export function CategoryPopover({
  onOpen,
  className,
  category,
}: CategoryPopoverProps) {
  const route = unwrap(
    useRoute(),
    '/services/[effectiveDate]/catalog/[categoryCode]',
  )!
  const router = useRouter()
  const { selectedCategory } = useSelectedCategory({
    effectiveDate: route.params.effectiveDate,
  })

  const { mutate: updateCategory } = useMutation(
    categoryMutation('/updateServiceCategory', {
      // If we've changed the selected category's code, we need to navigate to the
      // new code.
      onSuccess: ({ code: previousCode }, newValues) => {
        if (
          category &&
          selectedCategory &&
          selectedCategory.current.code === previousCode &&
          newValues.code &&
          newValues.code !== previousCode
        ) {
          router.push(
            routeTo(
              '/services/[effectiveDate]/catalog/[categoryCode]',
              { ...route.params, categoryCode: newValues.code },
              route.query,
            ),
          )
        }
      },
    }),
  )

  // Delete category is disabled, functionality is not supported on a server side.
  // const { mutate: deleteCategory } = useMutation(
  //   categoryMutation('/api/v1/deleteCategory', {
  //     // If we've deleted the selected category, we need to navigate to a category
  //     // that still exists.
  //     onSuccess: (_, categoryToRemove) => {
  //       if (
  //         selectedCategory &&
  //         selectedCategory.current.code === categoryToRemove.code
  //       ) {
  //         router.push(
  //           routeTo(
  //             '/services/[effectiveDate]/catalog/[categoryCode]',
  //             {
  //               ...route.params,
  //               categoryCode: categoryToRemove.parentCode ?? 'root',
  //             },
  //             {
  //               view: route.query.view,
  //               column:
  //                 route.query.column != undefined && route.query.column > 0 ?
  //                   route.query.column - 1
  //                 : undefined,
  //             },
  //           ),
  //         )
  //       }
  //     },
  //   }),
  // )
  //
  // const isEmptyCategory =
  //   category.serviceCodes.length === 0 && category.categoryCodes.length === 0

  if (!category) return

  return (
    <Popover className='inline-flex'>
      <PopoverButton role='button' className={className}>
        <EllipsisVerticalIcon className='size-5' />
      </PopoverButton>
      <PopoverPanel
        anchor='bottom start'
        className='flex min-w-44 flex-col rounded-md border bg-white text-sm'
        data-testid='categoryMenu'
      >
        {({ close: closePopover }) => (
          <>
            <Button
              onClick={() => {
                closePopover()
                if (onOpen) onOpen(category)
              }}
              className='flex items-center gap-1 border-none p-3 hover:bg-app-color-bg-secondary'
            >
              <FolderOpenIcon className='size-4' />
              Open
            </Button>
            <CategoryModal
              title='Edit'
              subtitle='You may update the name and code of this product category.'
              confirmText='Save'
              category={category}
              onConfirm={({ current }) => {
                updateCategory(current)
                closePopover()
              }}
              onClose={closePopover}
            >
              <ModalOpenButton
                className='flex items-center gap-1 border-none p-3 hover:bg-app-color-bg-secondary'
                onClick={preventDefault()}
              >
                <PencilSquareIcon className='size-4' />
                Edit
              </ModalOpenButton>
            </CategoryModal>

            {/*  Delete category is disabled, functionality is not supported on a server side.
            <Modal onClose={closePopover}>
              <Tooltip
                disabled={isEmptyCategory}
                content={'Category must be empty to be deleted'}
              >
                <ModalOpenButton
                  disabled={!isEmptyCategory}
                  onClick={preventDefault()}
                  className={clsx(
                    'flex w-full items-center gap-1 border-none p-3 hover:bg-app-color-bg-secondary',
                    {
                      'text-app-color-bg-disabled': !isEmptyCategory,
                      'text-app-color-fg-error-primary': isEmptyCategory,
                    },
                  )}
                >
                  <TrashIcon
                    title='Category must be empty to be deleted'
                    className='size-4'
                  />
                  Delete
                </ModalOpenButton>
              </Tooltip>
              <ModalWindow
                dataTestId='deleteCategoryModal'
                className='border-2 border-app-color-border-error'
              >
                <ModalTitle>
                  <ExclamationTriangleIcon className='size-8 text-app-color-fg-error-primary' />
                </ModalTitle>
                <h4 className='text-xl font-medium text-app-color-text-primary-800'>
                  Delete this Product category?
                </h4>
                <p className='mt-2 text-base font-normal text-app-color-text-secondary-hover'>
                  Permanently delete this empty Product category? You cannot
                  undo this action.
                </p>
                <ModalFooter>
                  <ModalCancelButton>No, cancel</ModalCancelButton>
                  <ModalConfirmButton
                    className='btn-alert'
                    onClick={() => {
                      // ToDo: Call delete category API
                    }}
                  >
                    Yes, delete
                  </ModalConfirmButton>
                </ModalFooter>
              </ModalWindow>
            </Modal> */}
          </>
        )}
      </PopoverPanel>
    </Popover>
  )
}
