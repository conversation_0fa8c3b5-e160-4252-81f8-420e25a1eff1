import { ListBulletIcon, ViewColumnsIcon } from '@heroicons/react/24/outline'
import Link from 'next/link'
import clsx from 'clsx'
import React from 'react'
import { routeTo, useRoute } from '@/app/[bankId]/services/routing'
import { unwrap } from '@/lib/unions/unwrap'
import { variant } from '@/lib/unions/Union'
import { match } from '@/lib/unions/match'

export interface ToggleViewProps {
  className?: string
}

export function ToggleView({ className }: ToggleViewProps) {
  const { params, query } = unwrap(
    useRoute(),
    '/services/[effectiveDate]/catalog/[categoryCode]',
  )!
  const activeView = variant(query.view ?? 'column')

  return (
    <div
      className={clsx(
        className,
        'flex overflow-hidden rounded-md border border-zinc-300 bg-white',
      )}
    >
      {match(activeView, {
        // Column view is active -- link to the list view.
        column: () => (
          <>
            <Link
              className='p-1'
              href={routeTo(
                '/services/[effectiveDate]/catalog/[categoryCode]',
                params,
                {
                  view: 'list',
                  column: query.column,
                },
              )}
            >
              <ListBulletIcon className='h-5 w-5' />
            </Link>
            <span className='bg-zinc-400 p-1'>
              <ViewColumnsIcon className='h-5 w-5' />
            </span>
          </>
        ),
        // List view is active -- link to the column view.
        list: () => (
          <>
            <span className='bg-zinc-400 p-1'>
              <ListBulletIcon className='h-5 w-5' />
            </span>
            <Link
              className='p-1'
              href={routeTo(
                '/services/[effectiveDate]/catalog/[categoryCode]',
                params,
                {
                  view: 'column',
                  column: query.column,
                },
              )}
            >
              <ViewColumnsIcon className='h-5 w-5' />
            </Link>
          </>
        ),
      })}
    </div>
  )
}
