import { Tooltip } from '@/components/Tooltip'
import { ServicesBreadcrumbs } from '../../../_components/ServicesBreadcrumbs'
import { useMemo } from 'react'
import { useServiceCatalog } from '../../../_hooks/useServiceCatalog'
import { useGeneralRouteParams } from '../../../routing'

interface BreadcrumbTooltipProps {
  code?: string
}

export function useGetCategory(code?: string) {
  const route = useGeneralRouteParams()
  const catalog = useServiceCatalog({
    effectiveDate: route.params.effectiveDate,
  })

  return useMemo(() => {
    if (!code || !catalog.data) {
      return
    }

    return catalog.data.categoryByCode[code]
  }, [catalog, code])
}

export function BreadcrumbTooltip({ code }: BreadcrumbTooltipProps) {
  const category = useGetCategory(code)

  if (!category || !category.parent?.code || category.parent.code === 'root') {
    return <span>{category?.current.name}</span>
  }

  return (
    <Tooltip
      className='rounded-md'
      bgClassName='bg-zinc-100'
      content={
        <ServicesBreadcrumbs
          categoryCode={category.current.code}
          showRoot={false}
        />
      }
    >
      {category.current.name}
    </Tooltip>
  )
}
