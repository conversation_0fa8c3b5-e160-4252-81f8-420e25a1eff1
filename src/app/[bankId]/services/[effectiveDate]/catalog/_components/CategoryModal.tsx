import {
  <PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON><PERSON>utton,
  <PERSON><PERSON><PERSON>on<PERSON>rmButton,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON>dalT<PERSON><PERSON>,
  ModalWindow,
} from '@/components/Modal'
import { useForm } from '@tanstack/react-form'
import React from 'react'
import { preventDefault } from '@/lib/preventDefault'
import { useFormTextInput } from '@/components/Form/useFormTextInput'
import { z } from 'zod'
import { alphaString } from '@/api/zodSchemas'
import {
  CatalogCategory,
  useServiceCatalog,
} from '../../../_hooks/useServiceCatalog'
import { useGeneralRouteParams } from '../../../routing'
import { useSelectedCategory } from '../../../_hooks/useSelectedCategory'

export interface AddCategoryProps {
  category?: CatalogCategory
  confirmText: string
  title: string
  subtitle: string
  onConfirm: (category: CatalogCategory) => void
  onClose?: () => void
}

const editCategorySchema = z.object({
  name: z.string().min(4).max(30),
  code: alphaString.length(3),
})

type EditableCategory = z.infer<typeof editCategorySchema>

export function CategoryModal({
  category,
  children,
  confirmText,
  title,
  subtitle,
  onConfirm,
  onClose,
}: React.PropsWithChildren<AddCategoryProps>) {
  const isEditCategory = category != null

  const {
    params: { effectiveDate },
  } = useGeneralRouteParams()

  const serviceCatalog = useServiceCatalog({ effectiveDate })
  const { selectedCategory } = useSelectedCategory({ effectiveDate })

  const form = useForm<EditableCategory>({
    defaultValues: {
      name: category?.current.name ?? '',
      code: category?.current.code ?? '',
    },
    onSubmit: ({ value }) => {
      const modifiedCategory: CatalogCategory = {
        categoryCodes: [],
        serviceCodes: [],
        parent: selectedCategory?.current,
        ...(category ?? {}),
        current: {
          ...category?.current!,
          code: value.code,
          name: value.name,
        },
      }
      onConfirm(modifiedCategory)
    },
  })

  const TextInput = useFormTextInput<EditableCategory>({ form })

  return (
    <Modal onClose={onClose}>
      {children}
      <ModalWindow dataTestId='categoryModal'>
        <ModalTitle>{title}</ModalTitle>
        <h4 className='mb-6 text-app-color-secondary'>{subtitle}</h4>
        <form onSubmit={preventDefault()} className='flex flex-col gap-4'>
          <fieldset>
            <TextInput
              name='name'
              label='Category name'
              validators={{
                onChange: editCategorySchema.shape.name,
              }}
            />
          </fieldset>
          <fieldset>
            <TextInput
              label='Category code'
              name='code'
              readonly={isEditCategory}
              validators={{
                onChange: editCategorySchema.shape.code.refine(
                  (value) =>
                    isEditCategory ||
                    serviceCatalog.data?.categoryByCode[value] == null,
                  'Code is already exists',
                ),
              }}
            />
          </fieldset>
          <ModalFooter>
            <ModalCancelButton>Cancel</ModalCancelButton>
            <form.Subscribe selector={(state) => state.isPristine}>
              {(isPrisitine) => (
                <ModalConfirmButton
                  type='submit'
                  aria-disabled={isPrisitine}
                  className='btn-primary aria-disabled:cursor-default aria-disabled:bg-indigo-300'
                  onClick={async (_, ctx) => {
                    await form.handleSubmit()
                    await form.validateAllFields('submit')
                    if (form.state.canSubmit) {
                      ctx.close()
                      form.reset()
                    }
                  }}
                >
                  {confirmText}
                </ModalConfirmButton>
              )}
            </form.Subscribe>
          </ModalFooter>
        </form>
      </ModalWindow>
    </Modal>
  )
}
