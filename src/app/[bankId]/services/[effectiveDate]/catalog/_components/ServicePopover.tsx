import { Popover, PopoverButton, PopoverPanel } from '@headlessui/react'
import React from 'react'
import { clsx } from 'clsx'
import {
  EllipsisVerticalIcon,
  FolderOpenIcon,
  PencilSquareIcon,
} from '@heroicons/react/24/outline'
import { <PERSON><PERSON> } from '@/components/Button'
import Link from 'next/link'
import { routeTo, useRoute } from '../../../routing'
import { unwrap } from '@/lib/unions/unwrap'

interface ServicePopoverProps {
  serviceCode: string
  serviceEffectiveDate: string
  className?: string
}

export function ServicePopover({
  serviceCode,
  serviceEffectiveDate,
  className,
}: ServicePopoverProps) {
  const route = unwrap(
    useRoute(),
    '/services/[effectiveDate]/catalog/[categoryCode]',
  )!

  return (
    <Popover className={clsx('inline-flex', className)}>
      <PopoverButton role='button'>
        <EllipsisVerticalIcon className='size-5' />
      </PopoverButton>
      <PopoverPanel
        anchor='bottom start'
        className='flex min-w-44 flex-col rounded-md border bg-white text-sm'
        data-testid='categoryMenu'
      >
        <>
          <Link
            href={routeTo(
              `/services/[effectiveDate]/view/[serviceCode]`,
              {
                // When viewing a Service, it's important that we do not change
                // the desired effectiveDate to simply match the `serviceEffectiveDate`
                // -- rather, the `view` page will determine which version of the
                // Service is effective given this date.
                //
                // If we used `serviceEffectiveDate` here, it would change the
                // the effectiveDate the user had previously selected.
                ...route.params,
                serviceCode: serviceCode,
              },
              { ...route.query, version: serviceEffectiveDate },
            )}
          >
            <Button className='flex w-full items-center gap-1 border-none p-3 hover:bg-app-color-bg-secondary'>
              <FolderOpenIcon className='size-4' />
              Open
            </Button>
          </Link>
          <Link
            href={routeTo(
              '/services/[effectiveDate]/edit/[serviceCode]',
              {
                ...route.params,
                serviceCode,
              },
              { ...route.query, version: serviceEffectiveDate },
            )}
          >
            <Button className='flex w-full items-center gap-1 border-none p-3 hover:bg-app-color-bg-secondary'>
              <PencilSquareIcon className='size-4' />
              Edit
            </Button>
          </Link>
        </>
      </PopoverPanel>
    </Popover>
  )
}
