import { isStringUnion } from '@/lib/guards/isStringUnion'
import { defineRoute } from '@/lib/defineRoute'
import { ReadonlyURLSearchParams } from 'next/navigation'

export const servicesRoute = defineRoute('services', ['effectiveDate'])

type CatalogQuery = {
  view: 'column' | 'list'
  column?: number
}

export const parseCatalogQuery = (
  query: ReadonlyURLSearchParams,
): CatalogQuery | false => {
  const view = query.get('view')
  if (!isStringUnion(['column', 'list'])(view)) return false
  const column = query.get('column')
  return {
    view,
    column: column ? parseInt(column) : undefined,
  }
}

type ServiceQuery = CatalogQuery & { version?: string }

export const parseServiceQuery = (
  query: ReadonlyURLSearchParams,
): ServiceQuery | false => {
  const catalogQuery = parseCatalogQuery(query)
  if (!catalogQuery) return false
  // The `version` key should be optional -- this is a quick way of achieving
  // that without having to define return types for both this and `parseCatalogQuery`.
  const version = query.get('version') ?? undefined
  return { ...catalogQuery, version }
}
