'use client'
import {
  serviceFormStateDefaultValues,
  UpdateServiceFormState,
} from './[effectiveDate]/(service)/UpdateServiceForm'
import { components } from '@/api/schema'
import { mapObject } from '@/lib/functional/mapObject'
import { Currency, ServiceType } from '@/api/zodSchemas'
import { replaceUndefinedWithNull } from '@/lib/functional/replaceUndefinedWithNull'
import { includes } from '@/lib/functional/includes'
import { unary } from '@/lib/functional/unary'
import { isNumber } from '@/lib/guards/isNumber'
import { servicePriceDefaultValues } from './_components/ServicePrice/servicePriceSchema'

const castNumberToString = (value: number | null) => {
  return value?.toString() ?? null
}

// By desing we only operate with inclusive values on UI,
// we need to transform exclusive values received from server.
const toInclusive = (v: number | null) => (isNumber(v) ? v - 1 : v)

export function schemaServicePriceToFromState(
  schemaServicePrice: components['schemas']['ServicePrice'],
) {
  const servicePrice = replaceUndefinedWithNull(schemaServicePrice)

  return {
    ...servicePrice,
    baseFee: castNumberToString(servicePrice.baseFee),
    costValue: castNumberToString(servicePrice.costValue),
    minimumFee: castNumberToString(servicePrice.minimumFee),
    maximumFee: castNumberToString(servicePrice.maximumFee),
    indexAdjustment: castNumberToString(servicePrice.indexAdjustment),
    indexMultiplier: castNumberToString(servicePrice.indexMultiplier),
    priceValue: castNumberToString(servicePrice.priceValue),
    tierMaxBalanceExclusive: castNumberToString(
      toInclusive(servicePrice.tierMaxBalanceExclusive),
    ),
    tierMaxVolumeExclusive: castNumberToString(
      toInclusive(servicePrice.tierMaxVolumeExclusive),
    ),
    tierMinBalanceInclusive: castNumberToString(
      servicePrice.tierMinBalanceInclusive,
    ),
    tierMinVolumeInclusive: castNumberToString(
      servicePrice.tierMinVolumeInclusive,
    ),
    units: castNumberToString(servicePrice.units),
    currency: servicePrice.currency as Currency,
  }
}

export function toFormState({
  serverService,
  servicesInServiceSet,
  servicePrices,
  serviceCategoryCode,
}: {
  serverService: components['schemas']['Service']
  servicesInServiceSet: components['schemas']['Service'][]
  servicePrices: components['schemas']['ServicePrice'][]
  serviceCategoryCode: string | undefined
}): UpdateServiceFormState {
  const servicePrice = schemaServicePriceToFromState(servicePrices[0]!)
  return {
    servicePrice: { ...servicePriceDefaultValues, ...servicePrice },
    servicesInServiceSet,
    serviceCategoryCode: serviceCategoryCode ?? null,
    service: {
      ...serviceFormStateDefaultValues,
      ...mapObject(serverService, (value) => value ?? null),
      code: serverService.code,
      effectiveDate: serverService.effectiveDate,
      serviceType: (serverService.serviceType as ServiceType) ?? null,
      addAfpCode:
        serverService.domesticAfpCode != undefined ||
        serverService.globalAfpCode != undefined,
      addInternalNote: serverService.internalNote != undefined,
    },
    pricingTiers:
      includes(servicePrice.priceType, ['PARTITIONED_TIER', 'THRESHOLD_TIER']) ?
        servicePrices.map(unary(schemaServicePriceToFromState))
      : [],
  }
}
