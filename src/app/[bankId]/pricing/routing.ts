import { defineChildRoute, defineRoute, defineRoutes } from '@/lib/defineRoute'
import { priceListRoute } from './[effectiveDate]/(table-view)/price-list/routing'
import { promotionsRoute } from './[effectiveDate]/(table-view)/promotions/routing'
import { addPriceListStep1Route } from './add-price-list/step-1/routing'
import { addPriceListStep2Route } from './add-price-list/step-2/routing'
import { addPriceListStep3Route } from './add-price-list/step-3/routing'
import { addPriceListStep4Route } from './add-price-list/step-4/routing'
import { pricingRoute } from './[effectiveDate]/routing'

import { manageRankingRoute } from './[effectiveDate]/manage-ranking/routing'

const viewPricingRoute = defineChildRoute(
  pricingRoute,
  defineRoute('view-price-list', ['code']),
)
const editPricingRoute = defineChildRoute(
  pricingRoute,
  defineRoute('edit-price-list', ['code']),
)
const viewPricingRouteDetails = defineChildRoute(
  viewPricingRoute,
  defineRoute('details'),
)
const viewPricingRouteCriteria = defineChildRoute(
  viewPricingRoute,
  defineRoute('criteria'),
)
const viewPricingRouteServicePricing = defineChildRoute(
  viewPricingRoute,
  defineRoute('service-pricing'),
)
const editPricingRouteServicePricing = defineChildRoute(
  editPricingRoute,
  defineRoute('service-pricing'),
)
const editPricingRouteDetails = defineChildRoute(
  editPricingRoute,
  defineRoute('details'),
)
const editPricingRouteCriteria = defineChildRoute(
  editPricingRoute,
  defineRoute('criteria'),
)
export const [useRoute, routeTo] = defineRoutes(
  priceListRoute,
  addPriceListStep1Route,
  addPriceListStep2Route,
  addPriceListStep3Route,
  addPriceListStep4Route,
  editPricingRoute,
  manageRankingRoute,
  viewPricingRoute,
  viewPricingRouteDetails,
  viewPricingRouteCriteria,
  viewPricingRouteServicePricing,
  editPricingRouteServicePricing,
  editPricingRouteDetails,
  editPricingRouteCriteria,
  promotionsRoute,
)
