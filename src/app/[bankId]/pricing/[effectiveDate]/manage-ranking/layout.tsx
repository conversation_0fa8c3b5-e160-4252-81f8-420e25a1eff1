'use client'

import { routeTo, useRoute } from '../../routing'
import { data } from '@/lib/unions/Union'
import Link from 'next/link'
import { Button } from '@/components/Button'
import { useForm, useStore } from '@tanstack/react-form'
import { AddPriceListRankingContextProvider } from './context'
import { startOfToday } from 'date-fns'
import { toServerFormat } from '@/lib/date'
import { useRouter } from 'next/navigation'
import { useMutation } from '@tanstack/react-query'
import { pricingMutation } from '../mutations'
import { formToApiSchemas } from '@/api/formToApiSchema'
import { RouteParamsRecord } from '@/lib/defineRoute'

export default function AddPriceListRankingLayout({
  children,
}: React.PropsWithChildren) {
  const router = useRouter()
  const route = useRoute()
  const { mutate } = useMutation(
    pricingMutation('/setDemographicPriceListRanking'),
  )

  const effectiveDate =
    route ?
      (data(route).params as RouteParamsRecord<['effectiveDate']>).effectiveDate
    : toServerFormat(startOfToday())
  const form = useForm({
    defaultValues: {
      effectiveDate,
      ranking: '',
    },
    validators: { onChange: formToApiSchemas.demographicPriceListRanking },
    onSubmit: ({ value }) => {
      const parsed =
        formToApiSchemas.demographicPriceListRanking.safeParse(value)
      console.debug('submit:', parsed)
      if (parsed.success) mutate(parsed.data)
      router.push(
        routeTo(
          '/pricing/[effectiveDate]/price-list',
          params as RouteParamsRecord<['effectiveDate']>,
        ),
      )
    },
  })

  const isFormValid = useStore(form.store, (state) => {
    return state.canSubmit && state.isTouched
  })

  if (!route)
    return 'AddPriceListRankingLayout must be used within a pricing route'
  const params = data(route).params

  return (
    <div className='flex min-h-0 flex-1 flex-col'>
      <div className='flex flex-auto flex-col overflow-y-auto p-8 pb-4'>
        <h1 className='mb-4 text-lg font-medium'>
          Manage price list priorities
        </h1>
        <AddPriceListRankingContextProvider state={form!}>
          <div className='flex min-h-0 flex-col rounded-lg border bg-white p-4'>
            {children}
          </div>
        </AddPriceListRankingContextProvider>
      </div>
      <div className='flex border-t bg-white p-4'>
        <div className='flex-auto'>
          <Link
            href={routeTo(
              '/pricing/[effectiveDate]/price-list',
              params as RouteParamsRecord<['effectiveDate']>,
            )}
          >
            <Button className='btn'>
              <span className='px-16'>Cancel</span>
            </Button>
          </Link>
        </div>
        <div className='flex gap-2'>
          <Button
            className='btn-primary disabled:cursor-not-allowed disabled:bg-zinc-300 disabled:text-zinc-500'
            disabled={!isFormValid}
            onClick={form.handleSubmit}
          >
            <span className='px-16'>Save</span>
          </Button>
        </div>
      </div>
    </div>
  )
}
