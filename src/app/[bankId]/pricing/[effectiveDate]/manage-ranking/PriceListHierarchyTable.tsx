import React, { CSSProperties, useRef } from 'react'
import { clsx } from 'clsx'
import {
  useReactTable,
  getCoreRowModel,
  getSortedRowModel,
  ColumnDef,
  flexRender,
  Row,
  getFilteredRowModel,
} from '@tanstack/react-table'
import { useVirtualizer, VirtualItem } from '@tanstack/react-virtual'
// needed for table body level scope DnD setup
import {
  DndContext,
  KeyboardSensor,
  MouseSensor,
  TouchSensor,
  closestCenter,
  type DragEndEvent,
  type UniqueIdentifier,
  useSensor,
  useSensors,
} from '@dnd-kit/core'
import { restrictToVerticalAxis } from '@dnd-kit/modifiers'
import {
  arrayMove,
  SortableContext,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable'

// needed for row & cell level scope DnD setup
import { useSortable } from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'
import { PriceListWithRanking } from './types'

// Row Component
const DraggableRow = ({
  virtualRow,
  row,
}: {
  virtualRow: VirtualItem
  row: Row<PriceListWithRanking>
}) => {
  const { transform, transition, setNodeRef, isDragging } = useSortable({
    id: row.original.code!,
  })

  const style: CSSProperties = {
    transform: CSS.Transform.toString(transform), //let dnd-kit do its thing
    transition: transition,
    opacity: isDragging ? 0.8 : 1,
    zIndex: isDragging ? 1 : 0,
    position: 'relative',
    height: `${virtualRow.size}px`,
  }
  return (
    // connect row ref to dnd-kit, apply important styles
    <div
      ref={setNodeRef}
      style={style}
      className={
        'flex items-center border-b hover:bg-indigo-200 active:bg-indigo-300'
      }
    >
      {row.getVisibleCells().map((cell) => {
        const columnMeta =
          (cell.column.columnDef.meta as DnDColumnMeta | undefined) ?? {}
        return (
          <div
            key={cell.id}
            className={clsx(
              columnMeta.className ?? 'basis-full px-6',
              'flex text-left',
            )}
            role='cell'
          >
            {flexRender(cell.column.columnDef.cell, cell.getContext())}
          </div>
        )
      })}
    </div>
  )
}

export interface DnDColumnMeta {
  className?: string
}

export interface DnDTableProps<T, V> {
  data: PriceListWithRanking[]
  columns: ColumnDef<PriceListWithRanking>[]
  onOrderChange: (data: PriceListWithRanking[]) => void
}

export function PriceListHierarchyTable<T, V>({
  data,
  columns,
  onOrderChange,
}: DnDTableProps<T, V>) {
  const [currData, setCurrData] = React.useState<PriceListWithRanking[]>(data)

  const dataIds = React.useMemo<UniqueIdentifier[]>(
    () =>
      currData
        ?.map(({ code }) => code)
        .filter(
          (code): code is Exclude<typeof code, null> => code !== null,
        ) as UniqueIdentifier[],
    [currData],
  )

  // reorder rows after drag & drop
  function handleDragEnd(event: DragEndEvent) {
    const { active, over } = event
    if (active && over && active.id !== over.id) {
      var newData: PriceListWithRanking[] = []
      setCurrData((currData) => {
        const oldIndex = dataIds.indexOf(active.id)
        const newIndex = dataIds.indexOf(over.id)
        newData = arrayMove(currData, oldIndex, newIndex)
        return newData
      })
      onOrderChange(newData)
    }
  }

  const table = useReactTable({
    data: currData,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getRowId: (row) => row.code!,
  })

  const sensors = useSensors(
    useSensor(MouseSensor, {}),
    useSensor(TouchSensor, {}),
    useSensor(KeyboardSensor, {}),
  )

  const { rows } = table.getRowModel()

  const tbodyRef = useRef<HTMLTableSectionElement>(null)
  const virtualizer = useVirtualizer({
    count: rows.length,
    getScrollElement: () => tbodyRef.current,
    estimateSize: () => 51,
    overscan: 100,
  })

  const [headerGroup] = table.getHeaderGroups()

  const getColumnMeta = <T,>(
    columnDef: ColumnDef<T, unknown>,
  ): DnDColumnMeta => {
    return (columnDef.meta as DnDColumnMeta | undefined) ?? {}
  }

  return (
    <DndContext
      collisionDetection={closestCenter}
      modifiers={[restrictToVerticalAxis]}
      onDragEnd={handleDragEnd}
      sensors={sensors}
    >
      <div className='flex min-h-0 overflow-hidden rounded-lg border border-zinc-300 bg-white shadow-md shadow-zinc-400/20'>
        <div className='flex min-h-0 w-full flex-col' role='table'>
          <div
            className='flex min-h-10 items-center bg-zinc-100 text-sm text-zinc-500'
            role='row'
          >
            {headerGroup.headers.map((header) => {
              const { className } = getColumnMeta(header.column.columnDef)
              return (
                <div
                  key={header.id}
                  className={clsx(
                    className ?? 'basis-full px-6',
                    'flex text-left font-normal',
                    header.column.getCanSort() && 'cursor-pointer',
                  )}
                  role='columnheader'
                  onClick={header.column.getToggleSortingHandler()}
                >
                  <span>
                    {flexRender(
                      header.column.columnDef.header,
                      header.getContext(),
                    )}
                  </span>
                </div>
              )
            })}
          </div>
          <div className='flex w-full overflow-y-scroll' ref={tbodyRef}>
            <div
              className='flex w-full flex-col'
              style={{ height: `${virtualizer.getTotalSize()}px` }}
            >
              <SortableContext
                items={dataIds}
                strategy={verticalListSortingStrategy}
              >
                {virtualizer.getVirtualItems().map((virtualRow) => {
                  const row = rows[virtualRow.index]
                  return (
                    <DraggableRow
                      key={row.id}
                      virtualRow={virtualRow}
                      row={row}
                    />
                  )
                })}
              </SortableContext>
            </div>
          </div>
        </div>
      </div>
    </DndContext>
  )
}
