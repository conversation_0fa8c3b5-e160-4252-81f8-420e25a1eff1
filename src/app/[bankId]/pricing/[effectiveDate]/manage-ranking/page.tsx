'use client'

import { routeTo, useRoute } from '../../routing'
import { data } from '@/lib/unions/Union'
import { useQuery } from '@tanstack/react-query'
import { query } from '../queries'
import { DateFilter } from '@/components/Filter/DateFilter'
import { parseServerFormat, toServerFormat, toUIFormat } from '@/lib/date'
import { matches } from '@/lib/unions/matches'
import { useRouter } from 'next/navigation'
import { useMemo, useState } from 'react'
import { ColumnDef } from '@tanstack/react-table'

import { useSortable } from '@dnd-kit/sortable'
import { EllipsisVerticalIcon } from '@heroicons/react/24/outline'
import { PriceListWithRanking } from './types'
import { useAddPriceListRankingContext } from './context'
import { PriceListHierarchyTable } from './PriceListHierarchyTable'
import { calculatePriceListRanking } from '../../helpers'
import { RouteParamsRecord } from '@/lib/defineRoute'
// Cell Component
const RowDragHandleCell = ({
  rowId,
  rank,
}: {
  rowId: string
  rank: number
}) => {
  const { attributes, listeners } = useSortable({
    id: rowId,
  })
  return (
    // Alternatively, you could set these attributes on the rows themselves
    <div className='flex items-center'>
      <button
        {...attributes}
        {...listeners}
        className={
          'ml-0 h-6 w-6 flex-none text-app-color-secondary hover:text-app-color-primary'
        }
      >
        <EllipsisVerticalIcon />
      </button>
      <div className='flex-grow text-center'>{rank}</div>
    </div>
  )
}

/**
 * Price list table column definitions
 */
const columns: ColumnDef<PriceListWithRanking>[] = [
  {
    header: 'Rank',
    accessorKey: 'ranking',
    cell: ({ row }) => (
      <RowDragHandleCell rowId={row.id} rank={row.original.ranking} />
    ),
  },
  {
    header: 'Name',
    accessorKey: 'name',
  },
  {
    header: 'Code',
    accessorKey: 'code',
  },
  {
    header: 'Lead price list',
    accessorKey: 'isLeadPriceList',
    cell: ({ getValue }) => (getValue<boolean>() ? 'Yes' : 'No'),
  },
  {
    //TODO: @ileana verify this when services is implemented
    header: 'Number of services',
    accessorKey: 'serviceCount',
    cell: ({ getValue }) => (getValue<number>() ? getValue<number>() : 0),
  },
  {
    header: 'Start date',
    accessorKey: 'effectiveDate',
    cell: ({ getValue }) => toUIFormat(getValue<Date>()),
  },
]

export default function PriceListPage() {
  const route = useRoute()
  const router = useRouter()
  const effectiveDate = (
    data(route!).params as RouteParamsRecord<['effectiveDate']>
  ).effectiveDate
  const {
    data: priceLists,
    status,
    error,
  } = useQuery(query('/listDemographicPriceLists', { effectiveDate }))

  const {
    data: priceListRanking,
    status: rankingStatus,
    error: rankingError,
  } = useQuery(query('/getDemographicPriceListRanking', { effectiveDate }))

  const form = useAddPriceListRankingContext()!

  const [orderedData, setOrderedData] = useState<PriceListWithRanking[]>()

  useMemo(() => {
    if (!priceListRanking?.ranking || !priceLists) {
      setOrderedData([])
      return []
    }
    const combinedList = calculatePriceListRanking(
      priceLists!,
      priceListRanking!,
    )
    setOrderedData(combinedList)
  }, [priceLists, priceListRanking, setOrderedData])

  /**
   * Bail out if in a bad state (e.g. wrong route, no data, query error)
   */
  if (!matches(route, '/pricing/[effectiveDate]/manage-ranking')) {
    return "PriceListPage must be used in the '/pricing/[effectiveDate]/manage-ranking' route."
  }
  if (status === 'pending' || rankingStatus === 'pending') return 'Loading...'
  if (status === 'error' && !error.message.includes('404')) {
    console.error(error)
    return `[${error.name}] ${error.message}`
  } else if (
    rankingStatus === 'error' &&
    !rankingError.message.includes('404')
  ) {
    console.error(rankingError)
    return `[${rankingError.name}] ${rankingError.message}`
  }

  const routeParams = data(route).params

  /**
   * Effective date
   */
  const updateEffectiveDate = (date: Date) => {
    router.push(
      routeTo('/pricing/[effectiveDate]/manage-ranking', {
        ...routeParams,
        effectiveDate: toServerFormat(date),
      }),
    )
  }

  const onOrderChange = (data: PriceListWithRanking[]) => {
    const newRankings = data.map((priceList) => priceList.code).join(', ')
    data.forEach((priceList, index) => {
      priceList.ranking = index + 1
    })
    setOrderedData(data)
    form.setFieldValue('ranking', newRankings)
  }

  return (
    <div className='flex min-h-0 w-full flex-auto flex-grow flex-col rounded-lg border bg-white p-6'>
      <div className='mb-6 flex'>
        <hgroup className='flex-auto'>
          <h1 className='text-lg'>Price list ranking</h1>
          <p className='text-zinc-400'>
            Change the order in which price lists will be evaluated for
            analysis.
          </p>
        </hgroup>
      </div>
      <div className='flex min-h-0 flex-auto flex-col rounded-lg border'>
        <div className='flex items-center p-4'>
          <div>
            <DateFilter
              className={
                'border border-zinc-300 bg-zinc-50 hover:bg-zinc-200 focus-visible:outline-indigo-300'
              }
              label='Effective date'
              title='Select the effective date'
              initialDate={parseServerFormat(routeParams.effectiveDate)}
              onDateChange={updateEffectiveDate}
            />
          </div>
        </div>
        <div className='flex min-h-0 flex-col'>
          {orderedData && (
            <PriceListHierarchyTable
              data={orderedData}
              columns={columns}
              onOrderChange={onOrderChange}
            />
          )}
        </div>
      </div>
    </div>
  )
}
