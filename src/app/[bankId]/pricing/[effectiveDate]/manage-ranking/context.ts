import { DemographicPriceListRankingForm } from '@/api/formToApiSchema'
import { defineContext } from '@/lib/state/defineContext'
import { ReactFormExtendedApi } from '@tanstack/react-form'

type AddPriceListRankingState =
  | ReactFormExtendedApi<DemographicPriceListRankingForm>
  | undefined
const initialState = undefined as AddPriceListRankingState

export const [
  useAddPriceListRankingContext,
  useAddPriceListRankingDispatchContext,
  AddPriceListRankingContextProvider,
] = defineContext('AddPriceListRanking', initialState, [], {})
