import { revenueConnectClient } from '@/api/revenueConnectClient'
import { defineMutation, defineMutations } from '@/lib/state/defineMutation'
import { query } from './queries'
import { compose } from '@/lib/functional/compose'
import { withMetrics } from '@/lib/middleware/withMetrics'
import { withLogOnError } from '@/lib/middleware/withLogOnError'

const createDemographicPriceList = defineMutation(
  revenueConnectClient,
  '/createDemographicPriceList',
  {
    invalidate: (request) => {
      return [
        { queryKey: ['/getServicePricesAsOf'] },
        { queryKey: ['/listDemographicPriceLists'] },
        { queryKey: ['/getDemographicPriceList'] },
        { queryKey: ['/getDemographicCriteria'] },
        { queryKey: ['/getDemographicPriceListRanking'] },
      ]
    },
  },
)

// This mutation is needed so that we can invalidate two effectiveDate queries at once. It is used when we edit priceList effectiveDate
const reFetchPriceAndRankingLists = defineMutation(
  revenueConnectClient,
  '/listDemographicPriceLists',
  {
    invalidate: (request) => {
      return [
        query('/listDemographicPriceLists', {
          effectiveDate: request.effectiveDate!,
        }),
        query('/getDemographicPriceListRanking', {
          effectiveDate: request.effectiveDate!,
        }),
      ]
    },
  },
)

// This mutation is needed so that we can invalidate two effectiveDate queries at once. It is used when we edit priceList effectiveDate
const reFetchPriceListVersions = defineMutation(
  revenueConnectClient,
  '/getDemographicPriceListVersions',
  {
    invalidate: (request) => {
      return [
        query('/getDemographicPriceListVersions', {
          code: request.code!,
        }),
      ]
    },
  },
)

const updateDemographicPriceListRanking = defineMutation(
  revenueConnectClient,
  '/setDemographicPriceListRanking',
  {
    invalidate: (request) => {
      return [
        query('/listDemographicPriceLists', {
          effectiveDate: request.effectiveDate!,
        }),
        query('/getDemographicPriceListRanking', {
          effectiveDate: request.effectiveDate!,
        }),
      ]
    },
  },
)

const updateDemographicPriceList = defineMutation(
  revenueConnectClient,
  '/updateDemographicPriceList',
  {
    invalidate: (request) => {
      return [
        { queryKey: ['/getServicePricesAsOf'] },
        { queryKey: ['/listDemographicPriceLists'] },
        { queryKey: ['/getDemographicPriceList'] },
        { queryKey: ['/getDemographicCriteria'] },
        { queryKey: ['/getDemographicPriceListRanking'] },
      ]
    },
  },
)

export type PricingMutation =
  | typeof createDemographicPriceList
  | typeof updateDemographicPriceListRanking
  | typeof updateDemographicPriceList
  | typeof reFetchPriceListVersions
export const pricingMutation = defineMutations(
  [
    createDemographicPriceList,
    updateDemographicPriceListRanking,
    updateDemographicPriceList,
    reFetchPriceAndRankingLists,
    reFetchPriceListVersions,
  ],
  compose(withMetrics(), withLogOnError()),
)
