'use client'

import { match } from '@/lib/unions/match'
import { useRouter } from 'next/navigation'
import { routeTo, useRoute } from '@/app/[bankId]/pricing/routing'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { query } from '../../queries'
import Link from 'next/link'
import { Button } from '@/components/Button'
import {
  AddPriceListContextProvider,
  AddPriceListForm,
  priceListFormSchema,
} from '../../../add-price-list/context'
import { pricingMutation } from '../../mutations'
import { useForm, useStore } from '@tanstack/react-form'
import { formToApiSchemas } from '@/api/formToApiSchema'
import { RouteParamsRecord } from '@/lib/defineRoute'
import { includes } from '@/lib/functional/includes'
import { tiersToPrices } from '../../../tiersToServicePricing'

export default function ViewPriceListLayout({
  children,
}: React.PropsWithChildren) {
  const router = useRouter()
  const route = useRoute()!

  const params = match(route, {
    '/pricing/[effectiveDate]/edit-price-list/[code]/details': ({ params }) =>
      params,
    '/pricing/[effectiveDate]/edit-price-list/[code]/criteria': ({ params }) =>
      params,
    '/pricing/[effectiveDate]/edit-price-list/[code]/service-pricing': ({
      params,
    }) => params,
    _: ({ params }) => ({
      ...(params as RouteParamsRecord<['effectiveDate', 'code']>),
    }),
  })
  const { effectiveDate, code } = params!

  const { mutate } = useMutation(pricingMutation('/updateDemographicPriceList'))

  const { mutate: listMutation } = useMutation(
    pricingMutation('/listDemographicPriceLists'),
  )
  const { mutate: versionsMutation } = useMutation(
    pricingMutation('/getDemographicPriceListVersions'),
  )

  //to get price list details
  const { data: priceListDetails } = useQuery({
    ...query('/getDemographicPriceList', { effectiveDate, code }),
    enabled: params != undefined,
  })!

  //to get price list demographic criteria based on effective date,code
  const { data: demographicCriteriaDetails } = useQuery(
    query('/getDemographicCriteria', { effectiveDate, code }),
  )

  //to get price list ranking
  const { data: priceListRanking, isLoading: isPriceListRankingLoading } =
    useQuery(query('/getDemographicPriceListRanking', { effectiveDate }))

  //to get price list based on effective date,code
  const { data: servicePriceData } = useQuery(
    query('/getServicePricesAsOf', {
      asOfDate: effectiveDate,
      pricingHierarchyEntryCode: code, //priceListCode
      pricingHierarchyEntryType: 'PRICE_LIST',
    }),
  )

  const form = useForm<AddPriceListForm>({
    defaultValues: {
      demographicCriteria:
        demographicCriteriaDetails ?
          demographicCriteriaDetails.map((criteria) => ({
            booleanValue: criteria.booleanValue ?? null,
            code: criteria.code ?? null,
            codeKey: criteria.codeKey ?? null,
            codeValue: criteria.codeValue ?? null,
            criteriaCode: criteria.criteriaCode,
            demographicCriteriaId: {
              codeKey: criteria.demographicCriteriaId?.codeKey ?? '',
              criteriaCode: criteria.demographicCriteriaId?.criteriaCode ?? '',
              keyType: criteria.demographicCriteriaId?.keyType ?? 'USERFIELD',
              priceListCode:
                criteria.demographicCriteriaId?.priceListCode ?? '',
              stringKey: criteria.demographicCriteriaId?.stringKey ?? '',
              valueCode: criteria.demographicCriteriaId?.valueCode ?? '',
            },
            effectiveDate: criteria.effectiveDate ?? effectiveDate,
            keyType: criteria.keyType ?? null,
            priceListCode: criteria.priceListCode ?? null,
            stringKey: criteria.stringKey ?? null,
            stringValue: criteria.stringValue ?? null,
            valueCode: criteria.valueCode ?? null,
          }))
        : [],

      demographicPriceList: {
        code: priceListDetails?.code ?? '',
        name: priceListDetails?.name ?? '',
        currency: 'USD',
        isLeadPriceList: priceListDetails?.isLeadPriceList ?? false,
        effectiveDate,
      },
      ranking: {
        effectiveDate: priceListRanking?.effectiveDate ?? effectiveDate,
        ranking: priceListRanking?.ranking ?? '',
      },
      servicePricing:
        servicePriceData ?
          servicePriceData.map((servicePrice) => ({
            applyServiceTo: servicePrice.applyServiceTo ?? null,
            balanceType: servicePrice.balanceType ?? null,
            baseFee: servicePrice.baseFee?.toString() ?? null,
            basisDays: servicePrice.basisDays ?? null,
            costType: servicePrice.costType ?? null,
            costValue: servicePrice.costValue?.toString() ?? null,
            currency: servicePrice.currency ?? null,
            cycleDefinitionCode: servicePrice.cycleDefinitionCode ?? null,
            disposition: servicePrice.disposition ?? null,
            effectiveDate: servicePrice.effectiveDate ?? effectiveDate,
            expirationDate: servicePrice.expirationDate ?? null,
            includeReferenceInformationOnStatements:
              servicePrice?.includeReferenceInformationOnStatements ?? null,
            indexAdjustment: servicePrice.indexAdjustment?.toString() ?? null,
            indexMultiplier: servicePrice.indexMultiplier?.toString() ?? null,
            indexRateCode: servicePrice.indexRateCode ?? null,
            lastFinalsDate: servicePrice.lastFinalsDate ?? null,
            maximumFee: servicePrice.maximumFee?.toString() ?? null,
            minimumFee: servicePrice.minimumFee?.toString() ?? null,
            priceType: servicePrice.priceType ?? null,
            priceValue: servicePrice.priceValue?.toString() ?? null,
            pricingHierarchyEntryCode:
              servicePrice.pricingHierarchyEntryCode ?? '',
            pricingHierarchyEntryType:
              servicePrice.pricingHierarchyEntryType ?? 'PRICE_LIST',
            serviceCode: servicePrice.serviceCode ?? '',
            subjectToDiscountOrPremium:
              servicePrice.subjectToDiscountOrPremium ?? null,
            tierMaxBalanceExclusive:
              servicePrice.tierMaxBalanceExclusive?.toString() ?? null,
            tierMaxVolumeExclusive: servicePrice.tierMaxVolumeExclusive ?? null,
            tierMinBalanceInclusive:
              servicePrice.tierMinBalanceInclusive?.toString() ?? null,
            tierMinVolumeInclusive: servicePrice.tierMinVolumeInclusive ?? null,
            tierNumber: servicePrice.tierNumber ?? '',
            tierPriceType: servicePrice.tierPriceType ?? null,
            units: servicePrice.units ?? null,
            balanceDivisor: servicePrice.balanceDivisor ?? null,
          }))
        : [],
      pricingTiers: [],
    },
    validators: { onChange: priceListFormSchema },
    onSubmit: ({ value }) => {
      const didDateChange =
        priceListDetails?.effectiveDate !==
        form?.state?.values.demographicPriceList.effectiveDate

      const doesRankExistInCurrentPriceList = newPriceListRanking?.ranking
        ?.split(', ')
        .find((rank) => rank === value.demographicPriceList.code)

      //to get price list ranking for new Date
      if (didDateChange && doesRankExistInCurrentPriceList == undefined) {
        value.ranking.effectiveDate = newPriceListRanking?.effectiveDate!
        value.ranking.ranking =
          newPriceListRanking?.ranking ?
            newPriceListRanking.ranking + ', ' + value.demographicPriceList.code
          : value.demographicPriceList.code
        //PriceListDetails should be same list, so this should be
        listMutation({ effectiveDate: priceListDetails?.effectiveDate! })
      }
      versionsMutation({ code: priceListDetails?.code! })
      value.demographicCriteria.forEach((criteria) => {
        criteria.effectiveDate =
          form?.state?.values.demographicPriceList.effectiveDate
      })
      value.servicePricing.forEach((service) => {
        service.effectiveDate =
          form?.state?.values.demographicPriceList.effectiveDate
      })

      if (
        includes(value.servicePricing[0].priceType, [
          'PARTITIONED_TIER',
          'THRESHOLD_TIER',
        ])
      ) {
        value.servicePricing = tiersToPrices(
          value.servicePricing[0],
          value.pricingTiers,
        )
      }

      const parsed =
        formToApiSchemas.hydratedDemographicPriceList.safeParse(value)

      console.debug('submit:', parsed)
      if (parsed.success) mutate(parsed.data)
      router.push(
        routeTo('/pricing/[effectiveDate]/price-list', {
          ...params,
          effectiveDate: form?.state?.values.demographicPriceList.effectiveDate,
        }),
      )
    },
  })

  const { data: newPriceListRanking } = useQuery({
    ...query('/getDemographicPriceListRanking', {
      effectiveDate: form?.state?.values.demographicPriceList.effectiveDate,
    }),
    enabled:
      priceListDetails?.effectiveDate !==
      form?.state?.values.demographicPriceList.effectiveDate,
  })

  const isFormValid = useStore(form.store, (state) => {
    return state.canSubmit && state.isTouched
  })

  if (!route) return 'ViewPriceListLayout must be used within a pricing route.'
  if (!params)
    return 'EditPriceListLayout must be used within a edit-price-list route.'

  return (
    <div className='flex min-h-0 flex-1 flex-col'>
      <div className='flex flex-auto flex-col overflow-y-auto p-8 pb-4'>
        <h1 className='mb-4 text-lg font-medium'>
          {'Edit ' + (priceListDetails?.name ?? '-')}
          <small className='ml-2 text-zinc-400'>{params.code}</small>
        </h1>
        <div className='flex-auto overflow-scroll rounded-lg border bg-white p-4'>
          <AddPriceListContextProvider state={form}>
            {children}
          </AddPriceListContextProvider>
        </div>
      </div>
      <div className='flex border-t bg-white p-4'>
        <div className='flex-auto'>
          <Link href={routeTo('/pricing/[effectiveDate]/price-list', params)}>
            <Button className='btn'>
              <span className='px-16'>Cancel</span>
            </Button>
          </Link>
        </div>
        <div className='flex gap-2'>
          <Button
            className='btn-primary disabled:cursor-not-allowed disabled:bg-zinc-300 disabled:text-zinc-500'
            disabled={!isFormValid}
            onClick={form.handleSubmit}
          >
            <span className='px-16'>Save</span>
          </Button>
        </div>
      </div>
    </div>
  )
}
