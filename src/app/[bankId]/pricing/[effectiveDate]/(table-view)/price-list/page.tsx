'use client'

import { But<PERSON> } from '@/components/Button'
import { routeTo, useRoute } from '../../../routing'
import { data } from '@/lib/unions/Union'
import Link from 'next/link'
import { useQuery } from '@tanstack/react-query'
import { query } from '../../queries'
import { DateFilter } from '@/components/Filter/DateFilter'
import { parseServerFormat, toServerFormat, toUIFormat } from '@/lib/date'
import { matches } from '@/lib/unions/matches'
import { useRouter } from 'next/navigation'
import { SearchBar } from '@/components/SearchBar'
import { useMemo, useState } from 'react'
import { SortedTable } from '@/components/Table/SortedTable'
import { ColumnDef, ColumnFiltersState } from '@tanstack/react-table'
import VerticalEllipsisMenu from '@/components/VerticalEllipsisMenu/VerticalEllipsisMenu'
import { FolderOpenIcon } from '@heroicons/react/24/outline'
import { ActionInterface } from '@/components/VerticalEllipsisMenu/type'
import { PriceListWithRanking } from '../../manage-ranking/types'
import { calculatePriceListRanking } from '../../../helpers'
import { RouteParamsRecord } from '@/lib/defineRoute'

export default function PriceListPage() {
  const route = useRoute()
  const router = useRouter()

  const effectiveDate = (
    data(route!).params as RouteParamsRecord<['effectiveDate']>
  ).effectiveDate
  const {
    data: priceLists,
    status,
    error,
  } = useQuery(query('/listDemographicPriceLists', { effectiveDate }))

  const {
    data: priceListRanking,
    status: rankingStatus,
    error: rankingError,
  } = useQuery(query('/getDemographicPriceListRanking', { effectiveDate }))

  const orderedData = useMemo(() => {
    if (!priceListRanking?.ranking || !priceLists) {
      return []
    }
    return calculatePriceListRanking(priceLists!, priceListRanking!)
  }, [priceLists, priceListRanking, error])

  /**
   * Search price lists by name
   */
  const [searchText, setSearchText] = useState('')
  const columnFilters: ColumnFiltersState = [{ id: 'name', value: searchText }]

  /**
   * Show number of visible price lists
   */
  const [numVisiblePriceLists, setNumVisiblePriceLists] = useState(
    priceLists?.length ?? 0,
  )

  /**
   * Bail out if in a bad state (e.g. wrong route, no data, query error)
   */
  if (!matches(route, '/pricing/[effectiveDate]/price-list')) {
    return "PriceListPage must be used in the '/pricing/[effectiveDate]/price-list' route."
  }
  // if (status === 'pending' || rankingStatus === 'pending') return 'Loading...'
  if (status === 'error' && !error.message.includes('404')) {
    console.error(error)
    return `[${error.name}] ${error.message}`
  } else if (
    rankingStatus === 'error' &&
    !rankingError.message.includes('404')
  ) {
    console.error(rankingError)
    return `[${rankingError.name}] ${rankingError.message}`
  }

  const routeParams = data(route).params

  const actionsList = (row: PriceListWithRanking): ActionInterface[] => {
    return [
      {
        label: 'Open',
        icon: <FolderOpenIcon className='size-4' />,
        onClick: () => {
          router.push(
            routeTo('/pricing/[effectiveDate]/view-price-list/[code]/details', {
              ...routeParams,
              effectiveDate: row.effectiveDate!,
              code: row.code!,
            }),
          )
        },
      },
    ]
  }

  /**
   * Price list table column definitions
   */
  const columns: ColumnDef<PriceListWithRanking>[] = [
    {
      header: 'Rank',
      accessorKey: 'ranking',
      meta: {
        className: 'pl-3',
      },
    },
    {
      header: 'Name',
      accessorKey: 'name',
    },
    {
      header: 'Code',
      accessorKey: 'code',
    },
    {
      header: 'Lead price list',
      accessorKey: 'isLeadPriceList',
      cell: ({ getValue }) => (getValue<boolean>() ? 'Yes' : 'No'),
    },
    {
      //TODO: @ileana verify this when services is implemented
      header: 'Number of services',
      accessorKey: 'serviceCount',
      cell: ({ getValue }) => (getValue<number>() ? getValue<number>() : 0),
    },
    {
      header: 'Start date',
      accessorKey: 'effectiveDate',
      cell: ({ getValue }) => toUIFormat(getValue<Date>()),
    },
    {
      header: '',
      accessorKey: 'none',
      cell: ({ row: { original } }) => (
        <VerticalEllipsisMenu actions={actionsList(original)} />
      ),
      meta: {
        className: 'justify-end pr-8',
      },
    },
  ]
  const updateEffectiveDate = (date: Date) => {
    router.push(
      routeTo('/pricing/[effectiveDate]/price-list', {
        ...routeParams,
        effectiveDate: toServerFormat(date),
      }),
    )
  }

  return (
    <div className='flex min-h-0 w-full flex-auto flex-grow flex-col rounded-lg border bg-white p-6'>
      <div className='mb-6 flex'>
        <hgroup className='flex-auto'>
          <h1 className='text-lg'>Price list</h1>
          <p className='text-zinc-400'>All the demographic-based price lists</p>
        </hgroup>
        <div className='flex items-center gap-4'>
          <Button className='btn'>Export</Button>
          <Link
            href={routeTo(
              '/pricing/[effectiveDate]/manage-ranking',
              data(route).params,
            )}
          >
            <Button className='btn'>Manage hierarchy</Button>
          </Link>
          <Link
            href={routeTo('/pricing/add-price-list/step-1', {
              bankId: data(route).params.bankId,
            })}
          >
            <Button className='btn-primary text-white'>Add price list</Button>
          </Link>
        </div>
      </div>
      <div className='flex min-h-0 flex-auto flex-col rounded-lg border'>
        <div className='flex items-center p-4'>
          <div>
            <DateFilter
              className={
                'border border-zinc-300 bg-zinc-50 hover:bg-zinc-200 focus-visible:outline-indigo-300'
              }
              label='Effective date'
              title='Select the effective date'
              initialDate={parseServerFormat(routeParams.effectiveDate)}
              onDateChange={updateEffectiveDate}
            />
          </div>
          <div className='flex flex-auto items-center justify-end gap-4'>
            <SearchBar
              className='w-80'
              defaultLabel='Search for a price list'
              value={searchText}
              onValueChange={setSearchText}
            />
            <p className='text-zinc-400'>
              {numVisiblePriceLists} price list
              {numVisiblePriceLists > 1 && 's'}
            </p>
          </div>
        </div>
        <div className='flex min-h-0 flex-col'>
          {orderedData && (
            <SortedTable
              data={orderedData}
              columns={columns}
              columnFilters={columnFilters}
              onVisibleRowsChanged={(rows) =>
                setNumVisiblePriceLists(rows.length)
              }
              initialSortingState={[{ desc: false, id: 'ranking' }]}
            />
          )}
        </div>
      </div>
    </div>
  )
}
