'use client'

import { Tab } from '@/components/Tabs/Tab'
import { TabList } from '@/components/Tabs/TabList'
import { routeTo, useRoute } from '../../routing'
import { matches } from '@/lib/unions/matches'
import { data } from '@/lib/unions/Union'
import { RouteParamsRecord } from '@/lib/defineRoute'

export default function PricingLayout({ children }: React.PropsWithChildren) {
  const route = useRoute()
  if (!route) return 'PricingLayout must be used within a pricing route'
  const params = data(route).params

  return (
    <div className='flex min-h-0 flex-1 flex-col p-8'>
      <h1 className='mb-4 text-lg font-medium'>Pricing</h1>
      <TabList>
        <Tab
          href={routeTo(
            '/pricing/[effectiveDate]/price-list',
            params as RouteParamsRecord<['effectiveDate']>,
          )}
          active={matches(route, '/pricing/[effectiveDate]/price-list')}
        >
          Price list
        </Tab>
        <Tab
          href={routeTo(
            '/pricing/[effectiveDate]/promotions',
            params as RouteParamsRecord<['effectiveDate']>,
          )}
          active={matches(route, '/pricing/[effectiveDate]/promotions')}
        >
          Promotions
        </Tab>
      </TabList>
      {children}
    </div>
  )
}
