import { routeTo } from '@/app/[bankId]/pricing/routing'
import { Page } from '@playwright/test'
import { createPageObjectProvider } from '../../../../../../../test/e2e/createPageObjectProvider'
import { PriceListTablePageObject } from './PriceListTablePageObject'
import { PromotionsTablePageObject } from './PromotionsTablePageObject'

export class PricingTableViewPageObject {
  static get provider() {
    return createPageObjectProvider(
      (page) => new PricingTableViewPageObject(page),
    )
  }

  constructor(readonly page: Page) {}

  async goto({
    effectiveDate = '2025-01-13',
  }: { effectiveDate?: string } = {}) {
    await this.page.goto(
      routeTo('/pricing/[effectiveDate]/price-list', {
        bankId: '999',
        effectiveDate,
      }),
    )
  }

  async viewPriceList() {
    await this.page
      .getByRole('link', { name: 'Price list', exact: true })
      .click()
    const priceListTable = new PriceListTablePageObject(this.page)
    await priceListTable.expectToBeVisible()
    return priceListTable
  }

  async viewPromotions() {
    await this.page
      .getByRole('link', { name: 'Promotions', exact: true })
      .click()
    const promotionsTable = new PromotionsTablePageObject(this.page)
    await promotionsTable.expectToBeVisible()
    return promotionsTable
  }
}
