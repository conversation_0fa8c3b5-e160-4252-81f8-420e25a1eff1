import { expect, Page } from '@playwright/test'
import { createPageObjectProvider } from '../../../../../../../test/e2e/createPageObjectProvider'

export class PromotionsTablePageObject {
  static get provider() {
    return createPageObjectProvider(
      (page) => new PromotionsTablePageObject(page),
    )
  }

  constructor(readonly page: Page) {}

  expectToBeVisible() {
    return expect(
      this.page.getByRole('heading', { name: 'Promotions' }),
    ).toBeVisible()
  }
}
