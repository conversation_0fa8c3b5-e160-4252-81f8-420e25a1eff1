import { expect, Page } from '@playwright/test'
import { createPageObjectProvider } from '../../../../../../../test/e2e/createPageObjectProvider'
import { AddPriceListPageObject } from '../../../add-price-list/_testing/AddPriceListPageObject'

export class PriceListTablePageObject {
  static get provider() {
    return createPageObjectProvider(
      (page) => new PriceListTablePageObject(page),
    )
  }

  constructor(readonly page: Page) {}

  async addPriceList() {
    await this.page
      .getByRole('link', { name: 'Add price list', exact: true })
      .click()
    const addPriceList = new AddPriceListPageObject(this.page)
    await addPriceList.expectToBeVisible()
    return addPriceList
  }

  expectToBeVisible() {
    return expect(
      this.page.getByRole('heading', { name: 'Price list' }),
    ).toBeVisible()
  }
}
