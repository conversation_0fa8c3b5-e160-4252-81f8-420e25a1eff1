import { revenueConnectClient } from '@/api/revenueConnectClient'
import { withMetrics } from '@/lib/middleware/withMetrics'
import { defineQueries, defineQuery } from '@/lib/state/defineQuery'

const allPriceLists = defineQuery(
  revenueConnectClient,
  '/listDemographicPriceLists',
  (reqPayload) => [reqPayload?.effectiveDate],
)

const demographicPriceListRanking = defineQuery(
  revenueConnectClient,
  '/getDemographicPriceListRanking',
  (reqPayload) => [reqPayload?.effectiveDate],
)

const allUserFields = defineQuery(
  revenueConnectClient,
  '/getUserFieldsConfigurations',
)

const getDemographicPriceList = defineQuery(
  revenueConnectClient,
  '/getDemographicPriceList',
  (reqPayload) => [reqPayload?.effectiveDate, reqPayload?.code],
)

const getDemographicPriceListVersions = defineQuery(
  revenueConnectClient,
  '/getDemographicPriceListVersions',
  (reqPayload) => [reqPayload?.code],
)

const getDemographicCriteria = defineQuery(
  revenueConnectClient,
  '/getDemographicCriteria',
  (reqPayload) => [reqPayload?.effectiveDate, reqPayload?.code],
)

const serviceCatalog = defineQuery(
  revenueConnectClient,
  '/getServiceCatalogMapping',
  (reqPayload) => [reqPayload?.effectiveDate],
)

const servicePrice = defineQuery(
  revenueConnectClient,
  '/getServicePricesAsOf',
  (payload) => (payload ? [payload] : []),
)

const getAccountTypes = defineQuery(
  revenueConnectClient,
  '/getAccountTypes',
  (payload) => (payload ? [payload] : []),
)

export const query = defineQueries(
  [
    allPriceLists,
    allUserFields,
    demographicPriceListRanking,
    getDemographicPriceList,
    getDemographicPriceListVersions,
    getDemographicCriteria,
    serviceCatalog,
    servicePrice,
    getAccountTypes,
  ],
  withMetrics(),
)
