import { components, paths } from '@/api/schema'
import {
  CatalogCategory as ParentCatalogCategory,
  CatalogService,
} from '@/app/[bankId]/services/_hooks/useServiceCatalog'

export interface CategoryByCodeType {
  categoryCodes: string[]
  current: Node
  parent: null | Node
  serviceCodes: string[]
}

export interface ServiceByCodeType {
  current: {
    code: string
    effectiveDate: string
    serviceType: string
    description: string
  }
  parent: Node
}

export type CatalogType = {
  categoryByCode: Record<string, CategoryByCodeType>
  serviceByCode: Record<string, ServiceByCodeType>
}

export type ServicePrice = components['schemas']['ServicePrice']

export type ServiceAndPriceObj = ServicePrice &
  CatalogService & {
    parentCatalog?: string[]
    priceTypes?: string[]
    serviceTypes?: string[]
  }

export type ServiceAndPrice = Record<string, ServiceAndPriceObj>

export type RowType = {
  name: string
  serviceCode: string
  serviceType: string
  priceType: string
  price: number
  disposition: string
  subRows: []
  serviceByCategoryCodes: string[]
  parentCatalog: string[]
  childrenCategories: string[]
  priceTypes: string[]
  serviceTypes: string[]
  isService: boolean
  baseFee?: number
  maximumFee?: number
  minimumFee?: number
  costType?: string
  cost?: number
  parent?: CatalogService['parent']
}

export type SubRows =
  | (Partial<components['schemas']['ServiceCategory']> & {
      subRows: SubRows[]
      parentCatalog?: string[]
      childrenCategories?: string[]
      priceTypes?: string[]
      serviceTypes?: string[]
      isService?: boolean
      serviceCode?: string
      serviceType?: string
      name?: string
      priceType?: string
      price?: number
      disposition?: string
      baseFee?: number
      maximumFee?: number
      minimumFee?: number
      costType?: string
      cost?: number
      parent?: CatalogService['parent']
    })
  | RowType
  | null

export type PricingCatalogCategory = ParentCatalogCategory & {
  subRows?: SubRows[]
  serviceByCategoryCodes?: string[]
  serviceCodes: string[]
  parentCatalog?: string[]
  childrenCategories?: string[]
  priceTypes?: string[]
  serviceTypes?: string[]
}
