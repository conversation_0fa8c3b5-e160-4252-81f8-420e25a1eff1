import { SortedTable } from '@/components/Table/SortedTable'
import PriceListViewGroup from '../../_components/PriceListViewGroup'
import { SubRows } from './types'
import { useMemo } from 'react'
import { useQuery } from '@tanstack/react-query'
import { query } from '../../../queries'
import clsx from 'clsx'
import { ColumnDef } from '@tanstack/react-table'
import { CheckIcon } from '@heroicons/react/24/outline'
import { XMarkIcon } from '@heroicons/react/24/solid'
import { Button } from '@/components/Button'
import Link from 'next/link'
import { routeTo, useRoute } from '@/app/[bankId]/pricing/routing'
import { unwrap } from '@/lib/unions/unwrap'
import { convertToSentenceCase } from './utils'

interface PriceChangedRow {
  changed: boolean
  pricingAttributeLabel: string
  priceListValue: string | number | undefined
  defaultListValue: string | number | undefined
}
const columns: ColumnDef<PriceChangedRow>[] = [
  {
    header: 'Changed',
    accessorKey: 'changed',
    meta: {
      className: 'min-w-[100px] max-w-[100px] pl-5',
    },
    cell: ({ getValue }) => (
      <div>
        <div className='flex flex-row gap-2'>
          <div className={clsx('text-pretty p-2')}>
            {getValue<boolean>() && (
              <CheckIcon className='h-4 w-4 text-indigo-600' />
            )}
          </div>
        </div>
      </div>
    ),
  },
  {
    header: 'Pricing Attribute',
    accessorKey: 'pricingAttributeLabel',
  },
  {
    header: 'Value in the Price List',
    accessorKey: 'priceListValue', //need to add modal to view tiers
  },
  {
    header: 'Default Pricing',
    accessorKey: 'defaultListValue',
  },
]
export function ServiceRowDrawer({
  rowData,
  effectiveDate,
  closeDrawer,
}: {
  rowData: SubRows
  effectiveDate: string
  closeDrawer: (close: boolean) => void
}) {
  const route = unwrap(
    useRoute(),
    '/pricing/[effectiveDate]/view-price-list/[code]/service-pricing',
  )!
  // fetch the default service list
  const {
    data: defaultData,
    error: defaultPriceListError,
    isSuccess: defaultPriceListSuccess,
  } = useQuery(
    query('/getServicePricesAsOf', {
      asOfDate: effectiveDate,
      serviceCodes: [rowData?.serviceCode!], //serviceCode from row data
      pricingHierarchyEntryType: 'STANDARD',
    }),
  )

  const data = useMemo(() => {
    const tierData = {
      priceType:
        convertToSentenceCase(defaultData?.[0]?.priceType) ?? 'Does Not Change',
      priceValue:
        defaultData?.[0]?.priceType?.includes('TIER') ?
          defaultData?.length + ' tier(s)'
        : defaultData?.[0]?.priceValue,
      baseFee: defaultData?.[0]?.baseFee,
      disposition:
        convertToSentenceCase(defaultData?.[0]?.disposition) ??
        'Does Not Change',
      maximumFee: defaultData?.[0]?.maximumFee,
      minimumFee: defaultData?.[0]?.minimumFee,
      costType:
        convertToSentenceCase(defaultData?.[0]?.costType) ?? 'Does Not Change',
      costValue: defaultData?.[0]?.costValue,
    }

    return tierData
  }, [defaultData])

  const {
    priceType,
    priceValue,
    baseFee,
    disposition,
    maximumFee,
    minimumFee,
    costType,
    costValue,
  } = data ?? {} //changed to accommodate tier data of price tier and threshold tier

  //make a list of row values
  const combinedListData = [
    {
      pricingAttributeLabel: 'Price type',
      priceListValue:
        convertToSentenceCase(rowData?.priceType) ?? 'Does not change',
      defaultListValue: priceType ?? 'Does not change',
    },
    {
      pricingAttributeLabel: 'Price',
      priceListValue:
        rowData?.priceType?.includes('TIER') ?
          defaultData?.length + ' tier(s)'
        : (rowData?.price ?? 'Does not change'),
      defaultListValue: priceValue ?? 'Does not change',
    },
    {
      pricingAttributeLabel: 'Base Fee',
      priceListValue: rowData?.baseFee ?? 'Does not change',
      defaultListValue: baseFee ?? 'Does not change',
    },
    {
      pricingAttributeLabel: 'Disposition',
      priceListValue:
        convertToSentenceCase(rowData?.disposition) ?? 'Does not change',
      defaultListValue: disposition ?? 'Does not change',
    },
    {
      pricingAttributeLabel: 'Max Fee',
      priceListValue: rowData?.maximumFee ?? 'Does not change',
      defaultListValue: maximumFee ?? 'Does not change',
    },
    {
      pricingAttributeLabel: 'Min Fee',
      priceListValue: rowData?.minimumFee ?? 'Does not change',
      defaultListValue: minimumFee ?? 'Does not change',
    },
    {
      pricingAttributeLabel: 'Cost type',
      priceListValue:
        convertToSentenceCase(rowData?.costType) ?? 'Does not change',
      defaultListValue: costType ?? 'Does not change',
    },
    {
      pricingAttributeLabel: 'Cost',
      priceListValue: rowData?.cost ?? 'Does not change',
      defaultListValue: costValue ?? 'Does not change',
    },
  ]

  // check if the current price list data is different from default price list
  const tableData: PriceChangedRow[] = combinedListData.map((row) => {
    // TODO: make a deep compare function when there are multiple tiers
    const changed = row.defaultListValue !== row.priceListValue
    return {
      changed: changed,
      ...row,
    }
  })

  const columnFilters = useMemo(() => [], [])

  if (!defaultPriceListSuccess) return <>Loading...</>
  if (defaultPriceListError) return defaultPriceListError
  return (
    <div className='overflow flex min-h-0 flex-1 flex-col'>
      <div className='flex flex-row justify-between'>
        <div className='mb-3 p-2 pt-0'>
          <h1 className='mb-1 text-lg'>
            {rowData?.name}{' '}
            <span className='text-sm text-zinc-500'>
              {rowData?.serviceCode}
            </span>
          </h1>
          <p className='text-zinc-500'>
            View service pricing details for this promotion
          </p>
        </div>
        <div
          role='button'
          onClick={() => {
            closeDrawer(false)
          }}
        >
          <XMarkIcon className='h-5 w-5' />
        </div>
      </div>
      <PriceListViewGroup
        label={'Service Name'}
        value={rowData?.name}
        className={'flex-1 basis-1/2 p-2 pt-0'}
      />
      <div className='flex flex-wrap'>
        <PriceListViewGroup
          label={'Service Code'}
          value={rowData?.serviceCode ?? '-'}
          className={'flex-1 basis-1/2 p-2 pt-0'}
        />
        <PriceListViewGroup
          label={'Service Category'}
          value={rowData?.parent?.name}
        />
        <PriceListViewGroup
          label={'Service Type'}
          value={rowData?.serviceType ?? '-'}
          className={'flex-1 basis-1/2 p-2 pt-0'}
        />
      </div>

      <div className='border-t-2 border-zinc-300 pt-3'>
        <h1 className='mb-1 p-2 text-lg'>Service pricing </h1>

        <PriceListViewGroup
          label='Effective Date'
          value={effectiveDate}
          className={'flex-1 basis-1/2 p-2 pt-0'}
        />

        <SortedTable
          data={tableData}
          columns={columns}
          columnFilters={columnFilters}
        />
      </div>
      <footer className='flex flex-row justify-end gap-3 p-2 pt-4 align-middle'>
        <Button onClick={() => closeDrawer(false)}>Cancel</Button>
        <Link
          href={routeTo(
            '/pricing/[effectiveDate]/edit-price-list/[code]/service-pricing',
            route.params,
          )}
        >
          <Button className='bg-indigo-600 text-white'>Edit</Button>
        </Link>
      </footer>
    </div>
  )
}
