'use client'

import { routeTo, useRoute } from '@/app/[bankId]/pricing/routing'
import { unwrap } from '@/lib/unions/unwrap'
import { useQuery } from '@tanstack/react-query'
import { query } from '../../../queries'
import {
  Catalog,
  useServiceCatalog,
} from '@/app/[bankId]/services/_hooks/useServiceCatalog'
import { ExpandedTable } from '@/components/Table/ExpandedTable'
import { ColumnDef, ColumnFiltersState, Row } from '@tanstack/react-table'
import { Button } from '@/components/Button'
import { ChevronDownIcon, ChevronRightIcon } from '@heroicons/react/24/outline'
import clsx from 'clsx'
import { useCallback, useMemo, useState } from 'react'
import {
  convertToSentenceCase,
  getCatalogFilterOptions,
  getFilterOptions,
  transformData,
} from './utils'
import { SubRows } from './types'
import { SearchBar } from '@/components/SearchBar'
import { Select } from '@/components/Input/Select'
import Drawer from '../../../../../../../components/SideDrawer'
import { ServiceRowDrawer } from './ServiceRowDrawer'
import Link from 'next/link'

const columns: ColumnDef<SubRows>[] = [
  {
    header: 'serviceByCategoryCodes', //will have all the services linked to the category
    accessorKey: 'serviceByCategoryCodes',
    filterFn: (row, columnId, filterValue) => {
      if (!filterValue || !filterValue.length) return true
      const rows = row.getValue<string[]>(columnId)
      return (
        rows.filter((serviceName) =>
          serviceName.toLowerCase().includes(filterValue.toLowerCase()),
        )?.length > 0
      )
    },
  },
  {
    header: 'parentCatalog', //will have all the services linked to the category
    accessorKey: 'parentCatalog',
    filterFn: (row, columnId, filterValue) => {
      if (!filterValue || !filterValue.length) return true
      const rows = row.getValue<string[]>(columnId)
      return (
        rows.filter((catalog) =>
          catalog.toLowerCase().includes(filterValue.toLowerCase()),
        )?.length > 0
      )
    },
  },
  {
    header: 'serviceCatalog',
    accessorKey: 'serviceCatalog',
  },
  {
    header: 'serviceCatalogCode',
    accessorKey: 'serviceCatalogCode',
  },

  {
    header: 'Name',
    accessorKey: 'name',
    meta: {
      className: 'min-w-[275px] max-w-[275px] overflow-hidden text-ellipsis',
    },
    cell: ({ row, getValue }) => (
      <div
        style={{
          paddingLeft: `${
            row.getCanExpand() ? row.depth * 1 : (row.depth + 1) * 2
          }rem`,
        }}
      >
        <div className='flex flex-row gap-2'>
          {row.getCanExpand() && (
            <Button
              className={'border-none'}
              onClick={row.getToggleExpandedHandler()}
            >
              {row.getIsExpanded() ?
                <ChevronDownIcon className='h-4 w-4 text-app-color-primary' />
              : <ChevronRightIcon className='h-4 w-4 text-app-color-primary' />}
            </Button>
          )}
          <div className={clsx('text-pretty p-2')}>{getValue<string>()}</div>
        </div>
      </div>
    ),
  },
  {
    header: '',
    meta: {
      className: 'min-w-[150px] max-w-[150px]',
    },
    accessorKey: 'priceTypes',
    filterFn: (row, columnId, filterValue) => {
      if (!filterValue || !filterValue.length) return true
      const rowValue = row.getValue<string[]>(columnId)
      return (
        rowValue.filter((catalog) =>
          catalog.toLowerCase().includes(filterValue.toLowerCase()),
        )?.length > 0
      )
    },
  },
  {
    header: '',
    accessorKey: 'serviceTypes',
    filterFn: (row, columnId, filterValue) => {
      if (!filterValue || !filterValue.length) return true
      const rowValue = row.getValue<string[]>(columnId)
      return (
        rowValue.filter((catalog) =>
          catalog.toLowerCase().includes(filterValue.toLowerCase()),
        )?.length > 0
      )
    },
  },
  {
    header: 'Code',
    accessorKey: 'serviceCode',
    meta: {
      className: 'min-w-[85px] max-w-[85px]',
    },
    cell: ({ getValue }) => (
      <div className={'text-pretty p-2'}>{getValue<string>()}</div>
    ),
  },
  {
    header: 'Service Type',
    meta: {
      className: 'min-w-[170px] max-w-[170px]',
    },
    accessorKey: 'serviceType',
    cell: ({ row, getValue }) => {
      return (
        <div className='text-pretty p-2'>
          {convertToSentenceCase(getValue<string>())}
        </div>
      )
    },
  },
  {
    header: 'Price Type',
    meta: {
      className: 'min-w-[170px] max-w-[170px]',
    },
    accessorKey: 'priceType',
    cell: ({ getValue }) => (
      <div className='text-pretty p-2'>
        {convertToSentenceCase(getValue<string>())}
      </div>
    ),
  },
  {
    header: 'Price',
    meta: {
      className: 'min-w-[120px] max-w-[120px]',
    },
    accessorKey: 'price',
    cell: ({ getValue }) => (
      <div className='text-pretty p-2'>{getValue<string>()}</div>
    ),
  },
  {
    header: 'Disposition',
    meta: {
      className: 'min-w-[175px] max-w-[175px]',
    },
    accessorKey: 'disposition',
    cell: ({ getValue }) => (
      <div className='text-pretty p-2'>
        {convertToSentenceCase(getValue<string>())}
      </div>
    ),
  },
]

export default function ViewPriceListServicePricingPage() {
  const route = unwrap(
    useRoute(),
    '/pricing/[effectiveDate]/view-price-list/[code]/service-pricing',
  )!
  const { effectiveDate, code } = route.params
  const [serviceFilter, setServiceFilter] = useState('')
  const [catalogFilter, setCatalogFilter] = useState('')
  const [priceTypeFilter, setPriceTypeFilter] = useState('')
  const [serviceTypeFilter, setServiceTypeFilter] = useState('')
  const [openServiceDrawer, setOpenServiceDrawer] = useState(false)
  const [selectedService, setSelectedService] = useState<SubRows>({} as SubRows)

  const catalogData = useServiceCatalog({
    effectiveDate: route.params.effectiveDate,
  })

  const { error: catalogError, isLoading: catalogLoading } = catalogData
  //to get price list based on effective date,code
  const {
    data: priceData,
    error: viewPriceListError,
    isSuccess: viewPriceListSuccess,
  } = useQuery(
    query('/getServicePricesAsOf', {
      asOfDate: effectiveDate,
      pricingHierarchyEntryCode: code, //priceListCode
      pricingHierarchyEntryType: 'PRICE_LIST',
    }),
  )

  const tableData = useMemo(() => {
    if (catalogData?.data && priceData) {
      const catalog: Catalog = {
        categoryByCode: catalogData.data.categoryByCode!,
        serviceByCode: catalogData.data.serviceByCode!,
      }
      return transformData(catalog, priceData)
    }
    return []
  }, [catalogData.data, priceData])

  //meta columns created for filtering hierarchy rows
  const columnFilters: ColumnFiltersState = [
    {
      id: 'serviceByCategoryCodes',
      value: serviceFilter,
    },
    {
      id: 'parentCatalog',
      value: catalogFilter,
    },
    {
      id: 'priceTypes',
      value: priceTypeFilter,
    },
    {
      id: 'serviceTypes',
      value: serviceTypeFilter,
    },
  ]

  const catalogFilterOptions = useMemo(() => {
    return getCatalogFilterOptions(catalogData.data!, tableData)
  }, [catalogData.data, tableData])

  const priceTypeFilterOptions = useMemo(() => {
    return Array.from(getFilterOptions('priceTypes', tableData, new Set([''])))
  }, [tableData])

  const serviceTypeFilterOptions = useMemo(() => {
    return Array.from(
      getFilterOptions('serviceTypes', tableData, new Set([''])),
    )
  }, [tableData])

  const onRowClick = useCallback((row: Row<SubRows>) => {
    if (row.original?.isService) {
      setOpenServiceDrawer(true)
      setSelectedService(row.original)
    }
  }, [])

  const closeDrawer = useCallback((close: boolean) => {
    setOpenServiceDrawer(close)
    setSelectedService({} as SubRows)
  }, [])

  if (!viewPriceListSuccess || catalogLoading) return <>Loading...</>
  if (viewPriceListError || catalogError) return viewPriceListError

  return (
    <div className='overflow flex min-h-0 flex-1 flex-col p-4'>
      <div className='mb-10 flex'>
        <hgroup className='flex-auto'>
          <h1 className='mb-1 text-lg'>Services and pricing</h1>
          <p className='text-zinc-500'>
            This price list includes {priceData?.length ?? 0} services.
          </p>
        </hgroup>
        <div className='flex items-center gap-4'>
          <Link
            href={routeTo(
              '/pricing/[effectiveDate]/edit-price-list/[code]/service-pricing',
              route.params,
            )}
          >
            <Button className='btn-primary text-white'>Edit</Button>
          </Link>
        </div>
      </div>

      <div className='flex flex-1 gap-5'>
        <Select
          name='serviceTypeFilter'
          options={serviceTypeFilterOptions}
          className='w-[175px]'
          value={serviceTypeFilter}
          onChange={setServiceTypeFilter}
          placeholder='Service Type'
          renderOption={(value) => {
            return value === '' ? 'All Service Types' : value
          }}
        />
        <Select
          name='priceTypeFilter'
          options={priceTypeFilterOptions}
          className='w-[175px]'
          value={priceTypeFilter}
          onChange={setPriceTypeFilter}
          placeholder='Price Type'
          renderOption={(value) => {
            return value === '' ? 'All Price Types' : value
          }}
        />
        <Select
          name='catalogFilter'
          placeholder='Search Service Catalog'
          options={Array.from(catalogFilterOptions.keys()) ?? []}
          className='w-[230px]'
          value={catalogFilter}
          onChange={setCatalogFilter}
          renderOption={(value) => {
            return catalogFilterOptions.get(value).label
          }}
          renderSelected={(value) => {
            return catalogFilterOptions.get(value).label
          }}
        />

        <SearchBar
          className='w-[300px]'
          defaultLabel='Search Services'
          value={serviceFilter}
          onValueChange={setServiceFilter}
        />
      </div>

      <ExpandedTable
        data={tableData}
        columns={columns}
        getSubRows={(row) => row?.subRows}
        onRowClick={onRowClick}
        columnFilters={columnFilters}
        noDataText={'No Data to display'}
        columnVisibility={{
          serviceCatalog: false,
          serviceCatalogCode: false,
          serviceByCategoryCodes: false,
          parentCatalog: false,
          priceTypes: false,
          serviceTypes: false,
        }}
      />
      <Drawer open={openServiceDrawer} closeDrawer={closeDrawer}>
        <ServiceRowDrawer
          rowData={selectedService}
          effectiveDate={route.params.effectiveDate}
          closeDrawer={closeDrawer}
        />
      </Drawer>
    </div>
  )
}
