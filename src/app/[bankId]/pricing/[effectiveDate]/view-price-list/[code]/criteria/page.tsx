'use client'

import { unwrap } from '@/lib/unions/unwrap'
import { useForm } from '@tanstack/react-form'
import { useQuery } from '@tanstack/react-query'
import { useMemo } from 'react'
import { isUnique } from '@/lib/functional/isUnique'
import { routeTo, useRoute } from '@/app/[bankId]/pricing/routing'
import { query } from '../../../queries'
import { ViewCriteria } from '@/app/[bankId]/pricing/[effectiveDate]/view-price-list/_components/ViewCriteria'
import {
  DemographicCriteria,
  HydratedDemographicPriceListForm,
} from '@/api/formToApiSchema'
import { defaultHydratedServicePricing } from '../../types'
import Link from 'next/link'
import { Button } from '@/components/Button'

const enum priceListType {
  DEMOGRAPHIC = 'DEMOGRAPHIC',
}

export default function ViewPriceListCriteriaPage() {
  const route = unwrap(
    useRoute(),
    '/pricing/[effectiveDate]/view-price-list/[code]/criteria',
  )!
  const { effectiveDate, code } = route.params

  //to get price list based on effective date,code
  const {
    data,
    error: viewCriteriaListError,
    isSuccess: viewCriteriaSuccess,
  } = useQuery(query('/getDemographicCriteria', { effectiveDate, code }))

  const viewCriteriaList: DemographicCriteria[] = useMemo(() => {
    if (data) {
      return data.map((item) => ({
        ...item,
        demographicCriteriaId: {
          ...item.demographicCriteriaId!,
        },
      }))
    }
    return []
  }, [data])

  const criteriaCount =
    viewCriteriaList.map(({ criteriaCode }) => criteriaCode).filter(isUnique())
      .length ?? 0

  const defaultValues = {
    demographicPriceList: {
      effectiveDate: effectiveDate ?? '',
      code: code,
      name: '',
      priceListType: priceListType.DEMOGRAPHIC,
      isLeadPriceList: false,
      term: null,
      currency: 'USD',
    },
    demographicCriteria: data ?? [],
    ranking: {
      effectiveDate: '',
      ranking: '',
    },
    servicePricing: defaultHydratedServicePricing,
  }

  const form = useForm<HydratedDemographicPriceListForm>({
    defaultValues: defaultValues as HydratedDemographicPriceListForm,
  }) //get count of unique criteria
  if (!viewCriteriaSuccess) return <> Loading...</>
  if (viewCriteriaListError) return viewCriteriaListError

  return (
    <div className='overflow flex min-h-0 flex-1 flex-col p-4'>
      <div className='mb-6 flex'>
        <hgroup className='flex-auto'>
          <h1 className='mb-1 text-lg'>Demographic and user field criteria</h1>
          <p className={'mb-3 text-app-color-secondary'}>
            This list includes {criteriaCount} criteria
          </p>
        </hgroup>
        <div className='flex items-center gap-4'>
          <Link
            href={routeTo(
              '/pricing/[effectiveDate]/edit-price-list/[code]/criteria',
              route.params,
            )}
          >
            <Button className='btn-primary text-white'>Edit</Button>
          </Link>
        </div>
      </div>
      <form.Field name={'demographicCriteria'}>
        {(field) => <ViewCriteria field={field} priceListCode={code} />}
      </form.Field>
    </div>
  )
}
