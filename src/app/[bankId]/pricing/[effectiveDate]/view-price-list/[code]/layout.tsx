'use client'

import { Tab } from '@/components/Tabs/Tab'
import { parseServerFormat, toServerFormat } from '@/lib/date'
import {
  Breadcrumbs,
  BreadcrumbSegment,
} from '@/components/Breadcrumbs/Breadcrumbs'
import { DateFilter } from '@/components/Filter/DateFilter'
import { matches } from '@/lib/unions/matches'
import { TabList } from '@/components/Tabs/TabList'
import { match } from '@/lib/unions/match'
import { useRouter } from 'next/navigation'
import { routeTo, useRoute } from '@/app/[bankId]/pricing/routing'
import { PriceListVersions } from '../_components/PriceListVersions'
import { useQuery } from '@tanstack/react-query'
import { query } from '../../queries'
import { RouteParamsRecord } from '@/lib/defineRoute'

export default function ViewPriceListLayout({
  children,
}: React.PropsWithChildren) {
  const router = useRouter()
  const route = useRoute()!

  const params = match(route, {
    '/pricing/[effectiveDate]/view-price-list/[code]/details': ({ params }) =>
      params,
    '/pricing/[effectiveDate]/view-price-list/[code]/criteria': ({ params }) =>
      params,
    '/pricing/[effectiveDate]/view-price-list/[code]/service-pricing': ({
      params,
    }) => params,
    _: ({ params }) => ({
      code: '',
      ...params,
    }),
  })
  const { effectiveDate, code } = params! as RouteParamsRecord<
    ['effectiveDate', 'code']
  >

  //to get price list  name
  const { data: viewPriceListDetails } = useQuery({
    ...query('/getDemographicPriceList', { effectiveDate, code }),
    enabled: params != undefined,
  })!

  if (!route) return 'ViewPriceListLayout must be used within a pricing route.'
  if (!params)
    return 'ViewPriceListLayout must be used within a view-price-list route.'

  return (
    <div className='flex min-h-0 flex-1 flex-col'>
      <header className='mx-8 mt-12 flex'>
        <Breadcrumbs>
          <BreadcrumbSegment
            href={routeTo('/pricing/[effectiveDate]/price-list', {
              ...(params as RouteParamsRecord<['effectiveDate', 'code']>),
            })}
          >
            {'Pricing'}
          </BreadcrumbSegment>
          <BreadcrumbSegment href='#'>
            {viewPriceListDetails?.name ?? '-'}
            <small className='ml-2 text-zinc-400'>{params.code}</small>
          </BreadcrumbSegment>
        </Breadcrumbs>

        <div className='ml-auto flex gap-2'>
          <DateFilter
            className='border-grey border bg-white hover:bg-indigo-200 focus-visible:outline-indigo-300'
            title='Effective date'
            label='View data effective on'
            initialDate={parseServerFormat(effectiveDate)}
            onDateChange={(date) => {
              router.push(
                routeTo(
                  '/pricing/[effectiveDate]/view-price-list/[code]/details',
                  {
                    ...params,
                    effectiveDate: toServerFormat(date),
                    code: params.code,
                  },
                ),
              )
            }}
          />
        </div>
      </header>
      <div className='m-8 mb-0 flex flex-auto gap-4 overflow-hidden pb-3'>
        <div className='flex-8 flex grow gap-4'>
          <div className='flex flex-auto flex-col'>
            <TabList>
              <Tab
                href={routeTo(
                  '/pricing/[effectiveDate]/view-price-list/[code]/details',
                  {
                    ...(params as RouteParamsRecord<['effectiveDate', 'code']>),
                  },
                )}
                active={matches(
                  route,
                  '/pricing/[effectiveDate]/view-price-list/[code]/details',
                )}
              >
                Details
              </Tab>
              <Tab
                href={routeTo(
                  '/pricing/[effectiveDate]/view-price-list/[code]/criteria',
                  {
                    ...(params as RouteParamsRecord<['effectiveDate', 'code']>),
                  },
                )}
                active={matches(
                  route,
                  '/pricing/[effectiveDate]/view-price-list/[code]/criteria',
                )}
              >
                Demographic and user field criteria
              </Tab>
              <Tab
                href={routeTo(
                  '/pricing/[effectiveDate]/view-price-list/[code]/service-pricing',
                  {
                    ...(params as RouteParamsRecord<['effectiveDate', 'code']>),
                  },
                )}
                active={matches(
                  route,
                  '/pricing/[effectiveDate]/view-price-list/[code]/service-pricing',
                )}
              >
                Services and pricing
              </Tab>
            </TabList>
            <div className='flex min-h-0 flex-auto gap-4'>
              <div className='flex-auto overflow-scroll rounded-lg border bg-white p-4'>
                {children}
              </div>
              <PriceListVersions />.
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
