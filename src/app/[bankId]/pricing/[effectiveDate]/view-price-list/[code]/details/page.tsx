'use client'

import { useQuery } from '@tanstack/react-query'

import { unwrap } from '@/lib/unions/unwrap'
import { routeTo, useRoute } from '@/app/[bankId]/pricing/routing'
import { query } from '../../../queries'
import PriceListViewGroup from '../../_components/PriceListViewGroup'
import Link from 'next/link'
import { Button } from '@/components/Button'

export default function ViewPriceListDetailsPage() {
  const route = unwrap(
    useRoute(),
    '/pricing/[effectiveDate]/view-price-list/[code]/details',
  )!
  const { effectiveDate, code } = route.params

  //to get price list on effective date
  const {
    data: viewPriceListDetails,
    error: viewPriceListDetailsError,
    isSuccess: viewPriceListDetailsSuccess,
  } = useQuery(query('/getDemographicPriceList', { effectiveDate, code }))

  const {
    code: priceListCode,
    currency,
    effectiveDate: priceListEffectiveDate,
    isLeadPriceList,
    name,
  } = viewPriceListDetails ?? {}

  if (!viewPriceListDetailsSuccess) return <>Loading...</>
  if (viewPriceListDetailsError) return viewPriceListDetailsError

  return (
    <div className='flex min-h-0 flex-1 flex-col'>
      <div className='mb-6 flex'>
        <hgroup className='flex-auto'>
          <h1 className='mb-5 p-4 text-lg'>Price list information</h1>
        </hgroup>
        <div className='flex items-center gap-4'>
          <Link
            href={routeTo(
              '/pricing/[effectiveDate]/edit-price-list/[code]/details',
              route.params,
            )}
          >
            <Button className='btn-primary text-white'>Edit</Button>
          </Link>
        </div>
      </div>
      <PriceListViewGroup
        label={'Effective date'}
        value={priceListEffectiveDate}
      />
      <div className='flex flex-wrap'>
        <PriceListViewGroup label={'Price list name'} value={name} />
        <PriceListViewGroup label={'Price list code'} value={priceListCode} />
        <PriceListViewGroup
          label={'Lead price list'}
          value={isLeadPriceList ? 'Yes' : 'No'}
        />
        <PriceListViewGroup label={'Currency'} value={currency} />
      </div>
    </div>
  )
}
