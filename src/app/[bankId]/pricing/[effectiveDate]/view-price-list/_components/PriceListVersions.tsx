import { useCallback, useMemo } from 'react'
import {
  TimelinePeriod,
  VersionTimeline,
} from '@/app/[bankId]/configuration/_components/VersionTimeline'
import { query } from '../../queries'
import { useQuery } from '@tanstack/react-query'
import { routeTo, useRoute } from '../../../routing'
import { match } from '@/lib/unions/match'
import { RouteParamsRecord } from '@/lib/defineRoute'

export function PriceListVersions() {
  const route = useRoute()!

  const params = match(route, {
    '/pricing/[effectiveDate]/view-price-list/[code]/details': ({ params }) =>
      params,
    '/pricing/[effectiveDate]/view-price-list/[code]/criteria': ({ params }) =>
      params,
    '/pricing/[effectiveDate]/view-price-list/[code]/service-pricing': ({
      params,
    }) => params,
    _: ({ params }) => ({
      code: '',
      ...params,
    }),
  })!
  const { code } = params!

  //to get the price version timeline
  const {
    data: priceListVersions,
    isSuccess,
    error,
  } = useQuery(query('/getDemographicPriceListVersions', { code }))

  const linkFormatter = useCallback(
    (effectiveDate: string, code: string) => {
      return routeTo(
        '/pricing/[effectiveDate]/view-price-list/[code]/details',
        {
          ...params,
          effectiveDate,
          code,
        },
      )
    },
    [params],
  )

  const timelineDates: TimelinePeriod[] = useMemo(() => {
    return (
      priceListVersions?.map((version: string) => ({
        code: code,
        effectiveDate: version,
      })) ?? []
    )
  }, [code, priceListVersions])

  if (!isSuccess) return <>Loading...</>
  if (error) return <>{error}</>

  return (
    <div className='flex-4 mt-1 flex w-72 overflow-scroll rounded-lg border bg-white p-6'>
      <VersionTimeline
        versions={timelineDates}
        currentEffectiveDate={
          (params as RouteParamsRecord<['effectiveDate', 'code']>).effectiveDate
        }
        linkFormatter={linkFormatter}
        shouldParseToDateLevel={true}
      />
    </div>
  )
}
