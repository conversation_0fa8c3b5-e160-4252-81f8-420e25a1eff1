import clsx from 'clsx'

interface PriceListViewGroupInterface {
  label: string
  value: string | number | null | undefined
  className?: string
}
export default function PriceListViewGroup({
  label,
  value,
  className,
  ...props
}: PriceListViewGroupInterface) {
  return (
    <div
      className={className ? clsx(className) : 'flex-1 basis-1/2 p-4 pt-0'}
      {...props}
    >
      <div className={'my-3 flex flex-col'}>
        <span className='inline-flex text-sm text-app-color-secondary'>
          {label}
        </span>
        <span className='inline-flex items-center text-[15px]/[22px] font-medium'>
          {value ?? '-'}
        </span>
      </div>
    </div>
  )
}
