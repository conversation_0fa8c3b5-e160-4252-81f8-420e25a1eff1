import { FieldApi } from '@tanstack/react-form'
import { isUnique } from '@/lib/functional/isUnique'
import { Criterion } from '@/app/[bankId]/pricing/add-price-list/step-2/Criterion'
import { HydratedDemographicPriceListForm } from '@/api/formToApiSchema'

export function ViewCriteria({
  field,
  priceListCode,
}: {
  field: FieldApi<HydratedDemographicPriceListForm, 'demographicCriteria'>
  priceListCode: string
}) {
  const criteria = field.state.value
  const criteriaCodes =
    criteria!.map(({ criteriaCode }) => criteriaCode).filter(isUnique()) ?? []
  return (
    <>
      {!criteriaCodes?.length ?
        <>No Criteria</>
      : criteriaCodes?.map((criteriaCode, i) => (
          <Criterion
            key={criteriaCode}
            name={`Criteria ${i + 1}`}
            field={field}
            priceListCode={priceListCode}
            criteriaCode={criteriaCode ?? null}
            viewOnly={true}
            effectiveDate={criteria[0].effectiveDate}
          />
        ))
      }
    </>
  )
}
