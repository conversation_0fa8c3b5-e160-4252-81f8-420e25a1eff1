import {
  DemographicPriceList,
  DemographicPriceListRanking,
  DemographicPriceListWithCount,
} from '@/api/formToApiSchema'
import { PriceListWithRanking } from './[effectiveDate]/manage-ranking/types'

export function calculatePriceListRanking(
  priceLists: DemographicPriceListWithCount[],
  priceListRanking: DemographicPriceListRanking,
) {
  const combinedList: PriceListWithRanking[] = []
  if (priceLists && priceListRanking?.ranking) {
    const parsedRanking = priceListRanking.ranking.split(', ')
    parsedRanking.forEach((ranking, index) => {
      const matchingPriceList = priceLists.find(
        (priceList) => priceList.code === ranking,
      )
      if (matchingPriceList) {
        combinedList.push({
          ...matchingPriceList,
          ranking: index + 1,
        })
      }
    })
  } else if (priceLists && priceListRanking?.ranking == '') {
    priceLists.forEach((priceList, index) => {
      combinedList.push({
        ...priceList,
        ranking: index + 1,
      })
    })
  }
  return combinedList
}
