import { SubPriceType } from '@/api/zodSchemas'
import { ServicePriceTier } from '@/app/[bankId]/services/[effectiveDate]/(service)/UpdateServiceForm'
import {
  getColumns,
  PricingTiersList,
} from '@/app/[bankId]/services/[effectiveDate]/(service)/view/_components/PricingTiers'
import {
  EditPricingTiers,
  EditPricingTiersProps,
  TiersFormState,
} from '@/app/[bankId]/services/_components/EditPricingTiers'
import { If } from '@/components/If'
import {
  Modal,
  ModalCancelButton,
  ModalConfirmButton,
  ModalFooter,
  ModalTitle,
  ModalWindow,
} from '@/components/Modal'
import React, { useMemo } from 'react'
import { useAddPriceListContext } from '../context'
import { ReactFormExtendedApi } from '@tanstack/react-form'

export interface AddCategoryProps extends Omit<EditPricingTiersProps, 'form'> {
  confirmText: string
  title: string
  subtitle: string
  viewOnly?: boolean
  viewOnlyTiers?: ServicePriceTier[]
  onClose?: () => void
}

export function PricingTiersModal({
  children,
  confirmText,
  title,
  subtitle,
  onClose,
  viewOnly,
  viewOnlyTiers,
  ...props
}: React.PropsWithChildren<AddCategoryProps>) {
  const pricingTiersColumns = useMemo(
    () =>
      getColumns({
        indexRate: parseFloat(props.indexRate ?? '0'),
        tierPriceType: props.subPriceType as SubPriceType,
      }),
    [props.indexRate, props.subPriceType],
  )

  const form = useAddPriceListContext()!

  return (
    <Modal onClose={onClose}>
      {children}
      <ModalWindow className='w-1/2 border-2'>
        <ModalTitle>{title}</ModalTitle>
        <h4 className='mb-6 text-app-color-secondary'>{subtitle}</h4>
        <If true={viewOnly !== true}>
          <EditPricingTiers
            {...props}
            form={form as unknown as ReactFormExtendedApi<TiersFormState>}
          />
        </If>
        <If true={viewOnly === true}>
          <PricingTiersList
            columns={pricingTiersColumns}
            serviceType={props.serviceType}
            tiers={viewOnlyTiers ?? []}
          />
        </If>
        <ModalFooter>
          <ModalCancelButton>Cancel</ModalCancelButton>
          <ModalConfirmButton
            type='submit'
            aria-disabled={false}
            className='btn-primary aria-disabled:cursor-default aria-disabled:bg-indigo-300'
            onClick={async (_, ctx) => {
              const errs = await form.validateField('pricingTiers', 'change')

              if (!errs || errs.length === 0) {
                ctx.close()
              }
            }}
          >
            {confirmText}
          </ModalConfirmButton>
        </ModalFooter>
      </ModalWindow>
    </Modal>
  )
}
