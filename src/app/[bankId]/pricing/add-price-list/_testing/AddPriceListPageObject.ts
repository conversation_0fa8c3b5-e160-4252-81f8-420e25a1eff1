import { expect, Page } from '@playwright/test'
import { createPageObjectProvider } from '../../../../../../test/e2e/createPageObjectProvider'

export class AddPriceListPageObject {
  static get provider() {
    return createPageObjectProvider((page) => new AddPriceListPageObject(page))
  }

  constructor(readonly page: Page) {}

  expectToBeVisible() {
    return expect(
      this.page.getByRole('heading', { name: 'Add a price list' }),
    ).toBeVisible()
  }
}
