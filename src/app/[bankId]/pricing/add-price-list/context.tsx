import {
  formToApiSchemas,
  HydratedDemographicPriceListForm,
  ServicePrice,
  ServicePriceForm,
} from '@/api/formToApiSchema'
import { defineContext } from '@/lib/state/defineContext'
import { ReactFormExtendedApi } from '@tanstack/react-form'
import { z } from 'zod'

type NonNullableProps<T> = {
  [K in keyof T]: NonNullable<T[K]>
}

export interface Option {
  value: string | null
  code: string | null
}

export type AddPriceListForm =
  NonNullableProps<HydratedDemographicPriceListForm> & {
    pricingTiers: ServicePriceForm[]
  }

export const priceListFormSchema =
  formToApiSchemas.hydratedDemographicPriceList.extend({
    pricingTiers: z.array(z.custom<ServicePriceForm>()),
  })

export const priceListFormSubmitSchema =
  formToApiSchemas.hydratedDemographicPriceList.extend({
    pricingTiers: z.array(z.custom<ServicePriceForm>()),
    demographicCriteria:
      formToApiSchemas.hydratedDemographicPriceList.shape.demographicCriteria.min(
        1,
      ),
    servicePricing:
      formToApiSchemas.hydratedDemographicPriceList.shape.servicePricing.min(1),
  })

type AddPriceListState = ReactFormExtendedApi<AddPriceListForm> | undefined
const initialState = undefined as AddPriceListState

export const [
  useAddPriceListContext,
  useAddPriceListDispatchContext,
  AddPriceListContextProvider,
] = defineContext('AddPriceList', initialState, [], {})
