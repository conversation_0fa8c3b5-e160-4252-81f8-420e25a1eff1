'use client'

import { routeTo, useRoute } from '../routing'
import { data, tag } from '@/lib/unions/Union'
import Link from 'next/link'
import { Button } from '@/components/Button'
import { OrderedTabs } from '@/components/Tabs/OrderedTabs/OrderedTabs'
import { OrderedTabList } from '@/components/Tabs/OrderedTabs/OrderedTabList'
import { OrderedTab } from '@/components/Tabs/OrderedTabs/OrderedTab'
import {
  NextTabLink,
  OnTab,
  PrevTabLink,
} from '@/components/Tabs/OrderedTabs/OrderedTabConditionalComponents'
import clsx from 'clsx'
import { useForm, useStore } from '@tanstack/react-form'
import { AddPriceListContextProvider, priceListFormSchema } from './context'
import { startOfToday } from 'date-fns'
import { toServerFormat } from '@/lib/date'
import { formToApiSchemas } from '@/api/formToApiSchema'
import { useMutation } from '@tanstack/react-query'
import { pricingMutation } from '../[effectiveDate]/mutations'
import { useRouter } from 'next/navigation'
import { includes } from '@/lib/functional/includes'
import { tiersToPrices } from '../tiersToServicePricing'
import { notify } from '@/components/Notifications'

export default function AddPriceListLayout({
  children,
}: React.PropsWithChildren) {
  const router = useRouter()
  const route = useRoute()

  const { mutate } = useMutation(
    pricingMutation('/createDemographicPriceList', {
      onSuccess: (data) => {
        router.push(
          routeTo('/pricing/[effectiveDate]/price-list', {
            bankId: params.bankId,
            effectiveDate: data.effectiveDate!,
          }),
        )
      },
    }),
  )

  let effectiveDate = toServerFormat(startOfToday())

  const form = useForm({
    defaultValues: {
      demographicCriteria: [],
      demographicPriceList: {
        code: '',
        name: '',
        currency: 'USD',
        isLeadPriceList: false,
        effectiveDate,
      },
      ranking: {
        effectiveDate,
        ranking: '',
      },
      servicePricing: [],
      pricingTiers: [],
    },
    validators: {
      onChange: priceListFormSchema,
    },
    onSubmit: ({ value }) => {
      const pricingData = value.servicePricing[0]
      if (
        pricingData &&
        includes(pricingData.priceType, ['PARTITIONED_TIER', 'THRESHOLD_TIER'])
      ) {
        value.servicePricing = tiersToPrices(pricingData, value.pricingTiers)
      }

      const parsed = formToApiSchemas.hydratedDemographicPriceList
        .extend({
          demographicCriteria:
            formToApiSchemas.hydratedDemographicPriceList.shape.demographicCriteria.min(
              1,
            ),
          servicePricing:
            formToApiSchemas.hydratedDemographicPriceList.shape.servicePricing.min(
              1,
            ),
        })
        .safeParse(value)

      if (parsed.success) {
        mutate(parsed.data)
      } else {
        const errorMessages = parsed.error.errors
          .map((err) => `${err.path.join('.')}: ${err.message}`)
          .join('; ')

        notify({
          key: 'AddPriceList',
          message: `Failed to save: ${errorMessages}`,
          type: 'ERROR',
        })
      }
    },
  })

  const isFormValid = useStore(form.store, (state) => {
    return state.canSubmit && state.isTouched
  })

  if (!route) return 'AddPriceListLayout must be used within a pricing route'
  const params = data(route).params

  const steps = [
    'Price list information',
    'Demographic and user fields criteria',
    'Services and pricing',
    'Price list ranking',
  ]

  const routeToStep = (step: number): string => {
    const stepRoute = `/pricing/add-price-list/step-${step}`
    return routeTo(stepRoute as Parameters<typeof routeTo>[0], params)
  }

  return (
    <div className='flex min-h-0 flex-1 flex-col'>
      <OrderedTabs>
        <div className='flex flex-auto flex-col overflow-y-auto p-8 pb-4'>
          <h1 className='mb-4 text-lg font-medium'>Add a price list</h1>
          <OrderedTabList
            className='mb-2 rounded-lg border bg-white p-4'
            activeTab={routeTo(tag(route), params)}
          >
            {steps.map((title, i) => (
              <div key={title} className='flex-1'>
                <OrderedTab
                  className='flex flex-col items-center'
                  index={i}
                  href={routeToStep(i + 1)}
                >
                  {i > 0 && (
                    <hr
                      className='relative -left-1/2 top-4 w-full border-b-2 border-t-0 border-dotted'
                      aria-hidden='true'
                    />
                  )}
                  <div
                    className={clsx(
                      'z-10 mb-1 h-8 w-8 rounded-full border-2 bg-white text-center leading-7',
                      'group-hover:border-zinc-300',
                      'group-data-[active=true]:border-indigo-600',
                    )}
                  >
                    {i + 1}
                  </div>
                  {title}
                </OrderedTab>
              </div>
            ))}
          </OrderedTabList>
          <AddPriceListContextProvider state={form}>
            <div className='flex-auto rounded-lg border bg-white p-4'>
              {children}
            </div>
          </AddPriceListContextProvider>
        </div>
        <div className='flex border-t bg-white p-4'>
          <div className='flex-auto'>
            <Link
              href={routeTo('/pricing/[effectiveDate]/price-list', {
                bankId: params.bankId,
                effectiveDate,
              })}
            >
              <Button className='btn'>
                <span className='px-16'>Cancel</span>
              </Button>
            </Link>
          </div>
          <div className='flex gap-2'>
            <PrevTabLink>
              <Button className='btn'>
                <span className='px-16'>Back</span>
              </Button>
            </PrevTabLink>
            <NextTabLink>
              <Button
                className='btn-primary disabled:cursor-not-allowed disabled:bg-zinc-300 disabled:text-zinc-500'
                disabled={!isFormValid}
              >
                <span className='px-16'>Next</span>
              </Button>
            </NextTabLink>
            <OnTab select={({ nextTab }) => !nextTab}>
              <Button
                className='btn-primary disabled:cursor-not-allowed disabled:bg-zinc-300 disabled:text-zinc-500'
                onClick={form.handleSubmit}
              >
                <span className='px-16'>Save</span>
              </Button>
            </OnTab>
          </div>
        </div>
      </OrderedTabs>
    </div>
  )
}
