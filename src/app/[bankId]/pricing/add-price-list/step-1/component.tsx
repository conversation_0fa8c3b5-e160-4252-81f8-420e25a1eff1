'use client'

import { AddPriceListForm, useAddPriceListContext } from '../context'
import {
  InfoSection,
  InfoSectionDescription,
  InfoSectionTitle,
} from '@/components/InfoSection'
import { useFormDatePicker } from '@/components/Form/useFormDatePicker'
import { Checkbox } from '@/components/Checkbox'
import { TextInputField } from '@/components/Input/TextInput'
import { HydratedDemographicPriceListForm } from '@/api/formToApiSchema'
export interface AddPriceListStep1ComponentPageProps {
  disableFields?: boolean
}

export default function AddPriceListStep1PageComponent({
  disableFields = false,
}: AddPriceListStep1ComponentPageProps) {
  // SAFETY: we can assert this is always defined, because the parent layout
  // provides a defined initial state to AddPriceListContextProvider.
  const form = useAddPriceListContext()!

  const DatePicker = useFormDatePicker<AddPriceListForm>({
    form,
  })

  return (
    <InfoSection>
      <InfoSectionTitle>Price list information</InfoSectionTitle>
      <InfoSectionDescription>
        Fill out some basic information about this price list.
      </InfoSectionDescription>
      <form className='flex flex-col gap-4'>
        <div className='flex gap-8'>
          <div className='flex-1'>
            <DatePicker
              name='demographicPriceList.effectiveDate'
              label='Effective date'
            />
          </div>
          <div className='flex-1'></div>
        </div>
        <div className='flex gap-8'>
          <div className='flex-1'>
            <form.Field name='demographicPriceList.name'>
              {(field) => (
                <TextInputField
                  field={field}
                  label='Price list name'
                  required
                />
              )}
            </form.Field>
          </div>
          <div className='flex-1'>
            <form.Field name='demographicPriceList.code'>
              {(field) => (
                <TextInputField
                  field={field}
                  label='Price list code'
                  required
                  disabled={disableFields}
                />
              )}
            </form.Field>
          </div>
        </div>
        <div className='flex gap-8'>
          <div className='flex-1'>
            <form.Field name='demographicPriceList.isLeadPriceList'>
              {({ handleChange, state }) => (
                <Checkbox
                  label='This is a lead price list.'
                  checked={state.value ?? false}
                  onChange={handleChange}
                />
              )}
            </form.Field>
          </div>
          <div className='flex-1'></div>
        </div>
        <div className='flex gap-8'>
          <div className='flex-1'>
            <form.Field name='demographicPriceList.currency'>
              {(field) => (
                <TextInputField field={field} label='Currency' disabled />
              )}
            </form.Field>
          </div>
          <div className='flex-1'></div>
        </div>
      </form>
    </InfoSection>
  )
}
