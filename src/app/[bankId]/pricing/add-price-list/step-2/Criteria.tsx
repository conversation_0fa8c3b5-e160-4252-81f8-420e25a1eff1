import { PlusIcon } from '@heroicons/react/24/outline'
import { Criterion } from './Criterion'
import { FieldApi } from '@tanstack/react-form'
import { isUnique } from '@/lib/functional/isUnique'
import { useState } from 'react'
import { DemographicCriteriaForm } from '@/api/formToApiSchema'
import { isNonNull } from '@/lib/guards/isNonNull'

export function Criteria({
  field,
  priceListCode,
  effectiveDate,
}: {
  field: FieldApi<any, any, any, any, DemographicCriteriaForm[]>
  priceListCode: string
  effectiveDate: string
}) {
  const criteria = field.state.value

  const [criteriaCodes, setCriteriaCodes] = useState<string[]>(
    criteria
      .map(({ criteriaCode }) => criteriaCode)
      .filter(isNonNull)
      .concat('0')
      .filter(isUnique()),
  )

  const nextCriteriaCode = String(
    (criteriaCodes
      .map((v) => parseInt(v))
      .sort((a, b) => a - b)
      .slice(-1)[0] ?? 0) + 1,
  )

  return (
    <>
      {criteriaCodes.map((criteriaCode, i) => (
        <Criterion
          key={criteriaCode}
          name={`Criteria ${i + 1}`}
          effectiveDate={effectiveDate}
          field={field}
          priceListCode={priceListCode}
          criteriaCode={criteriaCode}
          onDelete={() => {
            setCriteriaCodes(criteriaCodes.filter((c) => c !== criteriaCode))
          }}
        />
      ))}
      <div className='mt-4 rounded-lg border p-4'>
        <button
          className='flex items-center text-app-color-fg-brand-primary'
          onClick={() =>
            setCriteriaCodes(criteriaCodes.concat(nextCriteriaCode))
          }
        >
          <PlusIcon className='mr-2 size-5' /> Add criteria
        </button>
      </div>
    </>
  )
}
