import {
  Demographic<PERSON>riteriaForm,
  HydratedDemographicPriceListForm,
  HydratedUserFieldConfigurationForm,
} from '@/api/formToApiSchema'
import { Checkbox } from '@/components/Checkbox'
import { If } from '@/components/If'
import { TextInput } from '@/components/Input/TextInput'
import { fromEntries } from '@/lib/functional/fromEntries'
import { isDefined } from '@/lib/guards/isDefined'
import { CheckIcon, TrashIcon, XMarkIcon } from '@heroicons/react/24/outline'
import { FieldApi } from '@tanstack/react-form'
import clsx from 'clsx'
import { Option } from '../context'
import { Select, SelectOption } from '@/components/Input/Select'

export type CriteriaFieldDefinition = {
  keyType: HydratedDemographicPriceListForm['demographicCriteria'][number]['keyType']
  codeKey: HydratedDemographicPriceListForm['demographicCriteria'][number]['codeKey']
  stringKey: HydratedDemographicPriceListForm['demographicCriteria'][number]['stringKey']
  name: string
  fieldType: HydratedUserFieldConfigurationForm['fieldType']

  // TODO: We have a type mismatch between the string `codeValue` in
  // DemographicCriteria and the int `code` in UserFieldDropdownOptionUpdate.
  //
  // We should align on a common type.
  // const codeValue = criterion.codeValue ? Number(criterion.codeValue) : null
  options?: Option[]
  selected?: Option[]
}

export type CriteriaFieldProps = CriteriaFieldDefinition & {
  field: FieldApi<any, any, any, any, DemographicCriteriaForm[]>
  criteriaCode: HydratedDemographicPriceListForm['demographicCriteria'][number]['criteriaCode']
  readonly?: boolean
  viewOnly?: boolean
}

export function CriterionField({
  field,
  criteriaCode,
  keyType,
  codeKey,
  stringKey,
  name,
  fieldType,
  options,
  readonly,
  viewOnly,
}: CriteriaFieldProps) {
  const criteria = field.state.value

  const criterionFieldIndices = criteria
    .map((criterion, i) => {
      return (
          criterion.criteriaCode === criteriaCode &&
            criterion.keyType === keyType &&
            criterion.codeKey === codeKey &&
            criterion.stringKey === stringKey
        ) ?
          i
        : undefined
    })
    .filter(isDefined)

  // if (criterionFieldIndices.length !== 1) {
  //   throw new Error(
  //     'CriterionField expects criteriaCode + keyType + codeKey + stringKey ' +
  //       'to uniquely identify a single criterion.',
  //   )
  // }

  const criterion = criteria[criterionFieldIndices[0]]

  const handleChange =
    (key: 'booleanValue' | 'codeValue' | 'stringValue') =>
    (value: boolean | number | string | null) => {
      field.replaceValue(criterionFieldIndices[0], {
        ...criterion,
        [key]: value,
      })
    }

  const optionValueByCode = fromEntries(
    options?.map(({ code, value }) => [code!, value]) ?? [],
  )

  const selected = criterionFieldIndices
    .map((index) => ({
      code: criteria[index].stringValue,
      value: optionValueByCode[criteria[index].stringValue!],
    }))
    .filter((c) => c.code != null)

  return (
    <div className='flex'>
      <If true={fieldType === 'BOOLEAN'}>
        {readonly && (
          <div className='flex flex-1 flex-col'>
            <p className='text-sm font-medium text-app-color-secondary'>
              {name} must be:
            </p>
            <p className='font-medium'>
              {criterion.booleanValue ? 'True' : 'False'}
            </p>
          </div>
        )}
        {!readonly && (
          <Checkbox
            className={'flex-1'}
            label={`${name} is true:`}
            checked={criterion.booleanValue ?? false}
            onChange={handleChange('booleanValue')}
            prefix={true}
          />
        )}
      </If>
      <If true={fieldType === 'DROPDOWN'}>
        {readonly && (
          <div className='flex flex-1 flex-col'>
            <p className='text-sm font-medium text-app-color-secondary'>
              {name} must be:
            </p>
            <p className='font-medium'>
              {criterionFieldIndices
                .map((index) => criteria[index].stringValue)
                .filter((value) => value != null)
                .map((definedValue) => optionValueByCode[definedValue])
                .join(', ')}
            </p>
          </div>
        )}
        {!readonly && (
          <Select
            className={'flex-1'}
            name={name}
            label={`${name} must be:`}
            value={selected}
            by={(a, b) => a.code === b.code}
            multiple
            renderSelected={() => {
              return (
                <div className='flex'>
                  {selected.map((option) => (
                    <div
                      className='mr-2 flex items-center rounded-md border bg-app-color-bg-secondary px-1'
                      key={option.code}
                    >
                      {option.value}
                      <div
                        className='cursor-pointer border-none px-1'
                        onClick={(e) => {
                          e.preventDefault()

                          if (criterionFieldIndices.length === 1) {
                            field.replaceValue(criterionFieldIndices[0], {
                              ...criterion,
                              stringValue: null,
                              valueCode: '',
                            })
                          } else {
                            const indexForRemoval = criterionFieldIndices.find(
                              (index) =>
                                criteria[index].stringValue === option.code,
                            )!

                            field.removeValue(indexForRemoval)
                          }
                        }}
                      >
                        <XMarkIcon className='size-4 text-black' />
                      </div>
                    </div>
                  ))}
                </div>
              )
            }}
            onChange={(values) => {
              const criterias = criterionFieldIndices
                .map((index) => criteria[index])
                .filter(isDefined)

              values.forEach((value) => {
                if (criterias.every((c) => c.stringValue !== value.code)) {
                  field.pushValue({
                    ...criterion,
                    stringValue: value.code,
                    valueCode: value.code ?? '',
                  })
                }
              })

              criterias.forEach((c, index) => {
                if (values.every((v) => v.code !== c.stringValue)) {
                  field.removeValue(criterionFieldIndices[index])
                }
              })
            }}
            required={true}
            renderErrors={false}
          >
            {options &&
              options.map((option) => (
                <SelectOption
                  key={`${option.code}`}
                  value={option}
                  className='relative'
                >
                  <span className='block truncate'>{option.value}</span>
                  <span
                    className={clsx(
                      'absolute inset-y-0 right-0 flex items-center pr-4 text-indigo-600',
                      'group-[&:not([data-selected])]:hidden',
                    )}
                  >
                    <CheckIcon aria-hidden='true' className='size-5' />
                  </span>
                </SelectOption>
              ))}
          </Select>
        )}
      </If>
      <If true={fieldType === 'FREEFORM'}>
        {readonly && (
          <div className='flex flex-1 flex-col'>
            <p className='text-sm font-medium text-app-color-secondary'>
              {name} must be:
            </p>
            <p className='font-medium'>{criterion.stringValue}</p>
          </div>
        )}
        {!readonly && (
          <TextInput
            className={'flex-1'}
            name={name}
            label={`${name} must be:`}
            value={criterion.stringValue ?? ''}
            onChange={handleChange('stringValue')}
            required={true}
            renderErrors={false}
          />
        )}
      </If>
      {!viewOnly && (
        <button
          className='mt-5 pl-4 pr-2 text-app-color-button-primary-bg'
          onClick={() => {
            field.removeValue(criterionFieldIndices[0])
          }}
        >
          <TrashIcon className='size-5' />
        </button>
      )}
    </div>
  )
}
