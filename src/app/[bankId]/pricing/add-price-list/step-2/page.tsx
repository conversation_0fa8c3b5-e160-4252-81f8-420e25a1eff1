'use client'

import { useAddPriceListContext } from '../context'
import { useStore } from '@tanstack/react-form'
import { Criteria } from './Criteria'
import {
  InfoSection,
  InfoSectionDescription,
  InfoSectionTitle,
} from '@/components/InfoSection'

export default function AddPriceListStep2Page() {
  // SAFETY: we can assert this is always defined, because the parent layout
  // provides a defined initial state to AddPriceListContextProvider.
  const form = useAddPriceListContext()!
  const [effectiveDate, priceListCode] = useStore(form.store, (state) => [
    state.values.demographicPriceList.effectiveDate!,
    state.values.demographicPriceList.code,
  ])

  return (
    <>
      {priceListCode ?
        <>
          <InfoSection>
            <InfoSectionTitle>
              Add demographic and user field criteria
            </InfoSectionTitle>
            <InfoSectionDescription>
              Define criteria for which accounts qualify for this price list.
            </InfoSectionDescription>
          </InfoSection>
          <form.Field name='demographicCriteria'>
            {(field) => (
              <Criteria
                field={field}
                priceListCode={priceListCode}
                effectiveDate={effectiveDate}
              />
            )}
          </form.Field>
        </>
      : <div className='flex h-full flex-col items-center justify-center gap-2'>
          <p>The price list must be given a code before proceeding.</p>
          <p>Please return to step 1 and provide a code.</p>
        </div>
      }
    </>
  )
}
