import { ServicePriceForm } from '@/api/formToApiSchema'
import { ApiToFromServicePriceFormExt } from './add-price-list/step-3/page'
import { tryParseFloat } from '../services/_components/EditPricingTiers'

export function tiersToPrices(
  servicePrice: ServicePriceForm,
  tiers: ServicePriceForm[],
): ServicePriceForm[] {
  return tiers.map(
    (
      {
        tierMaxBalanceExclusive,
        tierMinBalanceInclusive,
        tierMaxVolumeExclusive,
        tierMinVolumeInclusive,
        priceValue,
        indexAdjustment,
      },
      index,
    ) => ({
      ...servicePrice,
      tierMaxBalanceExclusive,
      tierMaxVolumeExclusive: tryParseFloat(
        tierMaxVolumeExclusive as unknown as string,
      ),
      tierMinBalanceInclusive,
      tierMinVolumeInclusive: tryParseFloat(
        tierMinVolumeInclusive as unknown as string,
      ),
      priceValue,
      indexAdjustment,
      tierNumber: index,
    }),
  )
}

export function tiersToServicePricing(
  servicePricing: ServicePriceForm[],
): ServicePriceForm[] {
  const firstPrice = servicePricing[0] as ApiToFromServicePriceFormExt

  return firstPrice.pricingTiers.map(
    (
      {
        tierMaxBalanceExclusive,
        tierMinBalanceInclusive,
        tierMaxVolumeExclusive,
        tierMinVolumeInclusive,
        priceValue,
        indexAdjustment,
      },
      index,
    ) => ({
      ...firstPrice,
      tierMaxBalanceExclusive,
      tierMaxVolumeExclusive,
      tierMinBalanceInclusive,
      tierMinVolumeInclusive,
      priceValue,
      indexAdjustment,
      tierNumber: index,
    }),
  )
}
