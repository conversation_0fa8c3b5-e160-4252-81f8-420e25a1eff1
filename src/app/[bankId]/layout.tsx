import React, { Suspense } from 'react'
import { Sidebar } from '@/components/Sidebar'
import { TopNav } from '@/components/TopNav'
import { NotificationsContainer } from '@/components/Notifications'
import { ReactQueryDevtools } from '@tanstack/react-query-devtools'

import Providers from './context/Providers'

export default function BankIdLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <>
      <div className='relative isolate flex h-screen w-full'>
        <aside className='flex border-r border-zinc-200 px-6 pt-8'>
          <Suspense>
            <Sidebar className='flex-auto' />
          </Suspense>
        </aside>
        <Providers>
          <header className=''>
            <TopNav />
          </header>
          <main className='grow-1 shrink-1 flex min-w-0 flex-1 bg-zinc-400/5 pt-[59px]'>
            {children}
          </main>
          <ReactQueryDevtools initialIsOpen={false} position='right' />
        </Providers>
      </div>
      <NotificationsContainer />
    </>
  )
}
