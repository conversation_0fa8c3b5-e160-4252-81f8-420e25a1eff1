'use client'

import { useMutation } from '@tanstack/react-query'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/Button'

import { formatToMonthYearFromDate, formatToServerString } from '@/lib/date'
import { unwrap } from '@/lib/unions/unwrap'
import { routeTo, useRoute } from '../../../routing'

import { requiredBalanceMutation } from '../../mutations'
import RequiredBalanceForm from '../../_components/RequiredBalanceForm'

import {
  RequiredBalanceFormTypes,
  RequiredBalanceApiTypes,
  RequiredBalanceApiSchema,
} from '../../_components/RequiredBalanceFormTypes'

export default function AddRequiredBalance({}) {
  const router = useRouter()
  const route = unwrap(
    useRoute(),
    '/configuration/required-balance/[effectiveDate]/add',
  )!

  const addRequiredBalance = useMutation(
    requiredBalanceMutation('/createRequiredBalanceDefinition', {
      onSuccess: (_, request) => {
        router.push(
          routeTo(
            '/configuration/required-balance/[effectiveDate]/view/[requiredBalanceCode]',
            {
              ...route.params,
              requiredBalanceCode: request.code!,
              effectiveDate: formatToMonthYearFromDate(request.effectiveDate!),
            },
          ),
        )
      },
    }),
  )

  const defaultValues: RequiredBalanceFormTypes = {
    code: '',
    name: '',
    effectiveDate: formatToMonthYearFromDate(new Date()),
    baseBalanceType: 'AVERAGE_COLLECTED',
    subtractCompensatingBalance: false,
    subtractInterestPaidMonthToDate: false,

    otherBalanceCalculation: false,
    otherBalanceMethod: null,
    otherBalanceLabel: '',
  }

  const serialize = (formState: RequiredBalanceFormTypes) => {
    const hasOtherBalanceCalculation = formState.otherBalanceCalculation
    const { otherBalanceMethod } = formState

    const otherBalanceKeys =
      otherBalanceMethod === 'ADD' ?
        {
          addOtherBalanceLabel: formState.otherBalanceLabel,
          subtractOtherBalanceLabel: null,
        }
      : {
          addOtherBalanceLabel: null,
          subtractOtherBalanceLabel: formState.otherBalanceLabel,
        }

    const otherKeys =
      hasOtherBalanceCalculation ? otherBalanceKeys : (
        {
          addOtherBalanceLabel: null,
          subtractOtherBalanceLabel: null,
        }
      )

    const payload: RequiredBalanceApiTypes = RequiredBalanceApiSchema.parse({
      code: formState.code,
      name: formState.name,
      effectiveDate: formatToServerString(formState.effectiveDate),

      baseBalanceType: formState.baseBalanceType!,
      subtractCompensatingBalance: formState.subtractCompensatingBalance,
      subtractInterestPaidMonthToDate:
        formState.subtractInterestPaidMonthToDate,

      ...otherKeys,
    })
    return payload
  }

  return (
    <div className='w-full min-w-full overflow-auto pt-12'>
      <div className='text-md ml-8 font-medium'>Schedule Information</div>
      <RequiredBalanceForm
        defaultValues={defaultValues}
        onSubmit={(formState) => {
          const payload = serialize(formState)
          addRequiredBalance.mutate(payload)
        }}
      >
        <Button className='btn w-60' onClick={() => router.back()}>
          Cancel
        </Button>

        <Button type='submit' className='btn-primary w-60'>
          Save
        </Button>
      </RequiredBalanceForm>
    </div>
  )
}
