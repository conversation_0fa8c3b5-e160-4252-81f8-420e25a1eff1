'use client'

import { useQuery } from '@tanstack/react-query'
import { useRouter } from 'next/navigation'
import { But<PERSON> } from '@/components/Button'
import { ChevronRightIcon } from '@heroicons/react/24/outline'
import Link from 'next/link'

import {
  DetailsSection,
  DetailsSectionItem,
  DetailsSectionItemsRow,
  DetailsSectionTitle,
} from '@/components/DetailsSection'

import { VersionTimeline } from '@/app/[bankId]/configuration/_components/VersionTimeline'

import { unwrap } from '@/lib/unions/unwrap'
import { routeTo, useRoute } from '@/app/[bankId]/configuration/routing'

import { requiredBalanceQueries } from '../../../queries'
import { apiToFormSchemas } from '@/api/apiToFormSchema'
import { baseBalanceTypeLabel } from '../../../_components/RequiredBalanceFormTypes'
import { formatToMonthYearFromDate, formatToServerString } from '@/lib/date'

export default function ViewRequiredBalance({}) {
  const router = useRouter()
  const route = unwrap(
    useRoute(),
    '/configuration/required-balance/[effectiveDate]/view/[requiredBalanceCode]',
  )!
  const { effectiveDate, requiredBalanceCode } = route.params

  const { data, status, error } = useQuery(
    requiredBalanceQueries('/getRequiredBalanceDefinitionByCode', {
      code: requiredBalanceCode,
    }),
  )
  if (status === 'pending') return 'Loading...'
  if (!data || error) {
    return <div>404 Not Found</div>
  }

  const { balanceRequirementDefinition: RequiredBalanceType } = apiToFormSchemas

  const requiredBalances = data
    .map((item) => RequiredBalanceType.parse(item))
    .sort(
      (a, b) =>
        new Date(b.effectiveDate).getDate() -
        new Date(a.effectiveDate).getDate(),
    )

  const requiredBalance =
    requiredBalances.find((item) => {
      return item.effectiveDate === formatToServerString(effectiveDate)
    }) || requiredBalances[0]

  const onEditClick = () => {
    router.push(
      routeTo(
        '/configuration/required-balance/[effectiveDate]/edit/[requiredBalanceCode]',
        {
          ...route.params,
          effectiveDate: formatToMonthYearFromDate(
            requiredBalance.effectiveDate,
          ),
          requiredBalanceCode: requiredBalance.code,
        },
      ),
    )
  }

  const linkFormatter = (
    effectiveDate: string,
    requiredBalanceCode: string,
  ) => {
    return routeTo(
      '/configuration/required-balance/[effectiveDate]/view/[requiredBalanceCode]',
      {
        ...route.params,
        effectiveDate: formatToMonthYearFromDate(effectiveDate),
        requiredBalanceCode,
      },
    )
  }

  const renderOtherLabels = () => {
    const label =
      requiredBalance.subtractOtherBalanceLabel ||
      requiredBalance.addOtherBalanceLabel

    if (!label) {
      return
    }

    return (
      <DetailsSectionItemsRow>
        <DetailsSectionItem
          label={'Other balance'}
          info={requiredBalance.subtractOtherBalanceLabel ? 'Subtract' : 'Add'}
        />
        <DetailsSectionItem label={'Statement label'} info={label} />
      </DetailsSectionItemsRow>
    )
  }

  return (
    <div className='w-full overflow-hidden overflow-y-scroll px-8 py-10'>
      <div className='flex items-center justify-between'>
        <div className='flex items-center gap-2'>
          <Link
            href={routeTo(
              '/configuration/required-balance/[effectiveDate]',
              route.params,
            )}
            className='font-bold'
          >
            ...
          </Link>
          <ChevronRightIcon className='h-4 w-4 text-zinc-400' />
          <div className='text-md text-zinc-500'>
            <Link
              href={routeTo(
                '/configuration/required-balance/[effectiveDate]',
                route.params,
              )}
              className='font-bold'
            >
              Required balances
            </Link>
          </div>
          <ChevronRightIcon className='h-4 w-4 text-zinc-400' />
          <div className='text-lg'>{requiredBalance.name}</div>
          <div className='text-sm text-zinc-500'>{requiredBalance.code}</div>
        </div>
        <Button className='btn-primary text-white' onClick={onEditClick}>
          Edit
        </Button>
      </div>
      <div className='mt-8 flex gap-4'>
        <div className='flex grow flex-col'>
          <DetailsSection>
            <DetailsSectionTitle>Schedule information</DetailsSectionTitle>
            <DetailsSectionItemsRow>
              <DetailsSectionItem
                label={'Effective date *'}
                info={requiredBalance.effectiveDate}
              />
            </DetailsSectionItemsRow>
            <DetailsSectionItemsRow>
              <DetailsSectionItem
                label={'Name *'}
                info={requiredBalance.name ?? ''}
              />
              <DetailsSectionItem
                label={'Code *'}
                info={requiredBalance.code}
              />
            </DetailsSectionItemsRow>
          </DetailsSection>

          <DetailsSection className='mt-3'>
            <DetailsSectionTitle>
              Required balance calculation
            </DetailsSectionTitle>
            <div className='text-zinc-400'>
              Configure how this required balance is calculated and appears on
              statements.
            </div>
            <DetailsSectionItemsRow className='mt-6'>
              <DetailsSectionItem
                label={'Base balance *'}
                info={
                  baseBalanceTypeLabel(requiredBalance.baseBalanceType) || ''
                }
              />
            </DetailsSectionItemsRow>
            <DetailsSectionItemsRow className='mt-6'>
              <DetailsSectionItem
                label={'Subtract compensating balance'}
                info={
                  requiredBalance.subtractCompensatingBalance ? 'Yes' : 'No'
                }
              />
            </DetailsSectionItemsRow>
            <DetailsSectionItemsRow className='mt-6'>
              <DetailsSectionItem
                label={'Subtract interest paid month-to-date'}
                info={
                  requiredBalance.subtractInterestPaidMonthToDate ? 'Yes' : 'No'
                }
              />
            </DetailsSectionItemsRow>
            {renderOtherLabels()}
          </DetailsSection>
        </div>
        <div className='flex max-w-72 flex-col rounded-lg border bg-white p-6'>
          <VersionTimeline
            versions={requiredBalances}
            currentEffectiveDate={route.params.effectiveDate}
            linkFormatter={linkFormatter}
          />
        </div>
      </div>
    </div>
  )
}
