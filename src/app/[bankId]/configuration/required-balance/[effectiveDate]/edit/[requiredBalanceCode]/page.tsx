'use client'

import { useRouter } from 'next/navigation'
import { But<PERSON> } from '@/components/Button'
import { useMutation, useQuery } from '@tanstack/react-query'

import { formatToMonthYearFromDate, formatToServerString } from '@/lib/date'
import { unwrap } from '@/lib/unions/unwrap'
import { routeTo, useRoute } from '@/app/[bankId]/configuration/routing'

import { requiredBalanceMutation } from '../../../mutations'
import { requiredBalanceQueries } from '../../../queries'
import RequiredBalanceForm from '../../../_components/RequiredBalanceForm'

import {
  RequiredBalanceApiSchema,
  RequiredBalanceApiTypes,
  RequiredBalanceFormTypes,
  RequiredBalanceFormSchemaPartial,
} from '../../../_components/RequiredBalanceFormTypes'

export default function EditRequiredBalance({}) {
  const router = useRouter()
  const route = unwrap(
    useRoute(),
    '/configuration/required-balance/[effectiveDate]/edit/[requiredBalanceCode]',
  )!
  const { effectiveDate, requiredBalanceCode } = route.params

  const payload = {
    code: requiredBalanceCode,
    effectiveDate: formatToServerString(effectiveDate),
  }

  const { data, status, error } = useQuery(
    requiredBalanceQueries('/getRequiredBalanceDefinition', payload),
  )

  const updateRequiredBalance = useMutation(
    requiredBalanceMutation('/updateRequiredBalanceDefinition', {
      onSuccess: (_, request) => {
        router.push(
          routeTo(
            '/configuration/required-balance/[effectiveDate]/view/[requiredBalanceCode]',
            {
              ...route.params,
              requiredBalanceCode: request.code!,
              effectiveDate: formatToMonthYearFromDate(request.effectiveDate!),
            },
          ),
        )
      },
    }),
  )
  if (status === 'pending') return 'Loading...'
  if (!data || error) {
    return <div>404 Not Found</div>
  }

  const requiredBalance = RequiredBalanceFormSchemaPartial.parse(data)

  const { subtractOtherBalanceLabel, addOtherBalanceLabel } = requiredBalance
  const label = subtractOtherBalanceLabel || addOtherBalanceLabel

  const defaultValues: RequiredBalanceFormTypes = {
    code: requiredBalance.code,
    name: requiredBalance.name,
    effectiveDate: formatToMonthYearFromDate(requiredBalance.effectiveDate),
    baseBalanceType: requiredBalance.baseBalanceType,
    subtractCompensatingBalance: requiredBalance.subtractCompensatingBalance,
    subtractInterestPaidMonthToDate:
      requiredBalance.subtractInterestPaidMonthToDate,

    otherBalanceCalculation: !!label,
    otherBalanceMethod: subtractOtherBalanceLabel ? 'SUBTRACT' : 'ADD',
    otherBalanceLabel: label,
  }

  const serialize = (formState: RequiredBalanceFormTypes) => {
    const hasOtherBalanceCalculation = formState.otherBalanceCalculation
    const { otherBalanceMethod } = formState

    const otherBalanceKeys =
      otherBalanceMethod === 'ADD' ?
        {
          addOtherBalanceLabel: formState.otherBalanceLabel,
          subtractOtherBalanceLabel: null,
        }
      : {
          addOtherBalanceLabel: null,
          subtractOtherBalanceLabel: formState.otherBalanceLabel,
        }

    const otherKeys =
      hasOtherBalanceCalculation ? otherBalanceKeys : (
        {
          addOtherBalanceLabel: null,
          subtractOtherBalanceLabel: null,
        }
      )

    const payload: RequiredBalanceApiTypes = RequiredBalanceApiSchema.parse({
      code: formState.code,
      name: formState.name,
      effectiveDate: formatToServerString(formState.effectiveDate),

      baseBalanceType: formState.baseBalanceType!,
      subtractCompensatingBalance: formState.subtractCompensatingBalance,
      subtractInterestPaidMonthToDate:
        formState.subtractInterestPaidMonthToDate,

      ...otherKeys,
    })
    return payload
  }

  return (
    <div className='w-full min-w-full overflow-auto pt-12'>
      <div className='text-md ml-8 font-medium'>Schedule Information</div>
      <RequiredBalanceForm
        defaultValues={defaultValues}
        onSubmit={(formState) => {
          const payload = serialize(formState)
          updateRequiredBalance.mutate(payload)
        }}
        isEditMode
      >
        <Button className='btn w-60' onClick={() => router.back()}>
          Cancel
        </Button>

        <Button type='submit' className='btn-primary w-60'>
          Save
        </Button>
      </RequiredBalanceForm>
    </div>
  )
}
