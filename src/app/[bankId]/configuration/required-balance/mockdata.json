{"/createRequiredBalanceDefinition": {"payloads": [{"code": "99999", "name": "Average positive collected less reserves", "effectiveDate": "2025-01-01", "baseBalanceType": "AVERAGE_COLLECTED", "subtractCompensatingBalance": false, "subtractInterestPaidMonthToDate": true, "subtractOtherBalanceLabel": "Test Subtract balance label", "addOtherBalanceLabel": null}, {"code": "888", "name": "Test Required Balance 1", "effectiveDate": "2025-01-01", "baseBalanceType": "END_OF_MONTH_LEDGER", "subtractCompensatingBalance": true, "subtractInterestPaidMonthToDate": false, "subtractOtherBalanceLabel": null, "addOtherBalanceLabel": "Test Subtract balance label"}, {"code": "999", "name": "Test Required Balance 1", "effectiveDate": "2025-01-01", "baseBalanceType": "END_OF_MONTH_LEDGER", "subtractCompensatingBalance": true, "subtractInterestPaidMonthToDate": false, "subtractOtherBalanceLabel": null, "addOtherBalanceLabel": null}]}}