import { revenueConnectClient } from '@/api/revenueConnectClient'
import { defineQueries, defineQuery } from '@/lib/state/defineQuery'
import { withMetrics } from '@/lib/middleware/withMetrics'

const getRequiredBalanceByEffectiveDate = defineQuery(
  revenueConnectClient,
  '/getRequiredBalanceDefinitions',
  (payload) => [payload.effectiveDate],
)

const getRequiredBalanceByCode = defineQuery(
  revenueConnectClient,
  '/getRequiredBalanceDefinitionByCode',
  (payload) => [payload.code],
)

const getRequiredBalance = defineQuery(
  revenueConnectClient,
  '/getRequiredBalanceDefinition',
  (payload) => [payload.code, payload.effectiveDate],
)

export const requiredBalanceQueries = defineQueries(
  [
    getRequiredBalanceByEffectiveDate,
    getRequiredBalanceByCode,
    getRequiredBalance,
  ],
  withMetrics(),
)
