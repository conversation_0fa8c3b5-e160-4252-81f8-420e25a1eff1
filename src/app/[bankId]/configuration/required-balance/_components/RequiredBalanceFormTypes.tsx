import { z } from 'zod'

import { apiToFormSchemas } from '@/api/apiToFormSchema'
import { formToApiSchemas } from '@/api/formToApiSchema'
import { variant } from '@/lib/unions/Union'
import { match } from '@/lib/unions/match'
import { configCode } from '@/api/zodSchemas'

export const {
  balanceRequirementDefinition: RequiredBalanceFormSchemaPartial,
} = apiToFormSchemas
const { balanceRequirementDefinition: RequiredBalanceApiSchemaPartial } =
  formToApiSchemas

export const RequiredBalanceFormSchema =
  RequiredBalanceFormSchemaPartial.extend({
    otherBalanceCalculation: z.boolean(),
    otherBalanceMethod: z.enum(['ADD', 'SUBTRACT']).nullable(),
    otherBalanceLabel: z.string().nullable(),
  }).omit({
    addOtherBalanceLabel: true,
    subtractOtherBalanceLabel: true,
  })

export const RequiredBalanceApiSchema = RequiredBalanceApiSchemaPartial.extend({
  code: configCode,
})

export type RequiredBalanceFormTypes = z.infer<typeof RequiredBalanceFormSchema>
export type RequiredBalanceApiTypes = z.infer<typeof RequiredBalanceApiSchema>

export const baseBalanceTypeLabel = (
  value: z.infer<typeof RequiredBalanceFormSchema.shape.baseBalanceType>,
) => {
  if (value === null) {
    return value
  }
  return match(variant(value), {
    AVERAGE_COLLECTED: () => 'Average Collected',
    AVERAGE_LEDGER: () => 'Average Ledger',
    AVERAGE_NEGATIVE_COLLECTED: () => 'Average Negative Collected',
    AVERAGE_NEGATIVE_LEDGER: () => 'Average Negative Ledger',
    AVERAGE_POSITIVE_COLLECTED: () => 'Average Positive Collected',
    AVERAGE_POSITIVE_LEDGER: () => 'Average Positive Ledger',
    AVERAGE_UNCOLLECTED_FUNDS: () => 'Average Uncollected Funds',
    COMPENSATING_BALANCE: () => 'Compensating Balance',
    END_OF_MONTH_LEDGER: () => 'End Of Month Ledger',
    INVESTABLE_BALANCE: () => 'Investible Balance',
    AVERAGE_FLOAT: () => 'Average Float',
    AVERAGE_CLEARINGHOUSE_FLOAT: () => 'Average Clearinghouse Float',
    REQUIRED_BALANCE: () => 'Required balance',
  })
}
