import { withMetrics } from '@/lib/middleware/withMetrics'
import { defineMutations, defineMutation } from '@/lib/state/defineMutation'
import { revenueConnectClient } from '@/api/revenueConnectClient'
import { requiredBalanceQueries } from './queries'
import { compose } from '@/lib/functional/compose'
import { notifications } from './notifications'

const createRequiredBalance = defineMutation(
  revenueConnectClient,
  '/createRequiredBalanceDefinition',
  {
    invalidate: (request) => [
      requiredBalanceQueries('/getRequiredBalanceDefinitions', {
        effectiveDate: request.effectiveDate,
      }),
      requiredBalanceQueries('/getRequiredBalanceDefinitionByCode', {
        code: request.code,
      }),
      requiredBalanceQueries('/getRequiredBalanceDefinition', {
        effectiveDate: request.effectiveDate,
        code: request.code,
      }),
    ],
  },
)

const updateRequiredBalance = defineMutation(
  revenueConnectClient,
  '/updateRequiredBalanceDefinition',
  {
    invalidate: (request) => [
      requiredBalanceQueries('/getRequiredBalanceDefinitions', {
        effectiveDate: request.effectiveDate,
      }),
      requiredBalanceQueries('/getRequiredBalanceDefinitionByCode', {
        code: request.code,
      }),
      requiredBalanceQueries('/getRequiredBalanceDefinition', {
        effectiveDate: request.effectiveDate,
        code: request.code,
      }),
    ],
  },
)

export type RequiredBalanceMutation =
  | typeof createRequiredBalance
  | typeof updateRequiredBalance

export const requiredBalanceMutation = defineMutations(
  [createRequiredBalance, updateRequiredBalance],
  compose(notifications, withMetrics()),
)
