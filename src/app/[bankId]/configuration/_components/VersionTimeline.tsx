import clsx from 'clsx'
import Link from 'next/link'
import {
  formatToMonthYearFromDate,
  parseServerFormat,
  toUTCDateString,
} from '@/lib/date'
import { Badge } from '@/components/Badge'
import { compareAsc, subDays } from 'date-fns'

/**
 *  Credit: based on @mykola-kodrul-bond's ServiceVersionTimeline component
 */

export function VersionTimeline({
  currentEffectiveDate,
  versions,
  linkFormatter,
  title,
  description,
  shouldParseToDateLevel,
}: {
  currentEffectiveDate: string
  versions: TimelinePeriod[]
  linkFormatter: (date: string, code: string) => string
  title?: string
  description?: string
  shouldParseToDateLevel?: boolean
}) {
  const stringFormatter =
    shouldParseToDateLevel ?
      (v: string | Date) => toUTCDateString(v)
    : formatToMonthYearFromDate

  const timeline: TimelinePeriod[] = versions
    .sort((a, b) =>
      compareAsc(
        parseServerFormat(a.effectiveDate),
        parseServerFormat(b.effectiveDate),
      ),
    )
    .reduce((timeline, version) => {
      const previousService = timeline[timeline.length - 1]
      if (previousService) {
        previousService.expirationDate = stringFormatter(
          subDays(version.effectiveDate, 1),
        )
      }
      return timeline.concat({
        code: version.code,
        effectiveDate: version.effectiveDate,
        expirationDate: version.expirationDate,
        isExpired: version.isExpired,
      })
    }, [] as TimelinePeriod[])
    .reverse()

  return (
    <div>
      <div className='flex flex-col'>
        <div className='flex gap-2 text-xl font-bold text-app-color-primary'>
          {title ?? 'Timeline'}
        </div>
        <div className='text-app-color-secondary'>
          {description ?? 'All changes will appear on this timeline'}
        </div>
      </div>
      <div>
        {timeline.map((record, index) => (
          <TimelineItem
            key={index}
            isSelected={
              stringFormatter(record.effectiveDate) === currentEffectiveDate
            }
            record={{
              ...record,
              effectiveDate: stringFormatter(record.effectiveDate),
            }}
            isLast={timeline.length - 1 === index}
            isFirst={index === 0}
            isExpired={!!record.isExpired && index === 0}
            linkFormatter={linkFormatter}
          />
        ))}
      </div>
    </div>
  )
}

export interface TimelinePeriod {
  code: string
  effectiveDate: string
  expirationDate?: string
  isExpired?: boolean
}

function TimelineItem({
  record,
  isLast,
  isFirst,
  isSelected,
  isExpired,
  linkFormatter,
}: React.PropsWithChildren<{
  record: TimelinePeriod
  isLast: boolean
  isFirst: boolean
  isSelected: boolean
  isExpired: boolean
  linkFormatter: (date: string, code: string) => string
}>) {
  const link = linkFormatter(record.effectiveDate, record.code)

  return (
    <Link href={link}>
      <div className='flex min-h-20 gap-2'>
        <div className='relative flex min-w-10 flex-col items-center'>
          <div
            className={clsx(
              'absolute top-3 w-1 flex-auto bg-app-color-bg-brand-secondary',
              isLast ? 'h-3' : 'h-full',
              { 'top-2': isFirst },
            )}
          ></div>
          {isSelected ?
            <div className='z-10 mt-2 size-5 rounded-full border-4 border-app-color-bg-brand-secondary bg-app-color-bg-brand-solid'></div>
          : <div className='mt-3 size-3 rounded-full bg-app-color-bg-brand-secondary'></div>
          }
        </div>
        <div
          className={clsx(
            'flex flex-1 flex-col text-nowrap rounded-lg p-2 text-sm',
            {
              'bg-app-color-bg-brand-primary font-bold': isSelected,
            },
          )}
        >
          <div className='flex justify-between'>
            <span>Effective:</span>
            {isExpired && (
              <Badge type='error' className='text-xs font-medium'>
                Expired
              </Badge>
            )}
          </div>
          <span className='text-sm'>
            {` ${record.effectiveDate} - ${record.expirationDate ?? 'ongoing'}`}
          </span>
          <span className='text-xs font-semibold text-app-color-text-secondary'>
            {isSelected && 'Currently viewing'}
          </span>
        </div>
      </div>
    </Link>
  )
}
