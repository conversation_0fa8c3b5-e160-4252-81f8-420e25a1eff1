import { Updater } from '@tanstack/react-form'
import { useState } from 'react'
import { CheckIcon, MinusIcon } from '@heroicons/react/24/outline'

import { Checkbox } from '@/components/Checkbox'

const MonthOptions = [
  { value: 'JANUARY', label: '01 - January' },
  { value: 'FEBRUARY', label: '02 - February' },
  { value: 'MARCH', label: '03 - March' },
  { value: 'APRIL', label: '04 - April' },
  { value: 'MAY', label: '05 - May' },
  { value: 'JUNE', label: '06 - June' },
  { value: 'JULY', label: '07 - July' },
  { value: 'AUGUST', label: '08 - August' },
  { value: 'SEPTEMBER', label: '09 - September' },
  { value: 'OCTOBER', label: '10 - October' },
  { value: 'NOVEMBER', label: '11 - November' },
  { value: 'DECEMBER', label: '12 - December' },
]

interface MonthSelectorProps {
  viewOnly?: boolean
  values: (string | undefined)[]
  handleChange?: (updater: Updater<(string | undefined)[]>) => void
}

export default function MonthSelector({
  values,
  handleChange,
  viewOnly,
}: MonthSelectorProps) {
  const [selectedOptions, setSelectedOptions] = useState(values)

  const handleCheckboxChange = (optionValue: string) => {
    let newOptions = [...selectedOptions]
    const i = selectedOptions.indexOf(optionValue)
    if (i >= 0) {
      newOptions = newOptions.filter((_, j) => i !== j)
    } else {
      newOptions.push(optionValue)
    }
    setSelectedOptions(newOptions)
    handleChange?.(newOptions)
  }

  const renderStatus = (optionValue: string) => {
    const isActive = selectedOptions.includes(optionValue)
    if (viewOnly) {
      return isActive ?
          <CheckIcon className='size-6 pr-1' />
        : <MinusIcon className='size-6 pr-1' />
    }
    return (
      <Checkbox
        className='size-6 cursor-pointer pr-1'
        checked={isActive}
        onChange={() => handleCheckboxChange(optionValue)}
      />
    )
  }

  return (
    <div className='mt-6 flex flex-col rounded-lg border bg-white p-0'>
      <div className='m-0 border-b bg-app-color-bg-secondary p-3'>Month</div>
      {MonthOptions.map((option, i) => (
        <div key={option.value} className='flex justify-between border-b p-3'>
          <div>{option.label}</div>
          <div>{renderStatus(option.value)}</div>
        </div>
      ))}
    </div>
  )
}
