import { Popover, PopoverButton, PopoverPanel } from '@headlessui/react'
import React from 'react'
import { clsx } from 'clsx'
import {
  EllipsisVerticalIcon,
  PencilSquareIcon,
  TrashIcon,
} from '@heroicons/react/24/outline'

import { <PERSON><PERSON> } from '@/components/Button'
import { Tooltip } from '@/components/Tooltip'

interface RowPopoverProps {
  onOpen?: () => void
  onEdit?: () => void
  className?: string
}

export function RowPopover({ onOpen, onEdit, className }: RowPopoverProps) {
  return (
    <Popover className='inline-flex'>
      <PopoverButton role='button' className={className}>
        <EllipsisVerticalIcon className='size-5' />
      </PopoverButton>
      <PopoverPanel
        anchor='bottom start'
        className='flex min-w-44 flex-col rounded-md border bg-white text-sm'
      >
        <Button
          onClick={onEdit}
          className='flex items-center gap-1 border-none p-3 hover:bg-app-color-bg-secondary'
        >
          <PencilSquareIcon className='size-4' />
          Edit
        </Button>
        <Tooltip content={'Cannot be deleted'}>
          <Button
            onClick={() => {}}
            disabled
            className='flex w-full items-center gap-1 border-none p-3 text-gray-200 hover:bg-app-color-bg-secondary'
          >
            <TrashIcon className='size-4' />
            Delete
          </Button>
        </Tooltip>
      </PopoverPanel>
    </Popover>
  )
}
