'use client'

import { useBankOptions } from '../../_hooks/useBankOptions'
import { routeTo, useRoute } from '../../../routing'
import { unwrap } from '@/lib/unions/unwrap'
import { useRouter } from 'next/navigation'
import { UpdateBankOptionsForm } from './UpdateBankOptionsForm'
import { useMutation } from '@tanstack/react-query'
import { bankOptionsMutation } from '../../mutations'
import { Button } from '@/components/Button'
import { formToApiSchemas } from '@/api/formToApiSchema'
import { BankOptionsFormValidator } from '../../types'

export default function EditBankOptions() {
  const router = useRouter()
  const route = unwrap(
    useRoute(),
    '/configuration/bank-options/[effectiveDate]/edit',
  )!
  const bankId = route.params.bankId
  const effectiveDate = route.params.effectiveDate
  const { bankOptions, status, error } = useBankOptions({
    bankId,
    effectiveDate,
  })

  const updateBankOptions = useMutation(
    bankOptionsMutation('/updateBankOptions', {
      onSuccess: () => {
        router.push(
          routeTo(
            '/configuration/bank-options/[effectiveDate]/view',
            route.params,
          ),
        )
      },
    }),
  )

  if (status === 'pending') return 'Loading...'
  if (!bankOptions || error) return <div>404 Not Found</div>

  const defaultValues = {
    ...bankOptions,
    effectiveDate,
  }

  return (
    <div className='w-full overflow-hidden overflow-y-scroll pt-12'>
      <div className='ml-8 text-lg font-medium'>Edit bank options</div>
      <UpdateBankOptionsForm
        defaultValues={defaultValues}
        onSubmit={(formState) => {
          updateBankOptions.mutate(BankOptionsFormValidator.parse(formState))
        }}
      >
        <Button className='btn w-60' onClick={() => router.back()}>
          Cancel
        </Button>

        <Button type='submit' className='btn-primary w-60'>
          Save
        </Button>
      </UpdateBankOptionsForm>
    </div>
  )
}
