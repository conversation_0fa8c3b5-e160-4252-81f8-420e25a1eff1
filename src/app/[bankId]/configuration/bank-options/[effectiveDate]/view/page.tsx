'use client'

import { toUTCDateString } from '@/lib/date'
import { ChevronRightIcon } from '@heroicons/react/24/outline'
import { Button } from '@/components/Button'
import {
  DetailsSection,
  DetailsSectionItem,
  DetailsSectionItemsRow,
  DetailsSectionTitle,
} from '@/components/DetailsSection'
import { routeTo, useRoute } from '../../../routing'
import { unwrap } from '@/lib/unions/unwrap'
import { enumOptionRenderers, renderBoolean } from '../../helpers'
import { useCallback } from 'react'
import { useRouter } from 'next/navigation'
import { VersionTimeline } from '../../../_components/VersionTimeline'
import { useBankOptionsTimeline } from '../../_hooks/useBankOptionsTimeline'

export default function ViewBankOptions() {
  const router = useRouter()
  const route = unwrap(
    useRoute(),
    '/configuration/bank-options/[effectiveDate]/view',
  )!
  const bankId = route.params.bankId
  const effectiveDate = route.params.effectiveDate
  const { versions, atDateOrLatest } = useBankOptionsTimeline({ bankId })
  const bankOptions = atDateOrLatest(effectiveDate)
  const onEditClick = useCallback(() => {
    router.push(
      routeTo('/configuration/bank-options/[effectiveDate]/edit', route.params),
    )
  }, [router, route.params])

  if (!bankOptions) return null

  const linkFormatter = (effectiveDate: string) => {
    return routeTo('/configuration/bank-options/[effectiveDate]/view', {
      ...route.params,
      effectiveDate,
    })
  }

  return (
    <div className='w-full overflow-hidden overflow-y-scroll px-8 py-10'>
      <div className='flex items-center justify-between'>
        <div className='flex items-center gap-2'>
          <div className='font-bold'>
            {/*
              For MVP we can only view the default bank options.
              For GA, this will be a link to view BankOptions for all banks within a multi-bank holding company.
            */}
            {`...`}
          </div>
          <ChevronRightIcon className='h-4 w-4 text-zinc-400' />
          <div className='text-md text-zinc-500'>Bank options</div>
          <ChevronRightIcon className='h-4 w-4 text-zinc-400' />
          <div className='text-lg'>{bankOptions.name}</div>
          <div className='text-sm text-zinc-500'>{bankId}</div>
        </div>
        <Button className='btn-primary text-white' onClick={onEditClick}>
          Edit
        </Button>
      </div>
      <div className='mt-8 flex gap-4'>
        <div className='flex grow flex-col gap-4'>
          <div className='flex grow flex-col'>
            <DetailsSection>
              <DetailsSectionTitle>
                Bank options information
              </DetailsSectionTitle>
              <DetailsSectionItemsRow>
                <DetailsSectionItem
                  label='Effective date *'
                  info={toUTCDateString(bankOptions.effectiveDate)}
                />
              </DetailsSectionItemsRow>
              <DetailsSectionItemsRow>
                <DetailsSectionItem label='Name *' info={bankOptions.name} />
                <DetailsSectionItem label='Code *' info={bankOptions.code} />
              </DetailsSectionItemsRow>
            </DetailsSection>
          </div>
          <div className='flex grow flex-col'>
            <DetailsSection>
              <DetailsSectionTitle>
                Analysis processing dates
              </DetailsSectionTitle>
              <DetailsSectionItemsRow>
                <DetailsSectionItem
                  label='Preliminary analysis day each month *'
                  info={enumOptionRenderers.prelimAnalysis(
                    bankOptions.prelimAnalysis,
                  )}
                />
                <div className='flex-1'></div>
              </DetailsSectionItemsRow>
              <DetailsSectionItemsRow>
                <DetailsSectionItem
                  label='Final analysis *'
                  info={enumOptionRenderers.finalAnalysis(
                    bankOptions.finalAnalysis,
                  )}
                />
                <DetailsSectionItem
                  label={`${bankOptions.finalAnalysis === 'SAME_DATE_EACH_MONTH' ? 'Date each month' : 'Number of days after prelims'} *`}
                  info={bankOptions.finalAnalysisDays}
                />
              </DetailsSectionItemsRow>
              <DetailsSectionItemsRow>
                <DetailsSectionItem
                  label='Automatic daily analysis of accounts in preliminary status *'
                  info={renderBoolean(bankOptions.automaticPrelimAnalysis)}
                />
                <div className='flex-1'></div>
              </DetailsSectionItemsRow>
            </DetailsSection>
          </div>
          <div className='flex grow flex-col'>
            <DetailsSection>
              <DetailsSectionTitle>Balance calculations</DetailsSectionTitle>
              <DetailsSectionItemsRow>
                <DetailsSectionItem
                  label='Balance cycle days *'
                  info={enumOptionRenderers.balanceCycleDays(
                    bankOptions.balanceCycleDays,
                  )}
                />
                <DetailsSectionItem
                  label='Basis for calculating balance fee *'
                  info={enumOptionRenderers.calculatingBalanceFeeBasis(
                    bankOptions.calculatingBalanceFeeBasis,
                  )}
                />
              </DetailsSectionItemsRow>
            </DetailsSection>
          </div>
          <div className='flex grow flex-col'>
            <DetailsSection>
              <DetailsSectionTitle>
                Earnings credit calculations
              </DetailsSectionTitle>
              <DetailsSectionItemsRow>
                <DetailsSectionItem
                  label='Earnings credit cycle days *'
                  info={enumOptionRenderers.earningsCycleDays(
                    bankOptions.earningsCycleDays,
                  )}
                />
                <DetailsSectionItem
                  label='Basis for calculating earnings credit *'
                  info={enumOptionRenderers.calculatingEarningsCreditBasis(
                    bankOptions.calculatingEarningsCreditBasis,
                  )}
                />
              </DetailsSectionItemsRow>
            </DetailsSection>
          </div>
          <div className='flex grow flex-col'>
            <DetailsSection>
              <DetailsSectionTitle>
                Live link with key account
              </DetailsSectionTitle>
              <DetailsSectionItemsRow>
                <DetailsSectionItem
                  label='Copy analysis type from key account'
                  info={renderBoolean(bankOptions.copyAccountTypeOfKeyAccount)}
                />
                <DetailsSectionItem
                  label='Copy user fields from key account'
                  info={renderBoolean(bankOptions.copyUserFieldsFromKeyAccount)}
                />
              </DetailsSectionItemsRow>
            </DetailsSection>
          </div>
          <div className='flex grow flex-col'>
            <DetailsSection>
              <DetailsSectionTitle>Currency</DetailsSectionTitle>
              <DetailsSectionItemsRow>
                <DetailsSectionItem
                  label='Default currency'
                  info={enumOptionRenderers.defaultCurrency(
                    bankOptions.defaultCurrency,
                  )}
                />
                <div className='flex-1'></div>
              </DetailsSectionItemsRow>
            </DetailsSection>
          </div>
          <div className='flex grow flex-col'>
            <DetailsSection>
              <DetailsSectionTitle>History retention</DetailsSectionTitle>
              <DetailsSectionItemsRow>
                <DetailsSectionItem
                  label='Number of months'
                  info={bankOptions.retentionMonths}
                />
                <div className='flex-1'></div>
              </DetailsSectionItemsRow>
            </DetailsSection>
          </div>
          <div className='flex grow flex-col'>
            <DetailsSection>
              <DetailsSectionTitle>Statement archiving</DetailsSectionTitle>
              <DetailsSectionItemsRow>
                <DetailsSectionItem
                  label='Frequency *'
                  info={enumOptionRenderers.statementArchivingFrequency(
                    bankOptions.statementArchivingFrequency,
                  )}
                />
                <div className='flex-1'></div>
              </DetailsSectionItemsRow>
            </DetailsSection>
          </div>
          <div className='flex grow flex-col'>
            <DetailsSection>
              <DetailsSectionTitle>
                Account number structure
              </DetailsSectionTitle>
              <DetailsSectionItemsRow>
                <DetailsSectionItem
                  label='Number of digits *'
                  info={bankOptions.accountNumberDigits}
                />
                <DetailsSectionItem
                  label='Number Format *'
                  info={bankOptions.accountNumberPattern}
                />
              </DetailsSectionItemsRow>
              <DetailsSectionItemsRow>
                <DetailsSectionItem
                  label='Mask account number on statements'
                  info={bankOptions.accountNumberMasking}
                />
                <div className='flex-1'></div>
              </DetailsSectionItemsRow>
            </DetailsSection>
          </div>
          <div className='flex grow flex-col'>
            <DetailsSection>
              <DetailsSectionTitle>Miscellaneous</DetailsSectionTitle>
              <DetailsSectionItemsRow>
                <DetailsSectionItem
                  label='Assign default statement package to deposit accounts *'
                  info={renderBoolean(
                    bankOptions.assignDefaultStatementPackageToDepositAccount,
                  )}
                />
                <DetailsSectionItem
                  label='Default term in months for promotions and overrides *'
                  info={bankOptions.defaultTermInMonthsForPromoAndOverrides}
                />
              </DetailsSectionItemsRow>
            </DetailsSection>
          </div>
        </div>
        <div className='flex max-w-72 flex-col rounded-lg border bg-white p-6'>
          {versions && (
            <VersionTimeline
              versions={versions}
              currentEffectiveDate={route.params.effectiveDate}
              linkFormatter={linkFormatter}
            />
          )}
        </div>
      </div>
    </div>
  )
}
