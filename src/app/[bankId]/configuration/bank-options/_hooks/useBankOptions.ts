import { useMemo } from 'react'
import { useQuery, UseQueryResult } from '@tanstack/react-query'
import { BankOptions as ApiBankOptions } from '@/api/formToApiSchema'
import { toServerFormat } from '@/lib/date'
import { BankOptions, BankOptionsForm } from '../types'
import { bankOptionsQueries } from '../queries'
import { apiToFormSchemas } from '@/api/apiToFormSchema'
import { listSelector } from '@/api/selectors'

interface UseBankOptionsProps {
  bankId?: string
  effectiveDate?: string
}

export function useBankOptions({
  bankId,
  effectiveDate,
}: UseBankOptionsProps): Omit<UseQueryResult<ApiBankOptions[]>, 'data'> & {
  bankOptions?: BankOptions
  allBankOptions?: BankOptionsForm[]
} {
  const { data: allBankOptions, ...result } = useQuery({
    ...bankOptionsQueries('/listBankOptionsByEffectiveDate', {
      effectiveDate: effectiveDate!,
    }),
    enabled: !!bankId && !!effectiveDate,
    select: listSelector(apiToFormSchemas.bankOptions),
  })

  return useMemo(() => {
    return {
      bankOptions: allBankOptions?.find(
        (bankOptions) => bankOptions.code === bankId,
      ),
      allBankOptions,
      ...result,
    }
  }, [result, allBankOptions, bankId])
}
