import { useQuery, UseQueryResult } from '@tanstack/react-query'
import { bankOptionsQueries } from '../queries'
import { useMemo } from 'react'
import { BankOptions as ApiBankOptions } from '@/api/formToApiSchema'
import { BankOptionsForm, BankOptionsFormZodSchema } from '../types'
import { TimelinePeriod } from '../../_components/VersionTimeline'

export function useBankOptionsTimeline({ bankId }: { bankId?: string }): Omit<
  UseQueryResult<ApiBankOptions[]>,
  'data'
> & {
  timeline?: BankOptionsForm[]
  atDateOrLatest: (date: string) => BankOptionsForm | undefined
  versions: TimelinePeriod[]
} {
  const result = useQuery({
    ...bankOptionsQueries('/getBankOptionsByCode', {
      code: bankId!,
    }),
    enabled: !!bankId,
  })
  return useMemo(() => {
    const { data: timeline, ...restResult } = result
    // reverse chronological
    const sortedTimeline = timeline
      ?.map((bankOptions) => BankOptionsFormZodSchema.parse(bankOptions))
      .sort(
        (a, b) =>
          new Date(a.effectiveDate).getDate() -
          new Date(b.effectiveDate).getDate(),
      )

    const versions =
      sortedTimeline?.map(
        (
          bankOptions: BankOptionsForm,
          index: number,
          array: BankOptionsForm[],
        ) => {
          const expirationDate =
            index === 0 ? undefined : array[index - 1].effectiveDate
          return {
            code: bankOptions.code,
            effectiveDate: bankOptions.effectiveDate,
            expirationDate: expirationDate,
            isExpired:
              expirationDate ?
                new Date().getDate() > new Date(expirationDate).getDate()
              : undefined,
          }
        },
      ) ?? []
    return {
      timeline: sortedTimeline,
      atDateOrLatest: (date: string) =>
        sortedTimeline?.find((item) => {
          return item.effectiveDate === date
        }) || sortedTimeline?.[0],
      versions,
      ...restResult,
    }
  }, [result])
}
