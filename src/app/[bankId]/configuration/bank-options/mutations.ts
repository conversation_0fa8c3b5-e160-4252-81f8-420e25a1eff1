import { revenueConnectClient } from '@/api/revenueConnectClient'
import { defineMutation, defineMutations } from '@/lib/state/defineMutation'
import { compose } from '@/lib/functional/compose'
import { withMetrics } from '@/lib/middleware/withMetrics'
import { bankOptionsQueries } from './queries'
import { notifications } from './notifications'

const updateBankOptions = defineMutation(
  revenueConnectClient,
  '/updateBankOptions',
  {
    invalidate: (request) => [
      bankOptionsQueries('/getBankOptionsByCode', {
        code: request.code,
      }),
      bankOptionsQueries('/listBankOptionsByEffectiveDate', {
        effectiveDate: request.effectiveDate,
      }),
    ],
  },
)

export type BankOptionsMutation = typeof updateBankOptions

export const bankOptionsMutation = defineMutations(
  [updateBankOptions],
  compose(notifications, withMetrics()),
)
