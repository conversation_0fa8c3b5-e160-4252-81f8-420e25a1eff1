import { BankOptionsForm, BankOptionsFormZodSchema } from './types'

export function renderBoolean(value: boolean) {
  return value ? 'Yes' : 'No'
}

function toTitleCase(s: string): string {
  return s[0].toUpperCase() + s.slice(1)
}

export function formatEnumValue(value: string) {
  return toTitleCase(value.toLowerCase()).replaceAll('_', ' ')
}

const bankOptionsEnumFields = [
  'balanceCycleDays',
  'calculatingBalanceFeeBasis',
  'earningsCycleDays',
  'calculatingEarningsCreditBasis',
  'defaultCurrency',
  'prelimAnalysis',
  'finalAnalysis',
  'statementArchivingFrequency',
] as const

type BankOptionsEnumField = (typeof bankOptionsEnumFields)[number]

export const enumOptions = Object.fromEntries(
  bankOptionsEnumFields.map((fieldName) => [
    fieldName,
    BankOptionsFormZodSchema.shape[fieldName].options,
  ]) as [BankOptionsEnumField, string[]][],
) as { [FieldName in BankOptionsEnumField]: BankOptionsForm[FieldName][] }

const prelimAnalysisLabels: Record<BankOptionsForm['prelimAnalysis'], string> =
  {
    ...(Object.fromEntries(
      BankOptionsFormZodSchema.shape.prelimAnalysis.options.map((option) => [
        option,
        formatEnumValue(option),
      ]),
    ) as Record<BankOptionsForm['prelimAnalysis'], string>),
    LastBusinessDayPriorMonth: 'Last business day of prior month',
  }

const finalAnalysisLabels: Record<BankOptionsForm['finalAnalysis'], string> = {
  SAME_DATE_EACH_MONTH: 'Same date each month',
  SPECIFIC_DAYS_POST_PRELIM: 'Specific number of days after prelims',
}

const currencies: Record<BankOptionsForm['defaultCurrency'], string> = {
  US_DOLLARS: 'US dollars',
}

export const enumOptionRenderers = {
  balanceCycleDays: (value: BankOptionsForm['balanceCycleDays']) =>
    formatEnumValue(value),
  calculatingBalanceFeeBasis: (
    value: BankOptionsForm['calculatingBalanceFeeBasis'],
  ) => value,
  earningsCycleDays: (value: BankOptionsForm['earningsCycleDays']) =>
    formatEnumValue(value),
  calculatingEarningsCreditBasis: (
    value: BankOptionsForm['calculatingEarningsCreditBasis'],
  ) => value,
  defaultCurrency: (value: BankOptionsForm['defaultCurrency']) =>
    currencies[value],
  prelimAnalysis: (value: BankOptionsForm['prelimAnalysis']) =>
    prelimAnalysisLabels[value],
  finalAnalysis: (value: BankOptionsForm['finalAnalysis']) =>
    finalAnalysisLabels[value],
  statementArchivingFrequency: (
    value: BankOptionsForm['statementArchivingFrequency'],
  ) => formatEnumValue(value),
}
