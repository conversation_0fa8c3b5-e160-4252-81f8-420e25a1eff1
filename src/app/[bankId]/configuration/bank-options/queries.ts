import { revenueConnectClient } from '@/api/revenueConnectClient'
import { withMetrics } from '@/lib/middleware/withMetrics'
import { defineQueries, defineQuery } from '@/lib/state/defineQuery'

// TODO: AFIN-466 - Bank Options - will take care of reconciling these queries

const getBankOptionsByCode = defineQuery(
  revenueConnectClient,
  '/getBankOptionsByCode',
  (payload) => [payload.code],
)

const listBankOptionsByEffectiveDate = defineQuery(
  revenueConnectClient,
  '/listBankOptionsByEffectiveDate',
  (payload) => [payload.effectiveDate],
)

export const bankOptionsQueries = defineQueries(
  [listBankOptionsByEffectiveDate, getBankOptionsByCode],
  withMetrics(),
)
