import { formToApiSchemas, formValidators } from '@/api/formToApiSchema'
import { stringToNumber } from '@/strings/stringToNumber'
import { z } from 'zod'

export const BankOptionsFormZodSchema = formValidators.bankOptions.extend({
  // extra validationts for finalAnalysisDays
  finalAnalysisDays: formValidators.bankOptions.shape.finalAnalysisDays
    .min(0)
    .max(27),
})

export const BankOptionsFormValidator = formValidators.bankOptions.extend({
  finalAnalysisDays: z
    .union([z.string(), z.number()])
    .transform(stringToNumber)
    .pipe(z.number().int().min(0).max(27)),
  retentionMonths: z.union([z.string(), z.number()]).transform(stringToNumber),
  defaultTermInMonthsForPromoAndOverrides: z
    .union([z.string(), z.number()])
    .transform(stringToNumber),
  accountNumberDigits: z
    .union([z.string(), z.number()])
    .transform(stringToNumber),
})

export type BankOptionsForm = z.input<typeof BankOptionsFormZodSchema>
export type BankOptions = z.input<typeof BankOptionsFormValidator>
