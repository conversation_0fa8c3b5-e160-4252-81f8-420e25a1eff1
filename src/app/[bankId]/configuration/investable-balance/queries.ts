import { revenueConnectClient } from '@/api/revenueConnectClient'
import { defineQueries, defineQuery } from '@/lib/state/defineQuery'
import { withMetrics } from '@/lib/middleware/withMetrics'

const getInvestableBalanceByEffectiveDate = defineQuery(
  revenueConnectClient,
  '/getInvestableBalanceDefinitions',
  (payload) => [payload.effectiveDate],
)

const getInvestableBalanceByCode = defineQuery(
  revenueConnectClient,
  '/getInvestableBalanceDefinitionByCode',
  (payload) => [payload.code],
)

const getInvestableBalance = defineQuery(
  revenueConnectClient,
  '/getInvestableBalanceDefinition',
  (payload) => [payload.code, payload.effectiveDate],
)

export const investableBalanceQueries = defineQueries(
  [
    getInvestableBalanceByEffectiveDate,
    getInvestableBalanceByCode,
    getInvestableBalance,
  ],
  withMetrics(),
)
