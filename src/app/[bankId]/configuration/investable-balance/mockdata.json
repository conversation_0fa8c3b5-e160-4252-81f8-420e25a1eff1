{"/createInvestableBalanceDefinition": {"payloads": [{"effectiveDate": "2025-01-01", "name": "Average positive collected less reserves", "code": "90001", "baseBalanceType": "AVERAGE_COLLECTED", "subtractFederalReserveRequirement": true, "subtractCompensatingBalance": false, "subtractInterestPaidMonthToDate": true, "subtractOtherBalanceLabel": "Test Subtract balance label", "addOtherBalanceLabel": null}, {"effectiveDate": "2025-01-01", "name": "Test Investable Balance 1", "code": "90002", "baseBalanceType": "END_OF_MONTH_LEDGER", "subtractFederalReserveRequirement": false, "subtractCompensatingBalance": true, "subtractInterestPaidMonthToDate": false, "subtractOtherBalanceLabel": null, "addOtherBalanceLabel": "Test Add balance label"}, {"effectiveDate": "2025-01-01", "name": "Test Investable Balance 1", "code": "90003", "baseBalanceType": "END_OF_MONTH_LEDGER", "subtractFederalReserveRequirement": true, "subtractCompensatingBalance": true, "subtractInterestPaidMonthToDate": false, "subtractOtherBalanceLabel": null, "addOtherBalanceLabel": null}]}}