import { InvestableBalanceMutation } from './mutations'
import { Tag } from '@/lib/unions/Union'
import { withNotifications } from '@/components/Notifications'

export const messages: { [T in Tag<InvestableBalanceMutation>]: string } = {
  '/createInvestableBalanceDefinition':
    'Investable Balance successfully created.',
  '/updateInvestableBalanceDefinition':
    'Investable Balance successfully updated.',
}

export const notifications = withNotifications(messages)
