'use client'

import { useMutation } from '@tanstack/react-query'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/Button'

import { formatToMonthYearFromDate, formatToServerString } from '@/lib/date'
import { unwrap } from '@/lib/unions/unwrap'
import { routeTo, useRoute } from '../../../routing'

import { investableBalanceMutation } from '../../mutations'
import InvestableBalanceForm from '../../_components/InvestableBalanceForm'

import {
  InvestableBalanceFormTypes,
  InvestableBalanceApiTypes,
  InvestableBalanceApiSchema,
} from '../../_components/InvestableBalanceFormTypes'

export default function AddInvestableBalance({}) {
  const router = useRouter()
  const route = unwrap(
    useRoute(),
    '/configuration/investable-balance/[effectiveDate]/add',
  )!

  const addInvestableBalance = useMutation(
    investableBalanceMutation('/createInvestableBalanceDefinition', {
      onSuccess: (_, request) => {
        router.push(
          routeTo(
            '/configuration/investable-balance/[effectiveDate]/view/[investableBalanceCode]',
            {
              ...route.params,
              investableBalanceCode: request.code!,
              effectiveDate: formatToMonthYearFromDate(request.effectiveDate!),
            },
          ),
        )
      },
    }),
  )

  const defaultValues: InvestableBalanceFormTypes = {
    code: '',
    name: '',
    effectiveDate: formatToMonthYearFromDate(new Date()),
    baseBalanceType: 'AVERAGE_COLLECTED',
    subtractFederalReserveRequirement: false,
    subtractCompensatingBalance: false,
    subtractInterestPaidMonthToDate: false,

    otherBalanceCalculation: false,
    otherBalanceMethod: null,
    otherBalanceLabel: '',
  }

  const serialize = (formState: InvestableBalanceFormTypes) => {
    const hasOtherBalanceCalculation = formState.otherBalanceCalculation
    const { otherBalanceMethod } = formState

    const otherBalanceKeys =
      otherBalanceMethod === 'ADD' ?
        {
          addOtherBalanceLabel: formState.otherBalanceLabel,
          subtractOtherBalanceLabel: null,
        }
      : {
          addOtherBalanceLabel: null,
          subtractOtherBalanceLabel: formState.otherBalanceLabel,
        }

    const otherKeys =
      hasOtherBalanceCalculation ? otherBalanceKeys : (
        {
          addOtherBalanceLabel: null,
          subtractOtherBalanceLabel: null,
        }
      )

    const payload: InvestableBalanceApiTypes = InvestableBalanceApiSchema.parse(
      {
        code: formState.code,
        name: formState.name,
        effectiveDate: formatToServerString(formState.effectiveDate),

        baseBalanceType: formState.baseBalanceType!,
        subtractCompensatingBalance: formState.subtractCompensatingBalance,
        subtractFederalReserveRequirement:
          formState.subtractFederalReserveRequirement,
        subtractInterestPaidMonthToDate:
          formState.subtractInterestPaidMonthToDate,

        ...otherKeys,
      },
    )
    return payload
  }

  return (
    <div className='w-full min-w-full overflow-auto pt-12'>
      <div className='text-md ml-8 font-medium'>Schedule Information</div>
      <InvestableBalanceForm
        defaultValues={defaultValues}
        onSubmit={(formState) => {
          const payload = serialize(formState)
          addInvestableBalance.mutate(payload)
        }}
      >
        <Button className='btn w-60' onClick={() => router.back()}>
          Cancel
        </Button>

        <Button type='submit' className='btn-primary w-60'>
          Save
        </Button>
      </InvestableBalanceForm>
    </div>
  )
}
