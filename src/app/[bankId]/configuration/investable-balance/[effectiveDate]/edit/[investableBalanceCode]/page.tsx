'use client'

import { useRouter } from 'next/navigation'
import { But<PERSON> } from '@/components/Button'
import { useMutation, useQuery } from '@tanstack/react-query'

import { formatToMonthYearFromDate, formatToServerString } from '@/lib/date'
import { unwrap } from '@/lib/unions/unwrap'
import { routeTo, useRoute } from '@/app/[bankId]/configuration/routing'

import { investableBalanceMutation } from '../../../mutations'
import { investableBalanceQueries } from '../../../queries'
import InvestableBalanceForm from '../../../_components/InvestableBalanceForm'

import {
  InvestableBalanceApiSchema,
  InvestableBalanceApiTypes,
  InvestableBalanceFormTypes,
  InvestableBalanceFormSchemaPartial,
} from '../../../_components/InvestableBalanceFormTypes'

export default function EditInvestableBalance({}) {
  const router = useRouter()
  const route = unwrap(
    useRoute(),
    '/configuration/investable-balance/[effectiveDate]/edit/[investableBalanceCode]',
  )!
  const { effectiveDate, investableBalanceCode } = route.params

  const payload = {
    code: investableBalanceCode,
    effectiveDate: formatToServerString(effectiveDate),
  }

  const { data, status, error } = useQuery(
    investableBalanceQueries('/getInvestableBalanceDefinition', payload),
  )

  const updateInvestableBalance = useMutation(
    investableBalanceMutation('/updateInvestableBalanceDefinition', {
      onSuccess: (_, request) => {
        router.push(
          routeTo(
            '/configuration/investable-balance/[effectiveDate]/view/[investableBalanceCode]',
            {
              ...route.params,
              investableBalanceCode: request.code!,
              effectiveDate: formatToMonthYearFromDate(request.effectiveDate!),
            },
          ),
        )
      },
    }),
  )
  if (status === 'pending') return 'Loading...'
  if (!data || error) {
    return <div>404 Not Found</div>
  }

  const investableBalance = InvestableBalanceFormSchemaPartial.parse(data)

  const { subtractOtherBalanceLabel, addOtherBalanceLabel } = investableBalance
  const label = subtractOtherBalanceLabel || addOtherBalanceLabel

  const defaultValues: InvestableBalanceFormTypes = {
    code: investableBalance.code,
    name: investableBalance.name,
    effectiveDate: formatToMonthYearFromDate(investableBalance.effectiveDate),
    baseBalanceType: investableBalance.baseBalanceType,
    subtractCompensatingBalance: investableBalance.subtractCompensatingBalance,
    subtractFederalReserveRequirement:
      investableBalance.subtractFederalReserveRequirement,
    subtractInterestPaidMonthToDate:
      investableBalance.subtractInterestPaidMonthToDate,

    otherBalanceCalculation: !!label,
    otherBalanceMethod: subtractOtherBalanceLabel ? 'SUBTRACT' : 'ADD',
    otherBalanceLabel: label,
  }

  const serialize = (formState: InvestableBalanceFormTypes) => {
    const hasOtherBalanceCalculation = formState.otherBalanceCalculation
    const { otherBalanceMethod } = formState

    const otherBalanceKeys =
      otherBalanceMethod === 'ADD' ?
        {
          addOtherBalanceLabel: formState.otherBalanceLabel,
          subtractOtherBalanceLabel: null,
        }
      : {
          addOtherBalanceLabel: null,
          subtractOtherBalanceLabel: formState.otherBalanceLabel,
        }

    const otherKeys =
      hasOtherBalanceCalculation ? otherBalanceKeys : (
        {
          addOtherBalanceLabel: null,
          subtractOtherBalanceLabel: null,
        }
      )

    const payload: InvestableBalanceApiTypes = InvestableBalanceApiSchema.parse(
      {
        code: formState.code,
        name: formState.name,
        effectiveDate: formatToServerString(formState.effectiveDate),

        baseBalanceType: formState.baseBalanceType!,
        subtractCompensatingBalance: formState.subtractCompensatingBalance,
        subtractFederalReserveRequirement:
          formState.subtractFederalReserveRequirement,
        subtractInterestPaidMonthToDate:
          formState.subtractInterestPaidMonthToDate,

        ...otherKeys,
      },
    )
    return payload
  }

  return (
    <div className='w-full min-w-full overflow-auto pt-12'>
      <div className='text-md ml-8 font-medium'>Schedule Information</div>
      <InvestableBalanceForm
        defaultValues={defaultValues}
        onSubmit={(formState) => {
          const payload = serialize(formState)
          updateInvestableBalance.mutate(payload)
        }}
        isEditMode
      >
        <Button className='btn w-60' onClick={() => router.back()}>
          Cancel
        </Button>

        <Button type='submit' className='btn-primary w-60'>
          Save
        </Button>
      </InvestableBalanceForm>
    </div>
  )
}
