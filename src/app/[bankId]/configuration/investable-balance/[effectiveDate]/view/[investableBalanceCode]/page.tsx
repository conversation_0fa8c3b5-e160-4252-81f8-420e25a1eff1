'use client'

import { useQuery } from '@tanstack/react-query'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/Button'
import { ChevronRightIcon } from '@heroicons/react/24/outline'
import Link from 'next/link'

import {
  DetailsSection,
  DetailsSectionItem,
  DetailsSectionItemsRow,
  DetailsSectionTitle,
} from '@/components/DetailsSection'

import { VersionTimeline } from '@/app/[bankId]/configuration/_components/VersionTimeline'

import { unwrap } from '@/lib/unions/unwrap'
import { routeTo, useRoute } from '@/app/[bankId]/configuration/routing'

import { investableBalanceQueries } from '../../../queries'
import { apiToFormSchemas } from '@/api/apiToFormSchema'
import { baseBalanceTypeLabel } from '../../../_components/InvestableBalanceFormTypes'
import { formatToMonthYearFromDate, formatToServerString } from '@/lib/date'

export default function ViewInvestableBalance({}) {
  const router = useRouter()
  const route = unwrap(
    useRoute(),
    '/configuration/investable-balance/[effectiveDate]/view/[investableBalanceCode]',
  )!
  const { effectiveDate, investableBalanceCode } = route.params

  const { data, status, error } = useQuery(
    investableBalanceQueries('/getInvestableBalanceDefinitionByCode', {
      code: investableBalanceCode,
    }),
  )
  if (status === 'pending') return 'Loading...'
  if (!data || error) {
    return <div>404 Not Found</div>
  }

  const { investableBalanceDefinition: InvestableBalanceType } =
    apiToFormSchemas

  const investableBalances = data
    .map((item) => InvestableBalanceType.parse(item))
    .sort(
      (a, b) =>
        new Date(b.effectiveDate).getDate() -
        new Date(a.effectiveDate).getDate(),
    )

  const investableBalance =
    investableBalances.find((item) => {
      return item.effectiveDate === formatToServerString(effectiveDate)
    }) || investableBalances[0]

  const onEditClick = () => {
    router.push(
      routeTo(
        '/configuration/investable-balance/[effectiveDate]/edit/[investableBalanceCode]',
        {
          ...route.params,
          effectiveDate: formatToMonthYearFromDate(
            investableBalance.effectiveDate,
          ),
          investableBalanceCode: investableBalance.code,
        },
      ),
    )
  }

  const linkFormatter = (
    effectiveDate: string,
    investableBalanceCode: string,
  ) => {
    return routeTo(
      '/configuration/investable-balance/[effectiveDate]/view/[investableBalanceCode]',
      { ...route.params, effectiveDate, investableBalanceCode },
    )
  }

  const renderOtherLabels = () => {
    const label =
      investableBalance.subtractOtherBalanceLabel ||
      investableBalance.addOtherBalanceLabel

    if (!label) {
      return
    }

    return (
      <DetailsSectionItemsRow>
        <DetailsSectionItem
          label={'Other balance'}
          info={
            investableBalance.subtractOtherBalanceLabel ? 'Subtract' : 'Add'
          }
        />
        <DetailsSectionItem label={'Statement label'} info={label} />
      </DetailsSectionItemsRow>
    )
  }

  return (
    <div className='w-full overflow-hidden overflow-y-scroll px-8 py-10'>
      <div className='flex items-center justify-between'>
        <div className='flex items-center gap-2'>
          <Link
            href={routeTo(
              '/configuration/investable-balance/[effectiveDate]',
              route.params,
            )}
            className='font-bold'
          >
            ...
          </Link>
          <ChevronRightIcon className='h-4 w-4 text-zinc-400' />
          <div className='text-md text-zinc-500'>
            <Link
              href={routeTo(
                '/configuration/investable-balance/[effectiveDate]',
                route.params,
              )}
              className='font-bold'
            >
              Investable balances
            </Link>
          </div>
          <ChevronRightIcon className='h-4 w-4 text-zinc-400' />
          <div className='text-lg'>{investableBalance.name}</div>
          <div className='text-sm text-zinc-500'>{investableBalance.code}</div>
        </div>
        <Button className='btn-primary text-white' onClick={onEditClick}>
          Edit
        </Button>
      </div>
      <div className='mt-8 flex gap-4'>
        <div className='flex grow flex-col'>
          <DetailsSection>
            <DetailsSectionTitle>Schedule information</DetailsSectionTitle>
            <DetailsSectionItemsRow>
              <DetailsSectionItem
                label={'Effective date *'}
                info={investableBalance.effectiveDate}
              />
            </DetailsSectionItemsRow>
            <DetailsSectionItemsRow>
              <DetailsSectionItem
                label={'Name *'}
                info={investableBalance.name ?? ''}
              />
              <DetailsSectionItem
                label={'Code *'}
                info={investableBalance.code}
              />
            </DetailsSectionItemsRow>
          </DetailsSection>

          <DetailsSection className='mt-3'>
            <DetailsSectionTitle>
              Investable balance calculation
            </DetailsSectionTitle>
            <div className='text-zinc-400'>
              Configure how this investable balance is calculated and appears on
              statements.
            </div>
            <DetailsSectionItemsRow className='mt-6'>
              <DetailsSectionItem
                label={'Base balance *'}
                info={
                  baseBalanceTypeLabel(investableBalance.baseBalanceType) || ''
                }
              />
            </DetailsSectionItemsRow>
            <DetailsSectionItemsRow className='mt-6'>
              <DetailsSectionItem
                label={'Subtract compensating balance'}
                info={
                  investableBalance.subtractFederalReserveRequirement ? 'Yes'
                  : 'No'
                }
              />
            </DetailsSectionItemsRow>
            <DetailsSectionItemsRow className='mt-6'>
              <DetailsSectionItem
                label={'Subtract compensating balance'}
                info={
                  investableBalance.subtractCompensatingBalance ? 'Yes' : 'No'
                }
              />
            </DetailsSectionItemsRow>
            <DetailsSectionItemsRow className='mt-6'>
              <DetailsSectionItem
                label={'Subtract interest paid month-to-date'}
                info={
                  investableBalance.subtractInterestPaidMonthToDate ? 'Yes' : (
                    'No'
                  )
                }
              />
            </DetailsSectionItemsRow>
            {renderOtherLabels()}
          </DetailsSection>
        </div>
        <div className='flex max-w-72 flex-col rounded-lg border bg-white p-6'>
          <VersionTimeline
            versions={investableBalances}
            currentEffectiveDate={route.params.effectiveDate}
            linkFormatter={linkFormatter}
          />
        </div>
      </div>
    </div>
  )
}
