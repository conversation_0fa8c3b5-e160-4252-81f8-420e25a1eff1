import { withMetrics } from '@/lib/middleware/withMetrics'
import { defineMutations, defineMutation } from '@/lib/state/defineMutation'
import { revenueConnectClient } from '@/api/revenueConnectClient'
import { investableBalanceQueries } from './queries'
import { compose } from '@/lib/functional/compose'
import { notifications } from './notifications'

const createInvestableBalance = defineMutation(
  revenueConnectClient,
  '/createInvestableBalanceDefinition',
  {
    invalidate: (request) => [
      investableBalanceQueries('/getInvestableBalanceDefinitions', {
        effectiveDate: request.effectiveDate,
      }),
      investableBalanceQueries('/getInvestableBalanceDefinitionByCode', {
        code: request.code,
      }),
      investableBalanceQueries('/getInvestableBalanceDefinition', {
        effectiveDate: request.effectiveDate,
        code: request.code,
      }),
    ],
  },
)

const updateInvestableBalance = defineMutation(
  revenueConnectClient,
  '/updateInvestableBalanceDefinition',
  {
    invalidate: (request) => [
      investableBalanceQueries('/getInvestableBalanceDefinitions', {
        effectiveDate: request.effectiveDate,
      }),
      investableBalanceQueries('/getInvestableBalanceDefinitionByCode', {
        code: request.code,
      }),
      investableBalanceQueries('/getInvestableBalanceDefinition', {
        effectiveDate: request.effectiveDate,
        code: request.code,
      }),
    ],
  },
)

export type InvestableBalanceMutation =
  | typeof createInvestableBalance
  | typeof updateInvestableBalance

export const investableBalanceMutation = defineMutations(
  [createInvestableBalance, updateInvestableBalance],
  compose(notifications, withMetrics()),
)
