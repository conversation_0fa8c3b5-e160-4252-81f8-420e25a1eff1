'use client'
import { useField, useForm } from '@tanstack/react-form'

import {
  baseBalanceTypeLabel,
  InvestableBalanceApiSchema,
  InvestableBalanceFormTypes,
} from './InvestableBalanceFormTypes'

import { useFormTextInput } from '@/components/Form/useFormTextInput'
import { useFormMonthPicker } from '@/components/Form/useFormMonthPicker'
import { useFormSelect } from '@/components/Form/useFormSelect'
import { Checkbox } from '@/components/Checkbox'

export interface InvestableBalanceFormProps {
  defaultValues: InvestableBalanceFormTypes
  onSubmit: (state: InvestableBalanceFormTypes) => void
  isEditMode?: boolean
}

export default function InvestableBalanceForm({
  defaultValues,
  onSubmit,
  children,
  isEditMode,
}: React.PropsWithChildren<InvestableBalanceFormProps>) {
  const form = useForm({
    defaultValues,
    onSubmit: ({ value }) => {
      onSubmit(value)
    },
  })

  const FormInput = useFormTextInput<InvestableBalanceFormTypes>({
    form,
  })
  const FormMonthPicker = useFormMonthPicker<InvestableBalanceFormTypes>({
    form,
  })
  const Select = useFormSelect<InvestableBalanceFormTypes>({ form })

  const {
    setValue: setNewFormValues,
    state: { value: otherBalanceChecked },
  } = useField<InvestableBalanceFormTypes, 'otherBalanceCalculation'>({
    form: form,
    name: 'otherBalanceCalculation',
  })

  return (
    <form
      className='flex max-h-full flex-auto flex-col'
      onSubmit={async (event) => {
        form.reset(form.state.values)
        event.preventDefault()
        event.stopPropagation()
        await form.handleSubmit()
      }}
    >
      <div className='flex flex-auto flex-col gap-2 overflow-y-auto px-8'>
        <div className='mt-10 flex w-full flex-col rounded-lg border bg-white p-6'>
          <div className='font-semibold'>Investable balance information</div>
          <div className='mt-3 flex gap-9'>
            <div className='flex flex-1 flex-col gap-2'>
              <FormMonthPicker name='effectiveDate' label='Effective date *' />
            </div>
            <div className='flex-1'></div>
          </div>
          <div className='mt-3 flex gap-9'>
            <FormInput
              name='name'
              label='Name'
              required
              validators={{
                onChange: InvestableBalanceApiSchema.shape.name,
              }}
            />
            <FormInput
              name='code'
              label='Code'
              required
              validators={{
                onChange: InvestableBalanceApiSchema.shape.code,
              }}
              placeholder='Max 5 digits'
              type='number'
              invalidChars={['e', 'E']}
              readonly={(field) =>
                field.value != null &&
                field.value.length > 0 &&
                field.meta.isPristine &&
                !!isEditMode
              }
            />
          </div>
          <div className='mb-6 mt-3 flex w-full flex-col rounded-lg border bg-white p-6'>
            <div className='font-semibold'>Investable balance calculation</div>

            <Select
              label='Base balance'
              name='baseBalanceType'
              options={InvestableBalanceApiSchema.shape.baseBalanceType.options}
              renderSelected={baseBalanceTypeLabel}
              renderOption={baseBalanceTypeLabel}
              validators={{
                onChange: InvestableBalanceApiSchema.shape.baseBalanceType,
              }}
              required
            />
            <div className='mb-3 mt-3'>
              <form.Field name='subtractFederalReserveRequirement'>
                {({ state: { value }, handleChange }) => (
                  <Checkbox
                    label='Subtract reserve requirement'
                    onChange={handleChange}
                    checked={value}
                  />
                )}
              </form.Field>
            </div>
            <div className='mb-3 mt-3'>
              <form.Field name='subtractCompensatingBalance'>
                {({ state: { value }, handleChange }) => (
                  <Checkbox
                    label='Subtract compensating balance'
                    onChange={handleChange}
                    checked={value}
                  />
                )}
              </form.Field>
            </div>
            <div className='mb-3 mt-3'>
              <form.Field name='subtractInterestPaidMonthToDate'>
                {({ state: { value }, handleChange }) => (
                  <Checkbox
                    label='Subtract interest paid month-to-date'
                    onChange={handleChange}
                    checked={value}
                  />
                )}
              </form.Field>
            </div>
            <div className='mb-3 mt-3'>
              <form.Field name='otherBalanceCalculation'>
                {({ state: { value }, handleChange }) => (
                  <Checkbox
                    label='Other balance'
                    onChange={handleChange}
                    checked={value}
                  />
                )}
              </form.Field>
              <div className='ml-7 text-sm text-zinc-400'>
                Option to add or subtract an other balance
              </div>
            </div>

            {otherBalanceChecked && (
              <div className='mt-3 flex gap-9'>
                <Select
                  label='Other balance'
                  name='otherBalanceMethod'
                  options={['ADD', 'SUBTRACT']}
                  renderSelected={(value) =>
                    value === 'ADD' ? 'Add' : 'Subtract'
                  }
                  renderOption={(value) =>
                    value === 'ADD' ? 'Add' : 'Subtract'
                  }
                  validators={{
                    onChangeListenTo: ['otherBalanceCalculation'],
                    onChange: ({ value, fieldApi }) => {
                      if (
                        fieldApi.form.getFieldValue(
                          'otherBalanceCalculation',
                        ) &&
                        !value
                      ) {
                        return 'Field is Investable'
                      }
                      return undefined
                    },
                  }}
                  required
                />
                <FormInput
                  label='Statement Label'
                  name='otherBalanceLabel'
                  required
                  validators={{
                    onChangeListenTo: ['otherBalanceCalculation'],
                    onChange: ({ value, fieldApi }) => {
                      if (
                        fieldApi.form.getFieldValue(
                          'otherBalanceCalculation',
                        ) &&
                        !value
                      ) {
                        return 'Field is Required'
                      }
                      return undefined
                    },
                  }}
                />
              </div>
            )}
          </div>
        </div>
      </div>
      <div className='flex justify-end gap-4 border-t bg-white px-12 py-10'>
        {children}
      </div>
    </form>
  )
}
