import { z } from 'zod'

import { apiToFormSchemas } from '@/api/apiToFormSchema'
import { formToApiSchemas } from '@/api/formToApiSchema'
import { variant } from '@/lib/unions/Union'
import { match } from '@/lib/unions/match'
import { configCode } from '@/api/zodSchemas'

export const {
  investableBalanceDefinition: InvestableBalanceFormSchemaPartial,
} = apiToFormSchemas
const { investableBalanceDefinition: InvestableBalanceApiSchemaPartial } =
  formToApiSchemas

export const InvestableBalanceFormSchema =
  InvestableBalanceFormSchemaPartial.extend({
    otherBalanceCalculation: z.boolean(),
    otherBalanceMethod: z.enum(['ADD', 'SUBTRACT']).nullable(),
    otherBalanceLabel: z.string().nullable(),
  }).omit({
    addOtherBalanceLabel: true,
    subtractOtherBalanceLabel: true,
  })

export const InvestableBalanceApiSchema =
  InvestableBalanceApiSchemaPartial.extend({
    code: configCode,
  })

export type InvestableBalanceFormTypes = z.infer<
  typeof InvestableBalanceFormSchema
>
export type InvestableBalanceApiTypes = z.infer<
  typeof InvestableBalanceApiSchema
>

export const baseBalanceTypeLabel = (
  value: z.infer<typeof InvestableBalanceFormSchema.shape.baseBalanceType>,
) => {
  if (value === null) {
    return value
  }
  return match(variant(value), {
    AVERAGE_COLLECTED: () => 'Average Collected',
    AVERAGE_LEDGER: () => 'Average Ledger',
    AVERAGE_NEGATIVE_COLLECTED: () => 'Average Negative Collected',
    AVERAGE_NEGATIVE_LEDGER: () => 'Average Negative Ledger',
    AVERAGE_POSITIVE_COLLECTED: () => 'Average Positive Collected',
    AVERAGE_POSITIVE_LEDGER: () => 'Average Positive Ledger',
    AVERAGE_UNCOLLECTED_FUNDS: () => 'Average Uncollected Funds',
    COMPENSATING_BALANCE: () => 'Compensating Balance',
    END_OF_MONTH_LEDGER: () => 'End Of Month Ledger',
    INVESTABLE_BALANCE: () => 'Investible Balance',
    AVERAGE_FLOAT: () => 'Average Float',
    AVERAGE_CLEARINGHOUSE_FLOAT: () => 'Average Clearinghouse Float',
    REQUIRED_BALANCE: () => 'Required balance',
  })
}
