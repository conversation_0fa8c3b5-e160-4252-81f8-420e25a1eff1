import { apiToFormSchemas } from '@/api/apiToFormSchema'
import { z } from 'zod'

const { accountType, branch, officer } = apiToFormSchemas

// account types
export type AccountType = z.infer<typeof accountType>

// Schedule definitions
export interface ScheduleDefinition {
  name: string
  code: string
  effectiveDate: string
}

// User Fields
export interface UserField {
  name: string
  code: string
  fieldType: UserFieldTypes
}

export enum UserFieldTypes {
  'freeform' = 'Freeform',
  'drop-down' = 'Drop-down',
  'boolean' = 'Boolean',
}
export const userFieldOptions = Object.values(UserFieldTypes)

// reserve requirements
export enum BaseBalanceOptions {
  averageCollected = 'Average collected',
  averagePositiveCollected = 'Average positive collected',
}

export const baseBalanceOptions = Object.values(BaseBalanceOptions)

export enum CalculationMethodOptions {
  indexed = 'Indexed',
  percentage = 'Percentage',
}

export const calculationMethodOptions = Object.values(CalculationMethodOptions)

// index rates
export interface IndexRate {
  name: string
  code: string
  effectiveDate: string
  rate: number
}

// analysis result options
export interface AnalysisResultOptions {
  name: string
  code: string
  effectiveDate: string
}

enum ChargeTypes {
  DIRECT_DEBIT = 'DIRECT_DEBIT',
  WAIVE = 'WAIVE',
}
export const chargeTypeOptions: Record<ChargeTypes, string> = {
  [ChargeTypes.DIRECT_DEBIT]: 'Direct debit',
  [ChargeTypes.WAIVE]: 'Waive',
}
export const analysisChargeTypeOptions = Object.values(chargeTypeOptions)

enum SettlementOverrideType {
  SAME_DATE_EACH_MONTH = 'SAME_DATE_EACH_MONTH',
  SPECIFIC_DAYS = 'SPECIFIC_DAYS',
}
export const SettlementOverrideTypeOptions: Record<
  SettlementOverrideType,
  string
> = {
  [SettlementOverrideType.SAME_DATE_EACH_MONTH]: 'Same date each month',
  [SettlementOverrideType.SPECIFIC_DAYS]:
    'Specific number of days after preliminary analysis',
}
export const SettlementOverrideOptions = Object.values(
  SettlementOverrideTypeOptions,
)

// Earnings Credit
export interface EarningCredit {
  description: string
  code: string
  effectiveDate: string
}

export enum TieringMethodType {
  'partitioned' = 'Partitioned',
  'threshold' = 'Threshold',
}

export const TieringMethodTypeOptions = Object.values(TieringMethodType)

export enum RateSourceType {
  INDEX_RATE = 'INDEX_RATE',
  MANUAL_ENTRY = 'MANUAL_ENTRY',
}
export const RateSourceTypeOptions: Record<RateSourceType, string> = {
  [RateSourceType.INDEX_RATE]: 'Use index rate',
  [RateSourceType.MANUAL_ENTRY]: 'Manually Enter rate',
}
export const RateSourceOptions = Object.values(RateSourceTypeOptions)

// statement formats
export interface StatementFormat {
  name: string
  code: string
  effectiveDate: string
}
enum ServiceCategoryLevelType {
  ONE = 'ONE',
  TWO = 'TWO',
  THREE = 'THREE',
}
export const ServiceCategoryLevelTypeOptions: Record<
  ServiceCategoryLevelType,
  number
> = {
  [ServiceCategoryLevelType.ONE]: 1,
  [ServiceCategoryLevelType.TWO]: 2,
  [ServiceCategoryLevelType.THREE]: 3,
}
export const ServiceCategoryLevelOptions = Object.values(
  ServiceCategoryLevelTypeOptions,
)

// officer
export type Officer = z.infer<typeof officer>

// branch
export type Branch = z.infer<typeof branch>
