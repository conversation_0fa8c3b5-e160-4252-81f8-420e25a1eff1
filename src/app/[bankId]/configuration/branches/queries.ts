import { revenueConnectClient } from '@/api/revenueConnectClient'
import { defineQueries, defineQuery } from '@/lib/state/defineQuery'
import { withMetrics } from '@/lib/middleware/withMetrics'

const getBranchesByEffectiveDate = defineQuery(
  revenueConnectClient,
  '/getBranches',
  (payload) => [payload.effectiveDate],
)

const getBranchByCode = defineQuery(
  revenueConnectClient,
  '/getBranch',
  (payload) => [payload.code, payload.effectiveDate],
)

const getBranchTimeline = defineQuery(
  revenueConnectClient,
  '/getBranchTimeline',
  (payload) => [payload.code],
)

export const branchQueries = defineQueries(
  [getBranchesByEffectiveDate, getBranchByCode, getBranchTimeline],
  withMetrics(),
)
