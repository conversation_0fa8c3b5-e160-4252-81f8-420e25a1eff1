'use client'
import { useState } from 'react'
import { ColumnDef, ColumnFiltersState } from '@tanstack/react-table'
import { ChevronRightIcon } from '@heroicons/react/24/outline'
import { useRouter } from 'next/navigation'
import { useQuery } from '@tanstack/react-query'
import { z } from 'zod'

import { DateFilter } from '@/components/Filter/DateFilter'
import { SearchBar } from '@/components/SearchBar'
import { SortedTable } from '@/components/Table/SortedTable'

import { parseServerFormat, toServerFormat } from '@/lib/date'
import { unwrap } from '@/lib/unions/unwrap'
import { routeTo, useRoute } from '../../routing'
import { branchQueries } from '../queries'
import { apiToFormSchemas } from '@/api/apiToFormSchema'

export default function AllBranches() {
  const [searchText, setSearchText] = useState('')
  const [numVisibleBranches, setNumVisibleBranches] = useState(0)
  const columnFilters: ColumnFiltersState = [
    { id: 'branchName', value: searchText },
  ]

  const router = useRouter()
  const route = unwrap(useRoute(), '/configuration/branches/[effectiveDate]')!

  const { data, status, error } = useQuery(
    branchQueries('/getBranches', {
      effectiveDate: route.params.effectiveDate,
    }),
  )

  if (status === 'pending') return 'Loading...'
  if (!data || error) {
    return <div>404 Not Found</div>
  }

  const { branch: BranchType } = apiToFormSchemas
  const branches = data?.map((item) => {
    return BranchType.parse(item)
  })

  const columns: ColumnDef<z.infer<typeof BranchType>>[] = [
    {
      header: 'Name',
      accessorKey: 'branchName',
    },
    {
      header: 'Code',
      accessorKey: 'code',
    },
  ]

  return (
    <div className='flex min-h-0 flex-1 flex-col py-6 pl-12 pr-3'>
      <div className='flex items-center text-zinc-500'>
        Configurations
        <ChevronRightIcon className='h-4 w-4 text-zinc-400' />
        <span className='text-lg font-semibold text-black'>Branches</span>
      </div>
      <div className='mt-12 flex min-h-0 w-full flex-auto flex-grow flex-col rounded-lg border bg-white p-6'>
        <div className='flex w-full items-center justify-between'>
          <div className=''>
            <div className='text-lg font-semibold'>Branches</div>
            <div className='text-zinc-400'>
              View details for individual branches of your financial
              institution.
            </div>
          </div>
        </div>
        <div className='mt-8 flex min-h-0 flex-auto flex-col'>
          <div className='flex items-center'>
            <DateFilter
              className='border-grey bg-grey-500 border hover:bg-indigo-200 focus-visible:outline-indigo-300'
              title='Effective date'
              label='View data effective on'
              initialDate={parseServerFormat(route.params.effectiveDate)}
              onDateChange={(date) => {
                router.push(
                  routeTo('/configuration/branches/[effectiveDate]', {
                    ...route.params,
                    effectiveDate: toServerFormat(date),
                  }),
                )
              }}
            />
            <div className='flex flex-auto items-center justify-end gap-4'>
              <SearchBar
                className='w-80'
                defaultLabel='Search branches'
                value={searchText}
                onValueChange={setSearchText}
              />
              <p className='text-sm text-zinc-400'>
                {numVisibleBranches} branch
                {numVisibleBranches > 1 && 'es'}
              </p>
            </div>
          </div>
          <div className='mt-4 flex min-h-0 flex-col'>
            <SortedTable
              data={branches}
              columns={columns}
              columnFilters={columnFilters}
              onVisibleRowsChanged={(rows) =>
                setNumVisibleBranches(rows.length)
              }
              initialSortingState={[{ desc: false, id: 'branchName' }]}
              handleRowDoubleClick={({ original: { code } }) =>
                router.push(
                  routeTo(
                    '/configuration/branches/[effectiveDate]/view/[branchCode]',
                    {
                      ...route.params,
                      effectiveDate: route.params.effectiveDate,
                      branchCode: code,
                    },
                  ),
                )
              }
            />
          </div>
        </div>
      </div>
    </div>
  )
}
