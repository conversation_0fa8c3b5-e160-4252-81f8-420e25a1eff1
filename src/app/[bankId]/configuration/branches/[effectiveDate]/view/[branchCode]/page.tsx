'use client'

import Link from 'next/link'
import { useQuery } from '@tanstack/react-query'
import { ChevronRightIcon } from '@heroicons/react/24/outline'

import {
  DetailsSection,
  DetailsSectionTitle,
  DetailsSectionItemsRow,
  DetailsSectionItem,
} from '@/components/DetailsSection'
import { VersionTimeline } from '@/app/[bankId]/configuration/_components/VersionTimeline'

import { unwrap } from '@/lib/unions/unwrap'
import { routeTo, useRoute } from '@/app/[bankId]/configuration/routing'
import { branchQueries } from '../../../queries'
import { useBankOptions } from '@/app/[bankId]/configuration/bank-options/_hooks/useBankOptions'

export default function ViewBranch() {
  const route = unwrap(
    useRoute(),
    '/configuration/branches/[effectiveDate]/view/[branchCode]',
  )!

  const {
    data: branch,
    status: branchStatus,
    error,
  } = useQuery(
    branchQueries('/getBranch', {
      code: route.params.branchCode,
      effectiveDate: route.params.effectiveDate,
    }),
  )

  const {
    data: branchTimeline,
    status: branchTimelineStatus,
    error: timelineError,
  } = useQuery(
    branchQueries('/getBranchTimeline', {
      code: route.params.branchCode,
    }),
  )

  const bankId = branch?.bankNumber
  const { bankOptions } = useBankOptions({
    bankId,
    effectiveDate: route.params.effectiveDate,
  })

  if (branchStatus === 'pending' || branchTimelineStatus === 'pending')
    return 'Loading...'
  if (!branch || error || !branchTimeline || timelineError) {
    return <div>404 Not Found</div>
  }

  const bankOptionName = bankOptions?.name

  const linkFormatter = (effectiveDate: string, branchCode: string) => {
    return routeTo(
      '/configuration/branches/[effectiveDate]/view/[branchCode]',
      {
        ...route.params,
        effectiveDate,
        branchCode,
      },
    )
  }

  return (
    <div className='w-full overflow-hidden overflow-y-scroll px-8 py-10'>
      <div className='flex items-center justify-between'>
        <div className='flex items-center gap-2'>
          <Link
            href={routeTo(
              '/configuration/branches/[effectiveDate]',
              route.params,
            )}
            className='font-bold'
          >
            ...
          </Link>
          <ChevronRightIcon className='h-4 w-4 text-zinc-400' />
          <div className='text-md text-zinc-500'>
            <Link
              href={routeTo(
                '/configuration/branches/[effectiveDate]',
                route.params,
              )}
              className='font-bold'
            >
              Branches
            </Link>
          </div>
          <ChevronRightIcon className='h-4 w-4 text-zinc-400' />
          <div className='text-lg'>{branch.branchName}</div>
          <div className='text-sm text-zinc-500'>{branch.code}</div>
        </div>
      </div>
      <div className='mt-8 flex gap-4'>
        <div className='flex grow flex-col'>
          <DetailsSection>
            <DetailsSectionTitle>Branch information</DetailsSectionTitle>
            <DetailsSectionItemsRow>
              <DetailsSectionItem
                label={'Effective date *'}
                info={branch.effectiveDate}
              />
            </DetailsSectionItemsRow>
            <DetailsSectionItemsRow>
              <DetailsSectionItem label={'Name *'} info={branch.branchName} />
              <DetailsSectionItem label={'Code *'} info={branch.code} />
            </DetailsSectionItemsRow>
            <DetailsSectionItemsRow>
              <DetailsSectionItem
                label={'Bank'}
                info={`${branch.bankNumber} - ${bankOptionName}`}
              />
            </DetailsSectionItemsRow>
            <DetailsSectionItemsRow>
              <DetailsSectionItem
                label={'Branch address'}
                info={`${branch.addressLine1}\n${branch.addressLine2}\n${branch.addressLine3}`}
              />
              <DetailsSectionItem
                label={'ABA numbers'}
                info={`${branch.abaNumbers}`} // todo: map over aba numbers
              />
            </DetailsSectionItemsRow>
            <DetailsSectionItemsRow>
              <DetailsSectionItem
                label={'Phone'}
                info={`${branch.phoneNumber}`}
              />
            </DetailsSectionItemsRow>
          </DetailsSection>
        </div>
        <div className='flex max-w-72 flex-col rounded-lg border bg-white p-6'>
          <VersionTimeline
            versions={branchTimeline}
            currentEffectiveDate={route.params.effectiveDate}
            linkFormatter={linkFormatter}
            shouldParseToDateLevel
          />
        </div>
      </div>
    </div>
  )
}
