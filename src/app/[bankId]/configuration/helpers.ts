import { useMemo } from 'react'

// creates code to label mapping and a function to render the label from the code
export function useCodesAndRenderOption<T>(
  objects: T[],
  codeMapper: (obj: T) => string,
  labelMapper: (obj: T) => string,
): [string[], (code: string) => string] {
  return useMemo(() => {
    const codeMap: Record<string, string> = (objects ?? []).reduce(
      (previous, current) => ({
        ...previous,
        [codeMapper(current)]: [labelMapper(current)],
      }),
      {},
    )
    return [Object.keys(codeMap), (code: string) => codeMap[code]]
  }, [codeMapper, labelMapper, objects])
}

export function getNameAndCodeString<
  T extends { code?: string; name?: string; description?: string },
>(definitions: T[], searchCode: string | null): string {
  const matchingDefinition = definitions.find(
    (definition) => definition.code === searchCode,
  )
  if (matchingDefinition?.name) {
    return `${matchingDefinition.name} - ${matchingDefinition.code}`
  } else if (matchingDefinition?.description) {
    return `${matchingDefinition.description} - ${matchingDefinition.code}`
  } else {
    return ''
  }
}
