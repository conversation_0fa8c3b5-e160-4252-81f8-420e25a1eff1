'use client'

import { useRouter } from 'next/navigation'
import { useMutation, useQuery } from '@tanstack/react-query'

import { Button } from '@/components/Button'
import { unwrap } from '@/lib/unions/unwrap'
import { routeTo, useRoute } from '../../../routing'
import {
  formatToMonthYearFromDate,
  formatToServerString,
  getLastDayOfMonthString,
} from '@/lib/date'
import UpdateEarningsCreditForm from '../../_components/UpdateEarningsCreditForm'
import {
  EarningsCreditApiSchema,
  EarningsCreditFormStateType,
} from '../../_components/UpdateEarningsCreditFormTypes'
import { earningsCreditMutation } from '../../mutations'
import { indexRateQueries } from '../../../index-rates/queries'

export default function AddEarningsCredit({}) {
  const router = useRouter()
  const route = unwrap(
    useRoute(),
    '/configuration/earnings-credit/[effectiveDate]/add',
  )!

  const lastDayOfMonth = getLastDayOfMonthString(route.params.effectiveDate)
  const {
    data: indexRates,
    status,
    error: indexRateErrors,
  } = useQuery(
    indexRateQueries('/listIndexRatesByEffectiveDate', {
      effectiveDate: lastDayOfMonth,
    }),
  )

  const addStatementMessage = useMutation(
    earningsCreditMutation('/createEarningsCreditDefinition', {
      onSuccess: (_, request) => {
        router.push(
          routeTo(
            '/configuration/earnings-credit/[effectiveDate]/view/[earningsCreditCode]',
            {
              ...route.params,
              earningsCreditCode: request.code!,
              effectiveDate: formatToMonthYearFromDate(request.effectiveDate!),
            },
          ),
        )
      },
    }),
  )

  const defaultValues: EarningsCreditFormStateType = {
    earningsCreditInfo: {
      code: '',
      name: '',
      effectiveDate: formatToMonthYearFromDate(new Date()),
    },
    earningsCreditCalculation: {
      basedBalance: null,
      tieringMethod: null,
    },
    earningsCreditRate: {
      rateSource: null,
      indexRateOption: null,
      minimumRate: null,
      maximumRate: null,
      subPriceType: 'INDEXED',
    },
    pricingTiers: [],
  }
  if (status === 'pending') return 'Loading...'
  if (!indexRates || indexRateErrors) {
    return <div>404 Not Found</div>
  }

  const serializeForm = (formState: EarningsCreditFormStateType) => {
    const {
      earningsCreditInfo,
      earningsCreditCalculation,
      earningsCreditRate,
      pricingTiers,
    } = formState

    const tier = pricingTiers.map((item) => {
      const { rate, adjustment } =
        earningsCreditRate.rateSource === 'MANUAL' ?
          {
            rate: item.priceValue,
            adjustment: 0,
          }
        : {
            rate: 0,
            adjustment: item.indexAdjustment,
          }

      return {
        indexRate: rate?.toString(),
        indexAdjustmentRate: adjustment?.toString(),
        maxTierExclusive: item.tierMaxBalanceExclusive,
        minTierInclusive: item.tierMinBalanceInclusive,
      }
    })

    const parsed = EarningsCreditApiSchema.parse({
      code: earningsCreditInfo.code,
      description: earningsCreditInfo.name,
      effectiveDate: formatToServerString(earningsCreditInfo.effectiveDate),
      rateSource: earningsCreditRate.rateSource,
      tierMethod: earningsCreditCalculation.tieringMethod,
      baseBalanceType: earningsCreditCalculation.basedBalance,
      maxRateInclusive: earningsCreditRate.maximumRate,
      minRateInclusive: earningsCreditRate.minimumRate,
      balanceTiers: tier,
      indexRateCode:
        earningsCreditRate.rateSource === 'MANUAL' ?
          null
        : indexRates.find(
            (el) => el.code === earningsCreditRate.indexRateOption?.code,
          )?.code,
    })

    return parsed
  }

  return (
    <div className='w-full min-w-full overflow-auto pt-12'>
      <div className='text-md ml-8 font-medium'>Add earnings credit</div>

      <UpdateEarningsCreditForm
        defaultValues={defaultValues}
        indexRateOptions={indexRates}
        onSubmit={(formState) => {
          const payload = serializeForm(formState)
          addStatementMessage.mutate(payload)
        }}
      >
        <Button className='btn w-60' onClick={() => router.back()}>
          Cancel
        </Button>
        <Button type='submit' className='btn-primary w-60'>
          Save
        </Button>
      </UpdateEarningsCreditForm>
    </div>
  )
}
