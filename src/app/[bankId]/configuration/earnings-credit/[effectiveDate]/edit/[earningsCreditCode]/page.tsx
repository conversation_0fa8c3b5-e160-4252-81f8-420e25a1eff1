'use client'

import { useRouter } from 'next/navigation'
import { But<PERSON> } from '@/components/Button'

import UpdateEarningsCreditForm from '../../../_components/UpdateEarningsCreditForm'
import {
  EarningsCreditApiSchema,
  EarningsCreditFormSchema,
  EarningsCreditFormStateType,
} from '../../../_components/UpdateEarningsCreditFormTypes'
import { unwrap } from '@/lib/unions/unwrap'
import { routeTo, useRoute } from '@/app/[bankId]/configuration/routing'

import { useMutation, useQuery } from '@tanstack/react-query'
import { earningsCreditQueries } from '../../../queries'
import { earningsCreditMutation } from '../../../mutations'
import { ServicePriceTier } from '@/app/[bankId]/services/[effectiveDate]/(service)/UpdateServiceForm'
import {
  formatToServerString,
  getLastDayOfMonthString,
  formatToMonthYearFromDate,
} from '@/lib/date'
import { indexRateQueries } from '@/app/[bankId]/configuration/index-rates/queries'

export default function EditEarningsCredit({}) {
  const router = useRouter()
  const route = unwrap(
    useRoute(),
    '/configuration/earnings-credit/[effectiveDate]/edit/[earningsCreditCode]',
  )!

  const { effectiveDate: routeEffectiveDate, earningsCreditCode } = route.params
  const lastDayOfMonth = getLastDayOfMonthString(routeEffectiveDate)

  const payload = {
    code: earningsCreditCode,
  }

  const { data, status, error } = useQuery(
    earningsCreditQueries('/getEarningsCreditDefinitionsByCode', payload),
  )

  const {
    data: indexRates,
    status: indexRateStatus,
    error: indexRateErrors,
  } = useQuery(
    indexRateQueries('/listIndexRatesByEffectiveDate', {
      effectiveDate: lastDayOfMonth,
    }),
  )

  const updateEarningsCredit = useMutation(
    earningsCreditMutation('/updateEarningsCreditDefinition', {
      onSuccess: (_, request) => {
        router.push(
          routeTo(
            '/configuration/earnings-credit/[effectiveDate]/view/[earningsCreditCode]',
            {
              ...route.params,
              earningsCreditCode: request.code!,
              effectiveDate: formatToMonthYearFromDate(request.effectiveDate!),
            },
          ),
        )
      },
    }),
  )
  if (status === 'pending' || indexRateStatus === 'pending') return 'Loading...'
  if (!data || error || !indexRates || indexRateErrors) {
    return <div>404 Not Found</div>
  }

  const earningsCredits = data
    .map((item) => {
      const parsed = EarningsCreditFormSchema.parse(item)

      const { balanceTiers } = parsed

      const parsedTiers: ServicePriceTier[] = balanceTiers.map((tier) => {
        const { rate, adjustment } =
          item.rateSource === 'MANUAL' ?
            {
              rate: tier.indexRate,
              adjustment: 0,
            }
          : {
              rate: indexRates.find((el) => el.code === item.indexRateCode)
                ?.indexRate,
              adjustment: tier.indexAdjustmentRate,
            }
        return {
          priceValue: rate,
          tierMinVolumeInclusive: '0',
          tierMaxVolumeExclusive: '0',
          tierMinBalanceInclusive: tier.minTierInclusive,
          tierMaxBalanceExclusive: tier.maxTierExclusive,
          indexAdjustment: adjustment,
        } as ServicePriceTier
      })

      return {
        ...parsed,
        name: parsed.description,
        parsedTiers: parsedTiers,
      }
    })
    .sort(
      (a, b) =>
        new Date(b.effectiveDate).getDate() -
        new Date(a.effectiveDate).getDate(),
    )

  const earningsCredit =
    earningsCredits.find((item) => {
      return item.effectiveDate === formatToServerString(routeEffectiveDate)
    }) || earningsCredits[0]

  const defaultValues: EarningsCreditFormStateType = {
    earningsCreditInfo: {
      code: earningsCredit.code,
      name: earningsCredit.description,
      effectiveDate: earningsCredit.effectiveDate,
    },
    earningsCreditCalculation: {
      basedBalance: earningsCredit.baseBalanceType,
      tieringMethod: earningsCredit.tierMethod,
    },
    earningsCreditRate: {
      rateSource: earningsCredit.rateSource,
      indexRateOption:
        earningsCredit.rateSource === 'MANUAL' ?
          null
        : indexRates.find(
            (item) => item.code === earningsCredit.indexRateCode,
          ) || null,
      minimumRate: earningsCredit.minRateInclusive,
      maximumRate: earningsCredit.maxRateInclusive,
      subPriceType: 'INDEXED',
    },
    pricingTiers: earningsCredit.parsedTiers,
  }

  const serializeForm = (formState: EarningsCreditFormStateType) => {
    const {
      earningsCreditInfo,
      earningsCreditCalculation,
      earningsCreditRate,
      pricingTiers,
    } = formState

    const tiers = pricingTiers.map((item) => {
      const { rate, adjustment } =
        earningsCreditRate.rateSource === 'MANUAL' ?
          {
            rate: item.priceValue,
            adjustment: 0,
          }
        : {
            rate: 0,
            adjustment: item.indexAdjustment,
          }

      return {
        indexRate: rate?.toString(),
        indexAdjustmentRate: adjustment?.toString(),
        maxTierExclusive: item.tierMaxBalanceExclusive,
        minTierInclusive: item.tierMinBalanceInclusive,
      }
    })

    const parsed = EarningsCreditApiSchema.parse({
      code: earningsCreditInfo.code,
      description: earningsCreditInfo.name,
      effectiveDate: formatToServerString(earningsCreditInfo.effectiveDate),
      rateSource: earningsCreditRate.rateSource,
      tierMethod: earningsCreditCalculation.tieringMethod,
      baseBalanceType: earningsCreditCalculation.basedBalance,
      maxRateInclusive: earningsCreditRate.maximumRate,
      minRateInclusive: earningsCreditRate.minimumRate,
      balanceTiers: tiers,
      indexRateCode:
        earningsCreditRate.rateSource === 'MANUAL' ?
          null
        : indexRates.find(
            (el) => el.code === earningsCreditRate.indexRateOption?.code,
          )?.code,
    })

    return parsed
  }

  return (
    <div className='w-full min-w-full overflow-auto pt-12'>
      <div className='text-md ml-8 font-medium'>Edit earnings credit</div>

      <UpdateEarningsCreditForm
        defaultValues={defaultValues}
        indexRateOptions={indexRates}
        isEditMode
        onSubmit={(formState) => {
          const payload = serializeForm(formState)
          updateEarningsCredit.mutate(payload)
        }}
      >
        <Button className='btn w-60' onClick={() => router.back()}>
          Cancel
        </Button>
        <Button type='submit' className='btn-primary w-60'>
          Save
        </Button>
      </UpdateEarningsCreditForm>
    </div>
  )
}
