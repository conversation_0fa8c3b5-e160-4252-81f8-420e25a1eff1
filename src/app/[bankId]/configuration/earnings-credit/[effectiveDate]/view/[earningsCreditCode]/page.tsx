'use client'

import { useRouter } from 'next/navigation'
import { ColumnDef } from '@tanstack/react-table'
import { ChevronRightIcon } from '@heroicons/react/24/outline'
import Link from 'next/link'

import {
  DetailsSection,
  DetailsSectionItem,
  DetailsSectionItemsRow,
  DetailsSectionTitle,
} from '@/components/DetailsSection'

import { VersionTimeline } from '@/app/[bankId]/configuration/_components/VersionTimeline'

import { Button } from '@/components/Button'
import { unwrap } from '@/lib/unions/unwrap'
import { routeTo, useRoute } from '@/app/[bankId]/configuration/routing'
import { PricingTiersList } from '@/app/[bankId]/services/[effectiveDate]/(service)/view/_components/PricingTiers'
import { ServicePriceTier } from '@/app/[bankId]/services/[effectiveDate]/(service)/UpdateServiceForm'
import { useQuery } from '@tanstack/react-query'
import { earningsCreditQueries } from '../../../queries'
import {
  EarningsCreditFormSchema,
  baseBalanceTypeLabel,
  rateSourceTypeLabel,
  tierMethodTypeLabel,
} from '../../../_components/UpdateEarningsCreditFormTypes'
import { indexRateQueries } from '@/app/[bankId]/configuration/index-rates/queries'
import { formatToMonthYearFromDate, formatToServerString } from '@/lib/date'

export default function ViewEarningsCredit({}) {
  const router = useRouter()
  const route = unwrap(
    useRoute(),
    '/configuration/earnings-credit/[effectiveDate]/view/[earningsCreditCode]',
  )!
  const effectiveDate = route.params.effectiveDate

  const payload = {
    code: route.params.earningsCreditCode,
  }

  const { data, status, error } = useQuery(
    earningsCreditQueries('/getEarningsCreditDefinitionsByCode', payload),
  )

  const {
    data: indexRates,
    status: indexRateStatus,
    error: indexRateErrors,
  } = useQuery(
    indexRateQueries('/listIndexRatesByEffectiveDate', {
      effectiveDate: formatToServerString(effectiveDate),
    }),
  )
  if (status === 'pending' || indexRateStatus === 'pending') return 'Loading...'
  if (!data || error || !indexRates || indexRateErrors) {
    return <div>404 Not Found</div>
  }

  const earningsCredits = data
    .map((item) => {
      const parsed = EarningsCreditFormSchema.parse(item)

      const { balanceTiers } = parsed

      const parsedTiers: ServicePriceTier[] = balanceTiers.map((tier) => {
        const { rate, adjustment } =
          item.rateSource === 'MANUAL' ?
            {
              rate: tier.indexRate,
              adjustment: 0,
            }
          : {
              rate: indexRates.find((el) => el.code === item.indexRateCode)
                ?.indexRate,
              adjustment: tier.indexAdjustmentRate,
            }

        return {
          priceValue: rate,
          tierMinVolumeInclusive: '0',
          tierMaxVolumeExclusive: '0',
          tierMinBalanceInclusive: tier.minTierInclusive,
          tierMaxBalanceExclusive: tier.maxTierExclusive,
          indexAdjustment: adjustment,
        } as ServicePriceTier
      })

      return {
        ...parsed,
        name: parsed.description,
        tiers: parsedTiers,
      }
    })
    .sort(
      (a, b) =>
        new Date(b.effectiveDate).getDate() -
        new Date(a.effectiveDate).getDate(),
    )

  const earningsCredit =
    earningsCredits.find((item) => {
      return item.effectiveDate === formatToServerString(effectiveDate)
    }) || earningsCredits[0]

  const onEditClick = () => {
    router.push(
      routeTo(
        '/configuration/earnings-credit/[effectiveDate]/edit/[earningsCreditCode]',
        {
          ...route.params,
          effectiveDate: formatToMonthYearFromDate(
            earningsCredit.effectiveDate,
          ),
          earningsCreditCode: earningsCredit.code,
        },
      ),
    )
  }

  const linkFormatter = (effectiveDate: string, earningsCreditCode: string) => {
    return routeTo(
      '/configuration/earnings-credit/[effectiveDate]/view/[earningsCreditCode]',
      {
        ...route.params,
        effectiveDate: formatToMonthYearFromDate(effectiveDate),
        earningsCreditCode,
      },
    )
  }

  const tiersColumns =
    earningsCredit.rateSource === 'INDEX_RATE' ?
      ([
        {
          id: 'price',
          header: 'Index adjustment',
          cell: ({ row: { original } }) => `${original.indexAdjustment}%`,
          meta: {
            className: 'basis-2/12',
          },
        },
        {
          id: 'sub-type',
          header: 'Effective rate',
          cell: ({ row: { original } }) => {
            const indexAdjustment = parseFloat(original.indexAdjustment ?? '0')
            const indexRate = parseFloat(original.priceValue ?? '0')
            return `${(indexAdjustment + indexRate).toFixed(2)} %`
          },
          meta: {
            className: 'basis-2/12',
          },
        },
      ] as ColumnDef<ServicePriceTier>[])
    : ([
        {
          id: 'price',
          header: 'Index rate',
          cell: ({ row: { original } }) => `${original.priceValue}%`,
          meta: {
            className: 'basis-2/12',
          },
        },
      ] as ColumnDef<ServicePriceTier>[])

  return (
    <div className='w-full overflow-hidden overflow-y-scroll px-8 py-10'>
      <div className='flex items-center justify-between'>
        <div className='flex items-center gap-2'>
          <Link
            href={routeTo(
              '/configuration/earnings-credit/[effectiveDate]',
              route.params,
            )}
            className='font-bold'
          >
            ...
          </Link>
          <ChevronRightIcon className='h-4 w-4 text-zinc-400' />
          <div className='text-md text-zinc-500'>
            <Link
              href={routeTo(
                '/configuration/earnings-credit/[effectiveDate]',
                route.params,
              )}
              className='font-bold'
            >
              Earnings credit information
            </Link>
          </div>
          <ChevronRightIcon className='h-4 w-4 text-zinc-400' />
          <div className='text-lg'>{earningsCredit.name}</div>
          <div className='text-sm text-zinc-500'>{earningsCredit.code}</div>
        </div>
        <Button className='btn-primary text-white' onClick={onEditClick}>
          Edit
        </Button>
      </div>
      <div className='mt-8 flex gap-4'>
        <div className='flex grow flex-col'>
          <DetailsSection>
            <DetailsSectionTitle>
              Earnings credit information
            </DetailsSectionTitle>
            <DetailsSectionItemsRow>
              <DetailsSectionItem
                label={'Effective date *'}
                info={earningsCredit.effectiveDate}
              />
            </DetailsSectionItemsRow>
            <DetailsSectionItemsRow>
              <DetailsSectionItem label={'Name *'} info={earningsCredit.name} />
              <DetailsSectionItem label={'Code *'} info={earningsCredit.code} />
            </DetailsSectionItemsRow>
          </DetailsSection>
          <DetailsSection className='mt-3'>
            <DetailsSectionTitle>
              Earnings credit calculation
            </DetailsSectionTitle>
            <DetailsSectionItemsRow>
              <DetailsSectionItem
                label={'Base balance *'}
                info={'Investible balance'}
              />
            </DetailsSectionItemsRow>
            <DetailsSectionItemsRow>
              <DetailsSectionItem
                label={'Tiering method *'}
                info={tierMethodTypeLabel(earningsCredit.tierMethod)}
              />
            </DetailsSectionItemsRow>
          </DetailsSection>
          <DetailsSection className='mt-3'>
            <DetailsSectionTitle>Earnings credit rate</DetailsSectionTitle>
            <div className='text-zinc-400'>
              Configure the rate used to calculate earnings credits.
            </div>
            <DetailsSectionItemsRow>
              <DetailsSectionItem
                label={'Rate source *'}
                info={rateSourceTypeLabel(earningsCredit.rateSource)}
              />
            </DetailsSectionItemsRow>
            {earningsCredit.rateSource === 'INDEX_RATE' && (
              <>
                <DetailsSectionItemsRow>
                  <DetailsSectionItem
                    label={'Index Rate *'}
                    info={baseBalanceTypeLabel(earningsCredit.baseBalanceType)}
                  />
                </DetailsSectionItemsRow>
                <DetailsSectionItemsRow>
                  <DetailsSectionItem
                    label={'Minimum rate'}
                    info={
                      earningsCredit.minRateInclusive ?
                        `${earningsCredit.minRateInclusive} %`
                      : '--'
                    }
                  />
                  <DetailsSectionItem
                    label={'Maximum rate'}
                    info={
                      earningsCredit.maxRateInclusive ?
                        `${earningsCredit.maxRateInclusive} %`
                      : '--'
                    }
                  />
                </DetailsSectionItemsRow>
              </>
            )}
            <div className='mt-5'>
              <PricingTiersList
                columns={tiersColumns}
                serviceType={'BALANCE_BASED'}
                tiers={earningsCredit.tiers}
              />
            </div>
          </DetailsSection>
        </div>
        <div className='flex max-w-72 flex-col rounded-lg border bg-white p-6'>
          <VersionTimeline
            versions={earningsCredits}
            currentEffectiveDate={route.params.effectiveDate}
            linkFormatter={linkFormatter}
          />
        </div>
      </div>
    </div>
  )
}
