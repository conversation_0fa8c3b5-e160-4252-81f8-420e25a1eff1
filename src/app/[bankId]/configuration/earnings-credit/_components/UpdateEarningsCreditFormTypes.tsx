import { z } from 'zod'
import { name, configCode } from '@/api/zodSchemas'
import { FormApi, ReactFormApi } from '@tanstack/react-form'
import { percentageWithDecimals } from '@/lib/validation/percentageWithDecimals'
import { ServicePriceTier } from '@/app/[bankId]/services/[effectiveDate]/(service)/UpdateServiceForm'
import { apiToFormSchemas } from '@/api/apiToFormSchema'
import { formToApiSchemas } from '@/api/formToApiSchema'
import { components } from '@/api/schema'
import { match } from '@/lib/unions/match'
import { variant } from '@/lib/unions/Union'
import { nullableRequired } from '@/lib/validation/nullableRequiredNativeEnum'

export const {
  earningsCreditDefinition: EarningsCreditFormSchemaPartial,
  balanceTier: BalanceTierSchemaPartial,
} = apiToFormSchemas

// API TO FORM
const percentageString = z
  .number()
  .transform((v) => v.toFixed(2))
  .optional()
  .transform((v) => (v === undefined ? null : v))

const BalanceTierSchema = BalanceTierSchemaPartial.extend({
  indexAdjustmentRate: percentageString,
  indexRate: percentageString,
  maxTierExclusive: z.number().pipe(z.coerce.string()),
  minTierInclusive: z.number().pipe(z.coerce.string()),
})

export const EarningsCreditFormSchema = EarningsCreditFormSchemaPartial.extend({
  balanceTiers: z.array(BalanceTierSchema),
  maxRateInclusive: percentageString,
  minRateInclusive: percentageString,
})

// FORM TO API
export const { earningsCreditDefinition: EarningsCreditApiSchema } =
  formToApiSchemas

// FORM STATE
export const earningsCreditInfoSchema = z.object({
  name,
  code: configCode,
  effectiveDate: z.string(),
})

type EarningsCreditInfoState = z.infer<typeof earningsCreditInfoSchema>

export const EarningsCreditCalculationSchema = z.object({
  basedBalance: nullableRequired(
    EarningsCreditFormSchema.shape.baseBalanceType,
  ),
  tieringMethod: nullableRequired(EarningsCreditFormSchema.shape.tierMethod),
})

type EarningsCreditCalculationState = z.infer<
  typeof EarningsCreditCalculationSchema
>

export const EarningsCreditRateSchema = z.object({
  rateSource: nullableRequired(EarningsCreditFormSchema.shape.rateSource),
  indexRateOption: z.custom<components['schemas']['IndexRate']>().nullable(),
  minimumRate: z.string().nullable(),
  maximumRate: z.string().nullable(),
  subPriceType: z.string(),
})

type EarningsCreditRateState = z.infer<typeof EarningsCreditRateSchema>

// TODO create composite zod schema instead (e.g., updateEarningsCreditSchema) and infer this type from that schema.
// This new composite schema could also be used as onChange validator in useForm in UpdateEarningsCreditFormForm to reduce
// need to define validators.onChange on every form field.
export type EarningsCreditFormStateType = {
  earningsCreditInfo: EarningsCreditInfoState
  earningsCreditCalculation: EarningsCreditCalculationState
  earningsCreditRate: EarningsCreditRateState
  pricingTiers: ServicePriceTier[]
}

// Form Type
export type EarningsCreditFormApi = ReactFormApi<EarningsCreditFormStateType> &
  FormApi<EarningsCreditFormStateType>

// Enums to labels:
export const baseBalanceTypes = EarningsCreditFormSchema.shape.baseBalanceType
export const rateSourceTypes = EarningsCreditFormSchema.shape.rateSource
export const tierMethodTypes = EarningsCreditFormSchema.shape.tierMethod

export const baseBalanceTypeLabel = (value: z.infer<typeof baseBalanceTypes>) =>
  match(variant(value), {
    AVERAGE_COLLECTED: () => 'Average collected',
    AVERAGE_LEDGER: () => 'Average ledger',
    AVERAGE_NEGATIVE_COLLECTED: () => 'Average negative collected',
    AVERAGE_NEGATIVE_LEDGER: () => 'Average negative ledger',
    AVERAGE_POSITIVE_COLLECTED: () => 'Average positive collected',
    AVERAGE_POSITIVE_LEDGER: () => 'Average positive ledger',
    AVERAGE_UNCOLLECTED_FUNDS: () => 'Average uncollected funds',
    COMPENSATING_BALANCE: () => 'Compensating balance',
    END_OF_MONTH_LEDGER: () => 'End of month ledger',
    INVESTABLE_BALANCE: () => 'Investible balance',
    AVERAGE_FLOAT: () => 'Average float',
    AVERAGE_CLEARINGHOUSE_FLOAT: () => 'Average clearinghouse float',
    REQUIRED_BALANCE: () => 'Required balance',
  })

export const rateSourceTypeLabel = (value: z.infer<typeof rateSourceTypes>) =>
  match(variant(value), {
    INDEX_RATE: () => 'Use index rate',
    MANUAL: () => 'Manually enter rate',
  })

export const tierMethodTypeLabel = (value: z.infer<typeof tierMethodTypes>) =>
  match(variant(value), {
    THRESHOLD: () => 'Threshold',
    PARTITIONED: () => 'Partitioned',
  })
