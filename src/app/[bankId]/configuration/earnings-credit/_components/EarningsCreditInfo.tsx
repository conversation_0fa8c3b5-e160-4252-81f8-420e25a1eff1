'use client'

import { useFormTextInput } from '@/components/Form/useFormTextInput'
import {
  EarningsCreditFormApi,
  EarningsCreditFormStateType,
  earningsCreditInfoSchema,
} from './UpdateEarningsCreditFormTypes'
import { useFormMonthPicker } from '@/components/Form/useFormMonthPicker'

interface EarningsCreditInfoProps {
  form: EarningsCreditFormApi
  isEditMode?: boolean
}

export default function EarningsCreditInfo({
  form,
  isEditMode,
}: EarningsCreditInfoProps) {
  const FormTextInput = useFormTextInput<EarningsCreditFormStateType>({
    form,
  })
  const FormMonthPicker = useFormMonthPicker<EarningsCreditFormStateType>({
    form,
  })

  return (
    <div className='mt-10 flex w-full flex-col rounded-lg border bg-white p-6'>
      <div className='font-semibold'>Earnings credit information</div>
      <div className='mt-3 flex gap-9'>
        <div className='flex flex-1 flex-col gap-2'>
          <FormMonthPicker
            name='earningsCreditInfo.effectiveDate'
            label='Effective date *'
          />
        </div>
        <div className='flex-1'></div>
      </div>
      <div className='mt-3 flex gap-9'>
        <FormTextInput
          name='earningsCreditInfo.name'
          label='Name'
          required
          validators={{
            onChange: earningsCreditInfoSchema.shape.name,
          }}
        />
        {/* todo: check schedule definition code is unique */}
        <FormTextInput
          name='earningsCreditInfo.code'
          label='Code'
          required
          validators={{
            onChange: earningsCreditInfoSchema.shape.code,
          }}
          placeholder='Max 5 digits'
          type='number'
          invalidChars={['e', 'E']}
          readonly={(field) =>
            !!isEditMode &&
            field.value != null &&
            field.value.length > 0 &&
            field.meta.isPristine
          }
        />
      </div>
    </div>
  )
}
