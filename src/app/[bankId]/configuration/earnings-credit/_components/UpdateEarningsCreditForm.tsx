'use client'
import { useForm } from '@tanstack/react-form'

import { EarningsCreditFormStateType } from './UpdateEarningsCreditFormTypes'
import EarningsCreditInfo from './EarningsCreditInfo'
import EarningsCreditCalculation from './EarningsCreditCalculation'
import EarningsCreditRate from './EarningsCreditRate'
import { components } from '@/api/schema'

export interface UpdateEarningsCreditFormFormProps {
  defaultValues: EarningsCreditFormStateType
  indexRateOptions: components['schemas']['IndexRate'][]
  isEditMode?: boolean
  onSubmit: (state: EarningsCreditFormStateType) => void
}

export default function UpdateEarningsCreditFormForm({
  defaultValues,
  isEditMode,
  onSubmit,
  indexRateOptions,
  children,
}: React.PropsWithChildren<UpdateEarningsCreditFormFormProps>) {
  const form = useForm({
    defaultValues,
    onSubmit: ({ value }) => {
      onSubmit(value)
    },
  })

  // TODO can add `validators` prop with `onChange` pointing to composite zod schema
  // so we don't need to define `validators.onChange` on every form field.
  return (
    <form
      className='flex max-h-full flex-auto flex-col'
      onSubmit={async (event) => {
        form.reset(form.state.values)
        event.preventDefault()
        event.stopPropagation()
        await form.handleSubmit()
      }}
    >
      <div className='flex flex-auto flex-col gap-2 overflow-y-auto px-8'>
        <EarningsCreditInfo form={form} isEditMode={isEditMode} />
        <EarningsCreditCalculation form={form} isEditMode={isEditMode} />
        <EarningsCreditRate form={form} indexRateOptions={indexRateOptions} />
      </div>
      <div className='flex justify-end gap-4 border-t bg-white px-12 py-10'>
        {children}
      </div>
    </form>
  )
}
