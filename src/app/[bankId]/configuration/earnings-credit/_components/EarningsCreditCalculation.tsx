'use client'

import { useFormTextInput } from '@/components/Form/useFormTextInput'
import { useFormSelect } from '@/components/Form/useFormSelect'
import {
  EarningsCreditFormApi,
  EarningsCreditFormStateType,
  EarningsCreditCalculationSchema,
  baseBalanceTypeLabel,
  baseBalanceTypes,
  tierMethodTypeLabel,
  tierMethodTypes,
} from './UpdateEarningsCreditFormTypes'

import { TieringMethodTypeOptions } from '../../types'

interface UserFieldInfoProps {
  form: EarningsCreditFormApi
  isEditMode?: boolean
}

export default function EarningsCreditCalculation({
  form,
  isEditMode,
}: UserFieldInfoProps) {
  const FormTextInput = useFormTextInput<EarningsCreditFormStateType>({
    form,
  })
  const Select = useFormSelect<EarningsCreditFormStateType>({ form })

  return (
    <div className='mt-10 flex w-full flex-col rounded-lg border bg-white p-6'>
      <div className='font-semibold'>Earnings credit calculation</div>
      <div className='mt-3 flex gap-9'>
        <Select
          name='earningsCreditCalculation.basedBalance'
          label='Based balance'
          disabled={isEditMode}
          validators={{
            onChange: EarningsCreditCalculationSchema.shape.basedBalance,
          }}
          options={baseBalanceTypes.options}
          renderSelected={baseBalanceTypeLabel}
          renderOption={baseBalanceTypeLabel}
        />
      </div>
      <div className='mt-3 flex gap-9'>
        <Select
          name='earningsCreditCalculation.tieringMethod'
          label='Tiering Method'
          required
          validators={{
            onChange: EarningsCreditCalculationSchema.shape.tieringMethod,
          }}
          options={tierMethodTypes.options}
          renderSelected={tierMethodTypeLabel}
          renderOption={tierMethodTypeLabel}
        />
      </div>
    </div>
  )
}
