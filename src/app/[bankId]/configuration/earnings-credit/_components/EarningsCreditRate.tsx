'use client'

import { useFormTextInput } from '@/components/Form/useFormTextInput'
import { useFormSelect } from '@/components/Form/useFormSelect'
import {
  EarningsCreditFormApi,
  EarningsCreditFormStateType,
  EarningsCreditRateSchema,
  rateSourceTypeLabel,
  rateSourceTypes,
} from './UpdateEarningsCreditFormTypes'
import { ReactFormExtendedApi, useStore } from '@tanstack/react-form'
import { If } from '@/components/If'
import {
  EditPricingTiers,
  TiersFormState,
} from '@/app/[bankId]/services/_components/EditPricingTiers'
import { SelectOption } from '@/components/Input/Select'
import { components } from '@/api/schema'

interface UserFieldInfoProps {
  form: EarningsCreditFormApi
  isEditMode?: boolean
  indexRateOptions: components['schemas']['IndexRate'][]
}

export default function EarningsCreditRate({
  form,
  indexRateOptions,
}: UserFieldInfoProps) {
  const { rateSource, indexRateSelection } = useStore(form.store, (state) => ({
    rateSource: state.values.earningsCreditRate.rateSource,
    indexRateSelection: state.values.earningsCreditRate.indexRateOption,
  }))

  const Select = useFormSelect<EarningsCreditFormStateType>({ form })
  const FormTextInput = useFormTextInput<EarningsCreditFormStateType>({
    form,
  })

  let rate: string | null = '0%'

  if (rateSource === 'INDEX_RATE') {
    rate = indexRateSelection?.indexRate.toString() || '0%'
  } else {
    rate = null
  }

  return (
    <div className='mt-10 flex w-full flex-col rounded-lg border bg-white p-6'>
      <div className='font-semibold'>Earnings credit rate</div>
      <div className='mt-3 flex gap-9'>
        <Select
          name='earningsCreditRate.rateSource'
          label='Rate Source'
          required
          validators={{
            onChange: EarningsCreditRateSchema.shape.rateSource,
          }}
          options={rateSourceTypes.options}
          renderSelected={rateSourceTypeLabel}
          renderOption={rateSourceTypeLabel}
        />
      </div>

      <If true={rateSource == 'INDEX_RATE'}>
        <div className='mt-3 flex gap-9'>
          <Select
            name='earningsCreditRate.indexRateOption'
            label='Index rate'
            renderSelected={(value) => `${value?.code} - ${value?.name}`}
            required={rateSource !== 'INDEX_RATE'}
          >
            {indexRateOptions.map((value) => (
              <SelectOption key={value.code} value={value}>
                {value?.code} - {value?.name}
              </SelectOption>
            ))}
          </Select>
          <div className='flex-1'></div>
        </div>
        <div className='mt-3 flex gap-9'>
          <FormTextInput
            label='Minimum Rate'
            name='earningsCreditRate.minimumRate'
            invalidChars={['e', 'E']}
            type='number'
            validators={{
              onChange: ({ value }) => {
                const result =
                  EarningsCreditRateSchema.shape.minimumRate.safeParse(value)
                return result.success ? undefined : (
                    result.error.issues[0].message
                  )
              },
            }}
            placeholder='Add min'
          />
          <FormTextInput
            label='Maximum Rate'
            name='earningsCreditRate.maximumRate'
            invalidChars={['e', 'E']}
            type='number'
            validators={{
              onChange: ({ value }) => {
                const result =
                  EarningsCreditRateSchema.shape.maximumRate.safeParse(value)
                return result.success ? undefined : (
                    result.error.issues[0].message
                  )
              },
            }}
            placeholder='Add max'
          />
        </div>
      </If>
      <div className='mt-3'>
        <EditPricingTiers
          form={form as unknown as ReactFormExtendedApi<TiersFormState>}
          serviceType={'BALANCE_BASED'}
          subPriceType={rateSource}
          indexRate={rate}
        />
      </div>
    </div>
  )
}
