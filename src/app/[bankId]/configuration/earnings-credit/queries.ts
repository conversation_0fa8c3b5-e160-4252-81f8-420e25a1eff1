import { revenueConnectClient } from '@/api/revenueConnectClient'
import { defineQueries, defineQuery } from '@/lib/state/defineQuery'
import { withMetrics } from '@/lib/middleware/withMetrics'

const getEarningsCreditByEffectiveDate = defineQuery(
  revenueConnectClient,
  '/getEarningsCreditDefinitions',
  (payload) => [payload.effectiveDate],
)

const getEarningsCreditByCode = defineQuery(
  revenueConnectClient,
  '/getEarningsCreditDefinitionsByCode',
  (payload) => [payload.code],
)

export const earningsCreditQueries = defineQueries(
  [getEarningsCreditByEffectiveDate, getEarningsCreditByCode],
  withMetrics(),
)
