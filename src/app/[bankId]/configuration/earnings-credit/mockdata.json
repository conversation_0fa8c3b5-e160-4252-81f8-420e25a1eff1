{"/createEarningsCreditDefinition": {"payloads": [{"code": "22", "effectiveDate": "2025-01-01", "description": "Blend value tests", "baseBalanceType": "AVERAGE_COLLECTED", "tierMethod": "THRESHOLD", "rateSource": "INDEX_RATE", "indexRateCode": "1", "minRateInclusive": 2, "maxRateInclusive": 15, "balanceTiers": [{"indexAdjustmentRate": 3.3, "minTierInclusive": 0, "maxTierExclusive": 10}, {"indexAdjustmentRate": 2.2, "minTierInclusive": 10, "maxTierExclusive": 20}, {"indexAdjustmentRate": 1.1, "minTierInclusive": 20, "maxTierExclusive": 999999999}]}, {"code": "33", "effectiveDate": "2025-01-01", "description": "Index 101", "baseBalanceType": "AVERAGE_COLLECTED", "tierMethod": "THRESHOLD", "rateSource": "MANUAL", "balanceTiers": [{"indexRate": 3.1, "minTierInclusive": 0, "maxTierExclusive": 10}, {"indexRate": 2.1, "minTierInclusive": 10, "maxTierExclusive": 20}, {"indexRate": 1.1, "minTierInclusive": 20, "maxTierExclusive": 999999999}]}, {"code": "4", "effectiveDate": "2025-01-01", "description": "Index 101 Ave Coll", "baseBalanceType": "AVERAGE_COLLECTED", "tierMethod": "THRESHOLD", "rateSource": "INDEX_RATE", "balanceTiers": [{"indexRate": "5", "indexAdjustmentRate": "15", "minTierInclusive": "0", "maxTierExclusive": "999999999"}]}, {"code": "12", "effectiveDate": "2025-01-01", "description": "New earnings credit", "baseBalanceType": "AVERAGE_COLLECTED", "tierMethod": "THRESHOLD", "rateSource": "INDEX_RATE", "balanceTiers": []}, {"code": "120", "effectiveDate": "2025-01-01", "description": "Western region plan", "baseBalanceType": "AVERAGE_COLLECTED", "tierMethod": "THRESHOLD", "rateSource": "INDEX_RATE", "balanceTiers": []}]}}