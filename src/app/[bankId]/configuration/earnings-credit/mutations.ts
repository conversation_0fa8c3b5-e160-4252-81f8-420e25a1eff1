import { earningsCreditQueries } from '@/app/[bankId]/configuration/earnings-credit/queries'
import { withMetrics } from '@/lib/middleware/withMetrics'
import { defineMutations, defineMutation } from '@/lib/state/defineMutation'
import { revenueConnectClient } from '@/api/revenueConnectClient'
import { compose } from '@/lib/functional/compose'
import { notifications } from './notifications'

const createEarningsCredit = defineMutation(
  revenueConnectClient,
  '/createEarningsCreditDefinition',
  {
    invalidate: (request) => [
      earningsCreditQueries('/getEarningsCreditDefinitionsByCode', {
        code: request.code,
      }),
      earningsCreditQueries('/getEarningsCreditDefinitions', {
        effectiveDate: request.effectiveDate,
      }),
    ],
  },
)

const updateEarningsCredit = defineMutation(
  revenueConnectClient,
  '/updateEarningsCreditDefinition',
  {
    invalidate: (request) => [
      earningsCreditQueries('/getEarningsCreditDefinitionsByCode', {
        code: request.code,
      }),
      earningsCreditQueries('/getEarningsCreditDefinitions', {
        effectiveDate: request.effectiveDate,
      }),
    ],
  },
)

export type EarningsCreditMutation =
  | typeof createEarningsCredit
  | typeof updateEarningsCredit

export const earningsCreditMutation = defineMutations(
  [createEarningsCredit, updateEarningsCredit],
  compose(notifications, withMetrics()),
)
