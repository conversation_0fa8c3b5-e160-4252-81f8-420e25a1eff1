'use client'

import { useRouter } from 'next/navigation'
import { ChevronRightIcon } from '@heroicons/react/24/outline'
import Link from 'next/link'

import {
  DetailsSection,
  DetailsSectionItem,
  DetailsSectionItemsRow,
  DetailsSectionTitle,
} from '@/components/DetailsSection'

import { VersionTimeline } from '@/app/[bankId]/configuration/_components/VersionTimeline'
import { Button } from '@/components/Button'
import { unwrap } from '@/lib/unions/unwrap'
import { routeTo, useRoute } from '@/app/[bankId]/configuration/routing'

import { useQuery } from '@tanstack/react-query'
import { statementMessageQueries } from '../../../queries'
import { apiToFormSchemas } from '@/api/apiToFormSchema'
import { formatToMonthYearFromDate, formatToServerString } from '@/lib/date'

export default function ViewStatementMessage({}) {
  const router = useRouter()
  const route = unwrap(
    useRoute(),
    '/configuration/statement-message/[effectiveDate]/view/[statementMessageCode]',
  )!
  const effectiveDate = route.params.effectiveDate

  const payload = {
    code: route.params.statementMessageCode,
  }

  const { data, status, error } = useQuery(
    statementMessageQueries('/getStatementMessageByCode', payload),
  )

  if (status === 'pending') return 'Loading...'
  if (!data || error) {
    return <div>404 Not Found</div>
  }

  const { statementMessage: StatementMessageType } = apiToFormSchemas

  const statementMessages = data
    .map((item) => StatementMessageType.parse(item))
    .sort(
      (a, b) =>
        new Date(b.effectiveDate).getDate() -
        new Date(a.effectiveDate).getDate(),
    )

  const statementMessage =
    statementMessages.find((item) => {
      return item.effectiveDate === formatToServerString(effectiveDate)
    }) || statementMessages[0]

  const onEditClick = () => {
    router.push(
      routeTo(
        '/configuration/statement-message/[effectiveDate]/edit/[statementMessageCode]',
        {
          ...route.params,
          effectiveDate: formatToMonthYearFromDate(
            statementMessage.effectiveDate,
          ),
          statementMessageCode: statementMessage.code,
        },
      ),
    )
  }

  const linkFormatter = (
    effectiveDate: string,
    statementMessageCode: string,
  ) => {
    return routeTo(
      '/configuration/statement-message/[effectiveDate]/view/[statementMessageCode]',
      {
        ...route.params,
        effectiveDate: formatToMonthYearFromDate(effectiveDate),
        statementMessageCode,
      },
    )
  }

  return (
    <div className='w-full overflow-hidden overflow-y-scroll px-8 py-10'>
      <div className='flex items-center justify-between'>
        <div className='flex items-center gap-2'>
          <Link
            href={routeTo(
              '/configuration/statement-message/[effectiveDate]',
              route.params,
            )}
            className='font-bold'
          >
            ...
          </Link>
          <ChevronRightIcon className='h-4 w-4 text-zinc-400' />
          <div className='text-md text-zinc-500'>
            <Link
              href={routeTo(
                '/configuration/statement-message/[effectiveDate]',
                route.params,
              )}
              className='font-bold'
            >
              Statement message information
            </Link>
          </div>
          <ChevronRightIcon className='h-4 w-4 text-zinc-400' />
          <div className='text-lg'>{statementMessage.name}</div>
          <div className='text-sm text-zinc-500'>{statementMessage.code}</div>
        </div>
        <Button className='btn-primary text-white' onClick={onEditClick}>
          Edit
        </Button>
      </div>
      <div className='mt-8 flex gap-4'>
        <div className='flex grow flex-col'>
          <DetailsSection>
            <DetailsSectionTitle>
              Statement message information
            </DetailsSectionTitle>
            <DetailsSectionItemsRow>
              <DetailsSectionItem
                label={'Effective date *'}
                info={statementMessage.effectiveDate}
              />
            </DetailsSectionItemsRow>
            <DetailsSectionItemsRow>
              <DetailsSectionItem
                label={'Name *'}
                info={statementMessage.name}
              />
              <DetailsSectionItem
                label={'Code *'}
                info={statementMessage.code}
              />
            </DetailsSectionItemsRow>
          </DetailsSection>
          <DetailsSection className='mt-3'>
            <div className='flex text-zinc-500'>Body Text</div>
            <div
              className='mt-3'
              dangerouslySetInnerHTML={{ __html: statementMessage.message }}
            />
          </DetailsSection>
        </div>
        <div className='flex max-w-72 flex-col rounded-lg border bg-white p-6'>
          <VersionTimeline
            versions={statementMessages} //TODO: Make call for versions
            currentEffectiveDate={route.params.effectiveDate}
            linkFormatter={linkFormatter}
          />
        </div>
      </div>
    </div>
  )
}
