'use client'

import { useRouter } from 'next/navigation'
import { But<PERSON> } from '@/components/Button'

import { unwrap } from '@/lib/unions/unwrap'
import { routeTo, useRoute } from '../../../routing'
import { formatToMonthYearFromDate, formatToServerString } from '@/lib/date'
import UpdateStatementMessageForm from '../../_components/UpdateStatementMessageForm'
import { UpdateStatementMessageFormState } from '../../_components/UpdateStatementMessageFormTypes'
import { statementMessageMutation } from '../../mutations'
import { useMutation } from '@tanstack/react-query'

export default function AddStatementMessage({}) {
  const router = useRouter()
  const route = unwrap(
    useRoute(),
    '/configuration/statement-message/[effectiveDate]/add',
  )!

  const defaultValues: UpdateStatementMessageFormState = {
    statementMessageInfo: {
      code: '',
      name: '',
      effectiveDate: formatToMonthYearFromDate(new Date()),
      message: '',
    },
  }

  const addStatementMessage = useMutation(
    statementMessageMutation('/addStatementMessage', {
      onSuccess: (_, request) => {
        router.push(
          routeTo(
            '/configuration/statement-message/[effectiveDate]/view/[statementMessageCode]',
            {
              ...route.params,
              statementMessageCode: request.code!,
              effectiveDate: formatToMonthYearFromDate(request.effectiveDate!),
            },
          ),
        )
      },
    }),
  )

  return (
    <div className='w-full min-w-full overflow-auto pt-12'>
      <div className='text-md ml-8 font-medium'>Add statement message</div>

      <UpdateStatementMessageForm
        defaultValues={defaultValues}
        onSubmit={({ statementMessageInfo }) => {
          addStatementMessage.mutate({
            ...statementMessageInfo,
            effectiveDate: formatToServerString(
              statementMessageInfo.effectiveDate,
            ),
          })
        }}
      >
        <Button className='btn w-60' onClick={() => router.back()}>
          Cancel
        </Button>
        <Button type='submit' className='btn-primary w-60'>
          Save
        </Button>
      </UpdateStatementMessageForm>
    </div>
  )
}
