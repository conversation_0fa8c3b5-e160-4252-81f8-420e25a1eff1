'use client'

import { useRouter } from 'next/navigation'
import { <PERSON><PERSON> } from '@/components/Button'

import UpdateStatementMessageForm from '../../../_components/UpdateStatementMessageForm'
import { UpdateStatementMessageFormState } from '../../../_components/UpdateStatementMessageFormTypes'
import { unwrap } from '@/lib/unions/unwrap'
import { routeTo, useRoute } from '@/app/[bankId]/configuration/routing'

import { useMutation, useQuery } from '@tanstack/react-query'
import { statementMessageQueries } from '../../../queries'
import { apiToFormSchemas } from '@/api/apiToFormSchema'
import { statementMessageMutation } from '../../../mutations'
import { formatToServerString, formatToMonthYearFromDate } from '@/lib/date'

export default function EditStatementMessage({}) {
  const router = useRouter()
  const route = unwrap(
    useRoute(),
    '/configuration/statement-message/[effectiveDate]/edit/[statementMessageCode]',
  )!

  const effectiveDate = route.params.effectiveDate

  const payload = {
    code: route.params.statementMessageCode,
  }

  const { data, status, error } = useQuery(
    statementMessageQueries('/getStatementMessageByCode', payload),
  )

  const updateStatementMessage = useMutation(
    statementMessageMutation('/updateStatementMessage', {
      onSuccess: (_, request) => {
        router.push(
          routeTo(
            '/configuration/statement-message/[effectiveDate]/view/[statementMessageCode]',
            {
              ...route.params,
              statementMessageCode: request.code!,
              effectiveDate: formatToMonthYearFromDate(request.effectiveDate!),
            },
          ),
        )
      },
    }),
  )
  if (status === 'pending') return 'Loading...'
  if (!data || error) {
    return <div>404 Not Found</div>
  }

  const { statementMessage: StatementMessageType } = apiToFormSchemas

  const statementMessages = data
    .map((item) => StatementMessageType.parse(item))
    .sort(
      (a, b) =>
        new Date(b.effectiveDate).getDate() -
        new Date(a.effectiveDate).getDate(),
    )

  const statementMessage =
    statementMessages.find((item) => {
      return item.effectiveDate === effectiveDate
    }) || statementMessages[0]

  const defaultValues: UpdateStatementMessageFormState = {
    statementMessageInfo: {
      code: statementMessage.code,
      name: statementMessage.name,
      effectiveDate: statementMessage.effectiveDate,
      message: statementMessage.message,
    },
  }

  return (
    <div className='w-full min-w-full overflow-auto pt-12'>
      <div className='text-md ml-8 font-medium'>Edit statement message</div>

      <UpdateStatementMessageForm
        defaultValues={defaultValues}
        isEditMode
        onSubmit={({ statementMessageInfo }) => {
          updateStatementMessage.mutate({
            ...statementMessageInfo,
            effectiveDate: formatToServerString(
              statementMessageInfo.effectiveDate,
            ),
          })
        }}
      >
        <Button className='btn w-60' onClick={() => router.back()}>
          Cancel
        </Button>
        <Button type='submit' className='btn-primary w-60'>
          Save
        </Button>
      </UpdateStatementMessageForm>
    </div>
  )
}
