import { statementMessageQueries } from '@/app/[bankId]/configuration/statement-message/queries'
import { withMetrics } from '@/lib/middleware/withMetrics'
import { defineMutations, defineMutation } from '@/lib/state/defineMutation'
import { revenueConnectClient } from '@/api/revenueConnectClient'
import { compose } from '@/lib/functional/compose'
import { notifications } from './notifications'

const createStatementMessage = defineMutation(
  revenueConnectClient,
  '/addStatementMessage',
  {
    invalidate: (request) => [
      statementMessageQueries('/getStatementMessageByCode', {
        code: request.code,
      }),
      statementMessageQueries('/listStatementMessagesByEffectiveDate', {
        effectiveDate: request.effectiveDate,
      }),
    ],
  },
)

const updateStatementMessage = defineMutation(
  revenueConnectClient,
  '/updateStatementMessage',
  {
    invalidate: (request) => [
      statementMessageQueries('/getStatementMessageByCode', {
        code: request.code,
      }),
      statementMessageQueries('/listStatementMessagesByEffectiveDate', {
        effectiveDate: request.effectiveDate,
      }),
    ],
  },
)

export type StatementMessageMutation =
  | typeof createStatementMessage
  | typeof updateStatementMessage

export const statementMessageMutation = defineMutations(
  [createStatementMessage, updateStatementMessage],
  compose(notifications, withMetrics()),
)
