import { useState, useEffect } from 'react'
import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext'
import {
  $getSelection,
  $isRangeSelection,
  FORMAT_ELEMENT_COMMAND,
} from 'lexical'
import { $patchStyleText } from '@lexical/selection'
import {
  INSERT_UNORDERED_LIST_COMMAND,
  REMOVE_LIST_COMMAND,
} from '@lexical/list'

import FormatListIcon from '../../_components/svgs/format-list'
import FormatBoldIcon from '../../_components/svgs/format-bold'
import FormatItalicIcon from '../../_components/svgs/format-italic'
import FormatUnderlineIcon from '../../_components/svgs/format-underline'
import FormatAlignLeftIcon from '../../_components/svgs/align-left'
import FormatAlignRightIcon from '../../_components/svgs/align-right'
import FormatAlignCenterIcon from '../../_components/svgs/align-center'
import FormatAlignJustifyIcon from '../../_components/svgs/align-justify'

interface ToolbarButtonProps {
  onClick: (e: React.SyntheticEvent) => void
  children: React.ReactNode
  active?: boolean
}

type TextAlignment = 'left' | 'center' | 'right' | 'justify'

const ToolbarButton: React.FC<ToolbarButtonProps> = ({
  onClick,
  children,
  active,
}) => {
  return (
    <button
      onClick={onClick}
      className={`m-1 flex h-8 w-8 items-center justify-center rounded-md border hover:bg-gray-100 focus:outline-none ${active ? 'bg-gray-100' : ''}`}
    >
      {children}
    </button>
  )
}

const FormattingToolbar: React.FC = () => {
  const [editor] = useLexicalComposerContext()
  const [isBold, setIsBold] = useState(false)
  const [isItalic, setIsItalic] = useState(false)
  const [isUnderline, setIsUnderline] = useState(false)
  const [isList, setIsList] = useState(false)
  const [textAlignment, setTextAlignment] = useState<TextAlignment>('left')

  useEffect(() => {
    return editor.registerUpdateListener(({ editorState }) => {
      editorState.read(() => {
        const selection = $getSelection()
        if ($isRangeSelection(selection)) {
          setIsBold(selection.hasFormat('bold'))
          setIsItalic(selection.hasFormat('italic'))
          setIsUnderline(selection.hasFormat('underline'))
          const anchorNode = selection.anchor.getNode()
          const element =
            anchorNode.getKey() === 'root' ?
              anchorNode
            : anchorNode.getTopLevelElementOrThrow()

          const listStatus = element.getType() === 'list'
          setIsList(listStatus)

          const elementStyle = element.getStyle()
          if (elementStyle) {
            const textAlign = elementStyle
            if (textAlign) {
              setTextAlignment(textAlign as TextAlignment)
            } else {
              setTextAlignment('left')
            }
          } else {
            setTextAlignment('left')
          }
        }
      })
    })
  }, [editor])

  const formatBold = (e: React.SyntheticEvent) => {
    e.stopPropagation()
    e.preventDefault()
    editor.update(() => {
      const selection = $getSelection()
      if ($isRangeSelection(selection)) {
        selection.formatText('bold')
      }
    })
  }

  const formatItalic = (e: React.SyntheticEvent) => {
    e.stopPropagation()
    e.preventDefault()
    editor.update(() => {
      const selection = $getSelection()
      if ($isRangeSelection(selection)) {
        selection.formatText('italic')
      }
    })
  }

  const formatUnderline = (e: React.SyntheticEvent) => {
    e.stopPropagation()
    e.preventDefault()
    editor.update(() => {
      const selection = $getSelection()
      if ($isRangeSelection(selection)) {
        selection.formatText('underline')
      }
    })
  }

  const formatList = (e: React.SyntheticEvent) => {
    e.stopPropagation()
    e.preventDefault()
    if (isList) {
      editor.dispatchCommand(REMOVE_LIST_COMMAND, undefined)
    } else {
      editor.dispatchCommand(INSERT_UNORDERED_LIST_COMMAND, undefined)
    }
  }

  const formatAlignment = (alignment: TextAlignment) => {
    editor.dispatchCommand(FORMAT_ELEMENT_COMMAND, alignment)
  }

  return (
    <div className='flex items-center justify-between border-b border-gray-200 bg-white p-2'>
      <div className='flex'>
        <ToolbarButton onClick={formatBold} active={isBold}>
          <FormatBoldIcon />
        </ToolbarButton>
        <ToolbarButton onClick={formatItalic} active={isItalic}>
          <FormatItalicIcon />
        </ToolbarButton>
        <ToolbarButton onClick={formatUnderline} active={isUnderline}>
          <FormatUnderlineIcon />
        </ToolbarButton>
        <ToolbarButton onClick={formatList} active={isList}>
          <FormatListIcon />
        </ToolbarButton>
        <ToolbarButton
          active={textAlignment === 'left'}
          onClick={(e: React.SyntheticEvent) => {
            e.stopPropagation()
            e.preventDefault()
            formatAlignment('left')
          }}
        >
          <FormatAlignLeftIcon />
        </ToolbarButton>
        <ToolbarButton
          active={textAlignment === 'center'}
          onClick={(e: React.SyntheticEvent) => {
            e.stopPropagation()
            e.preventDefault()
            formatAlignment('center')
          }}
        >
          <FormatAlignCenterIcon />
        </ToolbarButton>
        <ToolbarButton
          active={textAlignment === 'right'}
          onClick={(e: React.SyntheticEvent) => {
            e.stopPropagation()
            e.preventDefault()
            formatAlignment('right')
          }}
        >
          <FormatAlignRightIcon />
        </ToolbarButton>
        <ToolbarButton
          active={textAlignment === 'justify'}
          onClick={(e: React.SyntheticEvent) => {
            e.stopPropagation()
            e.preventDefault()
            formatAlignment('justify')
          }}
        >
          <FormatAlignJustifyIcon />
        </ToolbarButton>
      </div>
    </div>
  )
}

export default FormattingToolbar
