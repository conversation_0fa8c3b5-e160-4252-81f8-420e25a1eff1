import { z } from 'zod'
import { configCode } from '@/api/zodSchemas'
import { FormApi, ReactFormApi } from '@tanstack/react-form'
import { replaceNullWithUndefined } from '@/lib/functional/replaceNullWithUndefined'

export const statementMessageInfoSchema = z.object({
  name: z.string(),
  code: configCode,
  effectiveDate: z.string(),
  message: z.string(),
})

type StatementMessageInfoState = z.infer<typeof statementMessageInfoSchema>

// TODO create composite zod schema instead (e.g., updateStatementMessageSchema) and infer this type from that schema.
// This new composite schema could also be used as onChange validator in useForm in UpdateStatementMessageForm to reduce
// need to define validators.onChange on every form field.
export type UpdateStatementMessageFormState = {
  statementMessageInfo: StatementMessageInfoState
}

export type UpdateStatementMessageFormApi =
  ReactFormApi<UpdateStatementMessageFormState> &
    FormApi<UpdateStatementMessageFormState>
