'use client'

import { useForm } from '@tanstack/react-form'

import Editor from './Editor'
import {
  statementMessageInfoSchema,
  UpdateStatementMessageFormState,
} from './UpdateStatementMessageFormTypes'
import { useFormTextInput } from '@/components/Form/useFormTextInput'
import { useFormMonthPicker } from '@/components/Form/useFormMonthPicker'
import { useState } from 'react'

export interface UpdateStatementMessageFormProps {
  defaultValues: UpdateStatementMessageFormState
  isEditMode?: boolean
  onSubmit: (state: UpdateStatementMessageFormState) => void
}

export default function UpdateStatementMessageForm({
  defaultValues,
  isEditMode,
  onSubmit,
  children,
}: React.PropsWithChildren<UpdateStatementMessageFormProps>) {
  const form = useForm({
    defaultValues,
    onSubmit: ({ value }) => {
      onSubmit(value)
    },
  })
  const FormTextInput = useFormTextInput<UpdateStatementMessageFormState>({
    form,
  })
  const FormMonthPicker = useFormMonthPicker<UpdateStatementMessageFormState>({
    form,
  })

  const [messageHtml, setMessageHtml] = useState(
    defaultValues.statementMessageInfo.message,
  )
  const [messageError, setMessageError] = useState<boolean>()

  const handleEditorChange = (html: string, error?: boolean) => {
    setMessageError(error)
    setMessageHtml(html)
  }

  // TODO can add `validators` prop with `onChange` pointing to composite zod schema
  // so we don't need to define `validators.onChange` on every form field.
  return (
    <form
      className='flex max-h-full flex-auto flex-col'
      onSubmit={async (event) => {
        event.preventDefault()
        event.stopPropagation()

        form.setFieldValue('statementMessageInfo.message', messageHtml)
        if (messageError) {
          return
        }

        form.reset(form.state.values)
        await form.handleSubmit()
      }}
    >
      <div className='flex flex-auto flex-col gap-2 overflow-y-auto px-8'>
        <div className='mt-6 flex w-full flex-col rounded-lg border bg-white p-6'>
          <div className='font-semibold'>Statement message information</div>
          <div className='mt-3 flex gap-9'>
            <div className='flex flex-1 flex-col gap-2'>
              <FormMonthPicker
                name='statementMessageInfo.effectiveDate'
                label='Effective date *'
              />
            </div>
            <div className='flex-1'></div>
          </div>
          <div className='mt-3 flex gap-9'>
            <FormTextInput
              name='statementMessageInfo.name'
              label='Name'
              required
              // TODO can be replaced with validator at useForm-level
              validators={{
                onChange: statementMessageInfoSchema.shape.name,
              }}
            />
            {/* todo: check schedule definition code is unique */}
            <FormTextInput
              name='statementMessageInfo.code'
              label='Code'
              required
              // TODO can be replaced with validator at useForm-level
              validators={{
                onChange: statementMessageInfoSchema.shape.code,
              }}
              placeholder='Max 5 digits'
              type='number'
              invalidChars={['e', 'E']}
              readonly={(field) =>
                !!isEditMode &&
                field.value != null &&
                field.value.length > 0 &&
                field.meta.isPristine
              }
            />
          </div>
        </div>
        <div className='mb-6 mt-3 flex w-full flex-col rounded-lg border bg-white p-6'>
          <div className='font-semibold'>Statement message</div>
          <div className='flex text-zinc-500'>Body Text</div>
          <Editor
            initialValue={defaultValues.statementMessageInfo.message}
            onChange={handleEditorChange}
            placeholder='Enter your message here'
          />
        </div>
      </div>
      <div className='flex justify-end gap-4 border-t bg-white px-12 py-10'>
        {children}
      </div>
    </form>
  )
}
