import { useEffect } from 'react'
import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext'
import { $getRoot, $createParagraphNode, $createTextNode } from 'lexical'
import { $generateNodesFromDOM } from '@lexical/html'

const InitialValuePlugin: React.FC<{
  initialValue: string
  isHtml: boolean
}> = ({ initialValue, isHtml }) => {
  const [editor] = useLexicalComposerContext()
  useEffect(() => {
    if (!initialValue) {
      return
    }
    editor.update(() => {
      const root = $getRoot()
      root.clear()
      if (isHtml) {
        const parser = new DOMParser()
        const dom = parser.parseFromString(initialValue, 'text/html')
        const nodes = $generateNodesFromDOM(editor, dom)
        root.append(...nodes)
      } else {
        const paragraph = $createParagraphNode()
        const textNode = $createTextNode(initialValue)
        paragraph.append(textNode)
        root.append(paragraph)
      }
    })
  }, [editor, initialValue, isHtml])
  return null
}

export default InitialValuePlugin
