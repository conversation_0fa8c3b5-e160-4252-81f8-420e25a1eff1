'use client'

import {
  InitialConfigType,
  LexicalComposer,
} from '@lexical/react/LexicalComposer'
import { RichTextPlugin } from '@lexical/react/LexicalRichTextPlugin'
import { ContentEditable } from '@lexical/react/LexicalContentEditable'
import { OnChangePlugin } from '@lexical/react/LexicalOnChangePlugin'
import { EditorState, LexicalEditor } from 'lexical'
import { LexicalErrorBoundary } from '@lexical/react/LexicalErrorBoundary'
import { ListPlugin } from '@lexical/react/LexicalListPlugin'
import { ListItemNode, ListNode } from '@lexical/list'

import PlaceHolder from './PlaceHolder'
import FormattingToolbar from './Toolbar'
import InitialValuePlugin from './InitialValuePlugin'
import { useState } from 'react'

export interface EditorProps {
  onChange: (html: string, error: boolean) => void
  initialValue: string
  placeholder?: string
}

export default function Editor({
  onChange,
  initialValue,
  placeholder,
}: React.PropsWithChildren<EditorProps>) {
  const [charCount, setCharCount] = useState(initialValue.length)

  const initConfig: InitialConfigType = {
    namespace: 'config-editor',
    onError: (err: Error) => console.error(err),
    theme: {
      text: {
        bold: 'font-bold',
        italic: 'italic',
        underline: 'underline',
      },
      list: {
        ul: 'list-disc ml-5',
        ol: 'list-decimal ml-5',
        listitem: 'mb-1',
      },
    },
    nodes: [ListNode, ListItemNode],
  }

  const handleChange = (editorState: EditorState, editor: LexicalEditor) => {
    editorState.read(() => {
      const root = editor._rootElement
      const html = root?.innerHTML ?? ''

      const count = html.length
      setCharCount(count)
      onChange(html, count > 600)
    })
  }

  return (
    <div className='mt-3 rounded-lg border border-gray-300'>
      <LexicalComposer initialConfig={initConfig}>
        <div className='flex flex-col'>
          <FormattingToolbar />
          <div className='relative'>
            <RichTextPlugin
              contentEditable={
                <ContentEditable className='min-h-[150px] px-3 py-4 outline-none focus:outline-none' />
              }
              placeholder={<PlaceHolder placeholderText={placeholder ?? ''} />}
              ErrorBoundary={LexicalErrorBoundary}
            />
          </div>
          <OnChangePlugin onChange={handleChange} />
          <ListPlugin />
          <InitialValuePlugin initialValue={initialValue} isHtml={true} />
          <div
            className={`px-3 py-1 text-sm ${charCount > 600 ? 'text-red-500' : 'text-gray-500'}`}
          >
            {`${charCount} / 600 characters`}
            {charCount > 600 && '   -   Character limit exceeded'}
          </div>
        </div>
      </LexicalComposer>
    </div>
  )
}
