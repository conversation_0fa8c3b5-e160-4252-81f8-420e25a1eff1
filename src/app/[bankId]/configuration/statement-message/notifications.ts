import { StatementMessageMutation } from './mutations'
import { Tag } from '@/lib/unions/Union'
import { withNotifications } from '@/components/Notifications'

export const messages: { [T in Tag<StatementMessageMutation>]: string } = {
  '/addStatementMessage': 'Statement Message successfully created.',
  '/updateStatementMessage': 'Statement Message successfully updated.',
}

export const notifications = withNotifications(messages)
