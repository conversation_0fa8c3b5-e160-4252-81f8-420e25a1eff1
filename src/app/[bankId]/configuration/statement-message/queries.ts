import { revenueConnectClient } from '@/api/revenueConnectClient'
import { defineQueries, defineQuery } from '@/lib/state/defineQuery'
import { withMetrics } from '@/lib/middleware/withMetrics'

const getStatementMessagesByEffectiveDate = defineQuery(
  revenueConnectClient,
  '/listStatementMessagesByEffectiveDate',
  (payload) => [payload.effectiveDate],
)

const getStatementMessageByCode = defineQuery(
  revenueConnectClient,
  '/getStatementMessageByCode',
  (payload) => [payload.code],
)

export const statementMessageQueries = defineQueries(
  [getStatementMessagesByEffectiveDate, getStatementMessageByCode],
  withMetrics(),
)
