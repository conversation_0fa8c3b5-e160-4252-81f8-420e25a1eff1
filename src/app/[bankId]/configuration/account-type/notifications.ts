import { AccountTypeMutation } from './mutations'
import { Tag } from '@/lib/unions/Union'
import { withNotifications } from '@/components/Notifications'

export const messages: { [T in Tag<AccountTypeMutation>]: string } = {
  '/createAccountType': 'Account Type successfully created.',
  '/updateAccountType': 'Account Type successfully updated.',
}

export const notifications = withNotifications(messages)
