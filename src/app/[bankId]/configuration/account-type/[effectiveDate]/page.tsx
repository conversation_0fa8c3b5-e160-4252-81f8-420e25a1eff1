'use client'
import { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { z } from 'zod'
import { ColumnDef, ColumnFiltersState } from '@tanstack/react-table'
import { ChevronRightIcon } from '@heroicons/react/24/outline'
import { useRouter } from 'next/navigation'

import { Button } from '@/components/Button'
import { MonthPicker } from '@/components/Filter/MonthPicker'
import { SearchBar } from '@/components/SearchBar'
import { SortedTable } from '@/components/Table/SortedTable'
import { RowPopover } from '../../_components/RowPopover'

import { routeTo, useRoute } from '../../routing'
import { unwrap } from '@/lib/unions/unwrap'
import { apiToFormSchemas } from '@/api/apiToFormSchema'
import { formatToServerString, formatToMonthYearFromDate } from '@/lib/date'
import { accountTypeQueries } from '../queries'

export default function AllAccountTypes() {
  const [searchText, setSearchText] = useState('')
  const columnFilters: ColumnFiltersState = [
    { id: 'description', value: searchText },
  ]
  const [numVisibleAccountTypes, setNumVisibleAccountTypes] = useState(0)

  const router = useRouter()
  const route = unwrap(
    useRoute(),
    '/configuration/account-type/[effectiveDate]',
  )!

  const payload = {
    effectiveDate: formatToServerString(route.params.effectiveDate),
  }
  const { data, status, error } = useQuery(
    accountTypeQueries('/getAccountTypes', payload),
  )

  if (status === 'pending') return 'Loading...'
  if (!data || error) {
    return <div>404 Not Found</div>
  }

  const { accountType: accountTypeType } = apiToFormSchemas
  const accountTypes = data?.map((item) => {
    return accountTypeType.parse(item)
  })
  const handleAddAccountTypeClick = () => {
    router.push(
      routeTo('/configuration/account-type/[effectiveDate]/add', route.params),
    )
  }

  const columns: ColumnDef<z.infer<typeof accountTypeType>>[] = [
    {
      header: 'Name',
      accessorKey: 'description',
    },
    {
      header: 'Code',
      accessorKey: 'accountTypeCode',
    },
    {
      header: '',
      accessorKey: 'none',
      cell: ({
        row: {
          original: { accountTypeCode },
        },
      }) => (
        <RowPopover
          className='pr-4'
          onEdit={() => {
            router.push(
              routeTo(
                '/configuration/account-type/[effectiveDate]/edit/[accountTypeCode]',
                {
                  ...route.params,
                  effectiveDate: formatToMonthYearFromDate(
                    route.params.effectiveDate,
                  ),
                  accountTypeCode,
                },
              ),
            )
          }}
        />
      ),
      meta: { className: 'basis-28 shrink-0 justify-end px-2' },
    },
  ]

  return (
    <div className='flex min-h-0 flex-1 flex-col py-6 pl-12 pr-3'>
      <div className='flex items-center text-zinc-500'>
        Configurations
        <ChevronRightIcon className='h-4 w-4 text-zinc-400' />
        <span className='text-lg font-semibold text-black'>Account Type</span>
      </div>
      <div className='mt-12 flex min-h-0 w-full flex-auto flex-grow flex-col rounded-lg border bg-white p-6'>
        <div className='flex w-full items-center justify-between'>
          <div className=''>
            <div className='text-lg font-semibold'>Account type</div>
            <div className='text-zinc-400'>
              Add or view account type details
            </div>
          </div>
          <Button className='btn' onClick={handleAddAccountTypeClick}>
            Add account type
          </Button>
        </div>
        <div className='mt-8 flex min-h-0 flex-auto flex-col'>
          <div className='flex items-center'>
            <MonthPicker
              title='Effective date'
              prefix='View data effective on'
              showIcon={true}
              onDateChange={(effectiveDate) => {
                router.push(
                  routeTo('/configuration/account-type/[effectiveDate]', {
                    ...route.params,
                    effectiveDate,
                  }),
                )
              }}
              initialDate={route.params.effectiveDate}
            />
            <div className='flex flex-auto items-center justify-end gap-4'>
              <SearchBar
                className='w-80'
                defaultLabel='Search account type'
                value={searchText}
                onValueChange={setSearchText}
              />
              <p className='text-sm text-zinc-400'>
                {numVisibleAccountTypes} account type
                {numVisibleAccountTypes > 1 && 's'}
              </p>
            </div>
          </div>
          <div className='mt-4 flex min-h-0 flex-col'>
            <SortedTable
              data={accountTypes}
              columns={columns}
              columnFilters={columnFilters}
              onVisibleRowsChanged={(rows) =>
                setNumVisibleAccountTypes(rows.length)
              }
              initialSortingState={[{ desc: false, id: 'description' }]}
              handleRowDoubleClick={({ original: { accountTypeCode } }) =>
                router.push(
                  routeTo(
                    '/configuration/account-type/[effectiveDate]/view/[accountTypeCode]',
                    {
                      ...route.params,
                      accountTypeCode,
                      effectiveDate: formatToMonthYearFromDate(
                        route.params.effectiveDate,
                      ),
                    },
                  ),
                )
              }
            />
          </div>
        </div>
      </div>
    </div>
  )
}
