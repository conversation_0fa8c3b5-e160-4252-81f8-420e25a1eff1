'use client'

import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { useQuery } from '@tanstack/react-query'
import { ChevronRightIcon } from '@heroicons/react/24/outline'

import { Button } from '@/components/Button'
import {
  DetailsSection,
  DetailsSectionTitle,
  DetailsSectionItemsRow,
  DetailsSectionItem,
} from '@/components/DetailsSection'
import { VersionTimeline } from '@/app/[bankId]/configuration/_components/VersionTimeline'

import { routeTo, useRoute } from '../../../../routing'
import { unwrap } from '@/lib/unions/unwrap'
import { apiToFormSchemas } from '@/api/apiToFormSchema'
import { formatToMonthYearFromDate, formatToServerString } from '@/lib/date'
import { accountTypeQueries } from '../../../queries'
import { statementFormatPlanQueries } from '@/app/[bankId]/configuration/statement-formats/queries'
import { statementMessageQueries } from '@/app/[bankId]/configuration/statement-message/queries'
import { investableBalanceQueries } from '@/app/[bankId]/configuration/investable-balance/queries'
import { earningsCreditQueries } from '@/app/[bankId]/configuration/earnings-credit/queries'
import { requiredBalanceQueries } from '@/app/[bankId]/configuration/required-balance/queries'
import { reserveRequirementQueries } from '@/app/[bankId]/configuration/reserve-requirements/queries'
import { analysisResultOptionsQueries } from '@/app/[bankId]/configuration/analysis-result-options/queries'
import { scheduleDefinitionQueries } from '@/app/[bankId]/configuration/schedule-definition/queries'
import { getNameAndCodeString } from '@/app/[bankId]/configuration/helpers'

export default function ViewAccountType() {
  const router = useRouter()
  const route = unwrap(
    useRoute(),
    '/configuration/account-type/[effectiveDate]/view/[accountTypeCode]',
  )!

  const effectiveDate = route.params.effectiveDate
  const accountTypeCode = route.params.accountTypeCode
  const payload = {
    code: route.params.accountTypeCode,
    effectiveDate: formatToServerString(effectiveDate),
  }
  const { data, status, error } = useQuery(
    accountTypeQueries('/getAccountType', payload),
  )
  const { data: accountTypeTimeline, error: accountTypeTimelineError } =
    useQuery(
      accountTypeQueries('/getAccountTypeTimeline', {
        /**
         * TODO: the /getAccountTypeTimeline API currently requires codes to be
         * prepended with `TEMPLATE-`.
         * If changed, this needs to be updated along with the mutation's cache invalidation
         */
        code: `TEMPLATE-${accountTypeCode}`,
      }),
    )

  /**
   * Fetch all related configs based on the effective date in order to get names
   */
  const { data: statementFormats, error: statementFormatsErrors } = useQuery(
    statementFormatPlanQueries('/getStatementFormatPlans', {
      effectiveDate: formatToServerString(effectiveDate),
    }),
  )
  const { data: statementMessages, error: statementMessagesErrors } = useQuery(
    statementMessageQueries('/listStatementMessagesByEffectiveDate', {
      effectiveDate: formatToServerString(effectiveDate),
    }),
  )
  const { data: scheduleDefinitions, error: scheduleDefinitionsErrors } =
    useQuery(
      scheduleDefinitionQueries('/getCycleDefinitions', {
        effectiveDate: formatToServerString(effectiveDate),
      }),
    )
  const { data: investableBalances, error: investableBalancesErrors } =
    useQuery(
      investableBalanceQueries('/getInvestableBalanceDefinitions', {
        effectiveDate: formatToServerString(effectiveDate),
      }),
    )
  const { data: earningsCredits, error: earningsCreditsErrors } = useQuery(
    earningsCreditQueries('/getEarningsCreditDefinitions', {
      effectiveDate: formatToServerString(effectiveDate),
    }),
  )
  const { data: requiredBalances, error: requiredBalancesErrors } = useQuery(
    requiredBalanceQueries('/getRequiredBalanceDefinitions', {
      effectiveDate: formatToServerString(effectiveDate),
    }),
  )
  const { data: reserveRequirements, error: reserveRequirementsErrors } =
    useQuery(
      reserveRequirementQueries('/getReserveRequirementDefinitions', {
        effectiveDate: formatToServerString(effectiveDate),
      }),
    )
  const { data: analysisResultOptions, error: analysisResultOptionsErrors } =
    useQuery(
      analysisResultOptionsQueries(
        '/listAnalysisResultOptionsByEffectiveDate',
        {
          effectiveDate: formatToServerString(effectiveDate),
        },
      ),
    )

  if (status === 'pending') return 'Loading...'
  if (!data || error || !accountTypeTimeline || accountTypeTimelineError) {
    return <div>404 Not Found</div>
  }

  const { accountType: accountTypeSchema } = apiToFormSchemas

  const parsedAccountTypes = accountTypeSchema.parse(data)
  const accountType = {
    ...parsedAccountTypes,
    effectiveDate: formatToMonthYearFromDate(parsedAccountTypes.effectiveDate),
  }

  const parsedAccountTypesTimeline = accountTypeTimeline
    .map((item) => {
      const parsed = accountTypeSchema.parse(item)
      return {
        ...parsed,
        code: parsed.accountTypeCode,
        effectiveDate: formatToMonthYearFromDate(parsed.effectiveDate),
      }
    })
    .sort(
      (a, b) =>
        new Date(b.effectiveDate).getDate() -
        new Date(a.effectiveDate).getDate(),
    )
  const statementFormatLabel = getNameAndCodeString(
    statementFormats ?? [],
    accountType.statementFormatPlanCode,
  )
  const statementMessageLabel = getNameAndCodeString(
    statementMessages ?? [],
    accountType.statementMessagePlanCode,
  )
  const statementScheduleLabel = getNameAndCodeString(
    scheduleDefinitions ?? [],
    accountType.statementCyclePlanCode,
  )
  const investableBalanceLabel = getNameAndCodeString(
    investableBalances ?? [],
    accountType.investableBalanceDefinitionCode,
  )
  const earningsCreditLabel = getNameAndCodeString(
    earningsCredits ?? [],
    accountType.earningsCreditDefinitionCode,
  )
  const requiredBalanceLabel = getNameAndCodeString(
    requiredBalances ?? [],
    accountType.balanceRequirementDefinitionCode,
  )
  const reserveRequirementLabel = getNameAndCodeString(
    reserveRequirements ?? [],
    accountType.reserveRequirementDefinitionCode,
  )
  const settlementScheduleLabel = getNameAndCodeString(
    scheduleDefinitions ?? [],
    accountType.settlementCyclePlanCode,
  )
  const analysisResultOptionLabel = getNameAndCodeString(
    analysisResultOptions ?? [],
    accountType.analysisResultOptionsPlanCode,
  )

  const onEditClick = () => {
    router.push(
      routeTo(
        '/configuration/account-type/[effectiveDate]/edit/[accountTypeCode]',
        {
          ...route.params,
          accountTypeCode: accountType.accountTypeCode,
          effectiveDate: accountType.effectiveDate,
        },
      ),
    )
  }

  const linkFormatter = (effectiveDate: string, accountTypeCode: string) => {
    return routeTo(
      '/configuration/account-type/[effectiveDate]/view/[accountTypeCode]',
      { ...route.params, accountTypeCode, effectiveDate },
    )
  }

  return (
    <div className='w-full overflow-hidden overflow-y-scroll px-8 py-10'>
      <div className='flex items-center justify-between'>
        <div className='flex items-center gap-2'>
          <Link
            href={routeTo(
              '/configuration/account-type/[effectiveDate]',
              route.params,
            )}
            className='font-bold'
          >
            ...
          </Link>
          <ChevronRightIcon className='h-4 w-4 text-zinc-400' />
          <div className='text-md text-zinc-500'>
            <Link
              href={routeTo(
                '/configuration/account-type/[effectiveDate]',
                route.params,
              )}
              className='font-bold'
            >
              Account type
            </Link>
          </div>
          <ChevronRightIcon className='h-4 w-4 text-zinc-400' />
          <div className='text-lg'>{accountType.description}</div>
          <div className='text-sm text-zinc-500'>
            {accountType.accountTypeCode}
          </div>
        </div>
        <Button className='btn-primary text-white' onClick={onEditClick}>
          Edit
        </Button>
      </div>
      <div className='mt-8 flex gap-4'>
        <div className='flex grow flex-col'>
          <DetailsSection>
            <DetailsSectionTitle>Account type information</DetailsSectionTitle>
            <DetailsSectionItemsRow>
              <DetailsSectionItem
                label={'Effective date *'}
                info={accountType.effectiveDate}
              />
            </DetailsSectionItemsRow>
            <DetailsSectionItemsRow>
              <DetailsSectionItem
                label={'Name *'}
                info={accountType.description}
              />
              <DetailsSectionItem
                label={'Code *'}
                info={accountType.accountTypeCode}
              />
            </DetailsSectionItemsRow>
          </DetailsSection>
          <DetailsSection className='mt-3'>
            <DetailsSectionTitle>Statement plans</DetailsSectionTitle>
            <DetailsSectionItemsRow>
              <DetailsSectionItem
                label={'Statement format *'}
                info={statementFormatLabel}
              />
              <DetailsSectionItem
                label={'Statement message'}
                info={statementMessageLabel}
              />
            </DetailsSectionItemsRow>
            <DetailsSectionItemsRow>
              <DetailsSectionItem
                label={'Statement schedule *'}
                info={statementScheduleLabel}
              />
            </DetailsSectionItemsRow>
          </DetailsSection>
          <DetailsSection className='mt-3'>
            <DetailsSectionTitle>Analysis plans</DetailsSectionTitle>
            <DetailsSectionItemsRow>
              <DetailsSectionItem
                label={'Investable balance *'}
                info={investableBalanceLabel}
              />
              <DetailsSectionItem
                label={'Earnings credit'}
                info={earningsCreditLabel}
              />
            </DetailsSectionItemsRow>
            <DetailsSectionItemsRow>
              <DetailsSectionItem
                label={'Required balance *'}
                info={requiredBalanceLabel}
              />
              <DetailsSectionItem
                label={'Reserve requirement'}
                info={reserveRequirementLabel}
              />
            </DetailsSectionItemsRow>
          </DetailsSection>
          <DetailsSection className='mt-3'>
            <DetailsSectionTitle>Results plans</DetailsSectionTitle>
            <DetailsSectionItemsRow>
              <DetailsSectionItem
                label={'Settlement schedule *'}
                info={settlementScheduleLabel}
              />
              <DetailsSectionItem
                label={'Analysis result options *'}
                info={analysisResultOptionLabel}
              />
            </DetailsSectionItemsRow>
          </DetailsSection>
        </div>
        <div className='flex max-w-72 flex-col rounded-lg border bg-white p-6'>
          <VersionTimeline
            versions={parsedAccountTypesTimeline}
            currentEffectiveDate={route.params.effectiveDate}
            linkFormatter={linkFormatter}
          />
        </div>
      </div>
    </div>
  )
}
