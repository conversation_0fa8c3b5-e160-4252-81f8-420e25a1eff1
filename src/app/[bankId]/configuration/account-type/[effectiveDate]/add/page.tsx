'use client'

import { useRouter } from 'next/navigation'
import { useMutation } from '@tanstack/react-query'

import { Button } from '@/components/Button'
import UpdateAccountTypeForm from '../../_components/UpdateAccountTypeForm'
import { UpdateAccountTypeFormState } from '../../_components/UpdateAccountTypeFormTypes'

import { unwrap } from '@/lib/unions/unwrap'
import { routeTo, useRoute } from '../../../routing'
import { accountTypeMutation } from '../../mutations'
import { formToApiSchemas } from '@/api/formToApiSchema'
import { formatToMonthYearFromDate, formatToServerString } from '@/lib/date'

export default function AddAccountType() {
  const router = useRouter()
  const route = unwrap(
    useRoute(),
    '/configuration/account-type/[effectiveDate]/add',
  )!

  const defaultValues: UpdateAccountTypeFormState = {
    accountTypeInfo: {
      accountTypeCode: '',
      description: '',
      effectiveDate: formatToMonthYearFromDate(new Date()),
    },
    statementPlans: {
      statementFormatPlanCode: '',
      statementMessagePlanCode: '',
      statementCyclePlanCode: '',
    },
    analysisPlans: {
      investableBalanceDefinitionCode: '',
      earningsCreditDefinitionCode: '',
      balanceRequirementDefinitionCode: '',
      reserveRequirementDefinitionCode: '',
    },
    resultPlans: {
      settlementCyclePlanCode: '',
      analysisResultOptionsPlanCode: '',
    },
  }
  const addAccountType = useMutation(
    accountTypeMutation('/createAccountType', {
      onSuccess: (_, request) => {
        router.push(
          routeTo(
            '/configuration/account-type/[effectiveDate]/view/[accountTypeCode]',
            {
              ...route.params,
              effectiveDate: formatToMonthYearFromDate(request.effectiveDate!),
              accountTypeCode: request.accountTypeCode!,
            },
          ),
        )
      },
    }),
  )
  const serializeForm = (formState: UpdateAccountTypeFormState) => {
    const { accountType } = formToApiSchemas
    const { accountTypeInfo, statementPlans, analysisPlans, resultPlans } =
      formState
    return accountType.parse({
      ...accountTypeInfo,
      effectiveDate: formatToServerString(accountTypeInfo.effectiveDate),
      ...statementPlans,
      statementMessagePlanCode:
        statementPlans.statementMessagePlanCode ?
          statementPlans.statementMessagePlanCode
        : null,
      ...analysisPlans,
      earningsCreditDefinitionCode:
        analysisPlans.earningsCreditDefinitionCode ?
          analysisPlans.earningsCreditDefinitionCode
        : null,
      reserveRequirementDefinitionCode:
        analysisPlans.reserveRequirementDefinitionCode ?
          analysisPlans.reserveRequirementDefinitionCode
        : null,
      ...resultPlans,
      /**
       * TODO: Interest-Requirement is not scope for MVP, however,
       * the BE API contract expects it as a field in the schema
       * Update interestRequirementDefinitionCode once Interest-Requirement is complete
       */
      interestRequirementDefinitionCode: null,
    })
  }

  return (
    <div className='w-full overflow-hidden overflow-y-scroll pt-12'>
      <div className='text-md ml-8 font-medium'>Add account type</div>
      <UpdateAccountTypeForm
        defaultValues={defaultValues}
        onSubmit={(formState) => {
          addAccountType.mutate(serializeForm(formState))
        }}
      >
        <Button className='btn w-60' onClick={() => router.back()}>
          Cancel
        </Button>

        <Button type='submit' className='btn-primary w-60'>
          Save
        </Button>
      </UpdateAccountTypeForm>
    </div>
  )
}
