'use client'

import { useRouter } from 'next/navigation'
import { useQuery, useMutation } from '@tanstack/react-query'

import { But<PERSON> } from '@/components/Button'
import UpdateAccountTypeForm from '../../../_components/UpdateAccountTypeForm'
import { UpdateAccountTypeFormState } from '../../../_components/UpdateAccountTypeFormTypes'

import { formatToMonthYearFromDate, formatToServerString } from '@/lib/date'
import { unwrap } from '@/lib/unions/unwrap'
import { routeTo, useRoute } from '../../../../routing'
import { accountTypeMutation } from '../../../mutations'
import { formToApiSchemas } from '@/api/formToApiSchema'
import { accountTypeQueries } from '../../../queries'
import { apiToFormSchemas } from '@/api/apiToFormSchema'

export default function EditAccountType() {
  const router = useRouter()
  const route = unwrap(
    useRoute(),
    '/configuration/account-type/[effectiveDate]/edit/[accountTypeCode]',
  )!

  const effectiveDate = route.params.effectiveDate
  const payload = {
    code: route.params.accountTypeCode,
    effectiveDate: formatToServerString(effectiveDate),
  }
  const { data, status, error } = useQuery(
    accountTypeQueries('/getAccountType', payload),
  )

  const updateAccountType = useMutation(
    accountTypeMutation('/updateAccountType', {
      onSuccess: (_, request) => {
        router.push(
          routeTo(
            '/configuration/account-type/[effectiveDate]/view/[accountTypeCode]',
            {
              ...route.params,
              effectiveDate: formatToMonthYearFromDate(request.effectiveDate!),
              accountTypeCode: request.accountTypeCode!,
            },
          ),
        )
      },
    }),
  )
  if (status === 'pending') return 'Loading...'
  if (!data || error) {
    return <div>404 Not Found</div>
  }
  const { accountType: accountTypeSchema } = apiToFormSchemas
  const accountType = accountTypeSchema.parse(data)

  const defaultValues: UpdateAccountTypeFormState = {
    accountTypeInfo: {
      accountTypeCode: accountType.accountTypeCode,
      description: accountType.description,
      effectiveDate: formatToMonthYearFromDate(accountType.effectiveDate),
    },
    statementPlans: {
      statementFormatPlanCode: accountType.statementFormatPlanCode,
      statementMessagePlanCode: accountType.statementMessagePlanCode || '',
      statementCyclePlanCode: accountType.statementCyclePlanCode,
    },
    analysisPlans: {
      investableBalanceDefinitionCode:
        accountType.investableBalanceDefinitionCode,
      earningsCreditDefinitionCode:
        accountType.earningsCreditDefinitionCode || '',
      balanceRequirementDefinitionCode:
        accountType.balanceRequirementDefinitionCode,
      reserveRequirementDefinitionCode:
        accountType.reserveRequirementDefinitionCode || '',
    },
    resultPlans: {
      settlementCyclePlanCode: accountType.settlementCyclePlanCode,
      analysisResultOptionsPlanCode: accountType.analysisResultOptionsPlanCode,
    },
  }
  const serializeForm = (formState: UpdateAccountTypeFormState) => {
    const { accountType } = formToApiSchemas
    const { accountTypeInfo, statementPlans, analysisPlans, resultPlans } =
      formState
    return accountType.parse({
      ...accountTypeInfo,
      effectiveDate: formatToServerString(accountTypeInfo.effectiveDate),
      ...statementPlans,
      statementMessagePlanCode:
        statementPlans.statementMessagePlanCode ?
          statementPlans.statementMessagePlanCode
        : null,
      ...analysisPlans,
      earningsCreditDefinitionCode:
        analysisPlans.earningsCreditDefinitionCode ?
          analysisPlans.earningsCreditDefinitionCode
        : null,
      reserveRequirementDefinitionCode:
        analysisPlans.reserveRequirementDefinitionCode ?
          analysisPlans.reserveRequirementDefinitionCode
        : null,
      ...resultPlans,
      /**
       * TODO: Interest-Requirement is not scope for MVP, however,
       * the BE API contract expects it as a field
       * Update interestRequirementDefinitionCode once Interest-Requirement is complete
       */
      interestRequirementDefinitionCode: null,
    })
  }
  return (
    <div className='w-full overflow-hidden overflow-y-scroll pt-12'>
      <div className='text-md ml-8 font-medium'>Edit account type</div>
      <UpdateAccountTypeForm
        defaultValues={defaultValues}
        onSubmit={(formState) => {
          updateAccountType.mutate(serializeForm(formState))
        }}
        isEditMode
      >
        <Button
          className='btn w-60'
          onClick={() => {
            router.back()
          }}
        >
          Cancel
        </Button>

        <Button type='submit' className='btn-primary w-60'>
          Save
        </Button>
      </UpdateAccountTypeForm>
    </div>
  )
}
