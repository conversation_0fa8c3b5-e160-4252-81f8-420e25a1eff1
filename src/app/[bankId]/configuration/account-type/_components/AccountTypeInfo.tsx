'use client'

import { useFormTextInput } from '@/components/Form/useFormTextInput'
import { useFormMonthPicker } from '@/components/Form/useFormMonthPicker'
import {
  UpdateAccountTypeFormApi,
  UpdateAccountTypeFormState,
  accountTypeInfoSchema,
} from './UpdateAccountTypeFormTypes'

interface AccountTypeInfoProps {
  form: UpdateAccountTypeFormApi
  isEditMode?: boolean
}
export default function AccountTypeInfo({
  form,
  isEditMode,
}: AccountTypeInfoProps) {
  const FormInput = useFormTextInput<UpdateAccountTypeFormState>({ form })
  const FormMonthPicker = useFormMonthPicker<UpdateAccountTypeFormState>({
    form,
  })
  // todo: make call to /getAccountTypeByCode. If list is empty, code is unique.
  return (
    <div className='mt-10 flex w-full flex-col rounded-lg border bg-white p-6'>
      <div className='font-semibold'>Account type information</div>
      <div className='mt-3 flex gap-9'>
        <div className='flex flex-1 flex-col gap-2'>
          <FormMonthPicker
            name='accountTypeInfo.effectiveDate'
            label='Effective date *'
          />
        </div>
        <div className='flex-1'></div>
      </div>
      <div className='mt-3 flex gap-9'>
        <FormInput
          name='accountTypeInfo.description'
          label='Name'
          required
          validators={{
            onChange: accountTypeInfoSchema.shape.description,
          }}
        />
        {/* TODO: check account-type code is unique */}
        <FormInput
          name='accountTypeInfo.accountTypeCode'
          label='Code'
          required
          validators={{
            onChange: accountTypeInfoSchema.shape.accountTypeCode,
          }}
          placeholder='Max 5 digits'
          type='number'
          invalidChars={['e', 'E']}
          readonly={(field) =>
            field.value != null &&
            field.value.length > 0 &&
            field.meta.isPristine &&
            !!isEditMode
          }
        />
      </div>
    </div>
  )
}
