'use client'
import { useForm, Form<PERSON>pi, ReactFormApi } from '@tanstack/react-form'
import AccountTypeInfo from './AccountTypeInfo'
import StatementPlans from './StatementPlans'
import AnalysisPlans from './AnalysisPlans'
import ResultPlans from './ResultPlans'
import { UpdateAccountTypeFormState } from './UpdateAccountTypeFormTypes'

interface UpdateAccountTypeFormProps {
  defaultValues: UpdateAccountTypeFormState
  onSubmit: (state: UpdateAccountTypeFormState) => void
  isEditMode?: boolean
}

export default function UpdateAccountTypeForm({
  defaultValues,
  onSubmit,
  children,
  isEditMode,
}: React.PropsWithChildren<UpdateAccountTypeFormProps>) {
  const form = useForm({
    defaultValues,
    onSubmit: ({ value }) => {
      onSubmit(value)
    },
  })

  // TODO can add `validators` prop with `onChange` pointing to composite zod schema
  // so we don't need to define `validators.onChange` on every form field.
  return (
    <form
      className='flex max-h-full flex-auto flex-col'
      onSubmit={async (event) => {
        form.reset(form.state.values)
        event.preventDefault()
        event.stopPropagation()
        await form.handleSubmit()
      }}
    >
      <div className='flex flex-auto flex-col gap-2 overflow-y-auto px-8'>
        <AccountTypeInfo form={form} isEditMode={isEditMode} />
        <StatementPlans form={form} />
        <AnalysisPlans form={form} />
        <ResultPlans form={form} />
      </div>
      <div className='flex justify-end gap-4 border-t bg-white px-12 py-10'>
        {children}
      </div>
    </form>
  )
}
