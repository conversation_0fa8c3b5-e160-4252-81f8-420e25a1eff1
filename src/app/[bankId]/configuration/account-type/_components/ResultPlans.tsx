'use client'

import { useStore } from '@tanstack/react-form'
import { useQuery } from '@tanstack/react-query'

import { useFormSelect } from '@/components/Form/useFormSelect'
import { SelectOption } from '@/components/Input/Select'
import {
  UpdateAccountTypeFormApi,
  UpdateAccountTypeFormState,
  resultPlansSchema,
} from './UpdateAccountTypeFormTypes'

import { analysisResultOptionsQueries } from '../../analysis-result-options/queries'
import { scheduleDefinitionQueries } from '../../schedule-definition/queries'
import { formatToServerString } from '@/lib/date'
import { AnalysisResultOption, CycleDefinition } from '@/api/formToApiSchema'
import { useCodesAndRenderOption } from '../../helpers'

interface ResultPlansProps {
  form: UpdateAccountTypeFormApi
}
export default function ResultPlans({ form }: ResultPlansProps) {
  const Select = useFormSelect<UpdateAccountTypeFormState>({ form })
  const [effectiveDate] = useStore(form.store, (state) => [
    state.values.accountTypeInfo.effectiveDate,
  ])
  const {
    data: analysisResultOptions,
    status,
    error: analysisResultOptionsErrors,
  } = useQuery(
    analysisResultOptionsQueries('/listAnalysisResultOptionsByEffectiveDate', {
      effectiveDate: formatToServerString(effectiveDate),
    }),
  )
  const { data: settlementSchedules, error: settlementSchedulesErrors } =
    useQuery(
      scheduleDefinitionQueries('/getCycleDefinitions', {
        effectiveDate: formatToServerString(effectiveDate),
      }),
    )

  const [analysisResultOptionsCodes, renderanalysisResultOptions] =
    useCodesAndRenderOption(
      analysisResultOptions as AnalysisResultOption[],
      (analysisResultOption) => analysisResultOption.code,
      (analysisResultOption) =>
        `${analysisResultOption.name} ${analysisResultOption.code}`,
    )
  const [scheduleDefinitionCodes, renderScheduleDefinitionOptions] =
    useCodesAndRenderOption(
      settlementSchedules as CycleDefinition[],
      (settlementSchedule) => settlementSchedule.code,
      (settlementSchedule) =>
        `${settlementSchedule.description} ${settlementSchedule.code}`,
    )
  if (status === 'pending') return 'Loading...'
  if (!analysisResultOptions) {
    return <div>404 Not Found</div>
  }
  return (
    <div className='mt-3 flex w-full flex-col rounded-lg border bg-white p-6'>
      <div className='font-semibold'>Result plans</div>
      <div className='mt-3 flex gap-9'>
        <Select
          label='Settlement schedule'
          name='resultPlans.settlementCyclePlanCode'
          required
          options={scheduleDefinitionCodes}
          renderOption={renderScheduleDefinitionOptions}
          renderSelected={renderScheduleDefinitionOptions}
          validators={{
            onChange: resultPlansSchema.shape.settlementCyclePlanCode,
          }}
        />
        <Select
          label='Analysis result options'
          name='resultPlans.analysisResultOptionsPlanCode'
          required
          validators={{
            onChange: resultPlansSchema.shape.analysisResultOptionsPlanCode,
          }}
          options={analysisResultOptionsCodes}
          renderOption={renderanalysisResultOptions}
          renderSelected={renderanalysisResultOptions}
        />
      </div>
    </div>
  )
}
