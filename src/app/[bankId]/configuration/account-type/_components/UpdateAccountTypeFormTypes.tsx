import { z } from 'zod'
import { Form<PERSON>pi, ReactFormApi } from '@tanstack/react-form'

import { name, configCode } from '@/api/zodSchemas'

export const accountTypeInfoSchema = z.object({
  effectiveDate: z.string(),
  description: name,
  accountTypeCode: configCode,
})
type AccountTypeInfoState = z.infer<typeof accountTypeInfoSchema>

export const analysisPlansSchema = z.object({
  investableBalanceDefinitionCode: z.string().min(1, 'Required'),
  earningsCreditDefinitionCode: z.string(),
  balanceRequirementDefinitionCode: z.string().min(1, 'Required'),
  reserveRequirementDefinitionCode: z.string(),
})

type AnalysisPlansState = z.infer<typeof analysisPlansSchema>

export const resultPlansSchema = z.object({
  settlementCyclePlanCode: z.string().min(1, 'Required'),
  analysisResultOptionsPlanCode: z.string().min(1, 'Required'),
})

type ResultPlansState = z.infer<typeof resultPlansSchema>

export const statementPlansSchema = z.object({
  statementFormatPlanCode: z.string().min(1, 'Required'),
  statementMessagePlanCode: z.string(),
  statementCyclePlanCode: z.string().min(1, 'Required'),
})

type StatementPlansState = z.infer<typeof statementPlansSchema>

// TODO create composite zod schema instead (e.g., updateAccountTypeSchema) and infer this type from that schema.
// This new composite schema could also be used as onChange validator in useForm in UpdateAccountTypeForm to reduce
// need to define validators.onChange on every form field.
export type UpdateAccountTypeFormState = {
  accountTypeInfo: AccountTypeInfoState
  statementPlans: StatementPlansState
  analysisPlans: AnalysisPlansState
  resultPlans: ResultPlansState
}
export type UpdateAccountTypeFormApi =
  ReactFormApi<UpdateAccountTypeFormState> & FormApi<UpdateAccountTypeFormState>
