'use client'

import { useStore } from '@tanstack/react-form'
import { useQuery } from '@tanstack/react-query'

import { useFormSelect } from '@/components/Form/useFormSelect'
import {
  UpdateAccountTypeFormApi,
  UpdateAccountTypeFormState,
  statementPlansSchema,
} from './UpdateAccountTypeFormTypes'

import { statementFormatPlanQueries } from '../../statement-formats/queries'
import { statementMessageQueries } from '../../statement-message/queries'
import { scheduleDefinitionQueries } from '../../schedule-definition/queries'
import { formatToServerString } from '@/lib/date'
import { useCodesAndRenderOption } from '../../helpers'
import {
  StatementFormatPlan,
  StatementMessage,
  CycleDefinition,
} from '@/api/formToApiSchema'
interface StatementPlansProps {
  form: UpdateAccountTypeFormApi
}
export default function StatementPlans({ form }: StatementPlansProps) {
  const Select = useFormSelect<UpdateAccountTypeFormState>({ form })
  const [effectiveDate] = useStore(form.store, (state) => [
    state.values.accountTypeInfo.effectiveDate,
  ])

  const {
    data: statementFormats,
    status: statementFormatsStatus,
    error: statementFormatsErrors,
  } = useQuery(
    statementFormatPlanQueries('/getStatementFormatPlans', {
      effectiveDate: formatToServerString(effectiveDate),
    }),
  )
  const {
    data: statementMessages,
    status: statementMessagesStatus,
    error: statementMessagesErrors,
  } = useQuery(
    statementMessageQueries('/listStatementMessagesByEffectiveDate', {
      effectiveDate: formatToServerString(effectiveDate),
    }),
  )
  const {
    data: scheduleDefinitions,
    status: scheduleDefinitionsStatus,
    error: scheduleDefinitionsErrors,
  } = useQuery(
    scheduleDefinitionQueries('/getCycleDefinitions', {
      effectiveDate: formatToServerString(effectiveDate),
    }),
  )
  const [statementFormatCodes, renderStatementFormatCodes] =
    useCodesAndRenderOption(
      statementFormats as StatementFormatPlan[],
      (statementFormat) => statementFormat.code,
      (statementFormat) =>
        `${statementFormat.description} ${statementFormat.code}`,
    )
  const [statementMessageCodes, renderStatementMessageCodes] =
    useCodesAndRenderOption(
      statementMessages as StatementMessage[],
      (statementMessage) => statementMessage.code,
      (statementMessage) => `${statementMessage.name} ${statementMessage.code}`,
    )
  const [scheduleDefinitionCodes, renderScheduleDefinitionCodes] =
    useCodesAndRenderOption(
      scheduleDefinitions as CycleDefinition[],
      (scheduleDefinition) => scheduleDefinition.code,
      (scheduleDefinition) =>
        `${scheduleDefinition.description} ${scheduleDefinition.code}`,
    )

  if (
    statementFormatsStatus === 'pending' ||
    statementMessagesStatus === 'pending' ||
    scheduleDefinitionsStatus === 'pending'
  )
    return 'Loading...'
  if (!statementFormats || !statementMessages || !scheduleDefinitions) {
    return <div>404 Not Found</div>
  }
  return (
    <div className='mt-3 flex w-full flex-col rounded-lg border bg-white p-6'>
      <div className='font-semibold'>Statement plans</div>

      <div className='mt-3 flex gap-9'>
        <Select
          label='Statement format'
          name='statementPlans.statementFormatPlanCode'
          validators={{
            onChange: statementPlansSchema.shape.statementFormatPlanCode,
          }}
          options={statementFormatCodes}
          renderOption={renderStatementFormatCodes}
          renderSelected={renderStatementFormatCodes}
          required
        />
        <Select
          label='Statement message'
          name='statementPlans.statementMessagePlanCode'
          options={statementMessageCodes}
          renderOption={renderStatementMessageCodes}
          renderSelected={renderStatementMessageCodes}
        />
      </div>
      <div className='mt-3 flex gap-9'>
        <Select
          label='Statement schedule'
          name='statementPlans.statementCyclePlanCode'
          validators={{
            onChange: statementPlansSchema.shape.statementCyclePlanCode,
          }}
          options={scheduleDefinitionCodes}
          renderOption={renderScheduleDefinitionCodes}
          renderSelected={renderScheduleDefinitionCodes}
          required
        />
        <div className='flex-1'></div>
      </div>
    </div>
  )
}
