'use client'

import { useStore } from '@tanstack/react-form'
import { useQuery } from '@tanstack/react-query'

import { useFormSelect } from '@/components/Form/useFormSelect'
import {
  UpdateAccountTypeFormApi,
  UpdateAccountTypeFormState,
  analysisPlansSchema,
} from './UpdateAccountTypeFormTypes'

import { investableBalanceQueries } from '../../investable-balance/queries'
import { earningsCreditQueries } from '../../earnings-credit/queries'
import { requiredBalanceQueries } from '../../required-balance/queries'
import { reserveRequirementQueries } from '../../reserve-requirements/queries'
import {
  InvestableBalanceDefinition,
  EarningsCreditDefinition,
  BalanceRequirementDefinition,
  ReserveRequirementDefinition,
} from '@/api/formToApiSchema'
import { formatToServerString } from '@/lib/date'
import { useCodesAndRenderOption } from '../../helpers'

interface AnalysisPlansProps {
  form: UpdateAccountTypeFormApi
}
export default function AnalysisPlans({ form }: AnalysisPlansProps) {
  const Select = useFormSelect<UpdateAccountTypeFormState>({ form })
  const [effectiveDate] = useStore(form.store, (state) => [
    state.values.accountTypeInfo.effectiveDate,
  ])
  const { data: investableBalances, error: investableBalancesErrors } =
    useQuery(
      investableBalanceQueries('/getInvestableBalanceDefinitions', {
        effectiveDate: formatToServerString(effectiveDate),
      }),
    )
  const { data: earningsCredits, error: earningsCreditsErrors } = useQuery(
    earningsCreditQueries('/getEarningsCreditDefinitions', {
      effectiveDate: formatToServerString(effectiveDate),
    }),
  )
  const { data: requiredBalances, error: requiredBalancesErrors } = useQuery(
    requiredBalanceQueries('/getRequiredBalanceDefinitions', {
      effectiveDate: formatToServerString(effectiveDate),
    }),
  )
  const { data: reserveRequirements, error: reserveRequirementsErrors } =
    useQuery(
      reserveRequirementQueries('/getReserveRequirementDefinitions', {
        effectiveDate: formatToServerString(effectiveDate),
      }),
    )
  const [investableBalanceCodes, renderInvestableBalanceCodes] =
    useCodesAndRenderOption(
      investableBalances as InvestableBalanceDefinition[],
      (investableBalance) => investableBalance.code,
      (investableBalance) =>
        `${investableBalance.name} ${investableBalance.code}`,
    )
  const [earningsCreditCodes, renderEarningsCreditCodes] =
    useCodesAndRenderOption(
      earningsCredits as EarningsCreditDefinition[],
      (earningsCredit) => earningsCredit.code,
      (earningsCredit) =>
        `${earningsCredit.description} ${earningsCredit.code}`,
    )
  const [requiredBalanceCodes, renderRequiredBalanceCodes] =
    useCodesAndRenderOption(
      requiredBalances as BalanceRequirementDefinition[],
      (requiredBalance) => requiredBalance.code,
      (requiredBalance) => `${requiredBalance.name} ${requiredBalance.code}`,
    )
  const [reserveRequirementCodes, renderReserveRequirementCodes] =
    useCodesAndRenderOption(
      reserveRequirements as ReserveRequirementDefinition[],
      (reserveRequirement) => reserveRequirement.code,
      (reserveRequirement) =>
        `${reserveRequirement.name} ${reserveRequirement.code}`,
    )

  return (
    <div className='mt-3 flex w-full flex-col rounded-lg border bg-white p-6'>
      <div className='font-semibold'>Analysis plans</div>
      <div className='mt-3 flex gap-9'>
        <Select
          label='Investable balance'
          name='analysisPlans.investableBalanceDefinitionCode'
          required
          validators={{
            onChange: analysisPlansSchema.shape.investableBalanceDefinitionCode,
          }}
          options={investableBalanceCodes}
          renderOption={renderInvestableBalanceCodes}
          renderSelected={renderInvestableBalanceCodes}
        />
        <Select
          label='Earnings credit'
          name='analysisPlans.earningsCreditDefinitionCode'
          options={earningsCreditCodes}
          renderOption={renderEarningsCreditCodes}
          renderSelected={renderEarningsCreditCodes}
        />
      </div>
      <div className='mt-3 flex gap-9'>
        <Select
          label='Required balance'
          name='analysisPlans.balanceRequirementDefinitionCode'
          required
          options={requiredBalanceCodes}
          renderOption={renderRequiredBalanceCodes}
          renderSelected={renderRequiredBalanceCodes}
          validators={{
            onChange:
              analysisPlansSchema.shape.balanceRequirementDefinitionCode,
          }}
        />
        <Select
          label='Reserve requirement'
          name='analysisPlans.reserveRequirementDefinitionCode'
          options={reserveRequirementCodes}
          renderOption={renderReserveRequirementCodes}
          renderSelected={renderReserveRequirementCodes}
        />
      </div>
    </div>
  )
}
