import { withMetrics } from '@/lib/middleware/withMetrics'
import { compose } from '@/lib/functional/compose'
import { defineMutations, defineMutation } from '@/lib/state/defineMutation'
import { revenueConnectClient } from '@/api/revenueConnectClient'

import { accountTypeQueries } from './queries'
import { notifications } from './notifications'

const createAccountType = defineMutation(
  revenueConnectClient,
  '/createAccountType',
  {
    invalidate: (request) => [
      accountTypeQueries('/getAccountTypes', {
        effectiveDate: request.effectiveDate,
      }),
      accountTypeQueries('/getAccountType', {
        code: request.accountTypeCode,
        effectiveDate: request.effectiveDate,
      }),
      accountTypeQueries('/getAccountTypeTimeline', {
        code: `TEMPLATE-${request.accountTypeCode}`,
      }),
    ],
  },
)

const updateAccountType = defineMutation(
  revenueConnectClient,
  '/updateAccountType',
  {
    invalidate: (request) => [
      accountTypeQueries('/getAccountTypes', {
        effectiveDate: request.effectiveDate,
      }),
      accountTypeQueries('/getAccountType', {
        code: request.accountTypeCode,
        effectiveDate: request.effectiveDate,
      }),
      accountTypeQueries('/getAccountTypeTimeline', {
        code: `TEMPLATE-${request.accountTypeCode}`,
      }),
    ],
  },
)
export type AccountTypeMutation =
  | typeof createAccountType
  | typeof updateAccountType

export const accountTypeMutation = defineMutations(
  [createAccountType, updateAccountType],
  compose(notifications, withMetrics()),
)
