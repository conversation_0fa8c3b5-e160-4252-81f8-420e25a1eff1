import { revenueConnectClient } from '@/api/revenueConnectClient'
import { defineQueries, defineQuery } from '@/lib/state/defineQuery'
import { withMetrics } from '@/lib/middleware/withMetrics'

const getAccountType = defineQuery(
  revenueConnectClient,
  '/getAccountType',
  (payload) => [payload.code, payload.effectiveDate],
)

const getAccountTypesByEffectiveDate = defineQuery(
  revenueConnectClient,
  '/getAccountTypes',
  (payload) => [payload.effectiveDate],
)

const getAccountTypeTimeline = defineQuery(
  revenueConnectClient,
  '/getAccountTypeTimeline',
  (payload) => [payload.code],
)

export const accountTypeQueries = defineQueries(
  [getAccountType, getAccountTypesByEffectiveDate, getAccountTypeTimeline],
  withMetrics(),
)
