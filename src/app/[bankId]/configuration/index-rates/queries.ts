import { revenueConnectClient } from '@/api/revenueConnectClient'
import { defineQueries, defineQuery } from '@/lib/state/defineQuery'
import { withMetrics } from '@/lib/middleware/withMetrics'

const getIndexRatesByEffectiveDate = defineQuery(
  revenueConnectClient,
  '/listIndexRatesByEffectiveDate',
  (payload) => [payload.effectiveDate],
)

const getIndexRateByCode = defineQuery(
  revenueConnectClient,
  '/getIndexRatesByCode',
  (payload) => [payload.code],
)

export const indexRateQueries = defineQueries(
  [getIndexRatesByEffectiveDate, getIndexRateByCode],
  withMetrics(),
)
