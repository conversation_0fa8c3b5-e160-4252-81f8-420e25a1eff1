'use client'
import { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { useRouter } from 'next/navigation'
import { z } from 'zod'
import { ColumnDef, ColumnFiltersState } from '@tanstack/react-table'
import { ChevronRightIcon } from '@heroicons/react/24/outline'

import { Button } from '@/components/Button'
import { DateFilter } from '@/components/Filter/DateFilter'
import { SearchBar } from '@/components/SearchBar'
import { SortedTable } from '@/components/Table/SortedTable'
import { RowPopover } from '../../_components/RowPopover'

import { parseServerFormat, toServerFormat } from '@/lib/date'
import { unwrap } from '@/lib/unions/unwrap'
import { routeTo, useRoute } from '../../routing'
import { indexRateQueries } from '../queries'
import { apiToFormSchemas } from '@/api/apiToFormSchema'

export default function AllIndexRates() {
  const [searchText, setSearchText] = useState('')
  const [numVisibleIndexRates, setNumVisibleIndexRates] = useState(0)
  const columnFilters: ColumnFiltersState = [{ id: 'name', value: searchText }]

  const router = useRouter()
  const route = unwrap(
    useRoute(),
    '/configuration/index-rates/[effectiveDate]',
  )!

  const { data, status, error } = useQuery(
    indexRateQueries('/listIndexRatesByEffectiveDate', {
      effectiveDate: route.params.effectiveDate,
    }),
  )
  if (status === 'pending') return 'Loading...'
  if (!data || error) {
    return <div>404 Not Found</div>
  }

  const { indexRate: IndexRateType } = apiToFormSchemas
  const indexRates = data?.map((item) => {
    return IndexRateType.parse(item)
  })

  const handleAddIndexRateClick = () => {
    router.push(
      routeTo('/configuration/index-rates/[effectiveDate]/add', route.params),
    )
  }

  const columns: ColumnDef<z.infer<typeof IndexRateType>>[] = [
    {
      header: 'Name',
      accessorKey: 'name',
    },
    {
      header: 'Code',
      accessorKey: 'code',
    },
    {
      header: 'Rate',
      accessorKey: 'rate',
    },
    {
      header: '',
      accessorKey: 'none',
      cell: ({
        row: {
          original: { code, effectiveDate },
        },
      }) => (
        <RowPopover
          className='pr-4'
          onEdit={() => {
            router.push(
              routeTo(
                '/configuration/index-rates/[effectiveDate]/edit/[indexRateCode]',
                {
                  ...route.params,
                  effectiveDate: route.params.effectiveDate,
                  indexRateCode: code,
                },
              ),
            )
          }}
        />
      ),
      meta: { className: 'basis-28 shrink-0 justify-end px-2' },
    },
  ]

  return (
    <div className='flex min-h-0 flex-1 flex-col py-6 pl-12 pr-3'>
      <div className='flex items-center text-zinc-500'>
        Configurations
        <ChevronRightIcon className='h-4 w-4 text-zinc-400' />
        <span className='text-lg font-semibold text-black'>Index rates</span>
      </div>
      <div className='mt-12 flex min-h-0 w-full flex-auto flex-grow flex-col rounded-lg border bg-white p-6'>
        <div className='flex w-full items-center justify-between'>
          <div className=''>
            <div className='text-lg font-semibold'>Index rates</div>
            <div className='text-zinc-400'>
              View details for index rates used to calculate earnings credits,
              interest, or balance based service fees.
            </div>
          </div>
          <Button className='btn' onClick={handleAddIndexRateClick}>
            Add index rate
          </Button>
        </div>
        <div className='mt-8 flex min-h-0 flex-auto flex-col'>
          <div className='flex items-center'>
            <DateFilter
              className='border-grey bg-grey-500 border hover:bg-indigo-200 focus-visible:outline-indigo-300'
              title='Effective date'
              label='View data effective on'
              initialDate={parseServerFormat(route.params.effectiveDate)}
              onDateChange={(date) => {
                router.push(
                  routeTo('/configuration/index-rates/[effectiveDate]', {
                    ...route.params,
                    effectiveDate: toServerFormat(date),
                  }),
                )
              }}
            />
            <div className='flex flex-auto items-center justify-end gap-4'>
              <SearchBar
                className='w-80'
                defaultLabel='Search index rates'
                value={searchText}
                onValueChange={setSearchText}
              />
              <p className='text-sm text-zinc-400'>
                {numVisibleIndexRates} index rate
                {numVisibleIndexRates > 1 && 's'}
              </p>
            </div>
          </div>
          <div className='mt-4 flex min-h-0 flex-col'>
            <SortedTable
              data={indexRates}
              columns={columns}
              columnFilters={columnFilters}
              onVisibleRowsChanged={(rows) =>
                setNumVisibleIndexRates(rows.length)
              }
              initialSortingState={[{ desc: false, id: 'name' }]}
              handleRowDoubleClick={({ original: { code } }) =>
                router.push(
                  routeTo(
                    '/configuration/index-rates/[effectiveDate]/view/[indexRateCode]',
                    {
                      ...route.params,
                      effectiveDate: route.params.effectiveDate,
                      indexRateCode: code,
                    },
                  ),
                )
              }
            />
          </div>
        </div>
      </div>
    </div>
  )
}
