'use client'

import { useRouter } from 'next/navigation'
import { useMutation, useQuery } from '@tanstack/react-query'

import { Button } from '@/components/Button'
import UpdateIndexRateForm from '../../../_components/UpdateIndexRateForm'
import {
  indexRateFormInput,
  indexRateFormToServer,
  UpdateIndexRateFormState,
} from '../../../_components/UpdateIndexRateFormTypes'

import { unwrap } from '@/lib/unions/unwrap'
import { routeTo, useRoute } from '@/app/[bankId]/configuration/routing'
import { indexRateQueries } from '../../../queries'
import { indexRateMutation } from '../../../mutations'
import { parseServerFormat } from '@/lib/date'

export default function EditIndexRate() {
  const router = useRouter()
  const route = unwrap(
    useRoute(),
    '/configuration/index-rates/[effectiveDate]/edit/[indexRateCode]',
  )!

  const { data, status, error } = useQuery(
    indexRateQueries('/getIndexRatesByCode', {
      code: route.params.indexRateCode,
    }),
  )
  const updateIndexRate = useMutation(
    indexRateMutation('/updateIndexRate', {
      onSuccess: (_, request) => {
        router.push(
          routeTo(
            '/configuration/index-rates/[effectiveDate]/view/[indexRateCode]',
            {
              ...route.params,
              effectiveDate: request.effectiveDate!,
              indexRateCode: request.code!,
            },
          ),
        )
      },
    }),
  )
  if (status === 'pending') return 'Loading...'
  if (!data || error) {
    return <div>404 Not Found</div>
  }
  const effectiveDate = route.params.effectiveDate
  const indexRateByEffectiveDate = data.find(
    (item) => item.effectiveDate <= effectiveDate,
  )

  if (!indexRateByEffectiveDate) {
    return <>Could not find index rate for {effectiveDate}</>
  }

  // effectiveDate string -> Date
  // indexRate number -> string
  const indexRateDefaults = {
    ...indexRateByEffectiveDate,
    effectiveDate: parseServerFormat(indexRateByEffectiveDate.effectiveDate),
    indexRate: indexRateByEffectiveDate.indexRate.toString(),
  }

  const indexRate = indexRateFormInput.parse(indexRateDefaults)

  const defaultValues: UpdateIndexRateFormState = {
    indexRateInfo: {
      ...indexRate,
      effectiveDate: indexRate.effectiveDate,
    },
  }

  return (
    <div className='w-full overflow-hidden overflow-y-scroll pt-12'>
      <div className='text-md ml-8 font-medium'>Edit index rate</div>
      <UpdateIndexRateForm
        defaultValues={defaultValues}
        onSubmit={({ indexRateInfo }) => {
          const result = indexRateFormToServer.safeParse(indexRateInfo)

          if (result.success) {
            updateIndexRate.mutate(result.data)
          } else {
            console.error(result.error)
          }
        }}
        isEditMode
      >
        <Button className='btn w-60' onClick={() => router.back()}>
          Cancel
        </Button>

        <Button type='submit' className='btn-primary w-60'>
          Save
        </Button>
      </UpdateIndexRateForm>
    </div>
  )
}
