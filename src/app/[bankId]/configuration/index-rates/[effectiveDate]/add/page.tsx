'use client'

import { useRouter } from 'next/navigation'
import { useMutation } from '@tanstack/react-query'

import { Button } from '@/components/Button'
import UpdateIndexRateForm from '../../_components/UpdateIndexRateForm'
import {
  indexRateFormToServer,
  UpdateIndexRateFormState,
} from '../../_components/UpdateIndexRateFormTypes'

import { unwrap } from '@/lib/unions/unwrap'
import { routeTo, useRoute } from '../../../routing'
import { indexRateMutation } from '../../mutations'

export default function AddIndexRate() {
  const router = useRouter()
  const route = unwrap(
    useRoute(),
    '/configuration/index-rates/[effectiveDate]/add',
  )!

  const defaultValues: UpdateIndexRateFormState = {
    indexRateInfo: {
      code: '',
      name: '',
      effectiveDate: new Date(),
      indexRate: '',
    },
  }

  const addIndexRate = useMutation(
    indexRateMutation('/addIndexRate', {
      onSuccess: (_, request) => {
        router.push(
          routeTo(
            '/configuration/index-rates/[effectiveDate]/view/[indexRateCode]',
            {
              ...route.params,
              effectiveDate: request.effectiveDate!,
              indexRateCode: request.code!,
            },
          ),
        )
      },
    }),
  )

  return (
    <div className='w-full overflow-hidden overflow-y-scroll pt-12'>
      <div className='text-md ml-8 font-medium'>Add index rate</div>
      <UpdateIndexRateForm
        defaultValues={defaultValues}
        onSubmit={({ indexRateInfo }) => {
          const result = indexRateFormToServer.safeParse(indexRateInfo)

          if (result.success) {
            addIndexRate.mutate(result.data)
          } else {
            console.error(result.error)
          }
        }}
      >
        <Button className='btn w-60' onClick={() => router.back()}>
          Cancel
        </Button>

        <Button type='submit' className='btn-primary w-60'>
          Save
        </Button>
      </UpdateIndexRateForm>
    </div>
  )
}
