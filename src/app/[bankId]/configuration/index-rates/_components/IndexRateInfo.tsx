'use client'

import { useFormTextInput } from '@/components/Form/useFormTextInput'
import { useFormDatePicker } from '@/components/Form/useFormDatePicker'
import {
  UpdateIndexRateFormApi,
  UpdateIndexRateFormState,
} from './UpdateIndexRateFormTypes'

interface IndexRateInfoProps {
  form: UpdateIndexRateFormApi
  isEditMode?: boolean
}
export default function IndexRateInfo({
  form,
  isEditMode,
}: IndexRateInfoProps) {
  const FormInput = useFormTextInput<UpdateIndexRateFormState>({ form })
  const FormDatePicker = useFormDatePicker<UpdateIndexRateFormState>({
    form,
  })
  // todo: make call to /getIndexRateByCode. If list is empty, code is unique.
  return (
    <div className='mt-10 flex w-full flex-col rounded-lg border bg-white p-6'>
      <div className='font-semibold'>Index rate information</div>
      <div className='mt-3 flex gap-9'>
        <div className='flex flex-1 flex-col gap-2'>
          <FormDatePicker
            name='indexRateInfo.effectiveDate'
            label='Effective date *'
          />
        </div>
        <div className='flex-1'></div>
      </div>
      <div className='mt-6 flex gap-9'>
        <FormInput name='indexRateInfo.name' label='Name' required />
        {/* todo: check account-type code is unique */}
        <FormInput
          name='indexRateInfo.code'
          label='Code'
          required
          placeholder='Max 5 digits'
          invalidChars={['e', 'E']}
          readonly={(field) =>
            field.value != null &&
            field.value.length > 0 &&
            field.meta.isPristine &&
            !!isEditMode
          }
        />
      </div>
      <div className='flex gap-9'>
        <FormInput
          name='indexRateInfo.indexRate'
          label='Rate'
          suffix='%'
          invalidChars={['e', 'E']}
          required
        />
        <div className='flex-1'></div>
      </div>
    </div>
  )
}
