'use client'
import { useForm } from '@tanstack/react-form'
import IndexRateInfo from './IndexRateInfo'
import {
  indexRateForm,
  UpdateIndexRateFormState,
} from './UpdateIndexRateFormTypes'

interface UpdateIndexRateFormProps {
  defaultValues: UpdateIndexRateFormState
  onSubmit: (state: UpdateIndexRateFormState) => void
  isEditMode?: boolean
}

export default function UpdateIndexRateForm({
  defaultValues,
  onSubmit,
  children,
  isEditMode,
}: React.PropsWithChildren<UpdateIndexRateFormProps>) {
  const form = useForm({
    defaultValues,
    onSubmit: ({ value }) => {
      onSubmit(value)
    },
    validators: {
      onChange: indexRateForm,
    },
  })

  return (
    <form
      className='flex min-h-screen flex-col'
      onSubmit={async (event) => {
        form.reset(form.state.values)
        event.preventDefault()
        event.stopPropagation()
        await form.handleSubmit()
      }}
    >
      <div className='flex flex-grow flex-col gap-2 overflow-y-auto px-8'>
        <IndexRateInfo form={form} isEditMode={isEditMode} />
      </div>
      <div className='sticky bottom-0 mt-auto flex justify-end gap-4 border-t bg-white px-12 py-10'>
        {children}
      </div>
    </form>
  )
}
