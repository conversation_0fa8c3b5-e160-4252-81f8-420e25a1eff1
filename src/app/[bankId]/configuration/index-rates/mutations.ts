import { indexRateQueries } from './queries'
import { withMetrics } from '@/lib/middleware/withMetrics'
import { defineMutations, defineMutation } from '@/lib/state/defineMutation'
import { revenueConnectClient } from '@/api/revenueConnectClient'
import { compose } from '@/lib/functional/compose'
import { notifications } from './notifications'

const createIndexRate = defineMutation(revenueConnectClient, '/addIndexRate', {
  invalidate: (request) => [
    { queryKey: ['/listIndexRatesByEffectiveDate'] },
    indexRateQueries('/getIndexRatesByCode', {
      code: request.code,
    }),
  ],
})

const updateIndexRate = defineMutation(
  revenueConnectClient,
  '/updateIndexRate',
  {
    invalidate: (request) => [
      { queryKey: ['/listIndexRatesByEffectiveDate'] },
      indexRateQueries('/getIndexRatesByCode', {
        code: request.code,
      }),
    ],
  },
)

export type IndexRateMutation = typeof createIndexRate | typeof updateIndexRate

export const indexRateMutation = defineMutations(
  [createIndexRate, updateIndexRate],
  compose(notifications, withMetrics()),
)
