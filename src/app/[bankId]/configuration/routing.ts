import { defineChildRoute, defineRoute, defineRoutes } from '@/lib/defineRoute'

const configuration = defineRoute('configuration')

const bankOptions = defineChildRoute(
  configuration,
  defineRoute('bank-options', ['effectiveDate']),
)

const accountType = defineChildRoute(
  configuration,
  defineRoute('account-type', ['effectiveDate']),
)

const analysisResultOptions = defineChildRoute(
  configuration,
  defineRoute('analysis-result-options', ['effectiveDate']),
)

const earningsCredit = defineChildRoute(
  configuration,
  defineRoute('earnings-credit', ['effectiveDate']),
)

const indexRates = defineChildRoute(
  configuration,
  defineRoute('index-rates', ['effectiveDate']),
)

const investableBalance = defineChildRoute(
  configuration,
  defineRoute('investable-balance', ['effectiveDate']),
)

const requiredBalance = defineChildRoute(
  configuration,
  defineRoute('required-balance', ['effectiveDate']),
)

const reserveRequirements = defineChildRoute(
  configuration,
  defineRoute('reserve-requirements', ['effectiveDate']),
)

const scheduleDefinition = defineChildRoute(
  configuration,
  defineRoute('schedule-definition', ['effectiveDate']),
)

const userFields = defineChildRoute(configuration, defineRoute('user-fields'))

const statementFormats = defineChildRoute(
  configuration,
  defineRoute('statement-formats', ['effectiveDate']),
)

const statementMessage = defineChildRoute(
  configuration,
  defineRoute('statement-message', ['effectiveDate']),
)

const officer = defineChildRoute(
  configuration,
  defineRoute('officer', ['effectiveDate']),
)

const branches = defineChildRoute(
  configuration,
  defineRoute('branches', ['effectiveDate']),
)

export const [useRoute, routeTo] = defineRoutes(
  bankOptions,
  defineChildRoute(bankOptions, defineRoute('view')),
  defineChildRoute(bankOptions, defineRoute('edit')),

  accountType,
  defineChildRoute(accountType, defineRoute('add')),
  defineChildRoute(accountType, defineRoute('edit', ['accountTypeCode'])),
  defineChildRoute(accountType, defineRoute('view', ['accountTypeCode'])),

  analysisResultOptions,
  defineChildRoute(analysisResultOptions, defineRoute('add')),
  defineChildRoute(
    analysisResultOptions,
    defineRoute('edit', ['analysisResultOptionCode']),
  ),
  defineChildRoute(
    analysisResultOptions,
    defineRoute('view', ['analysisResultOptionCode']),
  ),

  earningsCredit,
  defineChildRoute(earningsCredit, defineRoute('add')),
  defineChildRoute(earningsCredit, defineRoute('edit', ['earningsCreditCode'])),
  defineChildRoute(earningsCredit, defineRoute('view', ['earningsCreditCode'])),

  indexRates,
  defineChildRoute(indexRates, defineRoute('add')),
  defineChildRoute(indexRates, defineRoute('edit', ['indexRateCode'])),
  defineChildRoute(indexRates, defineRoute('view', ['indexRateCode'])),

  investableBalance,
  defineChildRoute(investableBalance, defineRoute('add')),
  defineChildRoute(
    investableBalance,
    defineRoute('edit', ['investableBalanceCode']),
  ),
  defineChildRoute(
    investableBalance,
    defineRoute('view', ['investableBalanceCode']),
  ),

  requiredBalance,
  defineChildRoute(requiredBalance, defineRoute('add')),
  defineChildRoute(
    requiredBalance,
    defineRoute('edit', ['requiredBalanceCode']),
  ),
  defineChildRoute(
    requiredBalance,
    defineRoute('view', ['requiredBalanceCode']),
  ),

  reserveRequirements,
  defineChildRoute(reserveRequirements, defineRoute('add')),
  defineChildRoute(
    reserveRequirements,
    defineRoute('edit', ['reserveRequirementCode']),
  ),
  defineChildRoute(
    reserveRequirements,
    defineRoute('view', ['reserveRequirementCode']),
  ),

  scheduleDefinition,
  defineChildRoute(scheduleDefinition, defineRoute('add')),
  defineChildRoute(
    scheduleDefinition,
    defineRoute('edit', ['scheduleDefinitionCode']),
  ),
  defineChildRoute(
    scheduleDefinition,
    defineRoute('view', ['scheduleDefinitionCode']),
  ),

  statementFormats,
  defineChildRoute(statementFormats, defineRoute('add')),
  defineChildRoute(
    statementFormats,
    defineRoute('edit', ['statementFormatCode']),
  ),
  defineChildRoute(
    statementFormats,
    defineRoute('view', ['statementFormatCode']),
  ),

  statementMessage,
  defineChildRoute(statementMessage, defineRoute('add')),
  defineChildRoute(
    statementMessage,
    defineRoute('edit', ['statementMessageCode']),
  ),
  defineChildRoute(
    statementMessage,
    defineRoute('view', ['statementMessageCode']),
  ),

  userFields,
  defineChildRoute(userFields, defineRoute('add')),
  defineChildRoute(userFields, defineRoute('edit', ['userFieldCode'])),
  defineChildRoute(userFields, defineRoute('view', ['userFieldCode'])),

  officer,
  defineChildRoute(officer, defineRoute('view', ['officerCode'])),

  branches,
  defineChildRoute(branches, defineRoute('view', ['branchCode'])),
)
