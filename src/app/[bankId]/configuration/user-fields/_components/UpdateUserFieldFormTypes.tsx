import { z } from 'zod'
import { name, configCode } from '@/api/zodSchemas'
import { FormApi, ReactFormApi } from '@tanstack/react-form'
import { apiToFormSchemas } from '@/api/apiToFormSchema'
import { variant } from '@/lib/unions/Union'
import { match } from '@/lib/unions/match'
import { formToApiSchemas } from '@/api/formToApiSchema'

const {
  hydratedUserFieldConfiguration,
  userFieldDropdownOptionCreate,
  userFieldDropdownOptionUpdate: UserFieldDropdownOptionUpdateType,
} = apiToFormSchemas

export const UserFieldUpdateFormToApiSchema =
  formToApiSchemas.hydratedUserFieldConfigurationUpdate.extend({
    updatedDropdownOptions: z
      .array(UserFieldDropdownOptionUpdateType)
      .optional(),
    code: z.string().pipe(z.coerce.number()),
  })
export const UserFieldConfigurationType = hydratedUserFieldConfiguration.extend(
  {
    newDropdownOptions: z.array(userFieldDropdownOptionCreate).optional(),
    updatedDropdownOptions: z
      .array(UserFieldDropdownOptionUpdateType)
      .optional(),
  },
)

export const userFieldInfoSchema = z.object({
  name,
  code: configCode,
  createdAt: z.string(),
  updatedAt: z.string(),
})
type UserFieldInfoState = z.infer<typeof userFieldInfoSchema>

export const UserFieldsTypes = hydratedUserFieldConfiguration.shape.fieldType

export const userFieldDetailSchema = z.object({
  availablePriceList: z.boolean(),
  fieldType: UserFieldsTypes,
  dropDownValues: z.array(
    z.object({
      code: z.number(),
      value: z.string(),
    }),
  ),
  newDropDownValues: z.array(
    z.object({
      value: z.string(),
    }),
  ),
})
type UserFieldDetailState = z.infer<typeof userFieldDetailSchema>

// TODO create composite zod schema instead (e.g., updateUserFieldSchema) and infer this type from that schema.
// This new composite schema could also be used as onChange validator in useForm in UpdateUserFieldForm to reduce
// need to define validators.onChange on every form field.
export type UpdateUserFieldFormState = {
  userFieldInfo: UserFieldInfoState
  userFieldDetail: UserFieldDetailState
}

export type UpdateUserFieldFormApi = ReactFormApi<UpdateUserFieldFormState> &
  FormApi<UpdateUserFieldFormState>

export const fieldTypesLabel = (value: z.infer<typeof UserFieldsTypes>) =>
  match(variant(value), {
    FREEFORM: () => 'Freeform',
    DROPDOWN: () => 'Dropdown',
    BOOLEAN: () => 'Boolean',
  })
