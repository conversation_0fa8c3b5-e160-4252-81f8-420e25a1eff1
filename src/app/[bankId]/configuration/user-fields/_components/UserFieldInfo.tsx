'use client'

import { useFormTextInput } from '@/components/Form/useFormTextInput'
import {
  UpdateUserFieldFormApi,
  UpdateUserFieldFormState,
  userFieldInfoSchema,
} from './UpdateUserFieldFormTypes'

interface UserFieldInfoProps {
  form: UpdateUserFieldFormApi
  isEditMode?: boolean
}

export default function UserFieldInfo({
  form,
  isEditMode,
}: UserFieldInfoProps) {
  const FormInput = useFormTextInput<UpdateUserFieldFormState>({ form })

  return (
    <div className='mt-10 flex w-full flex-col rounded-lg border bg-white p-6'>
      <div className='font-semibold'>User field information</div>
      <div className='mt-3 flex gap-9'>
        <FormInput
          name='userFieldInfo.createdAt'
          aria-label='Created date *'
          readonly={(field) =>
            field.value != null &&
            field.value.length > 0 &&
            field.meta.isPristine
          }
        />
        {isEditMode ?
          <FormInput
            name='userFieldInfo.updatedAt'
            aria-label='Last Updated *'
            readonly={(field) =>
              field.value != null &&
              field.value.length > 0 &&
              field.meta.isPristine
            }
          />
        : <div className='flex-1'></div>}
      </div>
      <div className='mt-3 flex gap-9'>
        <FormInput
          name='userFieldInfo.name'
          aria-label='Name'
          required
          validators={{
            onChange: userFieldInfoSchema.shape.name,
          }}
        />
        <div className='flex-1'></div>
      </div>
    </div>
  )
}
