'use client'

import { useState } from 'react'
import { PlusIcon, TrashIcon } from '@heroicons/react/24/outline'

import { useFormSelect } from '@/components/Form/useFormSelect'
import { Checkbox } from '@/components/Checkbox'

import {
  fieldTypesLabel,
  UpdateUserFieldFormApi,
  UpdateUserFieldFormState,
  userFieldDetailSchema,
  UserFieldsTypes,
} from './UpdateUserFieldFormTypes'
import { useField } from '@tanstack/react-form'

interface UserFieldDetailProps {
  form: UpdateUserFieldFormApi
}

export default function UserFieldDetail({ form }: UserFieldDetailProps) {
  const [selectedFieldType, setSelectedFieldType] = useState(
    form.getFieldValue('userFieldDetail.fieldType'),
  )
  const [newDropDownInputs, setNewDropDownInputs] = useState<string[]>([])
  const [newDropDownErrors, setNewDropDownErrors] = useState<
    Record<string, boolean>
  >({})

  const oldFormValues = form.getFieldValue('userFieldDetail.dropDownValues')
  const {
    setValue: setNewFormValues,
    state: { value: newFormValues },
  } = useField<UpdateUserFieldFormState, 'userFieldDetail.newDropDownValues'>({
    form: form,
    name: 'userFieldDetail.newDropDownValues',
  })

  const Select = useFormSelect<UpdateUserFieldFormState>({ form })

  return (
    <div className='mb-6 mt-3 flex w-full flex-col rounded-lg border bg-white p-6'>
      <div className='font-semibold'>User field details</div>

      <div className='mb-3 mt-3'>
        <form.Field name='userFieldDetail.availablePriceList'>
          {({ state: { value }, handleChange }) => (
            <Checkbox
              label='Available for selection in price lists'
              onChange={handleChange}
              checked={value}
            />
          )}
        </form.Field>
      </div>
      <Select
        label='Field type'
        name='userFieldDetail.fieldType'
        options={UserFieldsTypes.options}
        renderSelected={fieldTypesLabel}
        renderOption={fieldTypesLabel}
        onChange={(value) => {
          setSelectedFieldType(value)
        }}
        validators={{
          onChange: userFieldDetailSchema.shape.fieldType,
        }}
        required
      />
      {selectedFieldType === UserFieldsTypes.Enum.DROPDOWN && (
        <div className='max-w-full pt-2'>
          <div className='flex flex-col rounded-lg border bg-white p-0'>
            <div className='m-0 border-b bg-app-color-bg-secondary p-3 text-xs text-zinc-500'>
              Name
            </div>
            {oldFormValues.map(({ code, value }) => (
              <div key={code} className='flex justify-between border-b p-3'>
                <div className='text-zinc-500'>{value}</div>
              </div>
            ))}
            {newFormValues.map(({ value }, i) => (
              <div key={value} className='flex justify-between border-b p-3'>
                <div className='text-zinc-500'>{value}</div>
                <TrashIcon
                  className='m-1 h-4 cursor-pointer'
                  onClick={() => {
                    const vals = newFormValues.filter(
                      ({ value: existingValue }) => existingValue !== value,
                    )
                    setNewFormValues(vals)
                  }}
                />
              </div>
            ))}
            {newDropDownInputs.length ?
              newDropDownInputs.map((value, i) => (
                <div
                  key={value}
                  className='flex w-full flex-col items-center justify-between border-b px-5 py-2'
                >
                  <input
                    className='border-1 h-8 w-full rounded bg-transparent px-2 placeholder:text-zinc-400 focus:outline-none focus:ring-0'
                    onBlur={(e) => {
                      const { value: optionValue } = e.target
                      if (!optionValue) {
                        setNewDropDownInputs([])
                        return
                      }
                      const isExistingOldValue = oldFormValues.find(
                        ({ value }) => optionValue === value,
                      )
                      const isExistingNewValue = newFormValues.find(
                        ({ value }) => optionValue === value,
                      )

                      if (isExistingOldValue || isExistingNewValue) {
                        setNewDropDownErrors({
                          ...newDropDownErrors,
                          [i]: true,
                        })
                        return
                      } else {
                        setNewDropDownErrors({
                          ...newDropDownErrors,
                          [i]: false,
                        })
                      }
                      form.setFieldValue('userFieldDetail.newDropDownValues', [
                        ...newFormValues,
                        { value: optionValue },
                      ])
                      setNewDropDownInputs([])
                    }}
                  />
                  {newDropDownErrors[i] && (
                    <div className='mt-1 self-start text-xs text-red-500'>
                      Drop down values are required to be unique
                    </div>
                  )}
                </div>
              ))
            : null}
            <div className='flex justify-between border-b p-3'>
              <div
                className={`flex w-full items-center text-indigo-500 ${newDropDownInputs.length < 2 ? 'cursor-pointer' : 'cursor-disabled'}`}
                onClick={() => {
                  if (newDropDownInputs.length < 1) {
                    setNewDropDownInputs([''])
                  }
                }}
              >
                <PlusIcon className='m-1 h-5' />
                Add a drop-down value
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
