'use client'
import { useForm } from '@tanstack/react-form'

import UserFieldInfo from './UserFieldInfo'
import UserFieldDetail from './UserFieldDetail'
import { UpdateUserFieldFormState } from './UpdateUserFieldFormTypes'

interface UpdateUserFieldFormProps {
  defaultValues: UpdateUserFieldFormState
  isEditMode?: boolean
  onSubmit: (state: UpdateUserFieldFormState) => void
}

export default function UpdateUserFieldForm({
  defaultValues,
  isEditMode,
  onSubmit,
  children,
}: React.PropsWithChildren<UpdateUserFieldFormProps>) {
  const form = useForm({
    defaultValues,
    onSubmit: ({ value }) => {
      onSubmit(value)
    },
  })
  return (
    <form
      className='flex max-h-full flex-auto flex-col'
      onSubmit={async (event) => {
        form.reset(form.state.values)
        event.preventDefault()
        event.stopPropagation()
        await form.handleSubmit()
      }}
    >
      <div className='flex flex-auto flex-col gap-2 overflow-y-auto px-8'>
        <UserFieldInfo form={form} isEditMode={isEditMode} />
        <UserFieldDetail form={form} />
      </div>
      <div className='flex justify-end gap-4 border-t bg-white px-12 py-10'>
        {children}
      </div>
    </form>
  )
}
