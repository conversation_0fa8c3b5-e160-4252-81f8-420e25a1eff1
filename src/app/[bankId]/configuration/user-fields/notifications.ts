import { UserFieldMutation } from './mutations'
import { Tag } from '@/lib/unions/Union'
import { withNotifications } from '@/components/Notifications'

export const messages: { [T in Tag<UserFieldMutation>]: string } = {
  '/addUserFieldConfiguration': 'User Field successfully created.',
  '/updateUserFieldConfiguration': 'User Field successfully updated.',
}

export const notifications = withNotifications(messages)
