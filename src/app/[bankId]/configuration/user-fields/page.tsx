'use client'

import { useState } from 'react'
import { ColumnDef, ColumnFiltersState } from '@tanstack/react-table'
import { ChevronRightIcon } from '@heroicons/react/24/outline'
import { useRouter } from 'next/navigation'

import { Button } from '@/components/Button'
import { SearchBar } from '@/components/SearchBar'
import { SortedTable } from '@/components/Table/SortedTable'
import { RowPopover } from '../_components/RowPopover'

import { mockUserFields } from '../mockData'
import { unwrap } from '@/lib/unions/unwrap'
import { routeTo, useRoute } from '../routing'
import { useQuery } from '@tanstack/react-query'
import { userFieldQueries } from './queries'
import { z } from 'zod'
import {
  fieldTypesLabel,
  UserFieldConfigurationType,
} from './_components/UpdateUserFieldFormTypes'

export default function UserFields() {
  const [searchText, setSearchText] = useState('')
  const columnFilters: ColumnFiltersState = [{ id: 'name', value: searchText }]
  const [numItems, setNumItems] = useState(mockUserFields?.length ?? 0)
  const router = useRouter()
  const route = unwrap(useRoute(), '/configuration/user-fields')!

  const { data, status, error } = useQuery(
    userFieldQueries('/getUserFieldsConfigurations'),
  )
  if (status === 'pending') return 'Loading...'
  if (!data || error) {
    return <div>404 Not Found</div>
  }

  const userFields = data?.map((item) => {
    return UserFieldConfigurationType.parse(item)
  })

  const handleAddScheduleDefinitionClick = () => {
    router.push(routeTo('/configuration/user-fields/add', route.params))
  }

  const columns: ColumnDef<z.infer<typeof UserFieldConfigurationType>>[] = [
    {
      header: 'Name',
      accessorKey: 'name',
    },
    {
      header: 'Field Type',
      accessorKey: 'fieldType',
      cell: ({
        row: {
          original: { fieldType },
        },
      }) => fieldTypesLabel(fieldType),
    },
    {
      header: '',
      accessorKey: 'code',
      cell: ({
        row: {
          original: { code },
        },
      }) => (
        <RowPopover
          className='pr-4'
          onEdit={() => {
            router.push(
              routeTo('/configuration/user-fields/edit/[userFieldCode]', {
                ...route.params,
                userFieldCode: code.toString(),
              }),
            )
          }}
        />
      ),
      meta: { className: 'basis-28 shrink-0 justify-end px-2' },
    },
  ]

  return (
    <div className='flex min-h-0 flex-1 flex-col py-6 pl-12 pr-3'>
      <div className='flex items-center text-zinc-500'>
        Configurations
        <ChevronRightIcon className='h-4 w-4 text-zinc-400' />
        <span className='text-lg font-semibold text-black'>User Fields</span>
      </div>
      <div className='mt-12 flex min-h-0 w-full flex-auto flex-grow flex-col rounded-lg border bg-white p-6'>
        <div className='flex w-full items-center justify-between'>
          <div className=''>
            <div className='text-lg font-semibold'>User Fields</div>
            <div className='text-zinc-400'>
              View user fields containing additional account information for
              configuring a price list
            </div>
          </div>
          <Button className='btn' onClick={handleAddScheduleDefinitionClick}>
            Add users fields
          </Button>
        </div>
        <div className='mt-8 flex min-h-0 flex-auto flex-col'>
          <div className='flex items-center'>
            <div className='flex flex-auto items-center justify-end gap-4'>
              <SearchBar
                className='w-80'
                defaultLabel='Search user fields'
                value={searchText}
                onValueChange={setSearchText}
              />
              <p className='text-sm text-zinc-400'>
                {numItems} user field
                {numItems > 1 && 's'}
              </p>
            </div>
          </div>
          <div className='mt-4 flex min-h-0 flex-col'>
            <SortedTable
              data={userFields}
              columns={columns}
              columnFilters={columnFilters}
              onVisibleRowsChanged={(rows) => setNumItems(rows.length)}
              // Use the same initial sorting state as on accounts 360 page
              // initialSortingState={[{ desc: false, id: 'name' }]}
              handleRowDoubleClick={({ original: { code } }) =>
                router.push(
                  routeTo('/configuration/user-fields/view/[userFieldCode]', {
                    ...route.params,
                    userFieldCode: code.toString(),
                  }),
                )
              }
            />
          </div>
        </div>
      </div>
    </div>
  )
}
