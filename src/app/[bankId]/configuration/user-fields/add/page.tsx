'use client'

import { useRouter } from 'next/navigation'
import { But<PERSON> } from '@/components/Button'

import { unwrap } from '@/lib/unions/unwrap'
import { routeTo, useRoute } from '../../routing'
import { toServerFormat } from '@/lib/date'
import UpdateUserFieldForm from '../_components/UpdateUserFieldForm'
import { UpdateUserFieldFormState } from '../_components/UpdateUserFieldFormTypes'
import { userFieldMutation } from '../mutations'
import { useMutation } from '@tanstack/react-query'
import { formToApiSchemas } from '@/api/formToApiSchema'

export default function AddUserFields({}) {
  const router = useRouter()
  const route = unwrap(useRoute(), '/configuration/user-fields/add')!

  const addUserField = useMutation(
    userFieldMutation('/addUserFieldConfiguration', {
      onSuccess: (response, request) => {
        router.push(
          routeTo('/configuration/user-fields/view/[userFieldCode]', {
            ...route.params,
            userFieldCode: response.code.toString(),
          }),
        )
      },
    }),
  )

  const serialize = ({
    userFieldInfo,
    userFieldDetail,
  }: UpdateUserFieldFormState) => {
    return formToApiSchemas.hydratedUserFieldConfigurationCreate.parse({
      name: userFieldInfo.name,
      fieldType: userFieldDetail.fieldType,
      availableForPriceList: userFieldDetail.availablePriceList,
      newDropdownOptions: userFieldDetail.newDropDownValues,
    })
  }

  const defaultValues: UpdateUserFieldFormState = {
    userFieldInfo: {
      code: '',
      name: '',
      createdAt: toServerFormat(new Date()),
      updatedAt: toServerFormat(new Date()),
    },
    userFieldDetail: {
      availablePriceList: false,
      fieldType: 'FREEFORM',
      dropDownValues: [],
      newDropDownValues: [],
    },
  }

  return (
    <div className='w-full overflow-hidden overflow-y-scroll pt-12'>
      <div className='ml-8 text-lg font-medium'>Add user fields</div>
      <div className='mt-2 flex gap-4'>
        <UpdateUserFieldForm
          defaultValues={defaultValues}
          onSubmit={(formState) => {
            const payload = serialize(formState)
            addUserField.mutate(payload)
          }}
        >
          <Button className='btn w-60' onClick={() => router.back()}>
            Cancel
          </Button>
          <Button type='submit' className='btn-primary w-60'>
            Save
          </Button>
        </UpdateUserFieldForm>
      </div>
    </div>
  )
}
