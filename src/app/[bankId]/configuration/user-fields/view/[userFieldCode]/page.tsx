'use client'

import { useRouter } from 'next/navigation'
import { But<PERSON> } from '@/components/Button'
import { ChevronRightIcon } from '@heroicons/react/24/outline'
import Link from 'next/link'

import {
  DetailsSection,
  DetailsSectionItem,
  DetailsSectionItemsRow,
  DetailsSectionTitle,
} from '@/components/DetailsSection'

import { unwrap } from '@/lib/unions/unwrap'
import { routeTo, useRoute } from '../../../routing'
import { useQuery } from '@tanstack/react-query'
import { userFieldQueries } from '../../queries'
import {
  fieldTypesLabel,
  UserFieldConfigurationType,
} from '../../_components/UpdateUserFieldFormTypes'

export default function ViewUserFields({}) {
  const router = useRouter()
  const route = unwrap(
    useRoute(),
    '/configuration/user-fields/view/[userFieldCode]',
  )!

  const { data, status, error } = useQuery(
    userFieldQueries('/getUserFieldsConfigurations'),
  )

  const userFields = data?.map((item) => {
    return UserFieldConfigurationType.parse(item)
  })

  const userField = userFields?.find(
    (field) => field.code === parseInt(route.params.userFieldCode),
  )
  if (status === 'pending') return 'Loading...'
  if (!userField || error) {
    return <div>404 Not Found</div>
  }

  const onEditClick = () => {
    router.push(
      routeTo('/configuration/user-fields/edit/[userFieldCode]', route.params),
    )
  }

  return (
    <div className='w-full overflow-hidden overflow-y-scroll px-8 py-10'>
      <div className='flex items-center justify-between'>
        <div className='flex items-center gap-2'>
          <Link
            href={routeTo('/configuration/user-fields', route.params)}
            className='font-bold'
          >
            ...
          </Link>
          <ChevronRightIcon className='h-4 w-4 text-zinc-400' />
          <div className='text-md text-zinc-500'>
            <Link
              href={routeTo('/configuration/user-fields', route.params)}
              className='font-bold'
            >
              User fields
            </Link>
          </div>
          <ChevronRightIcon className='h-4 w-4 text-zinc-400' />
          <div className='text-lg'>{userField.name}</div>
        </div>
        <Button className='btn-primary text-white' onClick={onEditClick}>
          Edit
        </Button>
      </div>
      <div className='mt-8 flex gap-4'>
        <div className='flex grow flex-col'>
          <DetailsSection>
            <DetailsSectionTitle>User field information</DetailsSectionTitle>
            <DetailsSectionItemsRow>
              <DetailsSectionItem
                label={'Creation date *'}
                info={userField.createdDate || '--'}
              />
              <DetailsSectionItem
                label={'Last Updated *'}
                info={userField.lastUpdatedDate || '--'}
              />
            </DetailsSectionItemsRow>
            <DetailsSectionItemsRow>
              <DetailsSectionItem label={'Name *'} info={userField.name} />
            </DetailsSectionItemsRow>
          </DetailsSection>

          <DetailsSection className='mt-3'>
            <DetailsSectionTitle>User field details</DetailsSectionTitle>
            <DetailsSectionItemsRow>
              <DetailsSectionItem
                label={'Available for selection in price lists'}
                info={userField.availableForPriceList ? 'Yes' : 'No'}
              />
            </DetailsSectionItemsRow>
            <DetailsSectionItemsRow>
              <DetailsSectionItem
                label={'Field type*'}
                info={fieldTypesLabel(userField.fieldType)}
              />
            </DetailsSectionItemsRow>
            {userField.fieldType === 'DROPDOWN' && (
              <>
                <DetailsSectionItemsRow>
                  <DetailsSectionItem label={'Drop-down Values'} info={''} />
                </DetailsSectionItemsRow>
                <div className='max-w-full pt-2'>
                  <div className='flex flex-col rounded-lg border bg-white p-0'>
                    <div className='m-0 border-b bg-app-color-bg-secondary p-3 text-xs text-zinc-500'>
                      Name
                    </div>
                    {userField.updatedDropdownOptions?.map(
                      ({ value, code }) => (
                        <div
                          key={code}
                          className='flex justify-between border-b p-3'
                        >
                          <div className='text-zinc-500'>{value}</div>
                        </div>
                      ),
                    )}
                  </div>
                </div>
              </>
            )}
          </DetailsSection>
        </div>
      </div>
    </div>
  )
}
