'use client'

import { useRouter } from 'next/navigation'
import { But<PERSON> } from '@/components/Button'

import { toServerFormat } from '@/lib/date'
import UpdateUserFieldForm from '../../_components/UpdateUserFieldForm'
import {
  UpdateUserFieldFormState,
  UserFieldConfigurationType,
  UserFieldUpdateFormToApiSchema,
} from '../../_components/UpdateUserFieldFormTypes'

import { unwrap } from '@/lib/unions/unwrap'
import { routeTo, useRoute } from '../../../routing'
import { useMutation, useQuery } from '@tanstack/react-query'
import { userFieldQueries } from '../../queries'
import { userFieldMutation } from '../../mutations'

export default function EditUserFields({}) {
  const router = useRouter()
  const route = unwrap(
    useRoute(),
    '/configuration/user-fields/edit/[userFieldCode]',
  )!

  const { data, error, isLoading } = useQuery(
    userFieldQueries('/getUserFieldsConfigurations'),
  )

  const userFields = data?.map((item) => {
    return UserFieldConfigurationType.parse(item)
  })

  const userField = userFields?.find(
    (field) => field.code === parseInt(route.params.userFieldCode),
  )

  const updateUserField = useMutation(
    userFieldMutation('/updateUserFieldConfiguration', {
      onSuccess: (response, request) => {
        router.push(
          routeTo('/configuration/user-fields/view/[userFieldCode]', {
            ...route.params,
            userFieldCode: response.code.toString(),
          }),
        )
      },
    }),
  )

  if (isLoading) {
    return <div>Loading...</div>
  }

  if (!userField || error) {
    return <div>404 Not Found</div>
  }

  const defaultValues: UpdateUserFieldFormState = {
    userFieldInfo: {
      code: userField.code.toString(),
      name: userField.name,
      createdAt: userField.createdDate || 'asdf',
      updatedAt: toServerFormat(new Date()),
    },
    userFieldDetail: {
      availablePriceList: userField.availableForPriceList,
      fieldType: userField.fieldType,
      dropDownValues: userField.updatedDropdownOptions || [],
      newDropDownValues: [],
    },
  }

  const serialize = ({
    userFieldInfo,
    userFieldDetail,
  }: UpdateUserFieldFormState) => {
    return UserFieldUpdateFormToApiSchema.parse({
      name: userFieldInfo.name,
      code: userFieldInfo.code,
      fieldType: userFieldDetail.fieldType,
      availableForPriceList: userFieldDetail.availablePriceList,
      newDropdownOptions: userFieldDetail.newDropDownValues,
    })
  }

  return (
    <div className='w-full overflow-hidden overflow-y-scroll pt-12'>
      <div className='ml-8 text-lg font-medium'>Edit user fields</div>
      <div className='mt-2 flex gap-4'>
        <UpdateUserFieldForm
          defaultValues={defaultValues}
          isEditMode
          onSubmit={(formState) => {
            const payload = serialize(formState)
            updateUserField.mutate(payload)
          }}
        >
          <Button className='btn w-60' onClick={() => router.back()}>
            Cancel
          </Button>
          <Button type='submit' className='btn-primary w-60'>
            Save
          </Button>
        </UpdateUserFieldForm>
      </div>
    </div>
  )
}
