import { withMetrics } from '@/lib/middleware/withMetrics'
import { defineMutations, defineMutation } from '@/lib/state/defineMutation'
import { revenueConnectClient } from '@/api/revenueConnectClient'
import { userFieldQueries } from './queries'
import { compose } from '@/lib/functional/compose'
import { notifications } from './notifications'

const createUserField = defineMutation(
  revenueConnectClient,
  '/addUserFieldConfiguration',
  {
    invalidate: (request) => [userFieldQueries('/getUserFieldsConfigurations')],
  },
)

const updateUserField = defineMutation(
  revenueConnectClient,
  '/updateUserFieldConfiguration',
  {
    invalidate: (request) => [userFieldQueries('/getUserFieldsConfigurations')],
  },
)

export type UserFieldMutation = typeof createUserField | typeof updateUserField

export const userFieldMutation = defineMutations(
  [createUserField, updateUser<PERSON>ield],
  compose(notifications, withMetrics()),
)
