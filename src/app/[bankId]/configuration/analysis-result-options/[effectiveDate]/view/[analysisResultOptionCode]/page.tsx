'use client'

import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { useQuery } from '@tanstack/react-query'
import { ChevronRightIcon } from '@heroicons/react/24/outline'

import { formatUSD } from '@/lib/intlFormatters'
import { <PERSON><PERSON> } from '@/components/Button'
import { If } from '@/components/If'
import {
  DetailsSection,
  DetailsSectionTitle,
  DetailsSectionItemsRow,
  DetailsSectionItem,
} from '@/components/DetailsSection'
import { VersionTimeline } from '@/app/[bankId]/configuration/_components/VersionTimeline'

import { unwrap } from '@/lib/unions/unwrap'
import { routeTo, useRoute } from '@/app/[bankId]/configuration/routing'
import { apiToFormSchemas } from '@/api/apiToFormSchema'
import { analysisResultOptionsQueries } from '../../../queries'
import { formatToMonthYearFromDate, formatToServerString } from '@/lib/date'
import { analysisResultLabels } from '../../../_components/UpdateAnalysisResultOptionsFormTypes'

export default function ViewIndexRate() {
  const router = useRouter()
  const route = unwrap(
    useRoute(),
    '/configuration/analysis-result-options/[effectiveDate]/view/[analysisResultOptionCode]',
  )!

  const effectiveDate = route.params.effectiveDate

  const payload = {
    code: route.params.analysisResultOptionCode,
  }

  const { data, status, error } = useQuery(
    analysisResultOptionsQueries('/getAnalysisResultOptionsByCode', payload),
  )
  if (status === 'pending') return 'Loading...'
  if (!data || error) {
    return <div>404 Not Found</div>
  }

  const { analysisResultOption: analysisResultOptionSchema } = apiToFormSchemas
  const analysisResultOptions = data
    .map((item) => {
      const parsed = analysisResultOptionSchema.parse(item)
      return {
        ...parsed,
        effectiveDate: formatToMonthYearFromDate(parsed.effectiveDate),
        markdownRate: item.markdownRate,
        markupRate: item.markupRate,
        delay: item.delay,
      }
    })
    .sort(
      (a, b) =>
        new Date(b.effectiveDate).getTime() -
        new Date(a.effectiveDate).getTime(),
    )
  const analysisResultOption =
    analysisResultOptions.find(
      (item) => item.effectiveDate === effectiveDate,
    ) || analysisResultOptions[0]

  const onEditClick = () => {
    router.push(
      routeTo(
        '/configuration/analysis-result-options/[effectiveDate]/edit/[analysisResultOptionCode]',
        {
          ...route.params,
          analysisResultOptionCode: analysisResultOption.code,
          effectiveDate: formatToMonthYearFromDate(
            analysisResultOption.effectiveDate,
          ),
        },
      ),
    )
  }

  const linkFormatter = (
    effectiveDate: string,
    analysisResultOptionCode: string,
  ) => {
    return routeTo(
      '/configuration/analysis-result-options/[effectiveDate]/view/[analysisResultOptionCode]',
      { ...route.params, analysisResultOptionCode, effectiveDate },
    )
  }

  return (
    <div className='w-full overflow-hidden overflow-y-scroll px-8 py-10'>
      <div className='flex items-center justify-between'>
        <div className='flex items-center gap-2'>
          <Link
            href={routeTo(
              '/configuration/analysis-result-options/[effectiveDate]',
              route.params,
            )}
            className='font-bold'
          >
            ...
          </Link>
          <ChevronRightIcon className='h-4 w-4 text-zinc-400' />
          <div className='text-md text-zinc-500'>
            <Link
              href={routeTo(
                '/configuration/analysis-result-options/[effectiveDate]',
                route.params,
              )}
              className='font-bold'
            >
              Analysis result options
            </Link>
          </div>
          <ChevronRightIcon className='h-4 w-4 text-zinc-400' />
          <div className='text-lg'>{analysisResultOption.name}</div>
          <div className='text-sm text-zinc-500'>
            {analysisResultOption.code}
          </div>
        </div>
        <Button className='btn-primary text-white' onClick={onEditClick}>
          Edit
        </Button>
      </div>
      <div className='mt-8 flex gap-4'>
        <div className='flex grow flex-col'>
          <DetailsSection>
            <DetailsSectionTitle>
              Analysis result information
            </DetailsSectionTitle>
            <DetailsSectionItemsRow>
              <DetailsSectionItem
                label={'Effective date *'}
                info={analysisResultOption.effectiveDate}
              />
            </DetailsSectionItemsRow>
            <DetailsSectionItemsRow>
              <DetailsSectionItem
                label={'Name *'}
                info={analysisResultOption.name}
              />
              <DetailsSectionItem
                label={'Code *'}
                info={analysisResultOption.code}
              />
            </DetailsSectionItemsRow>
          </DetailsSection>
          <DetailsSection className='mt-3'>
            <DetailsSectionTitle>
              Analysis result settlement waiver
            </DetailsSectionTitle>
            <DetailsSectionItemsRow>
              <DetailsSectionItem
                label={'Waive analysis charges under'}
                info={
                  formatUSD(analysisResultOption.minChargeWaiveAmount) ?? '--'
                }
              />
              <DetailsSectionItem
                label={'Waive analysis charges over'}
                info={
                  formatUSD(analysisResultOption.maxChargeWaiveAmount) ?? '--'
                }
              />
            </DetailsSectionItemsRow>
          </DetailsSection>
          <DetailsSection className='mt-3'>
            <DetailsSectionTitle>
              Analysis result settlement
            </DetailsSectionTitle>
            <DetailsSectionItemsRow>
              <DetailsSectionItem
                label={'Analysis charges *'}
                info={
                  analysisResultLabels[analysisResultOption.analysisChargeType]
                }
              />
            </DetailsSectionItemsRow>
            <DetailsSectionItemsRow>
              <DetailsSectionItem
                label={'Hard charges *'}
                info={analysisResultLabels[analysisResultOption.hardCharge]}
              />
            </DetailsSectionItemsRow>
            <DetailsSectionItemsRow>
              <DetailsSectionItem
                label={'Excess credits *'}
                info={analysisResultLabels[analysisResultOption.excessCredits]}
              />
            </DetailsSectionItemsRow>
            <DetailsSectionItemsRow>
              <DetailsSectionItem
                label={'Markdown rate'}
                info={
                  analysisResultOption.markdownRate ?
                    `${analysisResultOption.markdownRate}%`
                  : '--'
                }
              />
              <If true={!!analysisResultOption.markdownRate}>
                <DetailsSectionItem
                  label={'Statement label'}
                  info={analysisResultOption.markdownStatementLabel ?? '--'}
                />
              </If>
            </DetailsSectionItemsRow>
            <DetailsSectionItemsRow>
              <DetailsSectionItem
                label={'Markup rate'}
                info={
                  analysisResultOption.markupRate ?
                    `${analysisResultOption.markupRate}%`
                  : '--'
                }
              />
              <If true={!!analysisResultOption.markupRate}>
                <DetailsSectionItem
                  label={'Statement label'}
                  info={analysisResultOption.markupStatementLabel ?? '--'}
                />
              </If>
            </DetailsSectionItemsRow>
            <DetailsSectionItemsRow>
              <DetailsSectionItem
                label={'Number of cycles to waive for a new account'}
                info={analysisResultOption.waiveCycle ?? '--'}
              />
            </DetailsSectionItemsRow>
          </DetailsSection>
          <DetailsSection className='mt-3'>
            <DetailsSectionTitle>Settlement dates</DetailsSectionTitle>
            <DetailsSectionItemsRow>
              <DetailsSectionItem
                label={'Final analysis override *'}
                info={
                  analysisResultLabels[
                    analysisResultOption.settlementOverrideType
                  ]
                }
              />
            </DetailsSectionItemsRow>

            <If
              true={
                analysisResultOption.settlementOverrideType ===
                'SAME_DATE_EACH_MONTH'
              }
            >
              <DetailsSectionItemsRow>
                <DetailsSectionItem
                  label={'Same date each month *'}
                  info={
                    analysisResultOption.settlementOverrideDateEachMonth || '--'
                  }
                />
              </DetailsSectionItemsRow>
            </If>

            <If
              true={
                analysisResultOption.settlementOverrideType ===
                'SPECIFIC_DAYS_AFTER_PRELIM_ANALYSIS'
              }
            >
              <DetailsSectionItemsRow>
                <DetailsSectionItem
                  label={'Number of days after preliminary analysis *'}
                  info={analysisResultOption.daysAfter || '--'}
                />
              </DetailsSectionItemsRow>
            </If>

            <DetailsSectionItemsRow>
              <DetailsSectionItem
                label={'Debit/Credit Delay'}
                info={analysisResultOption.delay || '--'}
              />
            </DetailsSectionItemsRow>
          </DetailsSection>
        </div>
        <div className='flex max-w-72 flex-col rounded-lg border bg-white p-6'>
          <VersionTimeline
            versions={analysisResultOptions}
            currentEffectiveDate={route.params.effectiveDate}
            linkFormatter={linkFormatter}
          />
        </div>
      </div>
    </div>
  )
}
