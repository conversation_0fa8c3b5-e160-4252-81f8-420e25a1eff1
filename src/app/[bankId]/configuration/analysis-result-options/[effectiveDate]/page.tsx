'use client'
import { useState } from 'react'
import { ColumnDef, ColumnFiltersState } from '@tanstack/react-table'
import { ChevronRightIcon } from '@heroicons/react/24/outline'
import { useRouter } from 'next/navigation'
import { useQuery } from '@tanstack/react-query'
import { z } from 'zod'

import { Button } from '@/components/Button'
import { MonthPicker } from '@/components/Filter/MonthPicker'
import { SearchBar } from '@/components/SearchBar'
import { SortedTable } from '@/components/Table/SortedTable'
import { RowPopover } from '../../_components/RowPopover'

import { routeTo, useRoute } from '../../routing'
import { unwrap } from '@/lib/unions/unwrap'
import { apiToFormSchemas } from '@/api/apiToFormSchema'
import { analysisResultOptionsQueries } from '../queries'
import { formatToMonthYearFromDate, formatToServerString } from '@/lib/date'

export default function AllAnalysisResultOptions() {
  const [searchText, setSearchText] = useState('')
  const [numVisibleAnalysisResultOptions, setNumVisibleAnalysisResultOptions] =
    useState(0)
  const columnFilters: ColumnFiltersState = [{ id: 'name', value: searchText }]

  const router = useRouter()
  const route = unwrap(
    useRoute(),
    '/configuration/analysis-result-options/[effectiveDate]',
  )!

  const payload = {
    effectiveDate: formatToServerString(route.params.effectiveDate),
  }
  const { data, status, error } = useQuery(
    analysisResultOptionsQueries(
      '/listAnalysisResultOptionsByEffectiveDate',
      payload,
    ),
  )
  if (status === 'pending') return 'Loading...'
  if (!data || error) {
    return <div>404 Not Found</div>
  }

  const { analysisResultOption: analysisResultOptionType } = apiToFormSchemas
  const analysisResultOptions = data?.map((item) => {
    return analysisResultOptionType.parse(item)
  })

  const handleAddAnalysisResultOptionsClick = () => {
    router.push(
      routeTo(
        '/configuration/analysis-result-options/[effectiveDate]/add',
        route.params,
      ),
    )
  }

  const columns: ColumnDef<z.infer<typeof analysisResultOptionType>>[] = [
    {
      header: 'Name',
      accessorKey: 'name',
    },
    {
      header: 'Code',
      accessorKey: 'code',
    },
    {
      header: '',
      accessorKey: 'none',
      cell: ({
        row: {
          original: { code },
        },
      }) => (
        <RowPopover
          className='pr-4'
          onEdit={() => {
            router.push(
              routeTo(
                '/configuration/analysis-result-options/[effectiveDate]/edit/[analysisResultOptionCode]',
                {
                  ...route.params,
                  effectiveDate: formatToMonthYearFromDate(
                    route.params.effectiveDate,
                  ),
                  analysisResultOptionCode: code,
                },
              ),
            )
          }}
        />
      ),
      meta: { className: 'basis-28 shrink-0 justify-end px-2' },
    },
  ]

  return (
    <div className='flex min-h-0 flex-1 flex-col py-6 pl-12 pr-3'>
      <div className='flex items-center text-zinc-500'>
        Configurations
        <ChevronRightIcon className='h-4 w-4 text-zinc-400' />
        <span className='text-lg font-semibold text-black'>
          Analysis result options
        </span>
      </div>
      <div className='mt-12 flex min-h-0 w-full flex-auto flex-grow flex-col rounded-lg border bg-white p-6'>
        <div className='flex w-full items-center justify-between'>
          <div className=''>
            <div className='text-lg font-semibold'>Analysis result options</div>
            <div className='text-zinc-400'>
              View settlement actions scheduled to occur based on monthly
              analysis results.
            </div>
          </div>
          <Button className='btn' onClick={handleAddAnalysisResultOptionsClick}>
            Add analysis result options
          </Button>
        </div>
        <div className='mt-8 flex min-h-0 flex-auto flex-col'>
          <div className='flex items-center'>
            <MonthPicker
              title='Effective date'
              prefix='View data effective on'
              showIcon={true}
              onDateChange={(effectiveDate) => {
                router.push(
                  routeTo(
                    '/configuration/analysis-result-options/[effectiveDate]',
                    { ...route.params, effectiveDate },
                  ),
                )
              }}
              initialDate={route.params.effectiveDate}
            />
            <div className='flex flex-auto items-center justify-end gap-4'>
              <SearchBar
                className='w-80'
                defaultLabel='Search analysis result options'
                value={searchText}
                onValueChange={setSearchText}
              />
              <p className='text-sm text-zinc-400'>
                {numVisibleAnalysisResultOptions} analysis result options
                {numVisibleAnalysisResultOptions > 1 && 's'}
              </p>
            </div>
          </div>
          <div className='mt-4 flex min-h-0 flex-col'>
            <SortedTable
              data={analysisResultOptions}
              columns={columns}
              columnFilters={columnFilters}
              onVisibleRowsChanged={(rows) =>
                setNumVisibleAnalysisResultOptions(rows.length)
              }
              initialSortingState={[{ desc: false, id: 'name' }]}
              handleRowDoubleClick={({ original: { code } }) =>
                router.push(
                  routeTo(
                    '/configuration/analysis-result-options/[effectiveDate]/view/[analysisResultOptionCode]',
                    {
                      ...route.params,
                      analysisResultOptionCode: code,
                      effectiveDate: formatToMonthYearFromDate(
                        route.params.effectiveDate,
                      ),
                    },
                  ),
                )
              }
            />
          </div>
        </div>
      </div>
    </div>
  )
}
