'use client'

import { useRouter, useParams } from 'next/navigation'
import { useQuery, useMutation } from '@tanstack/react-query'

import { Button } from '@/components/Button'
import UpdateAnalysisResultOptionsForm from '../../../_components/UpdateAnalysisResultOptionsForm'
import { UpdateAnalysisResultOptionsFormState } from '../../../_components/UpdateAnalysisResultOptionsFormTypes'

import { unwrap } from '@/lib/unions/unwrap'
import { routeTo, useRoute } from '@/app/[bankId]/configuration/routing'
import { analysisResultOptionsQueries } from '../../../queries'
import { analysisResultOptionsMutation } from '../../../mutations'
import { formToApiSchemas } from '@/api/formToApiSchema'
import { formatToServerString, formatToMonthYearFromDate } from '@/lib/date'
import { apiToFormSchemas } from '@/api/apiToFormSchema'

export default function EditAnalysisResultOption() {
  const router = useRouter()
  const route = unwrap(
    useRoute(),
    '/configuration/analysis-result-options/[effectiveDate]/edit/[analysisResultOptionCode]',
  )!

  const effectiveDate = route.params.effectiveDate

  const payload = {
    code: route.params.analysisResultOptionCode,
  }
  const { data, status, error } = useQuery(
    analysisResultOptionsQueries('/getAnalysisResultOptionsByCode', payload),
  )

  const updateAnalysisResultOption = useMutation(
    analysisResultOptionsMutation('/updateAnalysisResultOption', {
      onSuccess: (_, request) => {
        router.push(
          routeTo(
            '/configuration/analysis-result-options/[effectiveDate]/view/[analysisResultOptionCode]',
            {
              ...route.params,
              analysisResultOptionCode: request.code!,
              effectiveDate: formatToMonthYearFromDate(request.effectiveDate!),
            },
          ),
        )
      },
    }),
  )

  if (status === 'pending') return 'Loading...'
  if (!data || error) {
    return <div>404 Not Found</div>
  }

  const { analysisResultOption: analysisResultOptionSchema } = apiToFormSchemas
  const analysisResultOptions = data
    .map((item) => {
      const parsed = analysisResultOptionSchema.parse(item)
      return {
        ...parsed,
        effectiveDate: formatToMonthYearFromDate(parsed.effectiveDate),
        minChargeWaiveAmount:
          parsed.minChargeWaiveAmount ?
            parseFloat(parsed.minChargeWaiveAmount)
          : null,
        maxChargeWaiveAmount:
          parsed.maxChargeWaiveAmount ?
            parseFloat(parsed.maxChargeWaiveAmount)
          : null,
        markdownRate:
          parsed.markdownRate ? parseFloat(parsed.markdownRate) : null,
        markupRate: parsed.markupRate ? parseFloat(parsed.markupRate) : null,
        waiveCycle: parsed.waiveCycle ? Number(parsed.waiveCycle) : null,
        settlementOverrideDateEachMonth:
          parsed.settlementOverrideDateEachMonth ?
            Number(parsed.settlementOverrideDateEachMonth)
          : null,
        delay: parsed.delay ? Number(parsed.delay) : null,
        daysAfter: parsed.daysAfter ? Number(parsed.daysAfter) : null,
      }
    })
    .sort(
      (a, b) =>
        new Date(b.effectiveDate).getDate() -
        new Date(a.effectiveDate).getDate(),
    )
  const analysisResultOption =
    analysisResultOptions.find((item) => {
      return item.effectiveDate === effectiveDate
    }) || analysisResultOptions[0]

  const defaultValues: UpdateAnalysisResultOptionsFormState = {
    analysisResultInfo: {
      name: analysisResultOption.name,
      effectiveDate: analysisResultOption.effectiveDate,
      code: analysisResultOption.code,
    },
    analysisResultSettlementWaiver: {
      minChargeWaiveAmount: analysisResultOption.minChargeWaiveAmount,
      maxChargeWaiveAmount: analysisResultOption.maxChargeWaiveAmount,
    },
    analysisResultSettlement: {
      analysisChargeType: analysisResultOption.analysisChargeType,
      analysisDirectDebitTrailer:
        analysisResultOption.analysisDirectDebitTrailer,
      hardCharge: analysisResultOption.hardCharge,
      hardDirectDebitTrailer: analysisResultOption.hardDirectDebitTrailer,
      excessCredits: analysisResultOption.excessCredits,
      markdownRate: analysisResultOption.markdownRate,
      markdownStatementLabel: analysisResultOption.markdownStatementLabel,
      markupRate: analysisResultOption.markupRate,
      markupStatementLabel: analysisResultOption.markupStatementLabel,
      waiveCycle: analysisResultOption.waiveCycle,
    },
    settlementDate: {
      settlementOverrideType:
        analysisResultOption.settlementOverrideType ?? 'NO_OVERRIDE',
      settlementOverrideDateEachMonth:
        analysisResultOption.settlementOverrideDateEachMonth,
      delay: analysisResultOption.delay,
      daysAfter: analysisResultOption.daysAfter,
    },
  }

  const serializeForm = (formState: UpdateAnalysisResultOptionsFormState) => {
    const { analysisResultOption } = formToApiSchemas
    const {
      analysisResultInfo,
      analysisResultSettlement,
      analysisResultSettlementWaiver,
      settlementDate,
    } = formState

    return analysisResultOption.parse({
      ...analysisResultInfo,
      effectiveDate: formatToServerString(analysisResultInfo.effectiveDate),
      minChargeWaiveAmount:
        analysisResultSettlementWaiver.minChargeWaiveAmount?.toString() || null,
      maxChargeWaiveAmount:
        analysisResultSettlementWaiver.maxChargeWaiveAmount?.toString() || null,
      ...analysisResultSettlement,
      markdownRate: analysisResultSettlement.markdownRate?.toString() || null,
      markupRate: analysisResultSettlement.markupRate?.toString() || null,
      waiveCycle: Number(analysisResultSettlement.waiveCycle) ?? null,
      ...settlementDate,
      delay: settlementDate.delay?.toString() || null,
      settlementOverrideDateEachMonth:
        settlementDate.settlementOverrideType === 'SAME_DATE_EACH_MONTH' ?
          settlementDate.settlementOverrideDateEachMonth
        : null,
      daysAfter:
        (
          settlementDate.settlementOverrideType ===
          'SPECIFIC_DAYS_AFTER_PRELIM_ANALYSIS'
        ) ?
          (Number(settlementDate.daysAfter) ?? null)
        : null,
    })
  }

  return (
    <div className='w-full overflow-hidden overflow-y-scroll pt-12'>
      <div className='text-md ml-8 font-medium'>
        Edit analysis result options
      </div>
      <UpdateAnalysisResultOptionsForm
        defaultValues={defaultValues}
        onSubmit={(formState) => {
          updateAnalysisResultOption.mutate(serializeForm(formState))
        }}
        isEditMode
      >
        <Button className='btn w-60' onClick={() => router.back()}>
          Cancel
        </Button>

        <Button type='submit' className='btn-primary w-60'>
          Save
        </Button>
      </UpdateAnalysisResultOptionsForm>
    </div>
  )
}
