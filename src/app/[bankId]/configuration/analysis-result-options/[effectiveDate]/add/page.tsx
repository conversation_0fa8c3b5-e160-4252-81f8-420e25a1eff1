'use client'

import { useRouter } from 'next/navigation'
import { useMutation } from '@tanstack/react-query'

import { Button } from '@/components/Button'
import UpdateAnalysisResultOptionsForm from '../../_components/UpdateAnalysisResultOptionsForm'
import { UpdateAnalysisResultOptionsFormState } from '../../_components/UpdateAnalysisResultOptionsFormTypes'

import { formatToMonthYearFromDate, formatToServerString } from '@/lib/date'
import { unwrap } from '@/lib/unions/unwrap'
import { routeTo, useRoute } from '../../../routing'
import { analysisResultOptionsMutation } from '../../mutations'
import { formToApiSchemas } from '@/api/formToApiSchema'

export default function AddAnalysisResultOption() {
  const router = useRouter()
  const route = unwrap(
    useRoute(),
    '/configuration/analysis-result-options/[effectiveDate]/add',
  )!

  const defaultValues: UpdateAnalysisResultOptionsFormState = {
    analysisResultInfo: {
      name: '',
      effectiveDate: formatToMonthYearFromDate(new Date()),
      code: '',
    },
    analysisResultSettlementWaiver: {
      minChargeWaiveAmount: null,
      maxChargeWaiveAmount: null,
    },
    analysisResultSettlement: {
      analysisChargeType: null,
      analysisDirectDebitTrailer: '',
      hardCharge: null,
      hardDirectDebitTrailer: '',
      excessCredits: null,
      markdownRate: null,
      markdownStatementLabel: '',
      markupRate: null,
      markupStatementLabel: '',
      waiveCycle: null,
    },
    settlementDate: {
      settlementOverrideType: 'NO_OVERRIDE',
      settlementOverrideDateEachMonth: null,
      delay: null,
      daysAfter: null,
    },
  }
  const addAnalysisResultOption = useMutation(
    analysisResultOptionsMutation('/addAnalysisResultOption', {
      onSuccess: (_, request) => {
        router.push(
          routeTo(
            '/configuration/analysis-result-options/[effectiveDate]/view/[analysisResultOptionCode]',
            {
              ...route.params,
              effectiveDate: formatToMonthYearFromDate(request.effectiveDate!),
              analysisResultOptionCode: request.code!,
            },
          ),
        )
      },
    }),
  )

  const serializeForm = (formState: UpdateAnalysisResultOptionsFormState) => {
    const { analysisResultOption } = formToApiSchemas
    const {
      analysisResultInfo,
      analysisResultSettlementWaiver,
      analysisResultSettlement,
      settlementDate,
    } = formState

    if (settlementDate.settlementOverrideType === 'SAME_DATE_EACH_MONTH') {
      return analysisResultOption.parse({
        ...analysisResultInfo,
        effectiveDate: formatToServerString(analysisResultInfo.effectiveDate),
        ...analysisResultSettlementWaiver,
        ...analysisResultSettlement,
        waiveCycle: Number(analysisResultSettlement.waiveCycle) ?? null,
        ...settlementDate,
        settlementOverrideDateEachMonth:
          settlementDate.settlementOverrideDateEachMonth,
        daysAfter: null,
      })
    } else {
      return analysisResultOption.parse({
        ...analysisResultInfo,
        effectiveDate: formatToServerString(analysisResultInfo.effectiveDate),
        ...analysisResultSettlementWaiver,
        ...analysisResultSettlement,
        waiveCycle: Number(analysisResultSettlement.waiveCycle) ?? null,
        ...settlementDate,
        daysAfter: Number(settlementDate.daysAfter) ?? null,
        settlementOverrideDateEachMonth: null,
      })
    }
  }
  return (
    <div className='w-full overflow-hidden overflow-y-scroll pt-12'>
      <div className='text-md ml-8 font-medium'>
        Add analysis result options
      </div>
      <UpdateAnalysisResultOptionsForm
        defaultValues={defaultValues}
        onSubmit={(formState) => {
          addAnalysisResultOption.mutate(serializeForm(formState))
        }}
      >
        <Button className='btn w-60' onClick={() => router.back()}>
          Cancel
        </Button>

        <Button type='submit' className='btn-primary w-60'>
          Save
        </Button>
      </UpdateAnalysisResultOptionsForm>
    </div>
  )
}
