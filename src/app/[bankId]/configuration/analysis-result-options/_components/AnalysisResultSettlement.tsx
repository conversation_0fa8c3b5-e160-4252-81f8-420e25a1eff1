'use client'
import { useStore } from '@tanstack/react-form'

import { If } from '@/components/If'
import { useFormTextInput } from '@/components/Form/useFormTextInput'
import { useFormSelect } from '@/components/Form/useFormSelect'
import {
  analysisResultSettlementSchema,
  UpdateAnalysisResultOptionsFormApi,
  UpdateAnalysisResultOptionsFormState,
  analysisChargeTypeShape,
  hardChargeShape,
  excessCreditsShape,
  // settlementOverrideType,
  analysisChargeTypeLabel,
  hardChargeLabel,
  excessCreditsLabel,
  // settlementOverrideTypeLabel,
} from './UpdateAnalysisResultOptionsFormTypes'
import { chargeTypeOptions, analysisChargeTypeOptions } from '../../types'
interface AnalysisResultSettlementProps {
  form: UpdateAnalysisResultOptionsFormApi
}
export default function AnalysisResultSettlement({
  form,
}: AnalysisResultSettlementProps) {
  const [analysisChargeType, hardCharge, markdownRate, markupRate] = useStore(
    form.store,
    (state) => [
      state.values.analysisResultSettlement.analysisChargeType,
      state.values.analysisResultSettlement.hardCharge,
      state.values.analysisResultSettlement.markdownRate,
      state.values.analysisResultSettlement.markupRate,
    ],
  )
  const FormInput = useFormTextInput<UpdateAnalysisResultOptionsFormState>({
    form,
  })
  const Select = useFormSelect<UpdateAnalysisResultOptionsFormState>({ form })

  return (
    <div className='flex w-full flex-col rounded-lg border bg-white p-6'>
      <div className='font-semibold'>Analysis result settlement</div>
      <div className='text-sm text-zinc-500'>
        Configure how account fees are settled.
      </div>
      <div className='mt-3 flex gap-9'>
        <Select
          name='analysisResultSettlement.analysisChargeType'
          label='Analysis charges'
          required
          validators={{
            onChange: analysisResultSettlementSchema.shape.analysisChargeType,
          }}
          options={analysisChargeTypeShape.options}
          renderOption={analysisChargeTypeLabel}
          renderSelected={analysisChargeTypeLabel}
        />
        <If true={analysisChargeType !== chargeTypeOptions.DIRECT_DEBIT}>
          <div className='flex-1'></div>
        </If>
        <If true={analysisChargeType === chargeTypeOptions.DIRECT_DEBIT}>
          <FormInput
            name='analysisResultSettlement.analysisDirectDebitTrailer'
            label='Additional direct debit information'
            validators={{
              onChange:
                analysisResultSettlementSchema.shape.analysisDirectDebitTrailer,
            }}
          />
        </If>
      </div>
      <div className='flex gap-9'>
        <Select
          name='analysisResultSettlement.hardCharge'
          label='Hard charges'
          validators={{
            onChange: analysisResultSettlementSchema.shape.hardCharge,
          }}
          options={hardChargeShape.options}
          renderOption={hardChargeLabel}
          renderSelected={hardChargeLabel}
          required
        />
        <If true={hardCharge !== chargeTypeOptions.DIRECT_DEBIT}>
          <div className='flex-1'></div>
        </If>
        <If true={hardCharge === chargeTypeOptions.DIRECT_DEBIT}>
          <FormInput
            name='analysisResultSettlement.hardDirectDebitTrailer'
            label='Additional direct debit information'
            validators={{
              onChange:
                analysisResultSettlementSchema.shape.hardDirectDebitTrailer,
            }}
          />
        </If>
      </div>
      <div className='flex gap-9'>
        <Select
          name='analysisResultSettlement.excessCredits'
          label='Excess credits'
          validators={{
            onChange: analysisResultSettlementSchema.shape.excessCredits,
          }}
          options={excessCreditsShape.options}
          renderOption={excessCreditsLabel}
          renderSelected={excessCreditsLabel}
          required
        />
        <div className='flex-1'></div>
      </div>
      <div className='flex gap-9'>
        <FormInput
          name='analysisResultSettlement.markdownRate'
          label='Markdown rate'
          type='number'
          invalidChars={['e', 'E']}
          suffix='%'
          validators={{
            onChange: ({ value }) => {
              const result =
                analysisResultSettlementSchema.shape.markdownRate.safeParse(
                  value,
                )
              return result.success ? undefined : result.error.issues[0].message
            },
          }}
        />
        <If true={markdownRate === null}>
          <div className='flex-1'></div>
        </If>
        <If true={markdownRate !== null}>
          <FormInput
            name='analysisResultSettlement.markdownStatementLabel'
            label='Statement label'
            validators={{
              onChange:
                analysisResultSettlementSchema.shape.markdownStatementLabel,
            }}
          />
        </If>
      </div>
      <div className='flex gap-9'>
        <FormInput
          name='analysisResultSettlement.markupRate'
          label='Markup rate'
          type='number'
          invalidChars={['e', 'E']}
          suffix='%'
          validators={{
            onChange: ({ value }) => {
              const result =
                analysisResultSettlementSchema.shape.markupRate.safeParse(value)
              return result.success ? undefined : result.error.issues[0].message
            },
          }}
        />
        <If true={markupRate === null || markupRate === 0}>
          <div className='flex-1'></div>
        </If>
        <If true={markupRate !== null}>
          <FormInput
            name='analysisResultSettlement.markupStatementLabel'
            label='Statement label'
            validators={{
              onChange:
                analysisResultSettlementSchema.shape.markupStatementLabel,
            }}
          />
        </If>
      </div>
      <div className='flex gap-9'>
        <FormInput
          name='analysisResultSettlement.waiveCycle'
          label='Number of cycles to waive for a new account'
          type='number'
          invalidChars={['e', 'E']}
          validators={{
            onChange: analysisResultSettlementSchema.shape.waiveCycle,
          }}
        />
        <div className='flex-1'></div>
      </div>
    </div>
  )
}
