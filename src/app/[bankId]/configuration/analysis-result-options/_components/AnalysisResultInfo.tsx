'use client'

import { useFormTextInput } from '@/components/Form/useFormTextInput'
import { useFormMonthPicker } from '@/components/Form/useFormMonthPicker'
import {
  analysisResultInfoSchema,
  UpdateAnalysisResultOptionsFormApi,
  UpdateAnalysisResultOptionsFormState,
} from './UpdateAnalysisResultOptionsFormTypes'

interface AnalysisResultOptionsInfoProps {
  form: UpdateAnalysisResultOptionsFormApi
  isEditMode?: boolean
}
export default function AnalysisResultOptionsInfo({
  form,
  isEditMode,
}: AnalysisResultOptionsInfoProps) {
  const FormInput = useFormTextInput<UpdateAnalysisResultOptionsFormState>({
    form,
  })
  const FormMonthPicker =
    useFormMonthPicker<UpdateAnalysisResultOptionsFormState>({
      form,
    })
  return (
    <div className='mt-10 flex w-full flex-col rounded-lg border bg-white p-6'>
      <div className='font-semibold'>Analysis information</div>
      <div className='mt-3 flex gap-9'>
        <div className='flex flex-1 flex-col gap-2'>
          <FormMonthPicker
            name='analysisResultInfo.effectiveDate'
            label='Effective date *'
          />
        </div>
        <div className='flex-1'></div>
      </div>
      <div className='mt-6 flex gap-9'>
        <FormInput
          name='analysisResultInfo.name'
          label='Name'
          required
          validators={{
            onChange: analysisResultInfoSchema.shape.name,
          }}
        />
        <FormInput
          name='analysisResultInfo.code'
          label='Code'
          required
          validators={{
            onChange: analysisResultInfoSchema.shape.code,
          }}
          placeholder='Max 5 digits'
          type='number'
          invalidChars={['e', 'E']}
          readonly={(field) =>
            field.value != null &&
            field.value.length > 0 &&
            field.meta.isPristine &&
            !!isEditMode
          }
        />
      </div>
    </div>
  )
}
