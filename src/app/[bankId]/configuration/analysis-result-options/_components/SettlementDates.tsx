'use client'
import { useStore } from '@tanstack/react-form'

import { If } from '@/components/If'
import { useFormTextInput } from '@/components/Form/useFormTextInput'
import { useFormSelect } from '@/components/Form/useFormSelect'
import {
  settlementDateSchema,
  UpdateAnalysisResultOptionsFormApi,
  UpdateAnalysisResultOptionsFormState,
  settlementOverrideTypeShape,
  settlementOverrideTypeLabel,
} from './UpdateAnalysisResultOptionsFormTypes'

interface AnalysisResultOptionsInfoProps {
  form: UpdateAnalysisResultOptionsFormApi
}

const daysOfTheMonth: number[] = Array.from(
  { length: 31 },
  (_, index) => index + 1,
)

export default function SettlementDates({
  form,
}: AnalysisResultOptionsInfoProps) {
  const [settlementOverrideType] = useStore(form.store, (state) => [
    state.values.settlementDate.settlementOverrideType,
  ])
  const FormInput = useFormTextInput<UpdateAnalysisResultOptionsFormState>({
    form,
  })
  const Select = useFormSelect<UpdateAnalysisResultOptionsFormState>({ form })

  return (
    <div className='flex w-full flex-col rounded-lg border bg-white p-6'>
      <div className='font-semibold'>Settlement dates</div>
      <div className='mt-3 flex gap-9'>
        <Select
          name='settlementDate.settlementOverrideType'
          label='Final analysis override'
          placeholder='Select an override date'
          validators={{
            onChange: settlementDateSchema.shape.settlementOverrideType,
          }}
          options={settlementOverrideTypeShape.options}
          renderOption={settlementOverrideTypeLabel}
          renderSelected={settlementOverrideTypeLabel}
        />
        <div className='flex-1'></div>
      </div>
      <If
        true={settlementOverrideType === 'SPECIFIC_DAYS_AFTER_PRELIM_ANALYSIS'}
      >
        <div className='flex gap-9'>
          <FormInput
            name='settlementDate.daysAfter'
            label='Number of days after preliminary analysis'
            type='number'
            validators={{
              onChange: settlementDateSchema.shape.daysAfter,
            }}
            invalidChars={['e', 'E']}
            required
          />
          <div className='flex-1'></div>
        </div>
      </If>
      <If true={settlementOverrideType === 'SAME_DATE_EACH_MONTH'}>
        <div className='flex gap-9'>
          <Select
            name='settlementDate.settlementOverrideDateEachMonth'
            label='Same date each month'
            options={daysOfTheMonth}
            validators={{
              onChange:
                settlementDateSchema.shape.settlementOverrideDateEachMonth,
            }}
            required
          />
          <div className='flex-1'></div>
        </div>
      </If>

      <div className='flex gap-9'>
        <FormInput
          name='settlementDate.delay'
          label='Debit/Credit delay'
          validators={{
            onChange: settlementDateSchema.shape.delay,
          }}
          placeholder='Add number of days'
          type='number'
          invalidChars={['e', 'E']}
        />
        <div className='flex-1'></div>
      </div>
    </div>
  )
}
