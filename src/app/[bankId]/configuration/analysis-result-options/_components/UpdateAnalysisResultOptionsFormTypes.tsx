import { z } from 'zod'
import { Form<PERSON>pi, ReactFormApi } from '@tanstack/react-form'

import { nullableRequired } from '@/lib/validation/nullableRequiredNativeEnum'
import { percentageWithDecimals } from '@/lib/validation/percentageWithDecimals'
import { name, configCode } from '@/api/zodSchemas'
import { hasMaxDecimalPlaces } from '@/lib/validation/hasMaxDecimalPlaces'
import { apiToFormSchemas } from '@/api/apiToFormSchema'
import { match } from '@/lib/unions/match'
import { variant } from '@/lib/unions/Union'

const { analysisResultOption } = apiToFormSchemas

export const analysisResultInfoSchema = z.object({
  name,
  effectiveDate: z.string(),
  code: configCode,
})

export const priceValueSchema = z.coerce
  .number()
  .gt(0)
  .max(100_000_000)
  .superRefine(hasMaxDecimalPlaces(4))

type AnalysisResultInfoState = z.infer<typeof analysisResultInfoSchema>

export const analysisResultSettlementWaiverSchema = z.object({
  minChargeWaiveAmount: priceValueSchema.nullable(),
  maxChargeWaiveAmount: priceValueSchema.nullable(),
})

type AnalysisResultSettlementWaiverState = z.infer<
  typeof analysisResultSettlementWaiverSchema
>

export const analysisResultSettlementSchema = z.object({
  analysisChargeType: nullableRequired(
    analysisResultOption.shape.analysisChargeType,
  ),
  analysisDirectDebitTrailer: z.string().nullable(),
  hardCharge: nullableRequired(analysisResultOption.shape.hardCharge),
  hardDirectDebitTrailer: z.string().nullable(),
  excessCredits: nullableRequired(analysisResultOption.shape.excessCredits),
  markdownRate: percentageWithDecimals.nullable(),
  markdownStatementLabel: z.string().nullable(),
  markupRate: percentageWithDecimals.nullable(),
  markupStatementLabel: z.string().nullable(),
  waiveCycle: z.coerce.number().nullable(),
})

type AnalysisResultSettlementState = z.infer<
  typeof analysisResultSettlementSchema
>

export const settlementDateSchema = z.object({
  settlementOverrideType: nullableRequired(
    analysisResultOption.shape.settlementOverrideType,
  ),
  settlementOverrideDateEachMonth: nullableRequired(z.number()),
  daysAfter: nullableRequired(z.coerce.number()),
  delay: z.coerce.number().nullable(),
})

type SettlementDateSchemaState = z.infer<typeof settlementDateSchema>

// TODO create composite zod schema instead (e.g., updateAnalysisResultOptionsSchema) and infer this type from that schema.
// This new composite schema could also be used as onChange validator in useForm in UpdateAnalysisResultOptionsForm to reduce
// need to define validators.onChange on every form field.
export type UpdateAnalysisResultOptionsFormState = {
  analysisResultInfo: AnalysisResultInfoState
  analysisResultSettlementWaiver: AnalysisResultSettlementWaiverState
  analysisResultSettlement: AnalysisResultSettlementState
  settlementDate: SettlementDateSchemaState
}

export type UpdateAnalysisResultOptionsFormApi =
  ReactFormApi<UpdateAnalysisResultOptionsFormState> &
    FormApi<UpdateAnalysisResultOptionsFormState>

export const analysisChargeTypeShape =
  analysisResultOption.shape.analysisChargeType
export const hardChargeShape = analysisResultOption.shape.hardCharge
export const excessCreditsShape = analysisResultOption.shape.excessCredits
export const settlementOverrideTypeShape =
  analysisResultOption.shape.settlementOverrideType

export const analysisChargeTypeLabel = (
  value: z.infer<typeof analysisChargeTypeShape>,
) =>
  match(variant(value), {
    DIRECT_DEBIT: () => 'Direct debit',
    WAIVE: () => 'Waive',
  })

export const hardChargeLabel = (value: z.infer<typeof hardChargeShape>) =>
  match(variant(value), {
    DIRECT_DEBIT: () => 'Direct debit',
    WAIVE: () => 'Waive',
  })

export const excessCreditsLabel = (value: z.infer<typeof excessCreditsShape>) =>
  match(variant(value), {
    WAIVE: () => 'Waive',
  })

export const settlementOverrideTypeLabel = (
  value: z.infer<typeof settlementOverrideTypeShape>,
) =>
  match(variant(value), {
    NO_OVERRIDE: () => 'No override',
    SAME_DATE_EACH_MONTH: () => 'Same date each month',
    SPECIFIC_DAYS_AFTER_PRELIM_ANALYSIS: () =>
      'Specific number of days after preliminary analysis',
  })

export const analysisResultLabels: Record<string, string> = {
  DIRECT_DEBIT: 'Direct debit',
  WAIVE: 'Waive',
  SAME_DATE_EACH_MONTH: 'Same date each month',
  SPECIFIC_DAYS_AFTER_PRELIM_ANALYSIS:
    'Specific number of days after preliminary analysis',
  NO_OVERRIDE: 'No override',
}
