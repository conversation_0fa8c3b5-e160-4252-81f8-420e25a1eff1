'use client'

import { useFormTextInput } from '@/components/Form/useFormTextInput'
import {
  analysisResultSettlementWaiverSchema,
  UpdateAnalysisResultOptionsFormApi,
  UpdateAnalysisResultOptionsFormState,
} from './UpdateAnalysisResultOptionsFormTypes'

interface AnalysisResultOptionsInfoProps {
  form: UpdateAnalysisResultOptionsFormApi
}
export default function AnalysisResultSettlementWaiver({
  form,
}: AnalysisResultOptionsInfoProps) {
  const FormInput = useFormTextInput<UpdateAnalysisResultOptionsFormState>({
    form,
  })
  return (
    <div className='flex w-full flex-col rounded-lg border bg-white p-6'>
      <div className='font-semibold'>Analysis result settlement waiver</div>
      <div className='mt-3 flex gap-9'>
        <FormInput
          name='analysisResultSettlementWaiver.minChargeWaiveAmount'
          label='Waive analysis charges under'
          validators={{
            onChange:
              analysisResultSettlementWaiverSchema.shape.minChargeWaiveAmount,
          }}
          prefix='$'
          type='number'
          invalidChars={['e', 'E']}
        />
        <FormInput
          name='analysisResultSettlementWaiver.maxChargeWaiveAmount'
          label='Waive analysis charges over'
          type='number'
          validators={{
            onChange:
              analysisResultSettlementWaiverSchema.shape.maxChargeWaiveAmount,
          }}
          prefix='$'
          invalidChars={['e', 'E']}
        />
      </div>
    </div>
  )
}
