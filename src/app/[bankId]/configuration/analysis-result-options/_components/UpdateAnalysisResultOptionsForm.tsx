'use client'
import { useForm } from '@tanstack/react-form'

import { UpdateAnalysisResultOptionsFormState } from './UpdateAnalysisResultOptionsFormTypes'
import AnalysisResultInfo from './AnalysisResultInfo'
import AnalysisResultSettlementWaiver from './AnalysisResultSettlementWaiver'
import AnalysisResultSettlement from './AnalysisResultSettlement'
import SettlementDates from './SettlementDates'

interface UpdateAnalysisResultOptionsFormProps {
  defaultValues: UpdateAnalysisResultOptionsFormState
  onSubmit: (state: UpdateAnalysisResultOptionsFormState) => void
  isEditMode?: boolean
}

export default function UpdateAnalysisResultOptionsForm({
  defaultValues,
  onSubmit,
  children,
  isEditMode,
}: React.PropsWithChildren<UpdateAnalysisResultOptionsFormProps>) {
  const form = useForm({
    defaultValues,
    onSubmit: ({ value }) => {
      onSubmit(value)
    },
  })

  // TODO can add `validators` prop with `onChange` pointing to composite zod schema
  // so we don't need to define `validators.onChange` on every form field.
  return (
    <form
      className='flex min-h-screen flex-col'
      onSubmit={async (event) => {
        form.reset(form.state.values)
        event.preventDefault()
        event.stopPropagation()
        await form.handleSubmit()
      }}
    >
      <div className='flex flex-grow flex-col gap-2 overflow-y-auto px-8'>
        <AnalysisResultInfo form={form} isEditMode={isEditMode} />
        <AnalysisResultSettlementWaiver form={form} />
        <AnalysisResultSettlement form={form} />
        <SettlementDates form={form} />
      </div>
      <div className='sticky bottom-0 mt-auto flex justify-end gap-4 border-t bg-white px-12 py-10'>
        {children}
      </div>
    </form>
  )
}
