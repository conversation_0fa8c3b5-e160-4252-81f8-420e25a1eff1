import { withMetrics } from '@/lib/middleware/withMetrics'
import { defineMutations, defineMutation } from '@/lib/state/defineMutation'
import { revenueConnectClient } from '@/api/revenueConnectClient'
import { compose } from '@/lib/functional/compose'

import { analysisResultOptionsQueries } from './queries'
import { notifications } from './notifications'

const createAnalysisResultOption = defineMutation(
  revenueConnectClient,
  '/addAnalysisResultOption',
  {
    invalidate: (request) => [
      analysisResultOptionsQueries(
        '/listAnalysisResultOptionsByEffectiveDate',
        {
          effectiveDate: request.effectiveDate,
        },
      ),
      analysisResultOptionsQueries('/getAnalysisResultOptionsByCode', {
        code: request.code,
      }),
    ],
  },
)

const updateAnalysisResultOption = defineMutation(
  revenueConnectClient,
  '/updateAnalysisResultOption',
  {
    invalidate: (request) => [
      analysisResultOptionsQueries(
        '/listAnalysisResultOptionsByEffectiveDate',
        {
          effectiveDate: request.effectiveDate,
        },
      ),
      analysisResultOptionsQueries('/getAnalysisResultOptionsByCode', {
        code: request.code,
      }),
    ],
  },
)
export type AnalysisResultOptionsMutation =
  | typeof createAnalysisResultOption
  | typeof updateAnalysisResultOption

export const analysisResultOptionsMutation = defineMutations(
  [createAnalysisResultOption, updateAnalysisResultOption],
  compose(notifications, withMetrics()),
)
