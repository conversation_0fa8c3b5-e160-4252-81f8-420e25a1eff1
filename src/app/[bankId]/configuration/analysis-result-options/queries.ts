import { revenueConnectClient } from '@/api/revenueConnectClient'
import { defineQueries, defineQuery } from '@/lib/state/defineQuery'
import { withMetrics } from '@/lib/middleware/withMetrics'

const getAnalysisResultOptionsByEffectiveDate = defineQuery(
  revenueConnectClient,
  '/listAnalysisResultOptionsByEffectiveDate',
  (payload) => [payload.effectiveDate],
)

const getAnalysisResultOptionsByCode = defineQuery(
  revenueConnectClient,
  '/getAnalysisResultOptionsByCode',
  (payload) => [payload.code],
)

export const analysisResultOptionsQueries = defineQueries(
  [getAnalysisResultOptionsByEffectiveDate, getAnalysisResultOptionsByCode],
  withMetrics(),
)
