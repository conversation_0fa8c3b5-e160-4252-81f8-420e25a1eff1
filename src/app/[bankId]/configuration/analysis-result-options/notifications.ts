import { AnalysisResultOptionsMutation } from './mutations'
import { Tag } from '@/lib/unions/Union'
import { withNotifications } from '@/components/Notifications'

export const messages: { [T in Tag<AnalysisResultOptionsMutation>]: string } = {
  '/addAnalysisResultOption': 'Analysis Result Option successfully created.',
  '/updateAnalysisResultOption': 'Analysis Result Option successfully updated.',
}

export const notifications = withNotifications(messages)
