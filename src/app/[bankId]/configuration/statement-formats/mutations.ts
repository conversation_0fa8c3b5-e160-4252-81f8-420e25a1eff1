import { statementFormatPlanQueries } from './queries'
import { withMetrics } from '@/lib/middleware/withMetrics'
import { defineMutations, defineMutation } from '@/lib/state/defineMutation'
import { revenueConnectClient } from '@/api/revenueConnectClient'
import { compose } from '@/lib/functional/compose'
import { notifications } from './notifications'

const createStatementFormatPlan = defineMutation(
  revenueConnectClient,
  '/createStatementFormatPlan',
  {
    invalidate: (request) => [
      statementFormatPlanQueries('/getStatementFormatPlans', {
        effectiveDate: request.effectiveDate,
      }),
      statementFormatPlanQueries('/getStatementFormatPlansByCode', {
        code: request.code,
      }),
    ],
  },
)

const updateStatementFormatPlan = defineMutation(
  revenueConnectClient,
  '/updateStatementFormatPlan',
  {
    invalidate: (request) => [
      statementFormatPlanQueries('/getStatementFormatPlans', {
        effectiveDate: request.effectiveDate,
      }),
      statementFormatPlanQueries('/getStatementFormatPlansByCode', {
        code: request.code,
      }),
    ],
  },
)

export type StatementFormatPlanMutation =
  | typeof createStatementFormatPlan
  | typeof updateStatementFormatPlan

export const statementFormatPlanMutation = defineMutations(
  [createStatementFormatPlan, updateStatementFormatPlan],
  compose(notifications, withMetrics()),
)
