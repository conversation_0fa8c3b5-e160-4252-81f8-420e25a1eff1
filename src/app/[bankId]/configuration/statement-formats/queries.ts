import { revenueConnectClient } from '@/api/revenueConnectClient'
import { defineQueries, defineQuery } from '@/lib/state/defineQuery'
import { withMetrics } from '@/lib/middleware/withMetrics'

const getStatementFormatPlansByEffectiveDate = defineQuery(
  revenueConnectClient,
  '/getStatementFormatPlans',
  (payload) => [payload.effectiveDate],
)

const getStatementFormatPlansByCode = defineQuery(
  revenueConnectClient,
  '/getStatementFormatPlansByCode',
  (payload) => [payload.code],
)

export const statementFormatPlanQueries = defineQueries(
  [getStatementFormatPlansByEffectiveDate, getStatementFormatPlansByCode],
  withMetrics(),
)
