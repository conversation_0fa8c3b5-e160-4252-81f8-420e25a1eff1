'use client'

import { useFormTextInput } from '@/components/Form/useFormTextInput'
import { useFormMonthPicker } from '@/components/Form/useFormMonthPicker'
import {
  statementFormatInfoSchema,
  UpdateStatementFormatsFormApi,
  UpdateStatementFormatsFormState,
} from './UpdateStatementFormatsFormTypes'

interface StatementFormatInfoProps {
  form: UpdateStatementFormatsFormApi
  isEditMode?: boolean
}
export default function StatementFormatInfo({
  form,
  isEditMode,
}: StatementFormatInfoProps) {
  const FormInput = useFormTextInput<UpdateStatementFormatsFormState>({
    form,
  })
  const FormMonthPicker = useFormMonthPicker<UpdateStatementFormatsFormState>({
    form,
  })
  return (
    <div className='mt-10 flex w-full flex-col rounded-lg border bg-white p-6'>
      <div className='font-semibold'>Statement format information</div>
      <div className='mt-3 flex gap-9'>
        <div className='flex flex-1 flex-col gap-2'>
          <FormMonthPicker
            name='statementFormatInfo.effectiveDate'
            label='Effective date *'
          />
        </div>
        <div className='flex-1'></div>
      </div>
      <div className='mt-3 flex gap-9'>
        <FormInput
          name='statementFormatInfo.description'
          label='Name'
          required
          // TODO can be replaced with validator at useForm-level
          validators={{
            onChange: statementFormatInfoSchema.shape.description,
          }}
        />
        <FormInput
          name='statementFormatInfo.code'
          label='Code'
          required
          // TODO can be replaced with validator at useForm-level
          validators={{
            onChange: statementFormatInfoSchema.shape.code,
          }}
          placeholder='Max 5 digits'
          type='number'
          invalidChars={['e', 'E']}
          readonly={(field) =>
            field.value != null &&
            field.value.length > 0 &&
            field.meta.isPristine &&
            !!isEditMode
          }
        />
      </div>
    </div>
  )
}
