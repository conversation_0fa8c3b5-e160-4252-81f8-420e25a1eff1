'use client'
import { useStore } from '@tanstack/react-form'
import {
  Bars2Icon,
  Bars3Icon,
  InformationCircleIcon,
  ViewColumnsIcon,
} from '@heroicons/react/24/outline'

import { Checkbox } from '@/components/Checkbox'
import { useFormSelect } from '@/components/Form/useFormSelect'
import { useFormTextInput } from '@/components/Form/useFormTextInput'
import { Tooltip } from '@/components/Tooltip'
import { If } from '@/components/If'
import {
  summaryOptionsSchema,
  UpdateStatementFormatsFormApi,
  UpdateStatementFormatsFormState,
  historicalSummaryTypeShape,
  historicalSummaryTypeLabel,
  earningsCreditRateLabel,
  earningsCreditRateShape,
  dailyBalanceSummaryShape,
  dailyBalanceSummaryLabel,
  balanceSummarySizeLabel,
  balanceSummarySizeShape,
} from './UpdateStatementFormatsFormTypes'
import { mockLocations } from '../../mockData'

interface SummaryOptionsProps {
  form: UpdateStatementFormatsFormApi
}
export default function SummaryOptions({ form }: SummaryOptionsProps) {
  const Select = useFormSelect<UpdateStatementFormatsFormState>({ form })
  const FormInput = useFormTextInput<UpdateStatementFormatsFormState>({
    form,
  })

  const [
    relationshipSummary,
    balanceSummary,
    historicalSummaryType,
    dailyBalanceSummary,
  ] = useStore(form.store, (state) => [
    state.values.summaryOptions.relationshipSummary,
    state.values.summaryOptions.balanceSummary,
    state.values.summaryOptions.historicalSummaryType,
    state.values.summaryOptions.dailyBalanceSummary,
  ])

  const balanceAndResultsLayoutOptions = [
    {
      value: 'STACKED',
      icon: <Bars3Icon className='mr-2 inline-block h-5 w-5' />,
      label: 'Stacked',
      description: 'Full page width',
    },
    {
      value: 'SIDE_BY_SIDE',
      icon: <ViewColumnsIcon className='mr-2 inline-block h-5 w-5' />,
      label: 'Side by Side',
      description: 'Half page width',
    },
  ]

  return (
    <div className='flex w-full flex-col rounded-lg border bg-white p-6'>
      <div className='font-semibold'>Summary options</div>
      <div className='mt-3 flex gap-1'>
        <form.Field name='summaryOptions.relationshipSummary'>
          {({ state: { value }, handleChange }) => (
            <Checkbox
              label='Relationship summary'
              onChange={handleChange}
              checked={value}
            />
          )}
        </form.Field>
        <Tooltip className='size-4 self-center' content='Relationship summary'>
          <InformationCircleIcon />
        </Tooltip>
      </div>
      <If true={relationshipSummary === true}>
        <div className='mt-3 flex gap-9'>
          <FormInput
            name='summaryOptions.relationshipSummaryLabel'
            label='Label'
            // TODO can be replaced with validator at useForm-level
            validators={{
              onChange: summaryOptionsSchema.shape.relationshipSummaryLabel,
            }}
            required
          />
          <Select
            name='summaryOptions.relationshipSummaryLocation'
            label='Location'
            // TODO can be replaced with validator at useForm-level
            validators={{
              onChange: summaryOptionsSchema.shape.relationshipSummaryLocation,
            }}
            /**
             * TODO: Update location options once CSF integration with BE is complete
             * Locations are provided by CSF
             */
            options={mockLocations}
            required
          />
        </div>
      </If>
      <div className='mt-2 flex gap-1'>
        <form.Field name='summaryOptions.balanceSummary'>
          {({ state: { value }, handleChange }) => (
            <Checkbox
              label='Balance summary'
              onChange={handleChange}
              checked={value}
            />
          )}
        </form.Field>
      </div>
      <If true={balanceSummary === true}>
        <div className='mt-3 flex gap-9'>
          <Select
            name='summaryOptions.balanceAndResultsLayout'
            label='Size'
            validators={{
              onChange: summaryOptionsSchema.shape.balanceAndResultsLayout,
            }}
            options={balanceAndResultsLayoutOptions.map((opt) => opt.value)}
            renderOption={(value) => {
              const opt = balanceAndResultsLayoutOptions.find(
                (o) => o.value === value,
              )
              return (
                <div className='flex items-center gap-1'>
                  <div className=''>{opt?.icon}</div>
                  <div className='flex flex-col'>
                    <div className=''>{opt?.label}</div>
                    <div className='mt-1 text-xs text-gray-500'>
                      {opt?.description}
                    </div>
                  </div>
                </div>
              )
            }}
            renderSelected={(value) => {
              const opt = balanceAndResultsLayoutOptions.find(
                (o) => o.value === value,
              )
              return (
                <div className='flex items-center gap-2'>
                  {opt?.icon}
                  <span className='font-semibold'>{opt?.label}</span>
                  <span className='ml-2 text-xs text-gray-500'>
                    {opt?.description}
                  </span>
                </div>
              )
            }}
            required
          />
        </div>
        <div className='mt-3 flex gap-9'>
          <FormInput
            name='summaryOptions.balanceSummaryLabel'
            label='Label'
            // TODO can be replaced with validator at useForm-level
            validators={{
              onChange: summaryOptionsSchema.shape.balanceSummaryLabel,
            }}
            required
          />
          <Select
            name='summaryOptions.balanceSummaryLocation'
            label='Location'
            // TODO can be replaced with validator at useForm-level
            validators={{
              onChange: summaryOptionsSchema.shape.balanceSummaryLocation,
            }}
            /**
             * TODO: Update location options once CSF integration with BE is complete
             * Locations are provided by CSF
             */
            options={mockLocations}
            required
          />
        </div>
      </If>
      <div className='mt-2 flex gap-9'>
        <FormInput
          name='summaryOptions.resultsSummaryLabel'
          label='Results summary label'
          // TODO can be replaced with validator at useForm-level
          validators={{
            onChange: summaryOptionsSchema.shape.resultsSummaryLabel,
          }}
          required
        />
      </div>
      <div className='mt-1 flex gap-9'>
        <Select
          name='summaryOptions.resultsSummaryLocation'
          label='Location'
          // TODO can be replaced with validator at useForm-level
          validators={{
            onChange: summaryOptionsSchema.shape.resultsSummaryLocation,
          }}
          /**
           * TODO: Update location options once CSF integration with BE is complete
           * Locations are provided by CSF
           */
          options={mockLocations}
          required
        />
        <div className='flex-1'></div>
      </div>
      <div className='mt-2 flex gap-9'>
        <Select
          tooltip='Earnings credit row in Results Summary will only be shown for account types where an earnings credit plan is assigned.'
          name='summaryOptions.earningsCreditRate'
          label='Earnings credit rate'
          // TODO can be replaced with validator at useForm-level
          validators={{
            onChange: summaryOptionsSchema.shape.earningsCreditRate,
          }}
          options={earningsCreditRateShape.options}
          renderOption={earningsCreditRateLabel}
          renderSelected={earningsCreditRateLabel}
          required
        />
        <div className='flex-1'></div>
      </div>

      <div className='mt-1 flex gap-9'>
        <Select
          name='summaryOptions.historicalSummaryType'
          label='Historical analysis summary'
          // TODO can be replaced with validator at useForm-level
          validators={{
            onChange: summaryOptionsSchema.shape.historicalSummaryType,
          }}
          options={historicalSummaryTypeShape.options}
          renderSelected={historicalSummaryTypeLabel}
          renderOption={historicalSummaryTypeLabel}
          required
        />
        <div className='flex-1'></div>
      </div>
      <If true={!!historicalSummaryType && historicalSummaryType !== 'NO'}>
        <div className='mt-1 flex gap-9'>
          <FormInput
            name='summaryOptions.historicalSummaryLabel'
            label='Label'
            // TODO can be replaced with validator at useForm-level
            validators={{
              onChange: summaryOptionsSchema.shape.historicalSummaryLabel,
            }}
            required
          />
          <Select
            name='summaryOptions.historicalSummaryLocation'
            label='Location'
            // TODO can be replaced with validator at useForm-level
            validators={{
              onChange: summaryOptionsSchema.shape.historicalSummaryLocation,
            }}
            options={mockLocations}
            required
          />
        </div>
      </If>

      <div className='mt-1 flex gap-9'>
        <Select
          name='summaryOptions.dailyBalanceSummary'
          label='Daily balance summary'
          // TODO can be replaced with validator at useForm-level
          validators={{
            onChange: summaryOptionsSchema.shape.dailyBalanceSummary,
          }}
          options={dailyBalanceSummaryShape.options}
          renderSelected={dailyBalanceSummaryLabel}
          renderOption={dailyBalanceSummaryLabel}
          required
        />
        <div className='flex-1'></div>
      </div>
      <If true={!!dailyBalanceSummary && dailyBalanceSummary !== 'NO'}>
        <div className='mt-1 flex gap-9'>
          <FormInput
            name='summaryOptions.dailyBalanceSummaryLabel'
            label='Label'
            // TODO can be replaced with validator at useForm-level
            validators={{
              onChange: summaryOptionsSchema.shape.dailyBalanceSummaryLabel,
            }}
            required
          />
          <Select
            name='summaryOptions.dailyBalanceSummaryLocation'
            label='Location'
            // TODO can be replaced with validator at useForm-level
            validators={{
              onChange: summaryOptionsSchema.shape.dailyBalanceSummaryLocation,
            }}
            /**
             * TODO: Update location options once CSF integration with BE is complete
             * Locations are provided by CSF
             */
            options={mockLocations}
            required
          />
        </div>
      </If>
    </div>
  )
}
