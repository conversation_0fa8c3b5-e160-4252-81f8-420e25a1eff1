import { z } from 'zod'
import { Form<PERSON>pi, ReactFormApi } from '@tanstack/react-form'

import { name, configCode } from '@/api/zodSchemas'
import { nullableRequired } from '@/lib/validation/nullableRequiredNativeEnum'
import { apiToFormSchemas } from '@/api/apiToFormSchema'
import { match } from '@/lib/unions/match'
import { variant } from '@/lib/unions/Union'

const { statementFormatPlan } = apiToFormSchemas

/**
 * ZodEnums for Required Dropdowns from apiToFormSchemas.ts
 */
export const dailyBalanceSummaryShape =
  statementFormatPlan.shape.dailyBalanceSummary
export const earningsCreditRateShape =
  statementFormatPlan.shape.earningsCreditRate
export const historicalSummaryTypeShape =
  statementFormatPlan.shape.historicalSummaryType
export const sortServicesTypeShape = statementFormatPlan.shape.sortServicesType

/**
 * ZodEnums for NON-Required Dropdowns manually extracted from from apiToFormSchemas.ts
 */
export const balanceSummarySizeShape = z.enum(['FULL', 'HALF'])
export const serviceCategoryLevelShape = z.enum(['ONE', 'TWO', 'THREE'])
export const serviceCategorySortShape = z.enum([
  'USER_DEFINED',
  'ALPHABETICAL_BY_CATEGORY_NAME',
  'BY_LOWEST_TO_HIGHEST_SERVICE_CODE',
])

export const statementFormatInfoSchema = z.object({
  description: name,
  effectiveDate: z.string(),
  code: configCode,
})

type StatementFormatInfoState = z.infer<typeof statementFormatInfoSchema>

export const headerAndVisualFormattingSchema = z.object({
  headerLogoFileName: z.string().min(1, 'Required'),
  sectionBorderColor: z.string().min(1, 'Required'),
  sectionBackgroundColor: z.string().min(1, 'Required'),
  sectionTextColor: z.string().min(1, 'Required'),
  includeOfficerName: z.boolean(),
  includeOfficerPhone: z.boolean(),
  returnAddress: z.string().nullable(),
  returnAddressType: z
    .enum(['NO', 'DYNAMIC_BRANCH_ADDRESS', 'ADDRESS_OF_BRANCH_ASSIGNED'])
    .nullable(),
  enableBorderColor: z.boolean().nullable(),
  footerImageDisplay: z.string().nullable(),
})

type HeaderAndVisualFormattingState = z.infer<
  typeof headerAndVisualFormattingSchema
>

export const statementMessageAndImageSchema = z.object({
  statementMessage: z.boolean(),
  statementMessageLocation: nullableRequired(z.string()),
  /**
   * can add up-to 4 statement-message-images and corresponding statement-image-locations
   * the index of the arrays for both statementMessageImages and statementImageLocations
   * correspond to the mapping of an image and its respective location
   */
  statementMessageImages: z.array(z.string()),
  statementImageLocations: z.array(z.string()),
})

type StatementMessageAndImageState = z.infer<
  typeof statementMessageAndImageSchema
>

export const summaryOptionsSchema = z.object({
  relationshipSummary: z.boolean(),
  relationshipSummaryLocation: nullableRequired(z.string()),
  relationshipSummaryLabel: nullableRequired(z.string()),
  balanceSummary: z.boolean(),
  balanceSummaryLabel: nullableRequired(z.string()),
  balanceSummaryLocation: nullableRequired(z.string()),
  resultsSummaryLabel: z.string().min(1, 'Required'),
  resultsSummaryLocation: z.string().min(1, 'Required'),
  balanceAndResultsLayout: z.string().min(1, 'Required').nullable(),
  earningsCreditRate: nullableRequired(earningsCreditRateShape),
  historicalSummaryType: nullableRequired(historicalSummaryTypeShape),
  historicalSummaryLabel: nullableRequired(z.string()),
  historicalSummaryLocation: nullableRequired(z.string()),
  dailyBalanceSummary: nullableRequired(dailyBalanceSummaryShape),
  dailyBalanceSummaryLabel: nullableRequired(z.string()),
  dailyBalanceSummaryLocation: nullableRequired(z.string()),
})

type SummaryOptionsState = z.infer<typeof summaryOptionsSchema>

export const serviceInformationSchema = z.object({
  serviceDetailLabel: z.string().min(1, 'Required'),
  serviceDetailLocation: z.string().min(1, 'Required'),
  afpServiceCode: z.boolean(),
  serviceCode: z.boolean(),
  sortServicesType: nullableRequired(sortServicesTypeShape),
  requiredBalance: z.boolean(),
  requiredBalanceMultiplier: z.boolean(),
  enableServiceCategory: z.boolean(),
  serviceCategorySort: serviceCategorySortShape.nullable(),
  serviceCategoryBackgroundColor: nullableRequired(z.string()),
  serviceCategoryLevel: serviceCategoryLevelShape.nullable(),
  boldServiceCategoryLabel: z.boolean(),
  serviceCategorySubtotal: z.boolean(),
  boldServiceCategorySubtotalLabel: z.boolean(),
  serviceCategorySubtotalBackgroundColor: nullableRequired(z.string()),
  serviceChargesDueBarChart: z.boolean(),
  serviceChargesDueBarChartLabel: nullableRequired(z.string()),
  serviceChargesDueBarChartLocation: nullableRequired(z.string()),
  serviceCategoryPieChart: z.boolean(),
  serviceCategoryPieChartLabel: nullableRequired(z.string()),
  serviceCategoryPieChartLocation: nullableRequired(z.string()),
})

type ServiceInformationState = z.infer<typeof serviceInformationSchema>

// TODO create composite zod schema instead (e.g., updateStatementFormatsSchema) and infer this type from that schema.
// This new composite schema could also be used as onChange validator in useForm in UpdateScheduleDefinitionForm to reduce
// need to define validators.onChange on every form field.
export type UpdateStatementFormatsFormState = {
  statementFormatInfo: StatementFormatInfoState
  headerAndVisualFormatting: HeaderAndVisualFormattingState
  statementMessageAndImage: StatementMessageAndImageState
  summaryOptions: SummaryOptionsState
  serviceInformation: ServiceInformationState
}

export type UpdateStatementFormatsFormApi =
  ReactFormApi<UpdateStatementFormatsFormState> &
    FormApi<UpdateStatementFormatsFormState>

export const dailyBalanceSummaryLabel = (
  value: z.infer<typeof dailyBalanceSummaryShape>,
) =>
  match(variant(value), {
    NO: () => 'No',
    YES_ALL_ACCOUNTS: () => 'Yes - All Accounts',
    YES_COMPOSITE_ONLY: () => 'Yes - Composite Accounts Only',
  })

export const earningsCreditRateLabel = (
  value: z.infer<typeof earningsCreditRateShape>,
) =>
  match(variant(value), {
    EARNINGS_CREDIT_DEFINITION: () => 'Earnings credit definition',
    CALCULATED_RATE: () => 'Calculated rate',
  })

export const historicalSummaryTypeLabel = (
  value: z.infer<typeof historicalSummaryTypeShape>,
) =>
  match(variant(value), {
    NO: () => 'No',
    ROLLING_12_MONTHS: () => 'Rolling 12 months',
    YTD: () => 'Year to date',
  })

export const sortServicesTypeLabel = (
  value: z.infer<typeof sortServicesTypeShape>,
) =>
  match(variant(value), {
    USER_DEFINED: () => 'In user-defined order',
    ALPHABETICAL_BY_CATEGORY_NAME: () => 'Alphabetical based on Service Name',
    BY_LOWEST_TO_HIGHEST_SERVICE_CODE: () =>
      'By lowest to highest service code',
  })

/**
 * Below maps enums found in openapi schema to user-friendly labels
 * These are for optional/non-required <Select> fields
 */

export const balanceSummarySizeLabel = (
  value: z.infer<typeof balanceSummarySizeShape>,
) => {
  return match(variant(value), {
    FULL: () => 'Full page width',
    HALF: () => 'Half page width',
  })
}
export const serviceCategoryLevelLabel = (
  value: z.infer<typeof serviceCategoryLevelShape>,
) =>
  match(variant(value), {
    ONE: () => '1',
    TWO: () => '2',
    THREE: () => '3',
  })

export const serviceCategorySortLabel = (
  value: z.infer<typeof serviceCategorySortShape>,
) =>
  match(variant(value), {
    USER_DEFINED: () => 'In user-defined order',
    ALPHABETICAL_BY_CATEGORY_NAME: () => 'Alphabetical based on Service Name',
    BY_LOWEST_TO_HIGHEST_SERVICE_CODE: () =>
      'By lowest to highest service code',
  })

export const statementFormatPlanLabels: Record<string, string> = {
  ONE: '1',
  TWO: '2',
  THREE: '3',
  NO: 'No',
  FULL: 'Full page width',
  HALF: 'Half page width',
  YES_ALL_ACCOUNTS: 'Yes - All Accounts',
  YES_COMPOSITE_ONLY: 'Yes - Composite Accounts Only',
  EARNINGS_CREDIT_DEFINITION: 'Earnings credit definition',
  CALCULATED_RATE: 'Calculated rate',
  USER_DEFINED: 'In user-defined order',
  ALPHABETICAL_BY_CATEGORY_NAME: 'Alphabetical based on Service Name',
  BY_LOWEST_TO_HIGHEST_SERVICE_CODE: 'By lowest to highest service code',
  ROLLING_12_MONTHS: 'Rolling 12 months',
  YTD: 'Year to date',
}
