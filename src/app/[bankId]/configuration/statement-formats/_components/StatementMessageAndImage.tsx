'use client'
import { z } from 'zod'
import { useStore } from '@tanstack/react-form'
import { Checkbox } from '@/components/Checkbox'
import { PlusIcon, TrashIcon } from '@heroicons/react/24/outline'
import { Button } from '@headlessui/react'

import { useFormSelect } from '@/components/Form/useFormSelect'
import { If } from '@/components/If'
import {
  statementMessageAndImageSchema,
  UpdateStatementFormatsFormApi,
  UpdateStatementFormatsFormState,
} from './UpdateStatementFormatsFormTypes'
import { mockStatementImages, mockLocations } from '../../mockData'
interface StatementMessageAndImageProps {
  form: UpdateStatementFormatsFormApi
}
export default function StatementMessageAndImage({
  form,
}: StatementMessageAndImageProps) {
  const Select = useFormSelect<UpdateStatementFormatsFormState>({ form })

  const [statementMessage, statementMessageImages, statementImageLocations] =
    useStore(form.store, (state) => [
      state.values.statementMessageAndImage.statementMessage,
      state.values.statementMessageAndImage.statementMessageImages,
      state.values.statementMessageAndImage.statementImageLocations,
    ])

  /**
   * statementImageLocationOptions is used to prevent using the same location more than once
   * TODO: Update Statement image location options once CSF integration with BE is complete
   * Statement image locations are provided by CSF
   */
  const statementImageLocationOptions = mockLocations.filter(
    (option) => !statementImageLocations.includes(option),
  )

  const onAddImageClick = () => {
    form.setFieldValue('statementMessageAndImage.statementMessageImages', [
      ...statementMessageImages,
      '',
    ])
    form.setFieldValue('statementMessageAndImage.statementImageLocations', [
      ...statementImageLocations,
      '',
    ])
  }

  const onRemoveImageClick = (idx: number) => {
    // remove the index that was clicked from statement message Images and Locations
    const newImages = [...statementMessageImages]
    newImages.splice(idx, 1)
    const newLocations = [...statementImageLocations]
    newLocations.splice(idx, 1)
    form.setFieldValue(
      'statementMessageAndImage.statementMessageImages',
      newImages,
    )
    form.setFieldValue(
      'statementMessageAndImage.statementImageLocations',
      newLocations,
    )
  }

  return (
    <div className='flex w-full flex-col rounded-lg border bg-white p-6'>
      <div className='font-semibold'>Statement message and image</div>
      <div className='mb-3 mt-3 flex gap-9'>
        <form.Field name='statementMessageAndImage.statementMessage'>
          {({ state: { value }, handleChange }) => (
            <Checkbox
              label='Statement message'
              onChange={handleChange}
              checked={value}
            />
          )}
        </form.Field>
      </div>
      <If true={statementMessage === true}>
        <div className='flex gap-9'>
          <Select
            name='statementMessageAndImage.statementMessageLocation'
            label='Location'
            // TODO can be replaced with validator at useForm-level
            validators={{
              onChange:
                statementMessageAndImageSchema.shape.statementMessageLocation,
            }}
            /**
             * TODO: Update Statement message location options once CSF integration with BE is complete
             * Statement message locations are provided by CSF
             */
            options={statementImageLocationOptions}
            required
          />
          <div className='flex-1'></div>
        </div>
      </If>
      {statementMessageImages.map((_, i) => {
        return (
          <div className='flex gap-9' key={`statement-message-image-row-${i}`}>
            <Select
              key={`statement-image-${i}`}
              name={`statementMessageAndImage.statementMessageImages[${i}]`}
              label='Image'
              /**
               * TODO: Update Statement message image options once CSF integration with BE is complete
               * Statement message images are provided by CSF
               */
              options={mockStatementImages}
              validators={{ onChange: z.string().min(1, 'Required') }}
              required
            />
            <Select
              key={`statement-image-location-${i}`}
              name={`statementMessageAndImage.statementImageLocations[${i}]`}
              label='Location'
              validators={{ onChange: z.string().min(1, 'Required') }}
              /**
               * TODO: Update Statement image location options once CSF integration with BE is complete
               * Statement image locations are provided by CSF
               */
              options={statementImageLocationOptions}
              required
            />
            <Button onClick={() => onRemoveImageClick(i)}>
              <TrashIcon className='h-5 w-5' />
            </Button>
          </div>
        )
      })}
      {statementMessageImages.length < 4 && (
        <div>
          <Button
            className='flex px-6 align-middle text-sm font-semibold text-indigo-600 hover:cursor-pointer'
            onClick={onAddImageClick}
          >
            <PlusIcon className='size-5' />
            Add image
          </Button>
        </div>
      )}
    </div>
  )
}
