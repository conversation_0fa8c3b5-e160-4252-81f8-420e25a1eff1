'use client'

import { useStore } from '@tanstack/react-form'
import { InformationCircleIcon } from '@heroicons/react/24/outline'

import { Checkbox } from '@/components/Checkbox'
import { useFormSelect } from '@/components/Form/useFormSelect'
import { useFormTextInput } from '@/components/Form/useFormTextInput'
import { useFormColorPicker } from '@/components/Form/useFormColorPicker'
import { Tooltip } from '@/components/Tooltip'
import { If } from '@/components/If'
import {
  serviceInformationSchema,
  UpdateStatementFormatsFormApi,
  UpdateStatementFormatsFormState,
  serviceCategorySortLabel,
  serviceCategorySortShape,
  sortServicesTypeShape,
  sortServicesTypeLabel,
  serviceCategoryLevelLabel,
  serviceCategoryLevelShape,
} from './UpdateStatementFormatsFormTypes'
import { ServiceCategoryLevelOptions } from '../../types'
import { mockLocations } from '../../mockData'

interface ServiceInformationProps {
  form: UpdateStatementFormatsFormApi
}
export default function ServiceInformation({ form }: ServiceInformationProps) {
  const Select = useFormSelect<UpdateStatementFormatsFormState>({ form })
  const FormInput = useFormTextInput<UpdateStatementFormatsFormState>({
    form,
  })
  const ColorPicker = useFormColorPicker<UpdateStatementFormatsFormState>({
    form,
  })

  const [
    servicesCategory,
    serviceCategorySubtotal,
    serviceChargesDueBarChart,
    serviceCategoryPieChart,
  ] = useStore(form.store, (state) => [
    state.values.serviceInformation.enableServiceCategory,
    state.values.serviceInformation.serviceCategorySubtotal,
    state.values.serviceInformation.serviceChargesDueBarChart,
    state.values.serviceInformation.serviceCategoryPieChart,
  ])
  return (
    <div className='flex w-full flex-col rounded-lg border bg-white p-6'>
      <div className='font-semibold'>Service information</div>
      <div className='mt-3 flex gap-9'>
        <FormInput
          name='serviceInformation.serviceDetailLabel'
          label='Service detail label'
          // TODO can be replaced with validator at useForm-level
          validators={{
            onChange: serviceInformationSchema.shape.serviceDetailLabel,
          }}
          required
        />
        <Select
          name='serviceInformation.serviceDetailLocation'
          label='Location'
          // TODO can be replaced with validator at useForm-level
          validators={{
            onChange: serviceInformationSchema.shape.serviceDetailLocation,
          }}
          /**
           * TODO: Update location options once CSF integration with BE is complete
           * Locations are provided by CSF
           */
          options={mockLocations}
          required
        />
      </div>
      <div className='flex gap-1'>
        <form.Field name='serviceInformation.enableServiceCategory'>
          {({ state: { value }, handleChange }) => (
            <Checkbox
              label='Service category'
              onChange={handleChange}
              checked={value}
            />
          )}
        </form.Field>
      </div>
      <If true={servicesCategory === true}>
        <div className='mt-3 flex gap-9'>
          <Select
            name='serviceInformation.serviceCategorySort'
            label='Service category sort'
            // TODO can be replaced with validator at useForm-level
            validators={{
              onChange: serviceInformationSchema.shape.serviceCategorySort,
            }}
            options={serviceCategorySortShape.options}
            renderOption={serviceCategorySortLabel}
            renderSelected={serviceCategorySortLabel}
          />
          <ColorPicker
            name='serviceInformation.serviceCategoryBackgroundColor'
            label='Service category background color'
            // TODO can be replaced with validator at useForm-level
            validators={{
              onChange:
                serviceInformationSchema.shape.serviceCategoryBackgroundColor,
            }}
            required
          />
        </div>
        <div className='flex gap-9'>
          <Select
            name='serviceInformation.serviceCategoryLevel'
            label='Service categories levels shown'
            // TODO can be replaced with validator at useForm-level
            validators={{
              onChange: serviceInformationSchema.shape.serviceCategoryLevel,
            }}
            options={serviceCategoryLevelShape.options}
            renderOption={serviceCategoryLevelLabel}
            renderSelected={serviceCategoryLevelLabel}
          />
          <div className='flex-1'></div>
        </div>
        <div className='flex gap-1'>
          <form.Field name='serviceInformation.boldServiceCategoryLabel'>
            {({ state: { value }, handleChange }) => (
              <Checkbox
                label='Bold service category label'
                onChange={handleChange}
                checked={value}
              />
            )}
          </form.Field>
        </div>
        <div className='mt-6 flex gap-1'>
          <form.Field name='serviceInformation.serviceCategorySubtotal'>
            {({ state: { value }, handleChange }) => (
              <Checkbox
                label='Service category subtotal'
                onChange={handleChange}
                checked={value}
              />
            )}
          </form.Field>
        </div>
      </If>

      <If true={serviceCategorySubtotal && servicesCategory}>
        <div className='mt-3 flex gap-9'>
          <ColorPicker
            name='serviceInformation.serviceCategorySubtotalBackgroundColor'
            label='Service category subtotal background color'
            // TODO can be replaced with validator at useForm-level
            validators={{
              onChange:
                serviceInformationSchema.shape
                  .serviceCategorySubtotalBackgroundColor,
            }}
            required
          />
          <div className='flex-1'></div>
        </div>
        <div className='flex gap-1'>
          <form.Field name='serviceInformation.boldServiceCategorySubtotalLabel'>
            {({ state: { value }, handleChange }) => (
              <Checkbox
                label='Bold service category subtotal label'
                onChange={handleChange}
                checked={value}
              />
            )}
          </form.Field>
        </div>
      </If>
      <div className='mt-6 flex gap-1'>
        <form.Field name='serviceInformation.afpServiceCode'>
          {({ state: { value }, handleChange }) => (
            <Checkbox
              label='AFP Service Code'
              onChange={handleChange}
              checked={value}
            />
          )}
        </form.Field>
        <Tooltip className='size-4 self-center' content='AFP Service Code'>
          <InformationCircleIcon />
        </Tooltip>
      </div>
      <div className='mt-6 flex gap-1'>
        <form.Field name='serviceInformation.serviceCode'>
          {({ state: { value }, handleChange }) => (
            <Checkbox
              label='Service Code'
              onChange={handleChange}
              checked={value}
            />
          )}
        </form.Field>
        <Tooltip className='size-4 self-center' content='Service Code'>
          <InformationCircleIcon />
        </Tooltip>
      </div>
      <div className='mt-6 flex gap-9'>
        <Select
          name='serviceInformation.sortServicesType'
          label='Sort services'
          // TODO can be replaced with validator at useForm-level
          validators={{
            onChange: serviceInformationSchema.shape.sortServicesType,
          }}
          options={sortServicesTypeShape.options}
          renderOption={sortServicesTypeLabel}
          renderSelected={sortServicesTypeLabel}
          required
        />
        <div className='flex-1'></div>
      </div>
      <div className='mt-3 flex gap-1'>
        <form.Field name='serviceInformation.requiredBalance'>
          {({ state: { value }, handleChange }) => (
            <Checkbox
              label='Required balance'
              onChange={handleChange}
              checked={value}
            />
          )}
        </form.Field>
      </div>
      <div className='mt-6 flex gap-1'>
        <form.Field name='serviceInformation.requiredBalanceMultiplier'>
          {({ state: { value }, handleChange }) => (
            <Checkbox
              label='Required balance multiplier'
              onChange={handleChange}
              checked={value}
            />
          )}
        </form.Field>
      </div>

      <div className='mt-6 flex gap-1'>
        <form.Field name='serviceInformation.serviceChargesDueBarChart'>
          {({ state: { value }, handleChange }) => (
            <Checkbox
              label='Service charges due bar chart'
              onChange={handleChange}
              checked={value}
            />
          )}
        </form.Field>
      </div>
      <If true={serviceChargesDueBarChart === true}>
        <div className='mt-3 flex gap-9'>
          <FormInput
            name='serviceInformation.serviceChargesDueBarChartLabel'
            label='Label'
            // TODO can be replaced with validator at useForm-level
            validators={{
              onChange:
                serviceInformationSchema.shape.serviceChargesDueBarChartLabel,
            }}
            required
          />
          <Select
            name='serviceInformation.serviceChargesDueBarChartLocation'
            label='Location'
            // TODO can be replaced with validator at useForm-level
            validators={{
              onChange:
                serviceInformationSchema.shape
                  .serviceChargesDueBarChartLocation,
            }}
            /**
             * TODO: Update location options once CSF integration with BE is complete
             * Locations are provided by CSF
             */
            options={mockLocations}
            required
          />
        </div>
      </If>

      <div className='mt-6 flex gap-1'>
        <form.Field name='serviceInformation.serviceCategoryPieChart'>
          {({ state: { value }, handleChange }) => (
            <Checkbox
              label='Service category pie chart'
              onChange={handleChange}
              checked={value}
            />
          )}
        </form.Field>
      </div>
      <If true={serviceCategoryPieChart === true}>
        <div className='mt-3 flex gap-9'>
          <FormInput
            name='serviceInformation.serviceCategoryPieChartLabel'
            label='Label'
            // TODO can be replaced with validator at useForm-level
            validators={{
              onChange:
                serviceInformationSchema.shape.serviceCategoryPieChartLabel,
            }}
            required
          />
          <Select
            name='serviceInformation.serviceCategoryPieChartLocation'
            label='Location'
            // TODO can be replaced with validator at useForm-level
            validators={{
              onChange:
                serviceInformationSchema.shape.serviceCategoryPieChartLocation,
            }}
            /**
             * TODO: Update location options once CSF integration with BE is complete
             * Locations are provided by CSF
             */
            options={mockLocations}
            required
          />
        </div>
      </If>
    </div>
  )
}
