'use client'
import { useForm } from '@tanstack/react-form'

import { UpdateStatementFormatsFormState } from './UpdateStatementFormatsFormTypes'
import StatementFormatInfo from './StatementFormatInfo'
import HeaderAndVisualFormatting from './HeaderAndVisualFormatting'
import StatementMessageAndImage from './StatementMessageAndImage'
import SummaryOptions from './SummaryOptions'
import ServiceInformation from './ServiceInformation'
interface UpdateStatementFormatsFormProps {
  defaultValues: UpdateStatementFormatsFormState
  onSubmit: (state: UpdateStatementFormatsFormState) => void
  isEditMode?: boolean
}

export default function UpdateStatementFormatsForm({
  defaultValues,
  onSubmit,
  children,
  isEditMode,
}: React.PropsWithChildren<UpdateStatementFormatsFormProps>) {
  const form = useForm({
    defaultValues,
    onSubmit: ({ value }) => {
      onSubmit(value)
    },
  })

  // TODO can add `validators` prop with `onChange` pointing to composite zod schema
  // so we don't need to define `validators.onChange` on every form field.
  return (
    <form
      className='flex min-h-screen flex-col'
      onSubmit={async (event) => {
        form.reset(form.state.values)
        event.preventDefault()
        event.stopPropagation()
        await form.handleSubmit()
      }}
    >
      <div className='flex flex-grow flex-col gap-2 overflow-y-auto px-8'>
        <StatementFormatInfo form={form} isEditMode={isEditMode} />
        <HeaderAndVisualFormatting form={form} />
        <StatementMessageAndImage form={form} />
        <SummaryOptions form={form} />
        <ServiceInformation form={form} />
      </div>
      <div className='sticky bottom-0 mt-auto flex justify-end gap-4 border-t bg-white px-12 py-10'>
        {children}
      </div>
    </form>
  )
}
