'use client'

import { useRouter } from 'next/navigation'
import { useQuery, useMutation } from '@tanstack/react-query'

import { Button } from '@/components/Button'
import UpdateStatementFormatsForm from '../../../_components/UpdateStatementFormatsForm'
import { UpdateStatementFormatsFormState } from '../../../_components/UpdateStatementFormatsFormTypes'

import { routeTo, useRoute } from '../../../../routing'
import { unwrap } from '@/lib/unions/unwrap'
import { statementFormatPlanQueries } from '../../../queries'
import { statementFormatPlanMutation } from '../../../mutations'
import { formToApiSchemas } from '@/api/formToApiSchema'
import { formatToServerString, formatToMonthYearFromDate } from '@/lib/date'
import { apiToFormSchemas } from '@/api/apiToFormSchema'

export default function EditStatementFormat() {
  const router = useRouter()
  const route = unwrap(
    useRoute(),
    '/configuration/statement-formats/[effectiveDate]/edit/[statementFormatCode]',
  )!

  const effectiveDate = route.params.effectiveDate

  const payload = {
    code: route.params.statementFormatCode,
  }
  const { data, status, error } = useQuery(
    statementFormatPlanQueries('/getStatementFormatPlansByCode', payload),
  )
  const updateStatementFormatPlan = useMutation(
    statementFormatPlanMutation('/updateStatementFormatPlan', {
      onSuccess: (_, request) => {
        router.push(
          routeTo(
            '/configuration/statement-formats/[effectiveDate]/view/[statementFormatCode]',
            {
              ...route.params,
              effectiveDate: formatToMonthYearFromDate(request.effectiveDate!),
              statementFormatCode: request.code!,
            },
          ),
        )
      },
    }),
  )
  if (status === 'pending') return 'Loading...'
  if (!data || error) {
    return <div>404 Not Found</div>
  }
  const { statementFormatPlan } = apiToFormSchemas
  const statementFormatPlans = data
    .map((item) => {
      const parsed = statementFormatPlan.parse(item)
      return {
        ...parsed,
        effectiveDate: formatToMonthYearFromDate(parsed.effectiveDate),
      }
    })
    .sort(
      (a, b) =>
        new Date(b.effectiveDate).getDate() -
        new Date(a.effectiveDate).getDate(),
    )

  const statementFormat =
    statementFormatPlans.find((item) => {
      return item.effectiveDate === effectiveDate
    }) || statementFormatPlans[0]

  const defaultValues: UpdateStatementFormatsFormState = {
    statementFormatInfo: {
      description: statementFormat.description,
      effectiveDate: statementFormat.effectiveDate,
      code: statementFormat.code,
    },
    headerAndVisualFormatting: {
      enableBorderColor: statementFormat.enableBorderColor,
      headerLogoFileName: statementFormat.headerLogoFileName,
      sectionBorderColor: statementFormat.sectionBorderColor,
      sectionBackgroundColor: statementFormat.sectionBackgroundColor,
      sectionTextColor: statementFormat.sectionTextColor,
      includeOfficerName: !!statementFormat.includeOfficerName,
      includeOfficerPhone: !!statementFormat.includeOfficerPhone,
      returnAddress: statementFormat.returnAddress,
      returnAddressType: statementFormat.returnAddressType,
      footerImageDisplay: statementFormat.footerImageDisplay,
    },
    statementMessageAndImage: {
      statementMessage: !!statementFormat.statementMessage,
      statementMessageLocation: statementFormat.statementMessageLocation,
      statementMessageImages: statementFormat.statementMessageImages,
      statementImageLocations: statementFormat.statementImageLocations,
    },
    summaryOptions: {
      relationshipSummary: !!statementFormat.relationshipSummary,
      relationshipSummaryLabel: statementFormat.relationshipSummaryLabel,
      relationshipSummaryLocation: statementFormat.relationshipSummaryLocation,
      balanceSummary: !!statementFormat.balanceSummary,
      balanceSummaryLabel: statementFormat.balanceSummaryLabel,
      balanceSummaryLocation: statementFormat.balanceSummaryLocation,
      resultsSummaryLabel: statementFormat.resultsSummaryLabel,
      resultsSummaryLocation: statementFormat.resultsSummaryLocation,
      balanceAndResultsLayout: statementFormat.balanceAndResultsLayout,
      earningsCreditRate: statementFormat.earningsCreditRate,
      historicalSummaryType: statementFormat.historicalSummaryType,
      historicalSummaryLabel: statementFormat.historicalSummaryLabel,
      historicalSummaryLocation: statementFormat.historicalSummaryLocation,
      dailyBalanceSummary: statementFormat.dailyBalanceSummary,
      dailyBalanceSummaryLabel: statementFormat.dailyBalanceSummaryLabel,
      dailyBalanceSummaryLocation: statementFormat.dailyBalanceSummaryLocation,
    },
    serviceInformation: {
      serviceDetailLabel: statementFormat.serviceDetailLabel,
      serviceDetailLocation: statementFormat.serviceDetailLocation ?? '',
      afpServiceCode: !!statementFormat.afpServiceCode,
      serviceCode: !!statementFormat.serviceCode,
      sortServicesType: statementFormat.sortServicesType,
      requiredBalance: !!statementFormat.requiredBalance,
      requiredBalanceMultiplier: !!statementFormat.requiredBalanceMultiplier,
      enableServiceCategory: !!statementFormat.enableServiceCategory,
      serviceCategorySort: statementFormat.serviceCategorySort,
      serviceCategoryBackgroundColor:
        statementFormat.serviceCategoryBackgroundColor,
      serviceCategoryLevel: statementFormat.serviceCategoryLevel,
      boldServiceCategoryLabel: !!statementFormat.boldServiceCategoryLabel,
      serviceCategorySubtotal: !!statementFormat.serviceCategorySubtotal,
      serviceCategorySubtotalBackgroundColor:
        statementFormat.serviceCategorySubtotalBackgroundColor,
      boldServiceCategorySubtotalLabel:
        !!statementFormat.boldServiceCategorySubtotalLabel,
      serviceChargesDueBarChart: !!statementFormat.serviceChargesDueBarChart,
      serviceChargesDueBarChartLabel:
        statementFormat.serviceChargesDueBarChartLabel,
      serviceChargesDueBarChartLocation:
        statementFormat.serviceChargesDueBarChartLocation,
      serviceCategoryPieChart: !!statementFormat.serviceCategoryPieChart,
      serviceCategoryPieChartLabel:
        statementFormat.serviceCategoryPieChartLabel,
      serviceCategoryPieChartLocation:
        statementFormat.serviceCategoryPieChartLocation,
    },
  }
  const serializeForm = (formState: UpdateStatementFormatsFormState) => {
    const { statementFormatPlan } = formToApiSchemas
    const {
      statementFormatInfo,
      headerAndVisualFormatting,
      statementMessageAndImage,
      summaryOptions,
      serviceInformation,
    } = formState
    return statementFormatPlan.parse({
      ...statementFormatInfo,
      effectiveDate: formatToServerString(statementFormatInfo.effectiveDate),
      ...headerAndVisualFormatting,
      ...statementMessageAndImage,
      ...summaryOptions,
      ...serviceInformation,
      /**
       * TODO: printOfficer is not a field in the UI,
       * however, it is currently required in the API call
       */
      printOfficer: 'PRIMARY_OFFICER',
    })
  }

  return (
    <div className='w-full overflow-hidden overflow-y-scroll pt-12'>
      <div className='text-md ml-8 font-medium'>Edit statement format</div>
      <UpdateStatementFormatsForm
        defaultValues={defaultValues}
        onSubmit={(formState) => {
          updateStatementFormatPlan.mutate(serializeForm(formState))
        }}
        isEditMode
      >
        <Button className='btn w-60' onClick={() => router.back()}>
          Cancel
        </Button>

        <Button type='submit' className='btn-primary w-60'>
          Save
        </Button>
      </UpdateStatementFormatsForm>
    </div>
  )
}
