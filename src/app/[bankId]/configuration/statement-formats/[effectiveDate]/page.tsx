'use client'

import { useState } from 'react'
import { z } from 'zod'
import { useQuery } from '@tanstack/react-query'
import { ColumnDef, ColumnFiltersState } from '@tanstack/react-table'
import { ChevronRightIcon } from '@heroicons/react/24/outline'
import { useRouter } from 'next/navigation'

import { Button } from '@/components/Button'
import { MonthPicker } from '@/components/Filter/MonthPicker'
import { SearchBar } from '@/components/SearchBar'
import { SortedTable } from '@/components/Table/SortedTable'

import { RowPopover } from '../../_components/RowPopover'

import { unwrap } from '@/lib/unions/unwrap'
import { routeTo, useRoute } from '../../routing'
import { apiToFormSchemas } from '@/api/apiToFormSchema'
import { statementFormatPlanQueries } from '../queries'
import { formatToMonthYearFromDate, formatToServerString } from '@/lib/date'

export default function StatementFormats() {
  const [searchText, setSearchText] = useState('')
  const columnFilters: ColumnFiltersState = [
    { id: 'description', value: searchText },
  ]
  const [numStatementFormats, setNumStatementFormats] = useState(0)

  const router = useRouter()
  const route = unwrap(
    useRoute(),
    '/configuration/statement-formats/[effectiveDate]',
  )!

  const payload = {
    effectiveDate: formatToServerString(route.params.effectiveDate),
  }
  const { data, status, error } = useQuery(
    statementFormatPlanQueries('/getStatementFormatPlans', payload),
  )
  if (status === 'pending') return 'Loading...'
  if (!data || error) {
    return <div>404 Not Found</div>
  }
  const { statementFormatPlan: statementFormatPlanType } = apiToFormSchemas
  const statementFormatPlans = data?.map((item) => {
    return statementFormatPlanType.parse(item)
  })

  const handleAddStatementFormatClick = () => {
    router.push(
      routeTo(
        '/configuration/statement-formats/[effectiveDate]/add',
        route.params,
      ),
    )
  }

  const columns: ColumnDef<z.infer<typeof statementFormatPlanType>>[] = [
    {
      header: 'Name',
      accessorKey: 'description',
    },
    {
      header: 'Code',
      accessorKey: 'code',
    },
    {
      header: '',
      accessorKey: 'none',
      cell: ({
        row: {
          original: { code },
        },
      }) => (
        <RowPopover
          className='pr-4'
          onEdit={() => {
            router.push(
              routeTo(
                '/configuration/statement-formats/[effectiveDate]/edit/[statementFormatCode]',
                {
                  ...route.params,
                  effectiveDate: formatToMonthYearFromDate(
                    route.params.effectiveDate,
                  ),
                  statementFormatCode: code,
                },
              ),
            )
          }}
        />
      ),
      meta: { className: 'basis-28 shrink-0 justify-end px-2' },
    },
  ]

  return (
    <div className='flex min-h-0 flex-1 flex-col py-6 pl-12 pr-3'>
      <div className='flex items-center text-zinc-500'>
        Configurations
        <ChevronRightIcon className='h-4 w-4 text-zinc-400' />
        <span className='text-lg font-semibold text-black'>
          Statement formats
        </span>
      </div>
      <div className='mt-12 flex min-h-0 w-full flex-auto flex-grow flex-col rounded-lg border bg-white p-6'>
        <div className='flex w-full items-center justify-between'>
          <div className=''>
            <div className='text-lg font-semibold'>Statement formats</div>
            <div className='text-zinc-400'>
              View how statements will appear to customers.
            </div>
          </div>
          <Button className='btn' onClick={handleAddStatementFormatClick}>
            Add statement formats
          </Button>
        </div>
        <div className='mt-8 flex min-h-0 flex-auto flex-col'>
          <div className='flex items-center'>
            <MonthPicker
              title='Effective date'
              prefix='View data effective on'
              showIcon={true}
              onDateChange={(effectiveDate) => {
                router.push(
                  routeTo('/configuration/statement-formats/[effectiveDate]', {
                    ...route.params,
                    effectiveDate,
                  }),
                )
              }}
              initialDate={route.params.effectiveDate}
            />
            <div className='flex flex-auto items-center justify-end gap-4'>
              <SearchBar
                className='w-80'
                defaultLabel='Search statement formats'
                value={searchText}
                onValueChange={setSearchText}
              />
              <p className='text-sm text-zinc-400'>
                {numStatementFormats} statement format
                {numStatementFormats > 1 && 's'}
              </p>
            </div>
          </div>
          <div className='mt-4 flex min-h-0 flex-col'>
            <SortedTable
              data={statementFormatPlans}
              columns={columns}
              columnFilters={columnFilters}
              onVisibleRowsChanged={(rows) =>
                setNumStatementFormats(rows.length)
              }
              initialSortingState={[{ desc: false, id: 'description' }]}
              handleRowDoubleClick={({ original: { code } }) =>
                router.push(
                  routeTo(
                    '/configuration/statement-formats/[effectiveDate]/view/[statementFormatCode]',
                    {
                      ...route.params,
                      effectiveDate: formatToMonthYearFromDate(
                        route.params.effectiveDate,
                      ),
                      statementFormatCode: code,
                    },
                  ),
                )
              }
            />
          </div>
        </div>
      </div>
    </div>
  )
}
