'use client'

import { useRouter } from 'next/navigation'
import { useMutation } from '@tanstack/react-query'

import { Button } from '@/components/Button'
import UpdateStatementFormatsForm from '../../_components/UpdateStatementFormatsForm'
import { UpdateStatementFormatsFormState } from '../../_components/UpdateStatementFormatsFormTypes'

import { formatToMonthYearFromDate, formatToServerString } from '@/lib/date'
import { unwrap } from '@/lib/unions/unwrap'
import { routeTo, useRoute } from '../../../routing'
import { statementFormatPlanMutation } from '../../mutations'
import { formToApiSchemas } from '@/api/formToApiSchema'

export default function AddStatementFormat() {
  const router = useRouter()
  const route = unwrap(
    useRoute(),
    '/configuration/statement-formats/[effectiveDate]/add',
  )!
  const defaultValues: UpdateStatementFormatsFormState = {
    statementFormatInfo: {
      description: '',
      effectiveDate: formatToMonthYearFromDate(new Date()),
      code: '',
    },
    headerAndVisualFormatting: {
      headerLogoFileName: '',
      sectionBorderColor: '',
      sectionBackgroundColor: '',
      sectionTextColor: '',
      includeOfficerName: false,
      includeOfficerPhone: false,
      returnAddress: null,
      returnAddressType: null,
      enableBorderColor: false,
      footerImageDisplay: null,
    },
    statementMessageAndImage: {
      statementMessage: false,
      statementMessageLocation: '',
      // NEED THESE TO MAP TO UP 4 IMAGES
      statementMessageImages: [],
      statementImageLocations: [],
    },
    summaryOptions: {
      relationshipSummary: false,
      relationshipSummaryLabel: '',
      relationshipSummaryLocation: '',
      balanceSummary: false,
      balanceSummaryLabel: '',
      balanceSummaryLocation: '',
      resultsSummaryLabel: '',
      resultsSummaryLocation: '',
      balanceAndResultsLayout: null,
      earningsCreditRate: null,
      historicalSummaryType: null,
      historicalSummaryLabel: '',
      historicalSummaryLocation: '',
      dailyBalanceSummary: null,
      dailyBalanceSummaryLabel: '',
      dailyBalanceSummaryLocation: '',
    },
    serviceInformation: {
      serviceDetailLabel: '',
      serviceDetailLocation: '',
      afpServiceCode: false,
      serviceCode: false,
      sortServicesType: null,
      requiredBalance: false,
      requiredBalanceMultiplier: false,
      enableServiceCategory: false,
      serviceCategorySort: null,
      serviceCategoryBackgroundColor: '',
      serviceCategoryLevel: null,
      boldServiceCategoryLabel: false,
      serviceCategorySubtotal: false,
      serviceCategorySubtotalBackgroundColor: '',
      boldServiceCategorySubtotalLabel: false,
      serviceChargesDueBarChart: false,
      serviceChargesDueBarChartLabel: '',
      serviceChargesDueBarChartLocation: '',
      serviceCategoryPieChart: false,
      serviceCategoryPieChartLabel: '',
      serviceCategoryPieChartLocation: '',
    },
  }

  const addStatementFormatPlan = useMutation(
    statementFormatPlanMutation('/createStatementFormatPlan', {
      onSuccess: (_, request) => {
        router.push(
          routeTo(
            '/configuration/statement-formats/[effectiveDate]/view/[statementFormatCode]',
            {
              ...route.params,
              effectiveDate: formatToMonthYearFromDate(request.effectiveDate!),
              statementFormatCode: request.code!,
            },
          ),
        )
      },
    }),
  )
  const serializeForm = (formState: UpdateStatementFormatsFormState) => {
    const { statementFormatPlan } = formToApiSchemas
    const {
      statementFormatInfo,
      headerAndVisualFormatting,
      statementMessageAndImage,
      summaryOptions,
      serviceInformation,
    } = formState
    return statementFormatPlan.parse({
      ...statementFormatInfo,
      effectiveDate: formatToServerString(statementFormatInfo.effectiveDate),
      ...headerAndVisualFormatting,
      ...statementMessageAndImage,
      ...summaryOptions,
      ...serviceInformation,
      /**
       * TODO: printOfficer is not a field in the UI,
       * however, it is currently required in the API call
       */
      printOfficer: 'PRIMARY_OFFICER',
    })
  }
  return (
    <div className='w-full overflow-hidden overflow-y-scroll pt-12'>
      <div className='text-md ml-8 font-medium'>Add statement format</div>
      <UpdateStatementFormatsForm
        defaultValues={defaultValues}
        onSubmit={(formState) => {
          addStatementFormatPlan.mutate(serializeForm(formState))
        }}
      >
        <Button className='btn w-60' onClick={() => router.back()}>
          Cancel
        </Button>

        <Button type='submit' className='btn-primary w-60'>
          Save
        </Button>
      </UpdateStatementFormatsForm>
    </div>
  )
}
