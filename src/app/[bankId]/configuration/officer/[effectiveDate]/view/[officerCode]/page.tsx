'use client'

import Link from 'next/link'
import { useQuery } from '@tanstack/react-query'
import { ChevronRightIcon } from '@heroicons/react/24/outline'

import {
  DetailsSection,
  DetailsSectionTitle,
  DetailsSectionItemsRow,
  DetailsSectionItem,
} from '@/components/DetailsSection'
import { VersionTimeline } from '@/app/[bankId]/configuration/_components/VersionTimeline'

import { unwrap } from '@/lib/unions/unwrap'
import { routeTo, useRoute } from '@/app/[bankId]/configuration/routing'
import { officerQueries } from '../../../queries'
import { bankOptionsQueries } from '@/app/[bankId]/configuration/bank-options/queries'
import { apiToFormSchemas } from '@/api/apiToFormSchema'

export default function ViewOfficer() {
  const route = unwrap(
    useRoute(),
    '/configuration/officer/[effectiveDate]/view/[officerCode]',
  )!

  const {
    data: officer,
    status: officerStatus,
    error,
  } = useQuery(
    officerQueries('/getOfficer', {
      code: route.params.officerCode,
      effectiveDate: route.params.effectiveDate,
    }),
  )
  const {
    data: officerTimeline,
    status: timelineStatus,
    error: timelineError,
  } = useQuery(
    officerQueries('/getOfficerTimeline', {
      code: route.params.officerCode,
    }),
  )
  const bankCode = officer?.bankNumber

  const { data: bankOptions, error: bankOptionError } = useQuery({
    ...bankOptionsQueries('/getBankOptionsByCode', {
      code: bankCode!,
    }),
    enabled: !!bankCode,
  })
  if (officerStatus === 'pending' || timelineStatus === 'pending')
    return 'Loading...'
  if (!officer || error || !officerTimeline || timelineError) {
    return <div>404 Not Found</div>
  }
  const { bankOptions: BankOptionsType } = apiToFormSchemas
  const orderedBankOptions = bankOptions
    ?.map((item) => {
      return BankOptionsType.parse(item)
    })
    .sort((a, b) => {
      const newDateA = new Date(a.effectiveDate)
      const newDateB = new Date(b.effectiveDate)
      return newDateB.getTime() - newDateA.getTime()
    })

  const bankOptionName = orderedBankOptions?.[0]?.name

  const linkFormatter = (effectiveDate: string, officerCode: string) => {
    return routeTo(
      '/configuration/officer/[effectiveDate]/view/[officerCode]',
      { ...route.params, effectiveDate, officerCode },
    )
  }

  return (
    <div className='w-full overflow-hidden overflow-y-scroll px-8 py-10'>
      <div className='flex items-center justify-between'>
        <div className='flex items-center gap-2'>
          <Link
            href={routeTo(
              '/configuration/officer/[effectiveDate]',
              route.params,
            )}
            className='font-bold'
          >
            ...
          </Link>
          <ChevronRightIcon className='h-4 w-4 text-zinc-400' />
          <div className='text-md text-zinc-500'>
            <Link
              href={routeTo(
                '/configuration/officer/[effectiveDate]',
                route.params,
              )}
              className='font-bold'
            >
              Officer
            </Link>
          </div>
          <ChevronRightIcon className='h-4 w-4 text-zinc-400' />
          <div className='text-lg'>{officer.name}</div>
          <div className='text-sm text-zinc-500'>{officer.code}</div>
        </div>
      </div>
      <div className='mt-8 flex gap-4'>
        <div className='flex grow flex-col'>
          <DetailsSection>
            <DetailsSectionTitle>Officer information</DetailsSectionTitle>
            <DetailsSectionItemsRow>
              <DetailsSectionItem
                label={'Effective date *'}
                info={officer.effectiveDate}
              />
            </DetailsSectionItemsRow>
            <DetailsSectionItemsRow>
              <DetailsSectionItem label={'Name *'} info={officer.name} />
              <DetailsSectionItem label={'Code *'} info={officer.code} />
            </DetailsSectionItemsRow>
            <DetailsSectionItemsRow>
              <DetailsSectionItem
                label={'Bank'}
                info={`${officer.bankNumber} - ${bankOptionName}`}
              />
            </DetailsSectionItemsRow>
            <DetailsSectionItemsRow>
              <DetailsSectionItem label={'Phone'} info={`${officer.phone}`} />
            </DetailsSectionItemsRow>
          </DetailsSection>
        </div>
        <div className='flex max-w-72 flex-col rounded-lg border bg-white p-6'>
          <VersionTimeline
            versions={officerTimeline}
            currentEffectiveDate={route.params.effectiveDate}
            linkFormatter={linkFormatter}
            shouldParseToDateLevel
          />
        </div>
      </div>
    </div>
  )
}
