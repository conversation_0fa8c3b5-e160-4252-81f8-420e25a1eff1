import { revenueConnectClient } from '@/api/revenueConnectClient'
import { defineQueries, defineQuery } from '@/lib/state/defineQuery'
import { withMetrics } from '@/lib/middleware/withMetrics'

const getOfficerByEffectiveDate = defineQuery(
  revenueConnectClient,
  '/getOfficers',
  (payload) => [payload.effectiveDate],
)

const getOfficerByCode = defineQuery(
  revenueConnectClient,
  '/getOfficer',
  (payload) => [payload.code, payload.effectiveDate],
)

const getOfficerTimeline = defineQuery(
  revenueConnectClient,
  '/getOfficerTimeline',
  (payload) => [payload.code],
)

export const officerQueries = defineQueries(
  [getOfficerByEffectiveDate, getOfficerByCode, getOfficerTimeline],
  withMetrics(),
)
