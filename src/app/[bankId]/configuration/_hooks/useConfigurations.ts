import { useQuery } from '@tanstack/react-query'
import { userFieldQueries } from '../user-fields/queries'
import { analysisResultOptionsQueries } from '../analysis-result-options/queries'
import { scheduleDefinitionQueries } from '../schedule-definition/queries'
import { earningsCreditQueries } from '../earnings-credit/queries'
import { investableBalanceQueries } from '../investable-balance/queries'
import { requiredBalanceQueries } from '../required-balance/queries'
import { statementFormatPlanQueries } from '../statement-formats/queries'
import { listSelector } from '@/api/selectors'
import { apiToFormSchemas } from '@/api/apiToFormSchema'

export function useConfigurations({
  effectiveDate,
}: {
  effectiveDate: string
}) {
  return {
    userFieldConfigurations: useQuery({
      ...userFieldQueries('/getUserFieldsConfigurations'),
      select: listSelector(apiToFormSchemas.hydratedUserFieldConfiguration),
    }),
    analysisResultOptions: useQuery(
      analysisResultOptionsQueries(
        '/listAnalysisResultOptionsByEffectiveDate',
        {
          effectiveDate,
        },
      ),
    ),
    cycleDefinitions: useQuery(
      scheduleDefinitionQueries('/getCycleDefinitions', {
        effectiveDate,
      }),
    ),
    earningsCreditDefinitions: useQuery(
      earningsCreditQueries('/getEarningsCreditDefinitions', {
        effectiveDate,
      }),
    ),
    investableBalanceDefinitions: useQuery(
      investableBalanceQueries('/getInvestableBalanceDefinitions', {
        effectiveDate,
      }),
    ),
    requiredBalanceDefinitions: useQuery(
      requiredBalanceQueries('/getRequiredBalanceDefinitions', {
        effectiveDate,
      }),
    ),
    statementFormatPlans: useQuery(
      statementFormatPlanQueries('/getStatementFormatPlans', {
        effectiveDate,
      }),
    ),
  }
}
