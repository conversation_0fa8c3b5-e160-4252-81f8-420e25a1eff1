import { withMetrics } from '@/lib/middleware/withMetrics'
import { defineMutations, defineMutation } from '@/lib/state/defineMutation'
import { revenueConnectClient } from '@/api/revenueConnectClient'
import { scheduleDefinitionQueries } from './queries'
import { compose } from '@/lib/functional/compose'
import { notifications } from './notifications'

const createScheduleDefinition = defineMutation(
  revenueConnectClient,
  '/createCycleDefinition',
  {
    invalidate: (request) => [
      scheduleDefinitionQueries('/getCycleDefinitions', {
        effectiveDate: request.effectiveDate,
      }),
      scheduleDefinitionQueries('/getCycleDefinition', {
        effectiveDate: request.effectiveDate,
        code: request.code,
      }),
      scheduleDefinitionQueries('/getCycleDefinitionByCode', {
        code: request.code,
      }),
    ],
  },
)

const updateScheduleDefinition = defineMutation(
  revenueConnectClient,
  '/updateCycleDefinition',
  {
    invalidate: (request) => [
      scheduleDefinitionQueries('/getCycleDefinitions', {
        effectiveDate: request.effectiveDate,
      }),
      scheduleDefinitionQueries('/getCycleDefinition', {
        effectiveDate: request.effectiveDate,
        code: request.code,
      }),
      scheduleDefinitionQueries('/getCycleDefinitionByCode', {
        code: request.code,
      }),
    ],
  },
)

export type ScheduleDefinitionsMutation =
  | typeof createScheduleDefinition
  | typeof updateScheduleDefinition

export const scheduleDefinitionsMutation = defineMutations(
  [createScheduleDefinition, updateScheduleDefinition],
  compose(notifications, withMetrics()),
)
