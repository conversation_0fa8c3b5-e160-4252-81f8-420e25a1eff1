import { revenueConnectClient } from '@/api/revenueConnectClient'
import { defineQueries, defineQuery } from '@/lib/state/defineQuery'
import { withMetrics } from '@/lib/middleware/withMetrics'

const getScheduleDefinitionsByEffectiveDate = defineQuery(
  revenueConnectClient,
  '/getCycleDefinitions',
  (payload) => [payload.effectiveDate],
)

const getScheduleDefinition = defineQuery(
  revenueConnectClient,
  '/getCycleDefinition',
  (payload) => [payload.code, payload.effectiveDate],
)

const getScheduleDefinitionByCode = defineQuery(
  revenueConnectClient,
  '/getCycleDefinitionByCode',
  (payload) => [payload.code],
)

export const scheduleDefinitionQueries = defineQueries(
  [
    getScheduleDefinitionsByEffectiveDate,
    getScheduleDefinition,
    getScheduleDefinitionByCode,
  ],
  withMetrics(),
)
