'use client'

import { useFormTextInput } from '@/components/Form/useFormTextInput'
import { useFormMonthPicker } from '@/components/Form/useFormMonthPicker'
import {
  scheduleDefinitionInfoSchema,
  UpdateScheduleDefinitionFormApi,
  UpdateScheduleDefinitionFormState,
} from './UpdateScheduleDefinitionFormTypes'

export default function ScheduleDefinitionInfo({
  form,
  isEditMode,
}: {
  form: UpdateScheduleDefinitionFormApi
  isEditMode?: boolean
}) {
  const FormInput = useFormTextInput<UpdateScheduleDefinitionFormState>({
    form,
  })
  const FormMonthPicker = useFormMonthPicker<UpdateScheduleDefinitionFormState>(
    {
      form,
    },
  )

  // TODO: make call to get code. If list is empty, code is unique.

  return (
    <div className='mt-10 flex w-full flex-col rounded-lg border bg-white p-6'>
      <div className='font-semibold'>Schedule definition information</div>
      <div className='mt-3 flex gap-9'>
        <div className='flex flex-1 flex-col gap-2'>
          <FormMonthPicker
            name='scheduleDefinitionInfo.effectiveDate'
            label='Effective date *'
          />
        </div>
        <div className='flex-1'></div>
      </div>
      <div className='mt-3 flex gap-9'>
        <FormInput
          name='scheduleDefinitionInfo.name'
          label='Name'
          required
          // TODO can be replaced with validator at useForm-level

          validators={{
            onChange: scheduleDefinitionInfoSchema.shape.name,
          }}
        />
        {/* todo: check schedule definition code is unique */}
        <FormInput
          name='scheduleDefinitionInfo.code'
          label='Code'
          required
          // TODO can be replaced with validator at useForm-level

          validators={{
            onChange: scheduleDefinitionInfoSchema.shape.code,
          }}
          placeholder='Max 5 digits'
          type='number'
          invalidChars={['e', 'E']}
          readonly={(field) =>
            field.value != null &&
            field.value.length > 0 &&
            field.meta.isPristine &&
            !!isEditMode
          }
        />
      </div>
    </div>
  )
}
