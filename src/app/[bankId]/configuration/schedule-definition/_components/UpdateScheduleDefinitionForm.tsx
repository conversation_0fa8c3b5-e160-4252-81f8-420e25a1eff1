'use client'

import { useForm } from '@tanstack/react-form'

import { UpdateScheduleDefinitionFormState } from './UpdateScheduleDefinitionFormTypes'
import ScheduleDefinitionInfo from './ScheduleDefinitionInfo'
import ScheduleDefinitionMonths from './ScheduleDefinitionMonths'

export interface UpdateScheduleDefinitionFormProps {
  defaultValues: UpdateScheduleDefinitionFormState
  onSubmit: (state: UpdateScheduleDefinitionFormState) => void
  isEditMode?: boolean
}

export default function UpdateScheduleDefinitionForm({
  defaultValues,
  onSubmit,
  children,
  isEditMode,
}: React.PropsWithChildren<UpdateScheduleDefinitionFormProps>) {
  const form = useForm({
    defaultValues,
    onSubmit: ({ value }) => {
      onSubmit(value)
    },
  })

  // TODO can add `validators` prop with `onChange` pointing to composite zod schema
  // so we don't need to define `validators.onChange` on every form field.
  return (
    <form
      className='flex max-h-full flex-auto flex-col'
      onSubmit={async (event) => {
        form.reset(form.state.values)
        event.preventDefault()
        event.stopPropagation()
        await form.handleSubmit()
      }}
    >
      <div className='flex flex-auto flex-col gap-2 overflow-y-auto px-8'>
        <ScheduleDefinitionInfo form={form} isEditMode={isEditMode} />
        <ScheduleDefinitionMonths form={form} />
      </div>
      <div className='flex justify-end gap-4 border-t bg-white px-12 py-10'>
        {children}
      </div>
    </form>
  )
}
