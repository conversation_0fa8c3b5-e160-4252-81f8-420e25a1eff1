'use client'

import MonthSelector from '../../_components/MonthSelector'
import { UpdateScheduleDefinitionFormApi } from './UpdateScheduleDefinitionFormTypes'

export default function ScheduleDefinitionMonths({
  form,
  viewOnly,
}: {
  form: UpdateScheduleDefinitionFormApi
  viewOnly?: boolean
}) {
  return (
    <div className='mt-10 flex w-full flex-col rounded-lg border bg-white p-6'>
      <div className='font-semibold'>Schedule months</div>

      {!viewOnly && (
        <div className='text-zinc-400'>Add or view schedule details</div>
      )}

      <form.Field name='scheduleDefinitionMonths.monthsSelected'>
        {({ state: { value }, handleChange }) => (
          <MonthSelector values={value} handleChange={handleChange} />
        )}
      </form.Field>
    </div>
  )
}
