import { name, configCode } from '@/api/zodSchemas'
import { FormApi, ReactFormApi } from '@tanstack/react-form'
import { z } from 'zod'

export const scheduleDefinitionInfoSchema = z.object({
  effectiveDate: z.string(),
  name,
  code: configCode,
})

type ScheduleDefinitionInfoState = z.infer<typeof scheduleDefinitionInfoSchema>

// TODO create composite zod schema instead (e.g., updateScheduleDefinitionSchema) and infer this type from that schema.
// This new composite schema could also be used as onChange validator in useForm in UpdateScheduleDefinitionForm to reduce
// need to define validators.onChange on every form field.
export type UpdateScheduleDefinitionFormState = {
  scheduleDefinitionInfo: ScheduleDefinitionInfoState
  scheduleDefinitionMonths: ScheduleDefinitionMonthsState
}

export type UpdateScheduleDefinitionFormApi =
  ReactFormApi<UpdateScheduleDefinitionFormState> &
    FormApi<UpdateScheduleDefinitionFormState>

export type MonthKeys =
  | 'JANUARY'
  | 'FEBRUARY'
  | 'MARCH'
  | 'APRIL'
  | 'MAY'
  | 'JUNE'
  | 'JULY'
  | 'AUGUST'
  | 'SEPTEMBER'
  | 'OCTOBER'
  | 'NOVEMBER'
  | 'DECEMBER'

export type PayloadKeys =
  | 'isJanuarySelected'
  | 'isFebruarySelected'
  | 'isMarchSelected'
  | 'isAprilSelected'
  | 'isMaySelected'
  | 'isJuneSelected'
  | 'isJulySelected'
  | 'isAugustSelected'
  | 'isSeptemberSelected'
  | 'isOctoberSelected'
  | 'isNovemberSelected'
  | 'isDecemberSelected'

export const scheduleDefinitionMonthsSchema = z.object({
  monthsSelected: z.array(z.string().optional()),
})

export type ScheduleDefinitionMonthsState = z.infer<
  typeof scheduleDefinitionMonthsSchema
>
