'use client'

import { useRouter } from 'next/navigation'
import { But<PERSON> } from '@/components/Button'

import UpdateScheduleDefinitionForm from '../../../_components/UpdateScheduleDefinitionForm'

import { unwrap } from '@/lib/unions/unwrap'
import { routeTo, useRoute } from '@/app/[bankId]/configuration/routing'
import { scheduleDefinitionQueries } from '../../../queries'
import { useMutation, useQuery } from '@tanstack/react-query'
import { apiToFormSchemas } from '@/api/apiToFormSchema'
import { scheduleDefinitionsMutation } from '../../../mutations'
import { components } from '@/api/schema'
import {
  MonthKeys,
  PayloadKeys,
  UpdateScheduleDefinitionFormState,
} from '../../../_components/UpdateScheduleDefinitionFormTypes'
import { formatToServerString, formatToMonthYearFromDate } from '@/lib/date'

export default function EditScheduleDefinition({}) {
  const router = useRouter()
  const route = unwrap(
    useRoute(),
    '/configuration/schedule-definition/[effectiveDate]/edit/[scheduleDefinitionCode]',
  )!
  const effectiveDate = route.params.effectiveDate

  const payload = {
    code: route.params.scheduleDefinitionCode,
    effectiveDate: formatToServerString(effectiveDate),
  }

  const { data, error } = useQuery(
    scheduleDefinitionQueries('/getCycleDefinition', payload),
  )

  const updateScheduleDefinition = useMutation(
    scheduleDefinitionsMutation('/updateCycleDefinition', {
      onSuccess: (_, request) => {
        router.push(
          routeTo(
            '/configuration/schedule-definition/[effectiveDate]/view/[scheduleDefinitionCode]',
            {
              ...route.params,
              scheduleDefinitionCode: request.code!,
              effectiveDate: formatToMonthYearFromDate(request.effectiveDate!),
            },
          ),
        )
      },
    }),
  )

  if (!data || error) {
    return <div>404 Not Found</div>
  }

  const scheduleDefintion = apiToFormSchemas.cycleDefinition.parse(data)

  const defaultValues: UpdateScheduleDefinitionFormState = {
    scheduleDefinitionInfo: {
      code: scheduleDefintion.code,
      name: scheduleDefintion.description ?? '',
      effectiveDate: scheduleDefintion.effectiveDate,
    },
    scheduleDefinitionMonths: {
      // TODO CycleDefinition.includedMonths on backend needs @NotNull
      monthsSelected: scheduleDefintion.includedMonths!,
    },
  }

  const serialize = ({
    scheduleDefinitionInfo,
    scheduleDefinitionMonths,
  }: UpdateScheduleDefinitionFormState) => {
    const MonthsMap: Record<MonthKeys, PayloadKeys> = {
      JANUARY: 'isJanuarySelected',
      FEBRUARY: 'isFebruarySelected',
      MARCH: 'isMarchSelected',
      APRIL: 'isAprilSelected',
      MAY: 'isMaySelected',
      JUNE: 'isJuneSelected',
      JULY: 'isJulySelected',
      AUGUST: 'isAugustSelected',
      SEPTEMBER: 'isSeptemberSelected',
      OCTOBER: 'isOctoberSelected',
      NOVEMBER: 'isNovemberSelected',
      DECEMBER: 'isDecemberSelected',
    }

    const payload: components['schemas']['CycleDefinition'] = {
      code: scheduleDefinitionInfo.code,
      effectiveDate: formatToServerString(scheduleDefinitionInfo.effectiveDate),
      description: scheduleDefinitionInfo.name,
      cycleType: 'STATEMENT',
    }

    scheduleDefinitionMonths.monthsSelected.forEach(
      (month: string | undefined) => {
        if (!month) {
          return
        }
        const k = MonthsMap[month as MonthKeys]
        payload[k] = true
      },
    )
    return payload
  }

  return (
    <div className='w-full min-w-full overflow-auto pt-12'>
      <div className='text-md ml-8 font-medium'>Schedule Information</div>
      <UpdateScheduleDefinitionForm
        defaultValues={defaultValues}
        onSubmit={(formState) => {
          const payload = serialize(formState)
          updateScheduleDefinition.mutate(payload)
        }}
        isEditMode
      >
        <Button className='btn w-60' onClick={() => router.back()}>
          Cancel
        </Button>

        <Button type='submit' className='btn-primary w-60'>
          Save
        </Button>
      </UpdateScheduleDefinitionForm>
    </div>
  )
}
