'use client'

import { useRouter } from 'next/navigation'
import { <PERSON><PERSON> } from '@/components/Button'
import { ChevronRightIcon } from '@heroicons/react/24/outline'
import Link from 'next/link'

import {
  DetailsSection,
  DetailsSectionItem,
  DetailsSectionItemsRow,
  DetailsSectionTitle,
} from '@/components/DetailsSection'

import { VersionTimeline } from '@/app/[bankId]/configuration/_components/VersionTimeline'
import MonthSelector from '@/app/[bankId]/configuration/_components/MonthSelector'

import { unwrap } from '@/lib/unions/unwrap'
import { routeTo, useRoute } from '@/app/[bankId]/configuration/routing'
import { useQuery } from '@tanstack/react-query'
import { scheduleDefinitionQueries } from '../../../queries'
import { apiToFormSchemas } from '@/api/apiToFormSchema'
import { formatToMonthYearFromDate, formatToServerString } from '@/lib/date'

export default function ViewScheduleDefinition({}) {
  const router = useRouter()
  const route = unwrap(
    useRoute(),
    '/configuration/schedule-definition/[effectiveDate]/view/[scheduleDefinitionCode]',
  )!
  const effectiveDate = route.params.effectiveDate

  const { data, error } = useQuery(
    scheduleDefinitionQueries('/getCycleDefinitionByCode', {
      code: route.params.scheduleDefinitionCode,
    }),
  )

  if (!data || data.length < 1 || error) {
    return <div>404 Not Found</div>
  }

  const { cycleDefinition: ScheduleDefinitionType } = apiToFormSchemas

  const scheduleDefinitions = data
    .map((item) => ScheduleDefinitionType.parse(item))
    .sort(
      (a, b) =>
        new Date(b.effectiveDate).getDate() -
        new Date(a.effectiveDate).getDate(),
    )

  const scheduleDefinition =
    scheduleDefinitions.find((item) => {
      return item.effectiveDate === formatToServerString(effectiveDate)
    }) || scheduleDefinitions[0]

  const onEditClick = () => {
    router.push(
      routeTo(
        '/configuration/schedule-definition/[effectiveDate]/edit/[scheduleDefinitionCode]',
        {
          ...route.params,
          effectiveDate: formatToMonthYearFromDate(
            scheduleDefinition.effectiveDate,
          ),
          scheduleDefinitionCode: scheduleDefinition.code,
        },
      ),
    )
  }

  const linkFormatter = (
    effectiveDate: string,
    scheduleDefinitionCode: string,
  ) => {
    return routeTo(
      '/configuration/schedule-definition/[effectiveDate]/view/[scheduleDefinitionCode]',
      {
        ...route.params,
        effectiveDate: formatToMonthYearFromDate(effectiveDate),
        scheduleDefinitionCode,
      },
    )
  }

  return (
    <div className='w-full overflow-hidden overflow-y-scroll px-8 py-10'>
      <div className='flex items-center justify-between'>
        <div className='flex items-center gap-2'>
          <Link
            href={routeTo(
              '/configuration/schedule-definition/[effectiveDate]',
              route.params,
            )}
            className='font-bold'
          >
            ...
          </Link>
          <ChevronRightIcon className='h-4 w-4 text-zinc-400' />
          <div className='text-md text-zinc-500'>
            <Link
              href={routeTo(
                '/configuration/schedule-definition/[effectiveDate]',
                route.params,
              )}
              className='font-bold'
            >
              Schedule Information
            </Link>
          </div>
          <ChevronRightIcon className='h-4 w-4 text-zinc-400' />
          <div className='text-lg'>{scheduleDefinition.description}</div>
          <div className='text-sm text-zinc-500'>{scheduleDefinition.code}</div>
        </div>
        <Button className='btn-primary text-white' onClick={onEditClick}>
          Edit
        </Button>
      </div>
      <div className='mt-8 flex gap-4'>
        <div className='flex grow flex-col'>
          <DetailsSection>
            <DetailsSectionTitle>Schedule information</DetailsSectionTitle>
            <DetailsSectionItemsRow>
              <DetailsSectionItem
                label={'Effective date *'}
                info={scheduleDefinition.effectiveDate}
              />
            </DetailsSectionItemsRow>
            <DetailsSectionItemsRow>
              <DetailsSectionItem
                label={'Name *'}
                info={scheduleDefinition.description ?? ''}
              />
              <DetailsSectionItem
                label={'Code *'}
                info={scheduleDefinition.code}
              />
            </DetailsSectionItemsRow>
          </DetailsSection>

          <DetailsSection className='mt-3'>
            <DetailsSectionTitle>Schedule Month</DetailsSectionTitle>
            <MonthSelector
              values={
                // TODO CycleDefinition.includedMonths on backend needs @NotNull
                scheduleDefinition.includedMonths!
              }
              viewOnly
            />
          </DetailsSection>
        </div>
        <div className='flex max-w-72 flex-col rounded-lg border bg-white p-6'>
          <VersionTimeline
            versions={scheduleDefinitions}
            currentEffectiveDate={route.params.effectiveDate}
            linkFormatter={linkFormatter}
          />
        </div>
      </div>
    </div>
  )
}
