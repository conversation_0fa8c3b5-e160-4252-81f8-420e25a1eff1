'use client'

import { useRouter } from 'next/navigation'
import { But<PERSON> } from '@/components/Button'

import { formatToMonthYearFromDate, formatToServerString } from '@/lib/date'
import UpdateScheduleDefinitionForm from '../../_components/UpdateScheduleDefinitionForm'
import { unwrap } from '@/lib/unions/unwrap'
import { routeTo, useRoute } from '../../../routing'
import { useMutation } from '@tanstack/react-query'
import { scheduleDefinitionsMutation } from '../../mutations'
import { components } from '@/api/schema'
import {
  MonthKeys,
  PayloadKeys,
  UpdateScheduleDefinitionFormState,
} from '../../_components/UpdateScheduleDefinitionFormTypes'

export default function AddScheduleDefinition({}) {
  const router = useRouter()
  const route = unwrap(
    useRoute(),
    '/configuration/schedule-definition/[effectiveDate]/add',
  )!

  const addScheduleDefinition = useMutation(
    scheduleDefinitionsMutation('/createCycleDefinition', {
      onSuccess: (_, request) => {
        router.push(
          routeTo(
            '/configuration/schedule-definition/[effectiveDate]/view/[scheduleDefinitionCode]',
            {
              ...route.params,
              scheduleDefinitionCode: request.code!,
              effectiveDate: formatToMonthYearFromDate(request.effectiveDate!),
            },
          ),
        )
      },
    }),
  )

  const defaultValues: UpdateScheduleDefinitionFormState = {
    scheduleDefinitionInfo: {
      code: '',
      name: '',
      effectiveDate: formatToMonthYearFromDate(new Date()),
    },
    scheduleDefinitionMonths: {
      monthsSelected: [],
    },
  }

  const serialize = ({
    scheduleDefinitionInfo,
    scheduleDefinitionMonths,
  }: UpdateScheduleDefinitionFormState) => {
    const MonthsMap: Record<MonthKeys, PayloadKeys> = {
      JANUARY: 'isJanuarySelected',
      FEBRUARY: 'isFebruarySelected',
      MARCH: 'isMarchSelected',
      APRIL: 'isAprilSelected',
      MAY: 'isMaySelected',
      JUNE: 'isJuneSelected',
      JULY: 'isJulySelected',
      AUGUST: 'isAugustSelected',
      SEPTEMBER: 'isSeptemberSelected',
      OCTOBER: 'isOctoberSelected',
      NOVEMBER: 'isNovemberSelected',
      DECEMBER: 'isDecemberSelected',
    }

    const payload: components['schemas']['CycleDefinition'] = {
      code: scheduleDefinitionInfo.code,
      effectiveDate: formatToServerString(scheduleDefinitionInfo.effectiveDate),
      description: scheduleDefinitionInfo.name,
      cycleType: 'STATEMENT',
    }

    scheduleDefinitionMonths.monthsSelected.forEach(
      (month: string | undefined) => {
        if (!month) {
          return
        }
        const k = MonthsMap[month as MonthKeys]
        payload[k] = true
      },
    )
    return payload
  }

  return (
    <div className='w-full min-w-full overflow-auto pt-12'>
      <div className='text-md ml-8 font-medium'>Schedule Information</div>
      <UpdateScheduleDefinitionForm
        defaultValues={defaultValues}
        onSubmit={(formState) => {
          const payload = serialize(formState)
          addScheduleDefinition.mutate(payload)
        }}
      >
        <Button className='btn w-60' onClick={() => router.back()}>
          Cancel
        </Button>

        <Button type='submit' className='btn-primary w-60'>
          Save
        </Button>
      </UpdateScheduleDefinitionForm>
    </div>
  )
}
