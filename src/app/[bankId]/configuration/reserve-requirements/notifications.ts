import { ReserveRequirementMutation } from './mutations'
import { Tag } from '@/lib/unions/Union'
import { withNotifications } from '@/components/Notifications'

export const messages: { [T in Tag<ReserveRequirementMutation>]: string } = {
  '/createReserveRequirementDefinition':
    'Reserve Requirement successfully created.',
  '/updateReserveRequirementDefinition':
    'Reserve Requirement successfully updated.',
}

export const notifications = withNotifications(messages)
