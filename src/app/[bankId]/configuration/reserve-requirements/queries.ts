import { revenueConnectClient } from '@/api/revenueConnectClient'
import { defineQueries, defineQuery } from '@/lib/state/defineQuery'
import { withMetrics } from '@/lib/middleware/withMetrics'

const getReserveRequirementsByEffectiveDate = defineQuery(
  revenueConnectClient,
  '/getReserveRequirementDefinitions',
  (payload) => [payload.effectiveDate],
)

const getReserveRequirementsByCode = defineQuery(
  revenueConnectClient,
  '/getReserveRequirementsByCode',
  (payload) => [payload.code],
)

export const reserveRequirementQueries = defineQueries(
  [getReserveRequirementsByEffectiveDate, getReserveRequirementsByCode],
  withMetrics(),
)
