'use client'

import { useEffect, useMemo, useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { useStore } from '@tanstack/react-form'
import Decimal from 'decimal.js'

import { useFormSelect } from '@/components/Form/useFormSelect'
import { useFormTextInput } from '@/components/Form/useFormTextInput'
import { TextInput } from '@/components/Input/TextInput'
import { If } from '@/components/If'
import {
  calculationMethodSchema,
  UpdateReserveRequirementFormApi,
  UpdateReserveRequirementFormState,
  calculationMethodTypeLabel,
  calculationMethodTypes,
} from './UpdateReserveRequirementFormTypes'

import { indexRateQueries } from '../../index-rates/queries'
import { formatToServerString, getLastDayOfMonthString } from '@/lib/date'
import { IndexRate } from '@/api/formToApiSchema'
import { useCodesAndRenderOption } from '../../helpers'
interface CalculationMethodProps {
  form: UpdateReserveRequirementFormApi
}

export default function CalculationMethod({ form }: CalculationMethodProps) {
  const [effectiveRate, setEffectiveRate] = useState('')
  const Select = useFormSelect<UpdateReserveRequirementFormState>({ form })
  const FormInput = useFormTextInput<UpdateReserveRequirementFormState>({
    form,
  })
  const {
    calculationMethod: {
      calculationMethodType,
      indexAdjustment,
      indexRateCode,
    },
    reserveRequirementInfo: { effectiveDate },
  } = useStore(form.store, (state) => state.values)

  /**
   * Fetch Index-rates based off of the Reserve-requirement's effective-month
   * Index-rates have a full-effective-date, so we need to get Index-rates that are
   * effective as of the last-day of the month selected for Reserve-requirements
   */
  const { data: indexRates, error: indexRateErrors } = useQuery(
    indexRateQueries('/listIndexRatesByEffectiveDate', {
      effectiveDate: getLastDayOfMonthString(effectiveDate),
    }),
  )
  const [indexRateCodes, renderIndexRateOption] = useCodesAndRenderOption(
    indexRates as IndexRate[],
    (indexRate) => indexRate.code,
    (indexRate) => `${indexRate.code} - ${indexRate.name}`,
  )
  const indexRateCodeToRateMap: Record<string, number> = useMemo(
    () =>
      (indexRates ?? []).reduce(
        (result, indexRate) => {
          result[indexRate.code] = indexRate.indexRate
          return result
        },
        {} as Record<string, number>,
      ),
    [indexRates, effectiveDate],
  )

  useEffect(() => {
    const newEffectiveRate = calculateEffectiveRate(
      indexRateCode,
      indexAdjustment,
    )
    setEffectiveRate(newEffectiveRate)
  }, [indexRateCodeToRateMap, indexAdjustment, indexRateCode])

  const calculateEffectiveRate = (
    indexRateCode: string | null,
    indexAdjustment: number | null,
  ): string => {
    if (!indexRateCode) {
      return ''
    }
    if (!indexRateCodeToRateMap[indexRateCode]) {
      return ''
    }
    if (!indexAdjustment) {
      return indexRateCodeToRateMap[indexRateCode].toString()
    }
    const indexRateDecimal = new Decimal(indexRateCodeToRateMap[indexRateCode])
    return indexRateDecimal.add(indexAdjustment).toString()
  }

  return (
    <div className='mt-3 flex w-full flex-col rounded-lg border bg-white p-6'>
      <div className='font-semibold'>Calculation method</div>
      <div className='mt-4 flex gap-9'>
        <Select
          label='Calculation method'
          name='calculationMethod.calculationMethodType'
          required
          options={calculationMethodTypes.options}
          renderSelected={calculationMethodTypeLabel}
          renderOption={calculationMethodTypeLabel}
          // TODO can be replaced with validator at useForm-level
          validators={{
            onChange: calculationMethodSchema.shape.calculationMethodType,
          }}
        />
        <div className='flex-1'></div>
      </div>
      <If true={calculationMethodType === 'INDEXED'}>
        <div className='flex gap-9'>
          <Select
            label='Index rate'
            tooltip='Index rate'
            name='calculationMethod.indexRateCode'
            // TODO can be replaced with validator at useForm-level
            validators={{
              onChange: calculationMethodSchema.shape.indexRateCode,
            }}
            options={indexRateCodes}
            renderOption={renderIndexRateOption}
            renderSelected={renderIndexRateOption}
            required
          />
          <div className='flex-1'></div>
        </div>
        <div className='flex gap-9'>
          <FormInput
            label='Index adjustment'
            invalidChars={['e', 'E']}
            name='calculationMethod.indexAdjustment'
            type='number'
            validators={{
              onChange: ({ value }) => {
                const result =
                  calculationMethodSchema.shape.indexAdjustment.safeParse(value)
                return result.success ? undefined : (
                    result.error.issues[0].message
                  )
              },
            }}
          />
          <TextInput
            label='Effective rate'
            className='flex-1'
            name='effective rate'
            value={effectiveRate}
            readonly
          />
        </div>
        <div className='flex gap-9'>
          <FormInput
            label='Minimum rate'
            name='calculationMethod.floor'
            invalidChars={['e', 'E']}
            type='number'
            validators={{
              onChange: ({ value }) => {
                const result =
                  calculationMethodSchema.shape.floor.safeParse(value)
                return result.success ? undefined : (
                    result.error.issues[0].message
                  )
              },
            }}
          />
          <FormInput
            label='Maximum rate'
            name='calculationMethod.ceiling'
            invalidChars={['e', 'E']}
            type='number'
            validators={{
              onChange: ({ value }) => {
                const result =
                  calculationMethodSchema.shape.ceiling.safeParse(value)
                return result.success ? undefined : (
                    result.error.issues[0].message
                  )
              },
            }}
          />
        </div>
      </If>
      <If true={calculationMethodType === 'PERCENTAGE'}>
        <div className='flex gap-9'>
          <FormInput
            label='Reserve rate'
            name='calculationMethod.reserveRate'
            type='number'
            invalidChars={['e', 'E']}
            validators={{
              onChange: ({ value }) => {
                const result =
                  calculationMethodSchema.shape.reserveRate.safeParse(value)
                return result.success ? undefined : (
                    result.error.issues[0].message
                  )
              },
            }}
            required
          />
          <div className='flex-1'></div>
        </div>
      </If>
    </div>
  )
}
