'use client'

import { useFormSelect } from '@/components/Form/useFormSelect'

import {
  reserveRequirementSchema,
  UpdateReserveRequirementFormApi,
  UpdateReserveRequirementFormState,
  baseBalanceTypes,
  baseBalanceTypeLabel,
} from './UpdateReserveRequirementFormTypes'
interface ReserveRequirementProps {
  form: UpdateReserveRequirementFormApi
}

export default function ReserveRequirement({ form }: ReserveRequirementProps) {
  const Select = useFormSelect<UpdateReserveRequirementFormState>({ form })
  return (
    <div className='mt-3 flex w-full flex-col rounded-lg border bg-white p-6'>
      <div className='font-semibold'>Reserve requirement</div>

      <div className='mt-3 flex gap-9'>
        <Select
          label='Base balance'
          name='reserveRequirement.baseBalanceType'
          required
          options={baseBalanceTypes.options}
          renderSelected={baseBalanceTypeLabel}
          renderOption={baseBalanceTypeLabel}
          // TODO can be replaced with validator at useForm-level
          validators={{
            onChange: reserveRequirementSchema.shape.baseBalanceType,
          }}
        />
        <div className='flex-1'></div>
      </div>
    </div>
  )
}
