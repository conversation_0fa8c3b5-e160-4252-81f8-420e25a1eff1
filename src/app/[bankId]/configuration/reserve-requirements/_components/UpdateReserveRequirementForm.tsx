'use client'
import { useForm } from '@tanstack/react-form'

import ReserveRequirementInfo from './ReserveRequirementInfo'
import ReserveRequirement from './ReserveRequirement'
import CalculationMethod from './CalculationMethod'
import { UpdateReserveRequirementFormState } from './UpdateReserveRequirementFormTypes'

interface UpdateReserveRequirementFormProps {
  defaultValues: UpdateReserveRequirementFormState
  onSubmit: (state: UpdateReserveRequirementFormState) => void
  isEditMode?: boolean
}

export default function UpdateReserveRequirementForm({
  defaultValues,
  onSubmit,
  children,
  isEditMode,
}: React.PropsWithChildren<UpdateReserveRequirementFormProps>) {
  const form = useForm({
    defaultValues,
    onSubmit: ({ value }) => {
      onSubmit(value)
    },
  })

  // TODO can add `validators` prop with `onChange` pointing to composite zod schema
  // so we don't need to define `validators.onChange` on every form field.
  return (
    <form
      className='flex min-h-screen flex-col'
      onSubmit={async (event) => {
        form.reset(form.state.values)
        event.preventDefault()
        event.stopPropagation()
        await form.handleSubmit()
      }}
    >
      <div className='flex flex-grow flex-col gap-2 overflow-y-auto px-8'>
        <ReserveRequirementInfo form={form} isEditMode={isEditMode} />
        <ReserveRequirement form={form} />
        <CalculationMethod form={form} />
      </div>
      <div className='sticky bottom-0 mt-auto flex justify-end gap-4 border-t bg-white px-12 py-10'>
        {children}
      </div>
    </form>
  )
}
