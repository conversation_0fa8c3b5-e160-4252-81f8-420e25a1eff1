import { z } from 'zod'
import { <PERSON><PERSON><PERSON>, ReactFormApi } from '@tanstack/react-form'
import { match } from '@/lib/unions/match'
import { variant } from '@/lib/unions/Union'

import { apiToFormSchemas } from '@/api/apiToFormSchema'
import { name, configCode } from '@/api/zodSchemas'
import { nullableRequired } from '@/lib/validation/nullableRequiredNativeEnum'
import { percentageWithDecimals } from '@/lib/validation/percentageWithDecimals'

const { reserveRequirementDefinition } = apiToFormSchemas
export const reserveRequirementInfoSchema = z.object({
  effectiveDate: z.string(),
  name,
  code: configCode,
})

export const reserveRequirementSchema = z.object({
  baseBalanceType: nullableRequired(
    reserveRequirementDefinition.shape.baseBalanceType,
  ),
})

export const calculationMethodSchema = z.object({
  calculationMethodType: nullableRequired(
    reserveRequirementDefinition.shape.calculationMethodType,
  ),
  //conditional fields:
  indexRateCode: nullableRequired(z.string()),
  indexAdjustment: percentageWithDecimals.nullable(),
  floor: percentageWithDecimals.nullable(),
  ceiling: percentageWithDecimals.nullable(),
  reserveRate: nullableRequired(percentageWithDecimals.nullable()),
})

type ReserveRequirementInfoState = z.infer<typeof reserveRequirementInfoSchema>
type ReserveRequirementState = z.infer<typeof reserveRequirementSchema>
type CalculationMethodState = z.infer<typeof calculationMethodSchema>

// TODO create composite zod schema instead (e.g., updateReserveRequirementSchema) and infer this type from that schema.
// This new composite schema could also be used as onChange validator in useForm in UpdateReserveRequirementForm to reduce
// need to define validators.onChange on every form field.
export type UpdateReserveRequirementFormState = {
  reserveRequirementInfo: ReserveRequirementInfoState
  reserveRequirement: ReserveRequirementState
  calculationMethod: CalculationMethodState
}

export type UpdateReserveRequirementFormApi =
  ReactFormApi<UpdateReserveRequirementFormState> &
    FormApi<UpdateReserveRequirementFormState>

export const baseBalanceTypes =
  reserveRequirementDefinition.shape.baseBalanceType

export const baseBalanceTypeLabel = (value: z.infer<typeof baseBalanceTypes>) =>
  match(variant(value), {
    AVERAGE_COLLECTED: () => 'Average collected',
    AVERAGE_POSITIVE_COLLECTED: () => 'Average positive collected',
  })

export const calculationMethodTypes =
  reserveRequirementDefinition.shape.calculationMethodType

export const calculationMethodTypeLabel = (
  value: z.infer<typeof calculationMethodTypes>,
) =>
  match(variant(value), {
    INDEXED: () => 'Indexed',
    PERCENTAGE: () => 'Percentage',
  })
