import { reserveRequirementQueries } from './queries'
import { withMetrics } from '@/lib/middleware/withMetrics'
import { defineMutations, defineMutation } from '@/lib/state/defineMutation'
import { revenueConnectClient } from '@/api/revenueConnectClient'
import { compose } from '@/lib/functional/compose'
import { notifications } from './notifications'

const createReserveRequirement = defineMutation(
  revenueConnectClient,
  '/createReserveRequirementDefinition',
  {
    invalidate: (request) => [
      reserveRequirementQueries('/getReserveRequirementDefinitions', {
        effectiveDate: request.effectiveDate,
      }),
      reserveRequirementQueries('/getReserveRequirementsByCode', {
        code: request.code,
      }),
    ],
  },
)

const updateReserveRequirement = defineMutation(
  revenueConnectClient,
  '/updateReserveRequirementDefinition',
  {
    invalidate: (request) => [
      reserveRequirementQueries('/getReserveRequirementDefinitions', {
        effectiveDate: request.effectiveDate,
      }),
      reserveRequirementQueries('/getReserveRequirementsByCode', {
        code: request.code,
      }),
    ],
  },
)
export type ReserveRequirementMutation =
  | typeof createReserveRequirement
  | typeof updateReserveRequirement

export const reserveRequirementMutation = defineMutations(
  [createReserveRequirement, updateReserveRequirement],
  compose(notifications, withMetrics()),
)
