'use client'
import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useQuery } from '@tanstack/react-query'
import { z } from 'zod'
import { ColumnDef, ColumnFiltersState } from '@tanstack/react-table'
import { ChevronRightIcon } from '@heroicons/react/24/outline'

import { Button } from '@/components/Button'
import { MonthPicker } from '@/components/Filter/MonthPicker'
import { SearchBar } from '@/components/SearchBar'
import { SortedTable } from '@/components/Table/SortedTable'
import { RowPopover } from '../../_components/RowPopover'

import { unwrap } from '@/lib/unions/unwrap'
import { routeTo, useRoute } from '../../routing'
import { apiToFormSchemas } from '@/api/apiToFormSchema'
import { reserveRequirementQueries } from '../queries'
import { formatToMonthYearFromDate, formatToServerString } from '@/lib/date'

export default function AllReserveRequirement() {
  const [searchText, setSearchText] = useState('')
  const [numVisibleReserveRequirements, setNumVisibleReserveRequirements] =
    useState(0)
  const columnFilters: ColumnFiltersState = [{ id: 'name', value: searchText }]

  const router = useRouter()
  const route = unwrap(
    useRoute(),
    '/configuration/reserve-requirements/[effectiveDate]',
  )!

  const payload = {
    effectiveDate: formatToServerString(route.params.effectiveDate),
  }
  const { data, status, error } = useQuery(
    reserveRequirementQueries('/getReserveRequirementDefinitions', payload),
  )
  if (status === 'pending') return 'Loading...'
  if (!data || error) {
    return <div>404 Not Found</div>
  }

  const { reserveRequirementDefinition: ReserveRequirementType } =
    apiToFormSchemas
  const reserveRequirements = data?.map((item) => {
    return ReserveRequirementType.parse(item)
  })

  const handleAddAccountTypeClick = () => {
    router.push(
      routeTo(
        '/configuration/reserve-requirements/[effectiveDate]/add',
        route.params,
      ),
    )
  }

  const columns: ColumnDef<z.infer<typeof ReserveRequirementType>>[] = [
    {
      header: 'Name',
      accessorKey: 'name',
    },
    {
      header: 'Code',
      accessorKey: 'code',
    },
    {
      header: '',
      accessorKey: 'none',
      cell: ({
        row: {
          original: { code },
        },
      }) => (
        <RowPopover
          className='pr-4'
          onEdit={() => {
            router.push(
              routeTo(
                '/configuration/reserve-requirements/[effectiveDate]/edit/[reserveRequirementCode]',
                {
                  ...route.params,
                  effectiveDate: formatToMonthYearFromDate(
                    route.params.effectiveDate,
                  ),
                  reserveRequirementCode: code,
                },
              ),
            )
          }}
        />
      ),
      meta: { className: 'basis-28 shrink-0 justify-end px-2' },
    },
  ]

  return (
    <div className='flex min-h-0 flex-1 flex-col py-6 pl-12 pr-3'>
      <div className='flex items-center text-zinc-500'>
        Configurations
        <ChevronRightIcon className='h-4 w-4 text-zinc-400' />
        <span className='text-lg font-semibold text-black'>
          Reserve requirements
        </span>
      </div>
      <div className='mt-12 flex min-h-0 w-full flex-auto flex-grow flex-col rounded-lg border bg-white p-6'>
        <div className='flex w-full items-center justify-between'>
          <div className=''>
            <div className='text-lg font-semibold'>Reserve requirements</div>
            <div className='text-zinc-400'>
              Add and view reserve requirement details
            </div>
          </div>
          <Button className='btn' onClick={handleAddAccountTypeClick}>
            Add reserve requirement
          </Button>
        </div>
        <div className='mt-8 flex min-h-0 flex-auto flex-col'>
          <div className='flex items-center'>
            <MonthPicker
              title='Effective date'
              prefix='View data effective on'
              showIcon={true}
              onDateChange={(effectiveDate) => {
                router.push(
                  routeTo(
                    '/configuration/reserve-requirements/[effectiveDate]',
                    { ...route.params, effectiveDate },
                  ),
                )
              }}
              initialDate={route.params.effectiveDate}
            />
            <div className='flex flex-auto items-center justify-end gap-4'>
              <SearchBar
                className='w-80'
                defaultLabel='Search reserve requirements'
                value={searchText}
                onValueChange={setSearchText}
              />
              <p className='text-sm text-zinc-400'>
                {numVisibleReserveRequirements} reserve requirement
                {numVisibleReserveRequirements > 1 && 's'}
              </p>
            </div>
          </div>
          <div className='mt-4 flex min-h-0 flex-col'>
            <SortedTable
              data={reserveRequirements}
              columns={columns}
              columnFilters={columnFilters}
              onVisibleRowsChanged={(rows) =>
                setNumVisibleReserveRequirements(rows.length)
              }
              initialSortingState={[{ desc: false, id: 'name' }]}
              handleRowDoubleClick={({ original: { code } }) =>
                router.push(
                  routeTo(
                    '/configuration/reserve-requirements/[effectiveDate]/view/[reserveRequirementCode]',
                    {
                      ...route.params,
                      effectiveDate: formatToMonthYearFromDate(
                        route.params.effectiveDate,
                      ),
                      reserveRequirementCode: code,
                    },
                  ),
                )
              }
            />
          </div>
        </div>
      </div>
    </div>
  )
}
