'use client'

import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { useQuery } from '@tanstack/react-query'
import { ChevronRightIcon } from '@heroicons/react/24/outline'
import Decimal from 'decimal.js'

import { Button } from '@/components/Button'
import {
  DetailsSection,
  DetailsSectionTitle,
  DetailsSectionItemsRow,
  DetailsSectionItem,
} from '@/components/DetailsSection'
import { VersionTimeline } from '@/app/[bankId]/configuration/_components/VersionTimeline'
import { If } from '@/components/If'

import { unwrap } from '@/lib/unions/unwrap'
import { routeTo, useRoute } from '@/app/[bankId]/configuration/routing'
import { apiToFormSchemas } from '@/api/apiToFormSchema'
import { reserveRequirementQueries } from '../../../queries'
import { indexRateQueries } from '@/app/[bankId]/configuration/index-rates/queries'
import {
  formatToMonthYearFromDate,
  formatToServerString,
  getLastDayOfMonthString,
} from '@/lib/date'

export default function ViewReserveRequirement() {
  const router = useRouter()
  const route = unwrap(
    useRoute(),
    '/configuration/reserve-requirements/[effectiveDate]/view/[reserveRequirementCode]',
  )!
  const effectiveDate = route.params.effectiveDate

  const payload = {
    code: route.params.reserveRequirementCode,
  }

  const { data, status, error } = useQuery(
    reserveRequirementQueries('/getReserveRequirementsByCode', payload),
  )

  const { data: indexRates } = useQuery(
    indexRateQueries('/listIndexRatesByEffectiveDate', {
      effectiveDate: getLastDayOfMonthString(effectiveDate),
    }),
  )
  if (status === 'pending') return 'Loading...'
  if (!data || error) {
    return <div>404 Not Found</div>
  }

  const {
    reserveRequirementDefinition: ReserveRequirementType,
    indexRate: IndexRateSchema,
  } = apiToFormSchemas

  const reserveRequirements = data
    .map((item) => {
      const parsed = ReserveRequirementType.parse(item)
      return {
        ...parsed,
        effectiveDate: formatToMonthYearFromDate(parsed.effectiveDate),
      }
    })
    .sort(
      (a, b) =>
        new Date(b.effectiveDate).getDate() -
        new Date(a.effectiveDate).getDate(),
    )

  const reserveRequirement =
    reserveRequirements.find((item) => {
      return item.effectiveDate === effectiveDate
    }) || reserveRequirements[0]

  const { name: indexRateName, indexRate: indexRateRate } = indexRates?.find(
    (item) => item.code === reserveRequirement.indexRateCode,
  ) ?? { name: '', indexRate: 0 }

  const indexRateFloat = new Decimal(indexRateRate)
  const indexAdjustmentFloat = new Decimal(
    reserveRequirement?.indexAdjustment || 0,
  )
  const calculatedEffectiveRate = indexRateFloat.add(indexAdjustmentFloat)

  const onEditClick = () => {
    router.push(
      routeTo(
        '/configuration/reserve-requirements/[effectiveDate]/edit/[reserveRequirementCode]',
        {
          ...route.params,
          effectiveDate: formatToMonthYearFromDate(
            reserveRequirement.effectiveDate,
          ),
          reserveRequirementCode: reserveRequirement.code,
        },
      ),
    )
  }

  const linkFormatter = (
    effectiveDate: string,
    reserveRequirementCode: string,
  ) => {
    return routeTo(
      '/configuration/reserve-requirements/[effectiveDate]/view/[reserveRequirementCode]',
      {
        ...route.params,
        effectiveDate: formatToMonthYearFromDate(effectiveDate),
        reserveRequirementCode,
      },
    )
  }

  return (
    <div className='w-full overflow-hidden overflow-y-scroll px-8 py-10'>
      <div className='flex items-center justify-between'>
        <div className='flex items-center gap-2'>
          <Link
            href={routeTo(
              '/configuration/reserve-requirements/[effectiveDate]',
              route.params,
            )}
            className='font-bold'
          >
            ...
          </Link>
          <ChevronRightIcon className='h-4 w-4 text-zinc-400' />
          <div className='text-md text-zinc-500'>
            <Link
              href={routeTo(
                '/configuration/reserve-requirements/[effectiveDate]',
                route.params,
              )}
              className='font-bold'
            >
              Reserve requirements
            </Link>
          </div>
          <ChevronRightIcon className='h-4 w-4 text-zinc-400' />
          <div className='text-lg'>{reserveRequirement.name}</div>
          <div className='text-sm text-zinc-500'>{reserveRequirement.code}</div>
        </div>
        <Button className='btn-primary text-white' onClick={onEditClick}>
          Edit
        </Button>
      </div>
      <div className='mt-8 flex gap-4'>
        <div className='flex grow flex-col'>
          <DetailsSection>
            <DetailsSectionTitle>
              Reserve requirement information
            </DetailsSectionTitle>
            <DetailsSectionItemsRow>
              <DetailsSectionItem
                label={'Effective date *'}
                info={reserveRequirement.effectiveDate}
              />
            </DetailsSectionItemsRow>
            <DetailsSectionItemsRow>
              <DetailsSectionItem
                label={'Name *'}
                info={reserveRequirement.name}
              />
              <DetailsSectionItem
                label={'Code *'}
                info={reserveRequirement.code}
              />
            </DetailsSectionItemsRow>
          </DetailsSection>
          <DetailsSection className='mt-3'>
            <DetailsSectionTitle>Reserve requirement</DetailsSectionTitle>
            <DetailsSectionItemsRow>
              <DetailsSectionItem
                label={'Base balance *'}
                info={reserveRequirement.baseBalanceType}
              />
            </DetailsSectionItemsRow>
          </DetailsSection>
          <DetailsSection className='mt-3'>
            <DetailsSectionTitle>Calculation method</DetailsSectionTitle>
            <DetailsSectionItemsRow>
              <DetailsSectionItem
                label={'Calculation method *'}
                info={reserveRequirement.calculationMethodType}
              />
            </DetailsSectionItemsRow>
            <If true={reserveRequirement.calculationMethodType === 'INDEXED'}>
              <DetailsSectionItemsRow>
                <DetailsSectionItem
                  label={'Index rate *'}
                  info={indexRateName || '--'}
                />
              </DetailsSectionItemsRow>
              <DetailsSectionItemsRow>
                <DetailsSectionItem
                  label={'Index adjustment'}
                  info={
                    reserveRequirement.indexAdjustment ?
                      Number(reserveRequirement.indexAdjustment).toString()
                    : '--'
                  }
                />
                <DetailsSectionItem
                  label={'Effective rate'}
                  info={calculatedEffectiveRate.toString()}
                />
              </DetailsSectionItemsRow>
              <DetailsSectionItemsRow>
                <DetailsSectionItem
                  label={'Floor'}
                  info={
                    reserveRequirement.floor ?
                      Number(reserveRequirement.floor).toString()
                    : '--'
                  }
                />
                <DetailsSectionItem
                  label={'Ceiling'}
                  info={
                    reserveRequirement.ceiling ?
                      Number(reserveRequirement.ceiling).toString()
                    : '--'
                  }
                />
              </DetailsSectionItemsRow>
            </If>
            <If
              true={reserveRequirement.calculationMethodType === 'PERCENTAGE'}
            >
              <DetailsSectionItemsRow>
                <DetailsSectionItem
                  label={'Reserve rate *'}
                  info={
                    reserveRequirement.reserveRate ?
                      Number(reserveRequirement.reserveRate).toString()
                    : '--'
                  }
                />
              </DetailsSectionItemsRow>
            </If>
          </DetailsSection>
        </div>
        <div className='flex max-w-72 flex-col rounded-lg border bg-white p-6'>
          <VersionTimeline
            versions={reserveRequirements}
            currentEffectiveDate={route.params.effectiveDate}
            linkFormatter={linkFormatter}
          />
        </div>
      </div>
    </div>
  )
}
