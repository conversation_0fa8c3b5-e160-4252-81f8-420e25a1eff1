'use client'

import { useRouter } from 'next/navigation'
import { useQuery, useMutation } from '@tanstack/react-query'

import { Button } from '@/components/Button'
import UpdateReserveRequirementForm from '../../../_components/UpdateReserveRequirementForm'
import { UpdateReserveRequirementFormState } from '../../../_components/UpdateReserveRequirementFormTypes'

import { unwrap } from '@/lib/unions/unwrap'
import { routeTo, useRoute } from '@/app/[bankId]/configuration/routing'
import { reserveRequirementQueries } from '../../../queries'
import { reserveRequirementMutation } from '../../../mutations'
import { formToApiSchemas } from '@/api/formToApiSchema'
import { formatToServerString, formatToMonthYearFromDate } from '@/lib/date'
import { apiToFormSchemas } from '@/api/apiToFormSchema'

export default function EditReserveRequirement() {
  const router = useRouter()
  const route = unwrap(
    useRoute(),
    '/configuration/reserve-requirements/[effectiveDate]/edit/[reserveRequirementCode]',
  )!

  const effectiveDate = route.params.effectiveDate

  const payload = {
    code: route.params.reserveRequirementCode,
  }
  const { data, status, error } = useQuery(
    reserveRequirementQueries('/getReserveRequirementsByCode', payload),
  )
  const updateReserveRequirement = useMutation(
    reserveRequirementMutation('/updateReserveRequirementDefinition', {
      onSuccess: (_, request) => {
        router.push(
          routeTo(
            '/configuration/reserve-requirements/[effectiveDate]/view/[reserveRequirementCode]',
            {
              ...route.params,
              effectiveDate: formatToMonthYearFromDate(request.effectiveDate!),
              reserveRequirementCode: request.code!,
            },
          ),
        )
      },
    }),
  )
  if (status === 'pending') return 'Loading...'
  if (!data || error) {
    return <div>404 Not Found</div>
  }

  const { reserveRequirementDefinition } = apiToFormSchemas
  const reserveRequirements = data
    .map((item) => {
      const parsed = reserveRequirementDefinition.parse(item)
      return {
        ...parsed,
        effectiveDate: formatToMonthYearFromDate(parsed.effectiveDate),
        indexAdjustment:
          parsed.indexAdjustment ? parseFloat(parsed.indexAdjustment) : null,
        floor: parsed.floor ? parseFloat(parsed.floor) : null,
        ceiling: parsed.ceiling ? parseFloat(parsed.ceiling) : null,
        reserveRate: parsed.reserveRate ? parseFloat(parsed.reserveRate) : null,
      }
    })
    .sort(
      (a, b) =>
        new Date(b.effectiveDate).getDate() -
        new Date(a.effectiveDate).getDate(),
    )

  const reserveRequirement =
    reserveRequirements.find((item) => {
      return item.effectiveDate === effectiveDate
    }) || reserveRequirements[0]

  const defaultValues: UpdateReserveRequirementFormState = {
    reserveRequirementInfo: {
      code: reserveRequirement.code,
      name: reserveRequirement.name,
      effectiveDate: reserveRequirement.effectiveDate,
    },
    reserveRequirement: {
      baseBalanceType: reserveRequirement.baseBalanceType,
    },
    calculationMethod: {
      calculationMethodType: reserveRequirement.calculationMethodType,
      indexRateCode: reserveRequirement.indexRateCode,
      indexAdjustment: reserveRequirement.indexAdjustment,
      floor: reserveRequirement.floor,
      ceiling: reserveRequirement.ceiling,
      reserveRate: reserveRequirement.reserveRate,
    },
  }

  const serializeForm = (formState: UpdateReserveRequirementFormState) => {
    const { reserveRequirementDefinition } = formToApiSchemas
    const { reserveRequirementInfo, reserveRequirement, calculationMethod } =
      formState
    if (calculationMethod.calculationMethodType === 'INDEXED') {
      return reserveRequirementDefinition.parse({
        ...reserveRequirementInfo,
        effectiveDate: formatToServerString(
          reserveRequirementInfo.effectiveDate,
        ),
        ...reserveRequirement,
        calculationMethodType: calculationMethod.calculationMethodType,
        indexRateCode: calculationMethod.indexRateCode,
        indexAdjustment: calculationMethod.indexAdjustment?.toString() ?? null,
        floor: calculationMethod.floor?.toString() ?? null,
        ceiling: calculationMethod.ceiling?.toString() ?? null,
        reserveRate: null,
      })
    } else {
      return reserveRequirementDefinition.parse({
        ...reserveRequirementInfo,
        effectiveDate: formatToServerString(
          reserveRequirementInfo.effectiveDate,
        ),
        ...reserveRequirement,
        calculationMethodType: calculationMethod.calculationMethodType,
        reserveRate: calculationMethod.reserveRate?.toString() ?? null,
        indexRateCode: null,
        indexAdjustment: null,
        floor: null,
        ceiling: null,
      })
    }
  }
  return (
    <div className='w-full overflow-hidden overflow-y-scroll pt-12'>
      <div className='text-md ml-8 font-medium'>Edit reserve requirement</div>
      <UpdateReserveRequirementForm
        defaultValues={defaultValues}
        onSubmit={(formState) => {
          updateReserveRequirement.mutate(serializeForm(formState))
        }}
        isEditMode
      >
        <Button className='btn w-60' onClick={() => router.back()}>
          Cancel
        </Button>

        <Button type='submit' className='btn-primary w-60'>
          Save
        </Button>
      </UpdateReserveRequirementForm>
    </div>
  )
}
