'use client'

import { useRouter } from 'next/navigation'
import { useMutation } from '@tanstack/react-query'

import { Button } from '@/components/Button'
import UpdateReserveRequirementForm from '../../_components/UpdateReserveRequirementForm'
import { UpdateReserveRequirementFormState } from '../../_components/UpdateReserveRequirementFormTypes'

import { formatToMonthYearFromDate, formatToServerString } from '@/lib/date'
import { unwrap } from '@/lib/unions/unwrap'
import { routeTo, useRoute } from '../../../routing'
import { reserveRequirementMutation } from '../../mutations'
import { formToApiSchemas } from '@/api/formToApiSchema'

export default function AddReserveRequirement() {
  const router = useRouter()
  const route = unwrap(
    useRoute(),
    '/configuration/reserve-requirements/[effectiveDate]/add',
  )!
  const defaultValues: UpdateReserveRequirementFormState = {
    reserveRequirementInfo: {
      code: '',
      name: '',
      effectiveDate: formatToMonthYearFromDate(new Date()),
    },
    reserveRequirement: {
      baseBalanceType: null,
    },
    calculationMethod: {
      calculationMethodType: null,
      indexRateCode: null,
      indexAdjustment: null,
      floor: null,
      ceiling: null,
      reserveRate: null,
    },
  }

  const addReserveRequirement = useMutation(
    reserveRequirementMutation('/createReserveRequirementDefinition', {
      onSuccess: (_, request) => {
        router.push(
          routeTo(
            '/configuration/reserve-requirements/[effectiveDate]/view/[reserveRequirementCode]',
            {
              ...route.params,
              effectiveDate: formatToMonthYearFromDate(request.effectiveDate!),
              reserveRequirementCode: request.code!,
            },
          ),
        )
      },
    }),
  )
  const serializeForm = (formState: UpdateReserveRequirementFormState) => {
    const { reserveRequirementDefinition } = formToApiSchemas
    const { reserveRequirementInfo, reserveRequirement, calculationMethod } =
      formState
    if (calculationMethod.calculationMethodType === 'INDEXED') {
      return reserveRequirementDefinition.parse({
        ...reserveRequirementInfo,
        effectiveDate: formatToServerString(
          reserveRequirementInfo.effectiveDate,
        ),
        ...reserveRequirement,
        calculationMethodType: calculationMethod.calculationMethodType,
        indexRateCode: calculationMethod.indexRateCode,
        indexAdjustment: calculationMethod.indexAdjustment,
        floor: calculationMethod.floor ?? null,
        ceiling: calculationMethod.ceiling ?? null,
        reserveRate: null,
      })
    } else {
      return reserveRequirementDefinition.parse({
        ...reserveRequirementInfo,
        effectiveDate: formatToServerString(
          reserveRequirementInfo.effectiveDate,
        ),
        ...reserveRequirement,
        calculationMethodType: calculationMethod.calculationMethodType,
        reserveRate: calculationMethod.reserveRate,
        indexRateCode: null,
        indexAdjustment: null,
        floor: null,
        ceiling: null,
      })
    }
  }
  return (
    <div className='w-full overflow-hidden overflow-y-scroll pt-12'>
      <div className='text-md ml-8 font-medium'>Add reserve requirement</div>
      <UpdateReserveRequirementForm
        defaultValues={defaultValues}
        onSubmit={(formState) => {
          addReserveRequirement.mutate(serializeForm(formState))
        }}
      >
        <Button className='btn w-60' onClick={() => router.back()}>
          Cancel
        </Button>

        <Button type='submit' className='btn-primary w-60'>
          Save
        </Button>
      </UpdateReserveRequirementForm>
    </div>
  )
}
