'use client'
import { But<PERSON> } from '@/components/Button'
import { fetchImpl } from '@/lib/http/fetchImpl'
import { useMutation } from '@tanstack/react-query'

const fetchSeed = async () => {
  return await fetchImpl(
    new Request(`${process.env.NEXT_PUBLIC_RC_API_URL ?? ''}/seedInitialData`, {
      credentials: 'include',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({}),
      method: 'POST',
    }),
  )
}

export default function Page() {
  const seedMutation = useMutation({
    mutationFn: fetchSeed,
  })
  return (
    <div className='flex h-full w-full flex-col items-center justify-center'>
      {seedMutation.isPending ?
        <Button
          disabled
          onClick={() => seedMutation.mutate()}
          className='btn-alert w-[150px] text-white'
        >
          Seeding db
        </Button>
      : <>
          {(
            seedMutation.isError &&
            seedMutation.error.message ===
              'The response body from `http://localhost/seedInitialData` could not be parsed as JSON.'
          ) ?
            <div>DB Seeded</div>
          : null}

          <Button
            onClick={() => seedMutation.mutate()}
            className='btn-alert w-[150px] text-white'
          >
            Seed
          </Button>
        </>
      }
    </div>
  )
}
