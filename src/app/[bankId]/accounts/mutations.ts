import { defineMutation, defineMutations } from '@/lib/state/defineMutation'
import { withLogOnError } from '@/lib/middleware/withLogOnError'
import { withMetrics } from '@/lib/middleware/withMetrics'
import { compose } from '@/lib/functional/compose'
import { accountQueries } from './queries'
import { accountToAccountCode, toAccountCodeString } from './accountHelpers'
import { notifications } from './notifications'
import { revenueConnectClient } from '@/api/revenueConnectClient'
import { applicationId } from '@/api/zodSchemas'

// Account mutations

const createCompositeAccount = defineMutation(
  revenueConnectClient,
  '/createCompositeAccount',
  {
    invalidate: (request) => [
      accountQueries('/getAccounts', {
        effectiveDate: request.account.effectiveDate,
      }),
      accountQueries('/getParentlessOpenAccounts', {
        effectiveDate: request.account.effectiveDate,
      }),
      accountQueries('/getNextAvailableAccountNumber', {
        effectiveDate: request.account.effectiveDate,
      }),
      accountQueries('/getAddresses', {
        applicationId: request.account.applicationId,
        accountNumber: request.account.accountNumber,
        effectiveDate: request.account.effectiveDate,
        bankNumber: request.account.bankNumber,
      }),
      accountQueries('/listAllKeyAccountMappings'),
      accountQueries(
        '/getAccountTimeline',
        accountToAccountCode(request.account),
      ),
    ],
  },
)

const updateAccount = defineMutation(revenueConnectClient, '/updateAccount', {
  invalidate: (request) => [
    accountQueries('/getAccount', {
      applicationId: request.applicationId,
      accountNumber: request.accountNumber,
      effectiveDate: request.effectiveDate,
      bankNumber: request.bankNumber,
    }),
    accountQueries('/getAccounts', {
      effectiveDate: request.effectiveDate,
    }),
    accountQueries('/getParentlessOpenAccounts', {
      effectiveDate: request.effectiveDate,
    }),
    accountQueries('/getAccountMappings', {
      code: toAccountCodeString(request),
      effectiveDate: request.effectiveDate,
    }),
    accountQueries('/getAccountTimeline', accountToAccountCode(request)),
  ],
})

const upsertKeyAccountMapping = defineMutation(
  revenueConnectClient,
  '/upsertKeyAccountMapping',
  {
    invalidate: [accountQueries('/listAllKeyAccountMappings')],
  },
)

const removeKeyAccountMapping = defineMutation(
  revenueConnectClient,
  '/removeKeyAccountMapping',
  {
    invalidate: [accountQueries('/listAllKeyAccountMappings')],
  },
)

// invalidation happens within provider due to not having scope of page visited within just invalidation response
const addMapping = defineMutation(revenueConnectClient, '/addAccountMapping', {
  retry: 0,
})

const removeMapping = defineMutation(
  revenueConnectClient,
  '/removeAccountMappings',
  {
    retry: 0,
  },
)

const updateAccountMapping = defineMutation(
  revenueConnectClient,
  '/updateAccountMappings',
  {
    retry: 0,
  },
)

const setKeyAccount = defineMutation(revenueConnectClient, '/setKeyAccount', {
  retry: 0,
})

const removeKeyAccount = defineMutation(
  revenueConnectClient,
  '/removeKeyAccount',
  {
    retry: 0,
  },
)

const updateSettlementProcessingOptions = defineMutation(
  revenueConnectClient,
  '/updateSettlementProcessingOptions',
  {
    retry: 0,
    invalidate: (request) => [
      accountQueries('/getAccountTypeOverride', {
        accountNumber: request.accountCode.accountNumber,
        applicationId: request.accountCode.applicationId,
        bankNumber: request.accountCode.bankNumber,
        effectiveDate: request.effectiveDate,
      }),
      accountQueries('/getSettlementProcessingOptionsTimeline', {
        accountNumber: request.accountCode.accountNumber,
        applicationId: request.accountCode.applicationId,
        bankNumber: request.accountCode.bankNumber,
      }),
    ],
  },
)

const createAddress = defineMutation(revenueConnectClient, '/createAddress', {
  retry: 0,
  invalidate: (request) => [
    accountQueries('/getAddresses', {
      accountNumber: request.accountCode.accountNumber,
      applicationId: request.accountCode.applicationId,
      bankNumber: request.accountCode.bankNumber,
      effectiveDate: request.effectiveDate,
    }),
  ],
})

export type AccountMutation =
  | typeof createCompositeAccount
  | typeof updateAccount
  | typeof upsertKeyAccountMapping
  | typeof removeKeyAccountMapping
  | typeof addMapping
  | typeof removeMapping
  | typeof updateAccountMapping
  | typeof setKeyAccount
  | typeof removeKeyAccount
  | typeof updateSettlementProcessingOptions
  | typeof createAddress

export const accountMutation = defineMutations(
  [
    createCompositeAccount,
    updateAccount,
    upsertKeyAccountMapping,
    removeKeyAccountMapping,
    addMapping,
    removeMapping,
    updateAccountMapping,
    setKeyAccount,
    removeKeyAccount,
    updateSettlementProcessingOptions,
    createAddress,
  ],
  compose(notifications, withMetrics(), withLogOnError()),
)

// User fields mutations

const createUserFieldSelections = defineMutation(
  revenueConnectClient,
  '/createUserFieldSelections',
  {
    invalidate: (request) => [
      accountQueries('/getUserFieldSelections', {
        effectiveDate: request[0].effectiveDate!,
        applicationId: request[0].applicationId!,
        accountNumber: request[0].accountNumber!,
        bankNumber: request[0].bankNumber!,
      }),
    ],
  },
)

export type UserFieldsMutation = typeof createUserFieldSelections

export const userFieldsMutation = defineMutations(
  [createUserFieldSelections],
  compose(withMetrics(), withLogOnError()),
)
