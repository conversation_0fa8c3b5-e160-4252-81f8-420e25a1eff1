'use client'

import {
  InfoSection,
  InfoSectionDescription,
  InfoSectionTitle,
} from '@/components/InfoSection'
import { data } from '@/lib/unions/Union'
import { useAccountFromParams } from '@/app/[bankId]/accounts/_hooks/useAccountFromParams'
import { useRoute } from '../routing'

export default function Page() {
  const route = useRoute()!
  const routeParams = data(route).params
  const accountFromParams = useAccountFromParams(routeParams)
  const account = accountFromParams.account!

  return (
    <div>
      <InfoSection>
        <div className='flex items-center justify-between pb-4'>
          <div>
            <InfoSectionTitle>Add Pricing Overrides</InfoSectionTitle>
            <InfoSectionDescription>
              {`${account.shortName} ${account.applicationId}-${account.accountNumber}`}
            </InfoSectionDescription>
          </div>
        </div>
      </InfoSection>
    </div>
  )
}
