'use client'

import { useQuery } from '@tanstack/react-query'
import { data } from '@/lib/unions/Union'
import { useRoute } from '../routing'
import { useRouter } from 'next/navigation'
import { useAccountFromParams } from '@/app/[bankId]/accounts/_hooks/useAccountFromParams'
import { accountQueries } from '@/app/[bankId]/accounts/queries'
import { StatementPackageFormSchema } from '@/app/[bankId]/accounts/types'
import { listSelector } from '@/api/selectors'
import { apiToFormSchemas } from '@/api/apiToFormSchema'
import { useMemo, useState, useEffect, useCallback, useRef } from 'react'
import { AddressForm } from '@/api/formToApiSchema'
import {
  apiMappingWithKeyListSelector,
  mappingsToAccountsMap,
  toAccountCodeString,
} from '@/app/[bankId]/accounts/accountHelpers'
import {
  StatementPackageModal,
  StatementPackageModalInstanceProps,
} from '@/app/[bankId]/accounts/_components/StatementPackageModal'
import { useExternalModalStateAndProps } from '@/components/Modal'

export default function AddStatementPackagePage() {
  const route = useRoute()!
  const routeParams = data(route).params
  const { account, accountCode, effectiveDate } =
    useAccountFromParams(routeParams)
  const router = useRouter()

  const {
    data: statementPackages,
    isLoading,
    isError,
    error,
  } = useQuery({
    ...accountQueries('/listStatementPackages', {
      accountNumber: accountCode.accountNumber,
      applicationId: accountCode.applicationId,
      bankNumber: accountCode.bankNumber,
      effectiveDate,
    }),
    select: listSelector(apiToFormSchemas.hydratedStatementPackage),
  })

  const { data: addresses, isLoading: isLoadingAddresses } = useQuery({
    ...accountQueries('/getAddresses', {
      accountNumber: accountCode.accountNumber,
      applicationId: accountCode.applicationId,
      bankNumber: accountCode.bankNumber,
      effectiveDate,
    }),
    select: listSelector(apiToFormSchemas.address),
  })

  const existingStatementPackageNumbers = useMemo(
    () =>
      statementPackages?.map(
        (pkg) => pkg.statementPackage.statementPackageNumber,
      ) || [],
    [statementPackages],
  )

  const [accountAddresses, setAccountAddresses] = useState<AddressForm[]>([])

  useEffect(() => {
    if (addresses) {
      setAccountAddresses(addresses)
    }
  }, [addresses])

  const handleSave = useCallback(
    (_value: StatementPackageFormSchema) => {
      router.push(
        `/${routeParams.bankId}/accounts/${routeParams.effectiveMonth}/view/${routeParams.accountCode}/statement-package`,
      )
    },
    [
      router,
      routeParams.bankId,
      routeParams.effectiveMonth,
      routeParams.accountCode,
    ],
  )

  const handleCancel = useCallback(() => {
    router.push(
      `/${routeParams.bankId}/accounts/${routeParams.effectiveMonth}/view/${routeParams.accountCode}/statement-package`,
    )
  }, [
    router,
    routeParams.bankId,
    routeParams.effectiveMonth,
    routeParams.accountCode,
  ])
  const [modalState, modalInstanceProps, setModalInstanceProps] =
    useExternalModalStateAndProps<StatementPackageModalInstanceProps>({
      onClose: handleCancel,
    })

  const modalInitialized = useRef(false)
  useEffect(() => {
    if (
      !isLoading &&
      !isLoadingAddresses &&
      addresses &&
      account &&
      !modalInitialized.current
    ) {
      setModalInstanceProps({
        existingStatementPackageNumbers,
        onSave: handleSave,
      })
      modalState.show()
      modalInitialized.current = true
    }
  }, [
    isLoading,
    isLoadingAddresses,
    addresses,
    account,
    existingStatementPackageNumbers,
    handleSave,
    setModalInstanceProps,
    modalState,
  ])

  // Fetch account mappings to get subAccounts
  const {
    data: accountMappingData,
    isLoading: mappingsLoading,
  } = useQuery({
    ...accountQueries('/getAccountMappings', {
      code: toAccountCodeString(accountCode),
      effectiveDate,
    }),
    select: apiMappingWithKeyListSelector,
    enabled: !!account,
  })

  // Create subAccounts from mappings
  const subAccounts = useMemo(() => {
    if (!accountMappingData || !account) return []

    const accountsMap = mappingsToAccountsMap([
      {
        child: account,
        parent: null,
      },
      ...accountMappingData,
    ])

    const accountCodeString = toAccountCodeString(accountCode)
    const accountWithChildren = accountsMap[accountCodeString]

    return accountWithChildren?.children || []
  }, [accountMappingData, account, accountCode])

  if (isLoading || isLoadingAddresses || mappingsLoading) {
    return (
      <div className='flex min-h-screen items-center justify-center'>
        <div>Loading...</div>
      </div>
    )
  }

  if (isError) {
    return (
      <div className='flex min-h-screen items-center justify-center'>
        <div className='text-red-600'>Error: {error?.message}</div>
      </div>
    )
  }

  if (!addresses || !account) {
    return (
      <div className='flex min-h-screen items-center justify-center'>
        <div>Data not found</div>
      </div>
    )
  }

  return (
    <div className='min-h-screen'>
      {modalInstanceProps && (
        <StatementPackageModal
          modalState={modalState}
          {...modalInstanceProps}
          accountCode={accountCode}
          shortName={account.shortName}
          accountAddresses={accountAddresses}
          setAccountAddresses={setAccountAddresses}
          keyAccountAddresses={[]}
          subAccounts={subAccounts}
        />
      )}
    </div>
  )
}
