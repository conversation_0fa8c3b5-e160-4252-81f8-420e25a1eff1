'use client'

import { data } from '@/lib/unions/Union'
import { useRoute } from './routing'
import { useAccountFromParams } from '../../../_hooks/useAccountFromParams'

export default function CreateAccountLayout({
  children,
}: React.PropsWithChildren) {
  const route = useRoute()!
  const routeParams = data(route).params
  const account = useAccountFromParams(routeParams).account

  if (!account) return null

  return (
    <div className='flex flex-auto flex-col gap-3 overflow-y-scroll pt-8'>
      Create page
      <div className='mt-6 h-full'>{children}</div>
    </div>
  )
}
