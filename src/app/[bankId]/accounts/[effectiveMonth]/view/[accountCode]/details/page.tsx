'use client'

import { useAccountFromParams } from '@/app/[bankId]/accounts/_hooks/useAccountFromParams'
import { ViewAccountInformation } from './ViewAccountInformation'
import { routeTo, useRoute } from '../routing'
import { data } from '@/lib/unions/Union'
import { ViewDemographicsPricing } from './ViewDemographicsAndPricing'
import { accountQueries } from '@/app/[bankId]/accounts/queries'
import { useQuery } from '@tanstack/react-query'
import { useCallback, useMemo } from 'react'
import { formatAccountCodeForRoute } from '@/app/[bankId]/accounts/accountHelpers'
import { VersionTimeline } from '@/app/[bankId]/configuration/_components/VersionTimeline'
import { formatToMonthYearFromDate } from '@/lib/date'

export default function Page() {
  const route = useRoute()!
  const routeParams = data(route).params
  const bankId = routeParams.bankId
  const { account, keyAccount, effectiveDate, accountCode } =
    useAccountFromParams(routeParams)
  const currentMonth = useMemo(() => formatToMonthYearFromDate(new Date()), [])

  const { data: uniqueDates } = useQuery(
    accountQueries('/getAccountTimeline', accountCode),
  )

  const linkFormatter = useCallback(
    (effectiveMonth: string, accountCode: string) => {
      return routeTo('/accounts/[effectiveMonth]/view/[accountCode]/details', {
        bankId,
        effectiveMonth,
        accountCode,
      })
    },
    [bankId],
  )

  const versions = useMemo(
    () =>
      uniqueDates
        ?.reduce((uniqueMonths, date) => {
          if (uniqueMonths.indexOf(date.substring(0, 7)) === -1)
            uniqueMonths.push(date.substring(0, 7))
          return uniqueMonths
        }, [] as string[])
        .toSorted((a, b) => (a < b ? 1 : -1))
        .map((uniqueMonth, index, array) => ({
          code: formatAccountCodeForRoute(accountCode),
          effectiveDate: uniqueMonth,
          isExpired: index > 0 && array[index - 1] <= currentMonth,
        })),
    [uniqueDates, accountCode, currentMonth],
  )

  if (!account) return null

  return (
    <div className='flex gap-4'>
      <div className='flex grow flex-col gap-4'>
        <ViewAccountInformation account={account} />
        <ViewDemographicsPricing
          account={account}
          keyAccount={keyAccount}
          effectiveDate={effectiveDate}
        />
      </div>
      <div className='flex w-72 flex-col rounded-lg border bg-white p-6'>
        {versions && (
          <VersionTimeline
            versions={versions}
            currentEffectiveDate={routeParams.effectiveMonth}
            linkFormatter={linkFormatter}
          />
        )}
      </div>
    </div>
  )
}
