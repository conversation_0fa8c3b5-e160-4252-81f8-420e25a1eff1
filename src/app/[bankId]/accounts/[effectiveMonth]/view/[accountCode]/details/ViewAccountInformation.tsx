import { formatAccountCode } from '@/app/[bankId]/accounts/accountHelpers'
import { Account, AccountStatusLabels } from '@/app/[bankId]/accounts/types'
import { Button } from '@/components/Button'
import {
  DetailsSectionItem,
  DetailsSectionItemsRow,
} from '@/components/DetailsSection'
import { formatToMonthYearFromDate } from '@/lib/date'
import { useCallback } from 'react'
import { useRouter } from 'next/navigation'
import { routeTo } from '../../../edit/[accountCode]/routing'
import { data } from '@/lib/unions/Union'
import { useRoute } from '../routing'
import { InfoSection, InfoSectionTitle } from '@/components/InfoSection'

export function ViewAccountInformation({ account }: { account: Account }) {
  const router = useRouter()
  const route = useRoute()!
  const routerParams = data(route).params
  const onEditClick = useCallback(() => {
    router.push(
      routeTo(
        '/accounts/[effectiveMonth]/edit/[accountCode]/account-information',
        routerParams,
      ),
    )
  }, [router, routerParams])
  return (
    <InfoSection className='!gap-1'>
      <div className='flex items-center'>
        <InfoSectionTitle>Account information</InfoSectionTitle>
        <Button
          className='btn-primary ml-auto text-white'
          onClick={onEditClick}
        >
          Edit
        </Button>
      </div>
      <DetailsSectionItemsRow>
        <DetailsSectionItem
          label='Account open date'
          info={formatToMonthYearFromDate(account.openDate)}
        />
        <DetailsSectionItem
          label='Account status'
          info={
            account.accountStatus ?
              AccountStatusLabels[account.accountStatus]
            : 'Open'
          }
        />
      </DetailsSectionItemsRow>
      <DetailsSectionItemsRow>
        <DetailsSectionItem
          label={'Account number'}
          info={formatAccountCode(account)}
        />
        <DetailsSectionItem label={'Account name'} info={account.shortName} />
      </DetailsSectionItemsRow>
    </InfoSection>
  )
}
