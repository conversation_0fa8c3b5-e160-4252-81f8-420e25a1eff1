import { Account } from '@/app/[bankId]/accounts/types'
import { Button } from '@/components/Button'
import { useCallback } from 'react'
import { useRouter } from 'next/navigation'
import { routeTo } from '../../../edit/[accountCode]/routing'
import { data } from '@/lib/unions/Union'
import { useRoute } from '../routing'
import {
  InfoSection,
  InfoSectionSeparator,
  InfoSectionTitle,
} from '@/components/InfoSection'
import { ViewKeyAccount } from './ViewKeyAccount'
import { ViewDemographics } from './ViewDemographics'
import { ViewUserFields } from './ViewUserFields'
import { ViewPricingOptions } from './ViewPricingOptions'

export function ViewDemographicsPricing({
  account,
  keyAccount,
  effectiveDate,
}: {
  account: Account
  keyAccount?: Account
  effectiveDate: string
}) {
  const router = useRouter()
  const route = useRoute()!
  const routerParams = data(route).params

  const onEditClick = useCallback(() => {
    router.push(
      routeTo(
        '/accounts/[effectiveMonth]/edit/[accountCode]/demographics-and-pricing',
        routerParams,
      ),
    )
  }, [router, routerParams])

  return (
    <InfoSection className='!gap-1'>
      <div className='flex items-center'>
        <InfoSectionTitle>Demographic and pricing options</InfoSectionTitle>
        <Button
          className='btn-primary ml-auto text-white'
          onClick={onEditClick}
        >
          Edit
        </Button>
      </div>
      <ViewKeyAccount
        account={account}
        keyAccount={keyAccount}
        onEditClick={onEditClick}
      />
      <InfoSectionSeparator className='mb-3 mt-3' />

      <ViewDemographics account={account} effectiveDate={effectiveDate} />
      <InfoSectionSeparator className='mb-3 mt-3' />

      <ViewUserFields account={account} effectiveDate={effectiveDate} />
      <InfoSectionSeparator className='mb-3 mt-3' />

      <ViewPricingOptions account={account} />
    </InfoSection>
  )
}
