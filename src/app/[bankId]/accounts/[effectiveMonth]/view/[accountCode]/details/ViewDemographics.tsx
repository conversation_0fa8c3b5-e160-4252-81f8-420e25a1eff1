import { NameCode } from '@/app/[bankId]/accounts/_components/NameCode'
import { useAccountRelatedEntities } from '@/app/[bankId]/accounts/_hooks/useAccountRelatedEntities'
import { Account, DepositCategoryLabels } from '@/app/[bankId]/accounts/types'
import {
  DetailsSectionItem,
  DetailsSectionItemsRow,
  DetailsSectionTitle,
} from '@/components/DetailsSection'
import { InfoSectionDescription } from '@/components/InfoSection'

export function ViewDemographics({
  account,
  effectiveDate,
}: {
  account: Account
  effectiveDate: string
}) {
  const hasKeyAccount = !!account.keyAccountCode

  const {
    accountTypes: { data: accountTypes },
    branches: { data: branches },
    officers: { data: officers },
  } = useAccountRelatedEntities({ effectiveDate })
  const accountType = accountTypes?.find(
    (accountType) =>
      accountType.accountTypeCode === account.analysisAccountTypeCode,
  )
  const branch = branches?.find((branch) => branch.code === account.branchCode)
  const primaryOfficer = officers?.find(
    (officer) => officer.code === account.primaryOfficerCode,
  )
  const secondaryOfficer = officers?.find(
    (officer) => officer.code === account.secondaryOfficerCode,
  )
  const treasuryOfficer = officers?.find(
    (officer) => officer.code === account.treasuryOfficerCode,
  )

  return (
    <div>
      <DetailsSectionTitle>Demographics</DetailsSectionTitle>
      {hasKeyAccount && (
        <InfoSectionDescription className='text-sm'>
          These settings are automatically synced with the key account.
        </InfoSectionDescription>
      )}
      <DetailsSectionItemsRow>
        <DetailsSectionItem
          label='Account type'
          info={
            accountType && (
              <NameCode
                name={accountType.description}
                code={accountType.accountTypeCode}
              />
            )
          }
        />
        <DetailsSectionItem label='Cost center' info={account.costCenter} />
      </DetailsSectionItemsRow>
      <DetailsSectionItemsRow>
        <DetailsSectionItem
          label='Branch'
          info={
            branch && <NameCode name={branch.branchName} code={branch.code} />
          }
        />
        <DetailsSectionItem label='Currency' info={account.currencyCode} />
      </DetailsSectionItemsRow>
      <DetailsSectionItemsRow>
        <DetailsSectionItem
          label='Deposit type'
          info={account.depositAccountTypeCode || null}
        />
        <DetailsSectionItem
          label='Deposit category'
          info={
            account.depositCategory &&
            DepositCategoryLabels[account.depositCategory]
          }
        />
      </DetailsSectionItemsRow>
      <DetailsSectionItemsRow>
        <DetailsSectionItem
          label='Primary officer'
          info={
            primaryOfficer && (
              <NameCode name={primaryOfficer.name} code={primaryOfficer.code} />
            )
          }
        />
        <DetailsSectionItem
          label='Secondary officer'
          info={
            secondaryOfficer && (
              <NameCode
                name={secondaryOfficer.name}
                code={secondaryOfficer.code}
              />
            )
          }
        />
      </DetailsSectionItemsRow>
      <DetailsSectionItemsRow>
        <DetailsSectionItem
          label='Treasury officer'
          info={
            treasuryOfficer && (
              <NameCode
                name={treasuryOfficer.name}
                code={treasuryOfficer.code}
              />
            )
          }
        />
        <div className='flex-1'></div>
      </DetailsSectionItemsRow>
    </div>
  )
}
