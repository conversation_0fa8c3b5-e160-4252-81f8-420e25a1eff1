import { NameCode } from '@/app/[bankId]/accounts/_components/NameCode'
import { formatAccountCode } from '@/app/[bankId]/accounts/accountHelpers'
import { Account } from '@/app/[bankId]/accounts/types'
import {
  DetailsSectionItem,
  DetailsSectionItemsRow,
} from '@/components/DetailsSection'
import { Button } from '@headlessui/react'

export function ViewKeyAccount({
  keyAccount,
  onEditClick,
}: {
  account: Account
  keyAccount?: Account
  onEditClick: () => void
}) {
  return (
    <DetailsSectionItemsRow>
      <DetailsSectionItem
        label='Key account'
        info={
          <div className='flex gap-1'>
            {keyAccount ?
              <NameCode
                name={keyAccount.shortName}
                code={formatAccountCode(keyAccount)}
              />
            : '--'}
            <Button className='text-indigo-800 underline' onClick={onEditClick}>
              Edit
            </Button>
          </div>
        }
      />
      <div className='flex-1'></div>
    </DetailsSectionItemsRow>
  )
}
