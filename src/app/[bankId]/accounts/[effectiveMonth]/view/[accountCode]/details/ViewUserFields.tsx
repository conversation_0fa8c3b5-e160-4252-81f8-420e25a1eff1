import { useUserFieldsRelatedEntities } from '@/app/[bankId]/accounts/_hooks/useUserFieldsRelatedEntities'
import {
  accountToAccountCode,
  getUserFieldsListItemsFromConfigsAndSelections,
} from '@/app/[bankId]/accounts/accountHelpers'
import { Account, UserFieldListItem } from '@/app/[bankId]/accounts/types'
import {
  DetailsSectionSubtitle,
  DetailsSectionTitle,
} from '@/components/DetailsSection'
import { SortedTable } from '@/components/Table/SortedTable'
import { Tooltip } from '@/components/Tooltip'
import { CheckIcon, InformationCircleIcon } from '@heroicons/react/24/outline'
import { ColumnDef } from '@tanstack/react-table'
import { useMemo } from 'react'

type UserFieldsColumnDef = ColumnDef<UserFieldListItem>

export function ViewUserFields({
  account,
  effectiveDate,
}: {
  account: Account
  effectiveDate: string
}) {
  const { userFieldsConfigurations, userFieldSelections } =
    useUserFieldsRelatedEntities({
      effectiveDate,
      applicationId: account.applicationId,
      accountNumber: account.accountNumber,
      bankNumber: account.bankNumber,
    })

  const userFields = useMemo<UserFieldListItem[]>(() => {
    return getUserFieldsListItemsFromConfigsAndSelections(
      accountToAccountCode(account),
      userFieldsConfigurations.data,
      userFieldSelections.data,
    )
  }, [userFieldsConfigurations.data, userFieldSelections.data])

  const columns = useMemo<UserFieldsColumnDef[]>(() => {
    return [
      {
        header: 'User field',
        accessorKey: 'configuration.name',
      },
      {
        header: 'Value',
        cell: ({ row }) => {
          if (row.original.selection.isUnset) {
            return
          }

          if (row.original.configuration.fieldType === 'BOOLEAN') {
            return row.original.selection.booleanValue ? 'Yes' : 'No'
          }

          if (row.original.configuration.fieldType === 'DROPDOWN') {
            const options: number[] = []
            const optionsLabels: Record<number, string> = {}

            row.original.configuration.updatedDropdownOptions?.forEach(
              (option) => {
                options.push(option.code!)
                optionsLabels[option.code!] = option.value!
              },
            )

            return optionsLabels[
              row.original.selection.dropdownOptionCode as number
            ]
          }

          if (row.original.configuration.fieldType === 'FREEFORM') {
            return row.original.selection.freeformValue
          }
        },
      },
      {
        header: 'Expiration date',
        cell: ({ row }) => {
          return (
            row.original.selection.isUnset ? ''
            : row.original.selection.expiry ? row.original.selection.expiry
            : 'No expiration'
          )
        },
      },
      {
        header: 'Apply to child accounts',
        cell: ({ row }) => {
          return row.original.selection.isUnset ?
              ''
            : row.original.selection.applyToChildAccounts && (
                <CheckIcon className='ml-20 size-4' />
              )
        },
      },
    ]
  }, [])

  if (
    userFieldsConfigurations.status === 'pending' ||
    userFieldSelections.status === 'pending'
  ) {
    return 'Loading...'
  }

  if (userFieldsConfigurations.status === 'error') {
    throw userFieldsConfigurations.error
  }

  if (userFieldSelections.status === 'error') {
    throw userFieldSelections.error
  }

  return (
    <>
      <div className='flex flex-row gap-1'>
        <DetailsSectionTitle>User fields</DetailsSectionTitle>
        <Tooltip className='size-4 self-center' content='User fields'>
          <InformationCircleIcon />
        </Tooltip>
      </div>
      <DetailsSectionSubtitle>
        These settings are automatically synced with the key account.
      </DetailsSectionSubtitle>
      <SortedTable data={userFields} columns={columns} columnFilters={[]} />
    </>
  )
}
