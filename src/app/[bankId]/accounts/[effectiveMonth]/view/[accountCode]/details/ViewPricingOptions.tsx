import { Account, PricingOptionsItem } from '@/app/[bankId]/accounts/types'
import {
  DetailsSectionSubtitle,
  DetailsSectionTitle,
} from '@/components/DetailsSection'
import { SortedTable } from '@/components/Table/SortedTable'
import { ColumnDef } from '@tanstack/react-table'
import { useMemo } from 'react'

type ViewPricingOptionsColumnDef = ColumnDef<PricingOptionsItem>

export function ViewPricingOptions({ account }: { account: Account }) {
  const pricingOptionsData = useMemo(() => {
    return [
      {
        name: 'Customer specific pricing indicator',
      },
    ]
  }, [])

  const columns = useMemo<ViewPricingOptionsColumnDef[]>(() => {
    return [
      {
        header: 'Pricing option',
        accessorKey: 'name',
      },
      {
        header: 'Value',
        cell: ({ row }) => {
          return (
            <div className='ml-2'>
              {account.customerSpecificPricingIndicator ? 'Yes' : 'No'}
            </div>
          )
        },
      },
    ]
  }, [account])

  return (
    <>
      <div className='flex flex-row gap-1'>
        <DetailsSectionTitle>Pricing options</DetailsSectionTitle>
      </div>
      <DetailsSectionSubtitle>
        These settings are not synced with the key account.
      </DetailsSectionSubtitle>
      <SortedTable
        data={pricingOptionsData}
        columns={columns}
        columnFilters={[]}
      />
    </>
  )
}
