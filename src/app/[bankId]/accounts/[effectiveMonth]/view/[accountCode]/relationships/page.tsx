'use client'

import { accountQueries } from '@/app/[bankId]/accounts/queries'
import { Button } from '@/components/Button'
import { SegmentedTab, SegmentedTabList } from '@/components/SegmentedTabList'
import { TabGroup, TabPanel, TabPanels } from '@headlessui/react'
import { useQuery } from '@tanstack/react-query'
import { ReactFlowProvider } from '@xyflow/react'
import { AccountList } from '@/app/[bankId]/accounts/_components/AccountList'
import ViewAccountsHierarchy from '@/app/[bankId]/accounts/_components/accounts-hierarchy/ViewAccountsHierarchy'
import { InfoSection, InfoSectionTitle } from '@/components/InfoSection'
import Link from 'next/link'
import { routeTo } from '../../../edit/[accountCode]/routing'
import { accountTypeQueries } from '@/app/[bankId]/configuration/account-type/queries'
import { useAccounts } from '@/app/[bankId]/accounts/_hooks/useAccounts'
import {
  accountCodesAreEqual,
  apiMappingWithKeyListSelector,
  toAccountCodeString,
} from '@/app/[bankId]/accounts/accountHelpers'
import { apiToFormSchemas } from '@/api/apiToFormSchema'
import { useRoute } from '../routing'
import { data } from '@/lib/unions/Union'
import { useAccountFromParams } from '@/app/[bankId]/accounts/_hooks/useAccountFromParams'
import { HydratedAccountWithKeyAccountMappingForm } from '@/api/formToApiSchema'
import { listSelector } from '@/api/selectors'
export default function Page() {
  const route = useRoute()!
  const routeParams = data(route).params
  const {
    account,
    isLoading: accountLoading,
    accountCode,
    effectiveDate,
  } = useAccountFromParams(routeParams)

  const {
    data: accountMappingData,
    isLoading,
    isError,
    error,
  } = useQuery({
    ...accountQueries('/getAccountMappings', {
      code: toAccountCodeString(accountCode),
      effectiveDate,
    }),
    select: apiMappingWithKeyListSelector,
    enabled: !!account,
  })

  const {
    data: accountTypeData,
    isLoading: isAccountTypeLoading,
    isError: isAccountTypeError,
    error: accountTypeError,
  } = useQuery({
    ...accountTypeQueries('/getAccountTypes', {
      effectiveDate,
    }),
    select: listSelector(apiToFormSchemas.accountType),
  })

  if (!account) {
    return <>Account not found</>
  }

  if (isLoading || isAccountTypeLoading || accountLoading) {
    return <>Loading...</>
  }

  if (isError) {
    return <>{error.message}</>
  }

  if (isAccountTypeError) {
    return <>{accountTypeError.message}</>
  }

  const initialNode: HydratedAccountWithKeyAccountMappingForm[] = [
    {
      child: {
        accountNumber: account.accountNumber,
        applicationId: account.applicationId,
        bankNumber: account.bankNumber,
        shortName: account.shortName,
        analysisAccountTypeCode: account.analysisAccountTypeCode,
        branchCode: account.branchCode,
        currencyCode: account.currencyCode,
        effectiveDate: account.effectiveDate,
        openDate: account.openDate,
        primaryOfficerCode: account.primaryOfficerCode,
        depositCategory: null,
        depositAccountTypeCode: null,
        secondaryOfficerCode: null,
        treasuryOfficerCode: null,
        accountStatus: null,
        closeDate: null,
        costCenter: '',
        customerSpecificPricingIndicator: null,
        processingIndicator: null,
        keyAccountCode: null,
        isKeyAccount: null,
      },
      parent: null,
    },
  ]

  return (
    <InfoSection>
      <div className='flex items-center justify-between pb-4'>
        <InfoSectionTitle>Account relationship</InfoSectionTitle>
        <Link
          href={`${routeTo(
            '/accounts/[effectiveMonth]/edit/[accountCode]/relationships',
            routeParams,
          )}`}
        >
          <Button className='btn-primary ml-auto text-white'>Edit</Button>
        </Link>
      </div>
      <TabGroup className='flex flex-col gap-2'>
        <div className='flex flex-row gap-3'>
          <div>View</div>
          <SegmentedTabList>
            <SegmentedTab>List</SegmentedTab>
            <SegmentedTab>Graphical</SegmentedTab>
          </SegmentedTabList>
        </div>

        <TabPanels>
          <TabPanel>
            <AccountList
              account={account}
              accountMapping={
                accountMappingData && accountMappingData.length > 0 ?
                  accountMappingData
                : initialNode
              }
              accountTypes={accountTypeData}
            />
          </TabPanel>
          <TabPanel>
            <ReactFlowProvider>
              <ViewAccountsHierarchy
                accountMapping={
                  accountMappingData && accountMappingData.length > 0 ?
                    accountMappingData
                  : initialNode
                }
              />
            </ReactFlowProvider>
          </TabPanel>
        </TabPanels>
      </TabGroup>
    </InfoSection>
  )
}
