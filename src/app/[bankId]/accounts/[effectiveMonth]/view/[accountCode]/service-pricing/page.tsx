'use client'
import { ServicePrice } from '@/api/formToApiSchema'
import { Button } from '@/components/Button'
import { SimpleMultiselectFilter } from '@/components/Filter/SimpleMultiselectFilter'
import {
  InfoSection,
  InfoSectionDescription,
  InfoSectionTitle,
} from '@/components/InfoSection'
import { SearchBar } from '@/components/SearchBar'
import { ExpandedTable } from '@/components/Table/ExpandedTable'
import { ColumnDef } from '@tanstack/react-table'
import { useMemo } from 'react'

const updateSearchServicePricing = (searchTerm: string) => {
  console.debug('TODO', searchTerm)
}

const updateServiceFilter = (status: string[] | null) => {
  console.debug('TODO', status)
}
type ServicePriceListColumnDef = ColumnDef<ServicePrice>

export default function Page() {
  const columns = useMemo<ServicePriceListColumnDef[]>(() => {
    return [
      {
        header: 'Name',
        accessorKey: 'description',
        meta: {
          className: 'basis-3/12',
        },
      },
      {
        header: 'Code',
        accessorKey: 'service_code',
        meta: {
          className: 'basis-1/12',
        },
      },
      {
        header: 'Service Type',
        accessorKey: 'service_type',
        meta: {
          className: 'basis-2/12',
        },
      },
      {
        header: 'Price Type',
        accessorKey: 'price_type',
        meta: {
          className: 'basis-2/12',
        },
      },
      {
        header: 'Price',
        accessorKey: 'price',
        meta: {
          className: 'basis-1/12',
        },
      },
      {
        header: 'Source',
        accessorKey: 'source',
        meta: {
          className: 'basis-1/12',
        },
      },
      {
        header: 'Disposition',
        accessorKey: 'disposition',
        meta: {
          className: 'basis-1/12',
        },
      },
      {
        header: 'Source',
        accessorKey: 'service_source',
        meta: {
          className: 'basis-2/12',
        },
      },
    ]
  }, [])

  return (
    <InfoSection>
      <div className='flex items-center justify-between pb-4'>
        <div>
          <InfoSectionTitle>Service Pricing</InfoSectionTitle>
          <InfoSectionDescription>
            View actual service pricing infomration for this account.
          </InfoSectionDescription>
        </div>
        <div className='flex gap-2'>
          <Button>Export</Button>
        </div>
      </div>
      <div className='flex items-center justify-between'>
        <div>
          <SimpleMultiselectFilter
            defaultLabel='Source'
            name='source'
            options={['Default pricing', 'Price list', 'Promotion', 'Override']}
            value={null}
            onValueChange={updateServiceFilter}
          />
        </div>
        <div className='flex items-center gap-2'>
          <SearchBar
            className='max-w-96'
            defaultLabel='Search services'
            onValueChange={updateSearchServicePricing}
          />
          <div>0 Results</div>
        </div>
      </div>
      <ExpandedTable data={[]} columns={columns} getSubRows={(row) => []} />
    </InfoSection>
  )
}
