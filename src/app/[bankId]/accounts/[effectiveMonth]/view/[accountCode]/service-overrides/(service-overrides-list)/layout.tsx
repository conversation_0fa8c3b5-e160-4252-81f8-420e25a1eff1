'use client'
import { usePathname } from 'next/navigation'
import { data } from '@/lib/unions/Union'
import { useMemo } from 'react'
import { routeTo, useRoute } from '../../routing'
import { routeTo as routeToCreate } from '../../../../create/[accountCode]/routing'
import {
  InfoSection,
  InfoSectionDescription,
  InfoSectionTitle,
} from '@/components/InfoSection'
import { TabList } from '@/components/Tabs/TabList'
import { Tab } from '@/components/Tabs/Tab'
import Link from 'next/link'
import { Button } from '@/components/Button'

export default function ViewServiceOverridesLayout({
  children,
}: React.PropsWithChildren) {
  const pathname = usePathname()

  const route = useRoute()!
  const routeParams = data(route).params
  const tabs = useMemo(() => {
    return [
      {
        href: routeTo(
          '/accounts/[effectiveMonth]/view/[accountCode]/service-overrides/current-and-related',
          routeParams,
        ),
        key: 'current-and-related',
        label: 'Overrides on current and related accounts',
      },
      {
        href: routeTo(
          '/accounts/[effectiveMonth]/view/[accountCode]/service-overrides/account-only',
          routeParams,
        ),
        key: 'account-only',
        label: 'Overrides on this account only',
      },
    ]
  }, [routeParams])

  return (
    <InfoSection>
      <div className='flex items-center justify-between pb-4'>
        <div>
          <InfoSectionTitle>Service Overrides</InfoSectionTitle>
          <InfoSectionDescription>
            View overrides for this account and related accounts.
          </InfoSectionDescription>
        </div>
        <div className='flex gap-2'>
          <Button>Export</Button>
          <Link
            href={`${routeToCreate(
              '/accounts/[effectiveMonth]/create/[accountCode]/service-override',
              routeParams,
            )}`}
          >
            <Button className='btn-primary ml-auto text-white'>
              Add override
            </Button>
          </Link>
        </div>
      </div>
      <TabList>
        {tabs.map((tab) => (
          <Tab
            key={tab.href}
            href={tab.href}
            active={pathname.includes(tab.key)}
          >
            {tab.label}
          </Tab>
        ))}
      </TabList>
      <div className='overflow-auto'>{children}</div>
    </InfoSection>
  )
}
