'use client'
import { data } from '@/lib/unions/Union'
import { useRoute } from '../../../routing'
import { toUIFormat } from '@/lib/date'
import { useQuery } from '@tanstack/react-query'
import { accountQueries } from '@/app/[bankId]/accounts/queries'
import { toAccountCodeString } from '@/app/[bankId]/accounts/accountHelpers'
import {
  InfoSection,
  InfoSectionTitle,
  InfoSubSectionTitle,
} from '@/components/InfoSection'
import { useServiceCatalog } from '@/app/[bankId]/services/_hooks/useServiceCatalog'
import {
  serviceTypeLabel,
  priceTypeLabel,
  dispositionLabel,
  costTypeLabel,
} from '@/strings/enums'
import { formatUSD } from '@/lib/intlFormatters'
import { SortedTable } from '@/components/Table/SortedTable'
import { ColumnDef } from '@tanstack/react-table'
import { CheckIcon } from '@heroicons/react/24/outline'
import { useMemo } from 'react'
import { OverrideTableRow } from '@/app/[bankId]/accounts/types'

export default function Page() {
  const route = useRoute()!
  const routeParams = data(route).params
  const effectiveDate = toUIFormat(routeParams.effectiveMonth)
  const [applicationId, bankNumber, accountNumber] =
    routeParams.accountCode.split('-') as ['C' | 'D', string, string]
  const accountCode = { applicationId, bankNumber, accountNumber }

  const {
    data: serviceOverridesResponse,
    isLoading,
    error,
  } = useQuery({
    ...accountQueries('/getServiceOverridesByAccount', {
      code: toAccountCodeString({ bankNumber, applicationId, accountNumber }),
      effectiveDate,
    }),
  })

  const catalogData = useServiceCatalog({
    effectiveDate,
  })

  // Find service override item
  const serviceOverrideItem = serviceOverridesResponse?.find(
    (serviceOverrides) =>
      serviceOverrides.servicePrice.find(
        (servicePrice) =>
          servicePrice.servicePrice?.serviceCode === routeParams.serviceCode,
      ),
  )

  const service = serviceOverrideItem?.service
  const servicePrice = serviceOverrideItem?.servicePrice[0]?.servicePrice

  // Get service category from catalog
  const serviceCategory =
    catalogData.data?.serviceByCode[service?.code || '']?.parent

  // Create table data
  const tableData = useMemo<OverrideTableRow[]>(() => {
    if (!servicePrice) return []

    const data: OverrideTableRow[] = [
      {
        override: servicePrice?.priceType ? true : false,
        pricingAttribute: 'Price type',
        priceType:
          servicePrice?.priceType ?
            priceTypeLabel(servicePrice.priceType)
          : 'Unit priced',
      },

      // Price row
      {
        override: servicePrice?.priceType ? true : false,
        pricingAttribute: 'Price',
        priceType: formatUSD(servicePrice?.priceValue) || '$0.00',
        isSubRow: true,
      },

      // Base fee row
      {
        override: servicePrice?.priceType ? true : false,
        pricingAttribute: 'Base fee',
        priceType: 'No override',
        isSubRow: true,
      },

      // Disposition row
      {
        override: servicePrice?.priceType ? true : false,
        pricingAttribute: 'Disposition',
        priceType:
          servicePrice?.disposition ?
            dispositionLabel(servicePrice.disposition)
          : 'Waived',
      },

      // Max fee row
      {
        override: servicePrice?.priceType ? true : false,
        pricingAttribute: 'Max fee',
        priceType: 'No override',
        isSubRow: true,
      },

      // Min fee row
      {
        override: servicePrice?.priceType ? true : false,
        pricingAttribute: 'Min fee',
        priceType: 'No override',
        isSubRow: true,
      },

      // Cost type row
      {
        override: servicePrice?.priceType ? true : false,
        pricingAttribute: 'Cost type',
        priceType:
          servicePrice?.costType ?
            costTypeLabel(servicePrice.costType)
          : 'No cost',
        isSubRow: true,
      },

      // Cost row
      {
        override: servicePrice?.priceType ? true : false,
        pricingAttribute: 'Cost',
        priceType: '-',
      },
    ]

    return data
  }, [servicePrice])

  // Define columns
  const columns = useMemo<ColumnDef<OverrideTableRow>[]>(() => {
    return [
      {
        header: 'Override',
        accessorKey: 'override',
        meta: {
          className: 'w-24 pl-6 text-left',
        },
        cell: ({ row }) => (
          <div className='flex justify-center'>
            {row.original.override && !row.original.isSubRow ?
              <CheckIcon className='ml-2 size-6' />
            : null}
          </div>
        ),
      },
      {
        header: 'Pricing attribute',
        accessorKey: 'pricingAttribute',
        meta: {
          className: 'w-80 text-left px-6',
        },
        cell: ({ row }) => <div>{row.original.pricingAttribute}</div>,
      },
      {
        header: 'Price type',
        accessorKey: 'priceType',
        meta: {
          className: 'w-80 text-left px-6',
        },
      },
    ]
  }, [])

  if (isLoading) {
    return <div>Loading service overrides...</div>
  }

  if (error) {
    return (
      <div className='text-red-600'>
        Error loading service overrides: {String(error)}
      </div>
    )
  }

  if (!accountCode || !effectiveDate || !serviceOverridesResponse) {
    return <div>Account code and effective date are required</div>
  }

  if (!serviceOverrideItem || !service) {
    return <div>Service override not found</div>
  }

  return (
    <div className='flex flex-col gap-6'>
      {/* Service Information Section */}
      <InfoSection>
        <InfoSectionTitle>Service information</InfoSectionTitle>
        <div className='grid grid-cols-4 gap-4 text-sm'>
          <div>
            <div className='mb-1 text-gray-600'>Service category</div>
            <div>{serviceCategory?.name || 'Wire Services'}</div>
          </div>
          <div>
            <div className='mb-1 text-gray-600'>Service name</div>
            <div>{service.description}</div>
          </div>
          <div>
            <div className='mb-1 text-gray-600'>Service code</div>
            <div>{service.code}</div>
          </div>
          <div>
            <div className='mb-1 text-gray-600'>Service type</div>
            <div>{serviceTypeLabel(service.serviceType)}</div>
          </div>
        </div>
      </InfoSection>

      {/* Overrides on this account Section */}
      <InfoSection>
        <InfoSubSectionTitle>Overrides on this account</InfoSubSectionTitle>
        <div className='mb-4 text-sm text-gray-600'>
          View overrides for this account and related accounts.
        </div>

        <div className='mb-6 grid grid-cols-2 gap-8'>
          <div>
            <div className='mb-1 text-gray-600'>Start date</div>
            <div>{toUIFormat(service.effectiveDate)}</div>
          </div>
          <div>
            <div className='mb-1 text-gray-600'>Expiration date</div>
            <div>
              {servicePrice?.expirationDate ?
                toUIFormat(servicePrice.expirationDate)
              : '2025-08'}
            </div>
          </div>
        </div>
        <div>
          <div className='w-2/3'>
            <SortedTable data={tableData} columns={columns} />
          </div>
        </div>
      </InfoSection>
    </div>
  )
}
