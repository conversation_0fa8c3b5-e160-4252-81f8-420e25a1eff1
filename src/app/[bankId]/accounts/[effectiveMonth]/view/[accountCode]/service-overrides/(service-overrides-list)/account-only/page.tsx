'use client'

import { ExpandedTable } from '@/components/Table/ExpandedTable'
import { useAccountFromParams } from '@/app/[bankId]/accounts/_hooks/useAccountFromParams'
import { useRoute } from '../../../routing'
import { data } from '@/lib/unions/Union'
import { useMemo } from 'react'
import { ChevronDownIcon, ChevronRightIcon } from '@heroicons/react/24/outline'
import { Button } from '@headlessui/react'
import { useQuery } from '@tanstack/react-query'
import { toAccountCodeString } from '@/app/[bankId]/accounts/accountHelpers'
import { ServiceOverrideColumnDef } from '@/app/[bankId]/accounts/types'
import {
  combineServicePrices,
  transformServiceDetailsToRows,
} from '../../utils'
import { accountQueries } from '@/app/[bankId]/accounts/queries'
import { toUIFormat } from '@/lib/date'
import {
  Catalog,
  useServiceCatalog,
} from '@/app/[bankId]/services/_hooks/useServiceCatalog'
import {
  convertToSentenceCase,
  transformData,
} from '@/app/[bankId]/pricing/[effectiveDate]/view-price-list/[code]/service-pricing/utils'
import {
  ServicePrice,
  SubRows,
} from '@/app/[bankId]/pricing/[effectiveDate]/view-price-list/[code]/service-pricing/types'
import { ColumnDef } from '@tanstack/react-table'
import clsx from 'clsx'
import { routeTo } from '../../routing'

const columnsTable = (
  combinedServicePrices: ServicePrice[],
): ColumnDef<SubRows>[] => [
  {
    header: 'serviceByCategoryCodes', //will have all the services linked to the category
    accessorKey: 'serviceByCategoryCodes',
    filterFn: (row, columnId, filterValue) => {
      if (!filterValue || !filterValue.length) return true
      const rows = row.getValue<string[]>(columnId)
      return (
        rows.filter((serviceName) =>
          serviceName.toLowerCase().includes(filterValue.toLowerCase()),
        )?.length > 0
      )
    },
  },
  {
    header: 'parentCatalog', //will have all the services linked to the category
    accessorKey: 'parentCatalog',
    filterFn: (row, columnId, filterValue) => {
      if (!filterValue || !filterValue.length) return true
      const rows = row.getValue<string[]>(columnId)
      return (
        rows.filter((catalog) =>
          catalog.toLowerCase().includes(filterValue.toLowerCase()),
        )?.length > 0
      )
    },
  },
  {
    header: 'serviceCatalog',
    accessorKey: 'serviceCatalog',
  },
  {
    header: 'serviceCatalogCode',
    accessorKey: 'serviceCatalogCode',
  },

  {
    header: 'Name',
    accessorKey: 'name',
    meta: {
      className: 'min-w-[275px] max-w-[275px] overflow-hidden text-ellipsis',
    },
    cell: ({ row, getValue }) => (
      <div
        style={{
          paddingLeft: `${
            row.getCanExpand() ? row.depth * 1 : (row.depth + 1) * 2
          }rem`,
        }}
      >
        <div className='flex flex-row gap-2'>
          {row.getCanExpand() && (
            <Button
              className={'border-none'}
              onClick={row.getToggleExpandedHandler()}
            >
              {row.getIsExpanded() ?
                <ChevronDownIcon className='h-4 w-4 text-app-color-primary' />
              : <ChevronRightIcon className='h-4 w-4 text-app-color-primary' />}
            </Button>
          )}
          <div className={clsx('text-pretty p-2')}>{getValue<string>()}</div>
        </div>
      </div>
    ),
  },
  {
    header: '',
    meta: {
      className: 'min-w-[150px] max-w-[150px]',
    },
    accessorKey: 'priceTypes',
    filterFn: (row, columnId, filterValue) => {
      if (!filterValue || !filterValue.length) return true
      const rowValue = row.getValue<string[]>(columnId)
      return (
        rowValue.filter((catalog) =>
          catalog.toLowerCase().includes(filterValue.toLowerCase()),
        )?.length > 0
      )
    },
  },
  {
    header: '',
    accessorKey: 'serviceTypes',
    filterFn: (row, columnId, filterValue) => {
      if (!filterValue || !filterValue.length) return true
      const rowValue = row.getValue<string[]>(columnId)
      return (
        rowValue.filter((catalog) =>
          catalog.toLowerCase().includes(filterValue.toLowerCase()),
        )?.length > 0
      )
    },
  },
  {
    header: 'Code',
    accessorKey: 'serviceCode',
    meta: {
      className: 'min-w-[85px] max-w-[85px]',
    },
    cell: ({ getValue }) => (
      <div className={'text-pretty p-2'}>{getValue<string>()}</div>
    ),
  },
  {
    header: 'Service Type',
    meta: {
      className: 'min-w-[170px] max-w-[170px]',
    },
    accessorKey: 'serviceType',
    cell: ({ row, getValue }) => {
      return (
        <div className='text-pretty p-2'>
          {convertToSentenceCase(getValue<string>())}
        </div>
      )
    },
  },
  {
    header: 'Price Type',
    meta: {
      className: 'min-w-[170px] max-w-[170px]',
    },
    accessorKey: 'priceType',
    cell: ({ getValue }) => (
      <div className='text-pretty p-2'>
        {convertToSentenceCase(getValue<string>())}
      </div>
    ),
  },
  {
    header: 'Price',
    meta: {
      className: 'min-w-[120px] max-w-[120px]',
    },
    accessorKey: 'price',
    cell: ({ getValue }) => (
      <div className='text-pretty p-2'>{getValue<string>()}</div>
    ),
  },
  {
    header: 'Disposition',
    meta: {
      className: 'min-w-[175px] max-w-[175px]',
    },
    accessorKey: 'disposition',
    cell: ({ getValue }) => (
      <div className='text-pretty p-2'>
        {convertToSentenceCase(getValue<string>())}
      </div>
    ),
  },
  {
    header: 'Start date',
    cell: ({ row }) => {
      return combinedServicePrices.find(
        (servicePrice) =>
          servicePrice.serviceCode === row.original?.serviceCode,
      )?.effectiveDate
    },
  },
  {
    header: 'Expiration date',
    cell: ({ row }) => {
      return combinedServicePrices.find(
        (servicePrice) =>
          servicePrice.serviceCode === row.original?.serviceCode,
      )?.expirationDate
    },
  },
]

export default function Page() {
  const route = useRoute()!
  const routeParams = data(route).params
  const effectiveDate = toUIFormat(routeParams.effectiveMonth)
  const [applicationId, bankNumber, accountNumber] =
    routeParams.accountCode.split('-') as ['C' | 'D', string, string]
  const accountCode = { applicationId, bankNumber, accountNumber }

  const catalogData = useServiceCatalog({
    effectiveDate,
  })

  // Fetch service overrides data from API
  const {
    data: serviceOverridesResponse,
    isLoading,
    error,
  } = useQuery({
    ...accountQueries('/getServiceOverridesByAccount', {
      code: toAccountCodeString({ bankNumber, applicationId, accountNumber }),
      effectiveDate,
    }),
  })

  const combinedServicePrices = useMemo(() => {
    if (!serviceOverridesResponse) return []
    return combineServicePrices(serviceOverridesResponse)
  }, [serviceOverridesResponse])

  const tableData = useMemo(() => {
    if (catalogData?.data && combinedServicePrices) {
      const catalog: Catalog = {
        categoryByCode: catalogData.data.categoryByCode!,
        serviceByCode: catalogData.data.serviceByCode!,
      }
      return transformData(catalog, combinedServicePrices)
    }
  }, [catalogData.data, combinedServicePrices])

  const columns = useMemo(
    () => columnsTable(combinedServicePrices),
    [combinedServicePrices],
  )

  if (isLoading) {
    return <div>Loading service overrides...</div>
  }

  if (error) {
    return (
      <div className='text-red-600'>
        Error loading service overrides: {String(error)}
      </div>
    )
  }

  if (!accountCode || !effectiveDate || !tableData) {
    return <div>Account code and effective date are required</div>
  }

  return (
    <>
      <ExpandedTable
        data={tableData}
        columns={columns}
        getSubRows={(row) => row?.subRows}
        rowLink={(row) =>
          row.original?.serviceCode &&
          routeTo(
            '/accounts/[effectiveMonth]/view/[accountCode]/service-overrides/view/[serviceCode]',
            { ...routeParams, serviceCode: row.original?.serviceCode },
          )
        }
        columnVisibility={{
          serviceCatalog: false,
          serviceCatalogCode: false,
          serviceByCategoryCodes: false,
          parentCatalog: false,
          priceTypes: false,
          serviceTypes: false,
        }}
      />
    </>
  )
}
