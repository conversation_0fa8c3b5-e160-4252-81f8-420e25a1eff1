'use client'

import { useMemo } from 'react'
import { ColumnDef } from '@tanstack/react-table'
import { SortedTable } from '@/components/Table/SortedTable'
import { InfoSectionTitle } from '@/components/InfoSection'
import { SettlementProcessingOptionItemWithId } from '@/app/[bankId]/accounts/types'
import { CheckIcon } from '@heroicons/react/24/outline'
import { useSettlementOptionsData } from '@/components/SettlementOptions/useSettlementOptionsData'

interface SettlementOptionsProps {
  settlementOptions: SettlementProcessingOptionItemWithId[]
  effectiveDate: string
}

export default function SettlementOptions({
  settlementOptions,
  effectiveDate,
}: SettlementOptionsProps) {
  const settlementOptionsData = settlementOptions

  // Use shared settlement options data hook
  const { formatPlanDisplay } = useSettlementOptionsData({
    effectiveDate,
  })

  const columns = useMemo<
    ColumnDef<SettlementProcessingOptionItemWithId>[]
  >(() => {
    return [
      {
        header: 'Override',
        accessorKey: 'override',
        meta: {
          className: 'w-24 ml-6',
        },
        cell: ({ row }) => (
          <div className='flex justify-center'>
            {row.original.override ?
              <CheckIcon className='ml-2 size-6' />
            : null}
          </div>
        ),
      },
      {
        header: 'Settlement setting',
        accessorKey: 'name',
        meta: {
          className: 'w-120 flex-shrink-0 px-6 text-left flex flex-1',
        },
      },
      {
        header: 'Plan',
        meta: {
          className: 'flex px-6 flex-1',
        },
        cell: ({ row }) => {
          return formatPlanDisplay(row.original.planCode, row.original.field)
        },
      },
      {
        header: 'Expiration date',
        accessorKey: 'expiry',
        meta: {
          className: 'w-40 flex-shrink-0 px-6',
        },
        cell: ({ row }) => row.original.expiry || 'No expiration',
      },
    ]
  }, [formatPlanDisplay])

  return (
    <div className='mt-12 space-y-4'>
      <div>
        <InfoSectionTitle>Settlement options</InfoSectionTitle>
      </div>
      <SortedTable data={settlementOptionsData} columns={columns} />
    </div>
  )
}
