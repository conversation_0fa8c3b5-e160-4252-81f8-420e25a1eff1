'use client'

import {
  InfoSection,
  InfoSectionSeparator,
  InfoSectionTitle,
} from '@/components/InfoSection'
import Link from 'next/link'
import { Button } from '@/components/Button'
import { data } from '@/lib/unions/Union'
import { useAccountFromParams } from '@/app/[bankId]/accounts/_hooks/useAccountFromParams'
import { useRoute } from '../routing'
import { routeTo } from '../../../edit/[accountCode]/routing'
import { routeTo as routeToView } from '../../../view/[accountCode]/routing'

import { ViewSettlementAccount } from './ViewSettlementAccount'
import { accountQueries } from '@/app/[bankId]/accounts/queries'
import { useQuery } from '@tanstack/react-query'
import { formatToMonthYearFromDate, toUTCDateString } from '@/lib/date'
import SettlementOptions from './SettlementOptions'
import ProcessingOptions from './ProcessingOptions'
import {
  formatAccountCodeForRoute,
  transformToSettlementProcessingOptions,
} from '@/app/[bankId]/accounts/accountHelpers'
import { VersionTimeline } from '@/app/[bankId]/configuration/_components/VersionTimeline'
import { useCallback, useMemo } from 'react'

export default function Page() {
  const route = useRoute()!
  const routeParams = data(route).params
  const [applicationId, bankNumber, accountNumber] =
    routeParams.accountCode.split('-') as ['C' | 'D', string, string]
  const { account } = useAccountFromParams(routeParams)

  const {
    data: accountTypeOverride,
    isLoading,
    isError,
  } = useQuery(
    accountQueries('/getAccountTypeOverride', {
      bankNumber,
      applicationId,
      accountNumber,
      effectiveDate: toUTCDateString(routeParams.effectiveMonth),
    }),
  )

  const {
    data: settlementVersions,
    isLoading: versionsLoading,
    isError: versionsIsError,
  } = useQuery(
    accountQueries('/getSettlementProcessingOptionsTimeline', {
      bankNumber,
      applicationId,
      accountNumber,
    }),
  )
  // Transform the API response to UI data
  const { settlementOptions, processingOptions } =
    transformToSettlementProcessingOptions(accountTypeOverride)

  const linkFormatter = useCallback(
    (effectiveMonth: string, accountCode: string) => {
      return routeToView(
        '/accounts/[effectiveMonth]/view/[accountCode]/settlement-and-processing',
        {
          bankId: routeParams.bankId,
          effectiveMonth,
          accountCode,
        },
      )
    },
    [routeParams.bankId],
  )
  const currentMonth = useMemo(() => formatToMonthYearFromDate(new Date()), [])

  const versions = useMemo(
    () =>
      settlementVersions
        ?.reduce((uniqueMonths, date) => {
          if (uniqueMonths.indexOf(date.substring(0, 7)) === -1)
            uniqueMonths.push(date.substring(0, 7))
          return uniqueMonths
        }, [] as string[])
        .toSorted((a, b) => (a < b ? 1 : -1))
        .map((uniqueMonth, index, array) => ({
          code: formatAccountCodeForRoute({
            bankNumber,
            applicationId,
            accountNumber,
          }),
          effectiveDate: uniqueMonth,
          isExpired: index > 0 && array[index - 1] <= currentMonth,
        })),
    [
      settlementVersions,
      bankNumber,
      applicationId,
      accountNumber,
      currentMonth,
    ],
  )
  if (!account) {
    return <>404 could not load account.</>
  }

  if (isLoading || versionsLoading) {
    return <div>Loading settlement and processing options...</div>
  }

  if (isError || versionsIsError) {
    return <div>Error loading settlement and processing options.</div>
  }

  return (
    <div className='flex gap-4'>
      <InfoSection className='flex grow flex-col gap-4'>
        <div className='flex items-center justify-between'>
          <InfoSectionTitle>Settlements And Processing</InfoSectionTitle>
          <div className='flex gap-2'>
            <Button className='btn'>Export</Button>
            <Link
              href={`${routeTo(
                '/accounts/[effectiveMonth]/edit/[accountCode]/settlement-and-processing',
                routeParams,
              )}`}
            >
              <Button className='btn-primary text-white'>Edit</Button>
            </Link>
          </div>
        </div>
        <p className='text-zinc-400'>
          You can override the following settings for your composite account.
        </p>
        <ViewSettlementAccount
          account={account}
          chargeAccountCode={accountTypeOverride?.chargeAccountCode}
        />
        <SettlementOptions
          settlementOptions={settlementOptions}
          effectiveDate={toUTCDateString(routeParams.effectiveMonth)}
        />
        <ProcessingOptions
          processingOptions={processingOptions}
          effectiveDate={toUTCDateString(routeParams.effectiveMonth)}
        />
      </InfoSection>
      <div className='flex w-72 flex-col rounded-lg border bg-white p-6'>
        {versions && (
          <VersionTimeline
            versions={versions}
            currentEffectiveDate={routeParams.effectiveMonth}
            linkFormatter={linkFormatter}
          />
        )}
      </div>
    </div>
  )
}
