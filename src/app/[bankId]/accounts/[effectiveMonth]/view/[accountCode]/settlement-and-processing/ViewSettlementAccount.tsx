import {
  DetailsSectionItem,
  DetailsSectionItemsRow,
} from '@/components/DetailsSection'
import { Account } from '@/app/[bankId]/accounts/types'
import { AccountTypeOverride } from '@/api/formToApiSchema'

export function ViewSettlementAccount({
  account,
  chargeAccountCode,
}: {
  account: Account
  chargeAccountCode: string | undefined
}) {
  return (
    <DetailsSectionItemsRow>
      <DetailsSectionItem
        label='Settlement Account'
        info={`${account.shortName} ${account.applicationId}-${account.accountNumber}`}
      />
      <DetailsSectionItem label='Account to Charge' info={chargeAccountCode} />
    </DetailsSectionItemsRow>
  )
}
