'use client'

import { formatAccountCode } from '@/app/[bankId]/accounts/accountHelpers'
import { Tab } from '@/components/Tabs/Tab'
import { TabList } from '@/components/Tabs/TabList'
import { data, tag } from '@/lib/unions/Union'
import { ChevronRightIcon } from '@heroicons/react/24/outline'
import Link from 'next/link'
import { usePathname, useRouter } from 'next/navigation'
import { useMemo } from 'react'
import { routeTo, useRoute } from './routing'
import { routeTo as routeToAccounts } from '../../routing'
import { useAccountFromParams } from '../../../_hooks/useAccountFromParams'
import { WarningRibbon } from '@/components/WarningRibbon'
import { MonthPicker } from '@/components/Filter/MonthPicker'
import { formatToServerString } from '@/lib/date'
import { AccountCode } from '@/api/formToApiSchema'

export default function ViewAccountLayout({
  children,
}: React.PropsWithChildren) {
  const router = useRouter()
  const pathname = usePathname()
  const route = useRoute()!
  const routeParams = data(route).params
  const [applicationId, bankNumber, accountNumber] =
    routeParams.accountCode.split('-')

  const { account, isLoading, isError, error } =
    useAccountFromParams(routeParams)

  const tabs = useMemo(() => {
    return [
      {
        href: routeTo(
          '/accounts/[effectiveMonth]/view/[accountCode]/statements',
          routeParams,
        ),
        key: 'statements',
        label: 'Statements',
      },
      {
        href: routeTo(
          '/accounts/[effectiveMonth]/view/[accountCode]/details',
          routeParams,
        ),
        key: 'details',
        label: 'Details',
      },
      {
        href: routeTo(
          '/accounts/[effectiveMonth]/view/[accountCode]/relationships',
          routeParams,
        ),
        key: 'relationships',
        label: 'Relationships',
      },
      {
        href: routeTo(
          '/accounts/[effectiveMonth]/view/[accountCode]/settlement-and-processing',
          routeParams,
        ),
        key: 'settlement-and-processing',
        label: 'Settlement and Processing',
      },
      {
        href: routeTo(
          '/accounts/[effectiveMonth]/view/[accountCode]/service-overrides/account-only',
          routeParams,
        ),
        key: 'service-overrides',
        label: 'Service overrides',
      },
      {
        href: routeTo(
          '/accounts/[effectiveMonth]/view/[accountCode]/service-pricing',
          routeParams,
        ),
        key: 'service-pricing',
        label: 'Service pricing',
      },
      {
        href: routeTo(
          '/accounts/[effectiveMonth]/view/[accountCode]/statement-package',
          routeParams,
        ),
        key: 'statement-package',
        label: 'Statement package',
      },
    ]
  }, [routeParams])

  if (isError && error) {
    console.error(error)
    return `[${error.name}] ${error.message}`
  }

  if (!account && !isLoading) {
    return <>Not Found</>
  }

  return (
    <div className='flex min-h-0 flex-1 flex-col overflow-hidden p-8'>
      <div className='mb-4 flex items-center gap-4'>
        <Link href={routeToAccounts('/accounts/[effectiveMonth]', routeParams)}>
          <span className='text-xl text-gray-500'>All accounts</span>
        </Link>
        <ChevronRightIcon width={20} height={20} />
        <div className='flex gap-2'>
          <span className='font-bold'>{account?.shortName}</span>
          <span>
            {formatAccountCode({
              applicationId,
              accountNumber,
            } as AccountCode)}
          </span>
        </div>
        <MonthPicker
          className='ml-auto'
          title='Effective date'
          prefix='View data effective on'
          showIcon={true}
          onDateChange={(effectiveMonth) => {
            router.push(
              routeTo(tag(route), {
                ...routeParams,
                effectiveMonth,
              }),
            )
          }}
          initialDate={formatToServerString(routeParams.effectiveMonth)}
        />
      </div>
      <TabList>
        {tabs.map((tab) => (
          <Tab
            key={tab.href}
            href={tab.href}
            active={pathname.includes(tab.key)}
          >
            {tab.label}
          </Tab>
        ))}
      </TabList>
      {account && account.applicationId === 'C' && !account.keyAccountCode && (
        <WarningRibbon message="This composite account doesn't currently have a key account" />
      )}
      <div className='overflow-auto'>{children}</div>
    </div>
  )
}
