'use client'

import { accountQueries } from '@/app/[bankId]/accounts/queries'
import { Button } from '@/components/Button'

import {
  InfoSection,
  InfoSectionDescription,
  InfoSectionTitle,
} from '@/components/InfoSection'
import { SortedTable } from '@/components/Table/SortedTable'
import { useQuery } from '@tanstack/react-query'
import { routeTo } from '../routing'
import { routeTo as routeToCreate } from '../../../create/[accountCode]/routing'
import { useRoute } from '../routing'
import { data } from '@/lib/unions/Union'
import { useAccountFromParams } from '@/app/[bankId]/accounts/_hooks/useAccountFromParams'
import { useMemo } from 'react'
import { ColumnDef } from '@tanstack/react-table'
import { formatAddress } from '@/app/[bankId]/accounts/accountHelpers'
import { listSelector } from '@/api/selectors'
import { Address, PackageDeliveryLabels } from '@/app/[bankId]/accounts/types'
import { toUIFormat } from '@/lib/date'
import { apiToFormSchemas } from '@/api/apiToFormSchema'
import { z } from 'zod'
import Link from 'next/link'

type StatementPackageListColumnDef = ColumnDef<
  z.infer<typeof apiToFormSchemas.hydratedStatementPackage>
>

export default function Page() {
  const route = useRoute()!
  const routeParams = data(route).params
  const { accountCode, effectiveDate } = useAccountFromParams(routeParams)


  const {
    data: statementPackages,
    isLoading,
    isError,
    error,
  } = useQuery({
    ...accountQueries('/listStatementPackages', {
      accountNumber: accountCode.accountNumber,
      applicationId: accountCode.applicationId,
      bankNumber: accountCode.bankNumber,
      effectiveDate,
    }),
    select: listSelector(apiToFormSchemas.hydratedStatementPackage),
  })

  const columns = useMemo<StatementPackageListColumnDef[]>(() => {
    return [
      {
        header: 'Code',
        accessorKey: 'statementPackage.statementPackageNumber',
        meta: {
          className: 'basis-3/12 px-6',
        },
      },
      {
        header: 'Delivery method',
        meta: {
          className: 'basis-3/12',
        },
        cell: ({ row }) => (
          <>
            {
              PackageDeliveryLabels[
                row.original.statementPackage.packageDelivery
              ]
            }
          </>
        ),
      },
      {
        header: 'Delivery address',
        cell: ({ row }) => {
          return (
            <div className='flex flex-col'>
              <span>{formatAddress(row.original.address as Address)}</span>
              <span className='text-gray-500'>
                {row.original.address.applicationId} -
                {row.original.address.addressNumber}
              </span>
            </div>
          )
        },
      },
      {
        header: 'Effective date',
        cell: ({ row }) =>
          toUIFormat(row.original.statementPackage.effectiveDate),
      },
    ]
  }, [])

  if (isLoading) {
    return <>Loading...</>
  }

  if (isError) {
    return <>{error.message}</>
  }

  if (!statementPackages) {
    return <>There was an error</>
  }

  return (
    <InfoSection>
      <div className='flex items-center justify-between pb-4'>
        <div>
          <InfoSectionTitle>Statement packages</InfoSectionTitle>
          <InfoSectionDescription>
            View statement packages for this composite account.
          </InfoSectionDescription>
        </div>
        <div className='flex gap-2'>
          <Button>Export</Button>
          <Link
            href={`${routeToCreate('/accounts/[effectiveMonth]/create/[accountCode]/statement-package', routeParams)}`}
          >
            <Button className='btn-primary text-white'>
              Add statement package
            </Button>
          </Link>
        </div>
      </div>
      <SortedTable
        rowLink={({ original }) => {
          return routeTo(
            '/accounts/[effectiveMonth]/view/[accountCode]/statement-package/[packageNumber]',
            {
              ...routeParams,
              packageNumber: `${original.statementPackage.statementPackageNumber}`,
            },
          )
        }}
        data={statementPackages}
        columns={columns}
      />
    </InfoSection>
  )
}
