'use client'

import { accountQueries } from '@/app/[bankId]/accounts/queries'
import { Button } from '@/components/Button'
import { InfoSection, InfoSectionTitle } from '@/components/InfoSection'
import { useQuery } from '@tanstack/react-query'
import { routeTo, useRoute } from '../../routing'

import { match } from '@/lib/unions/match'
import { useAccountFromParams } from '@/app/[bankId]/accounts/_hooks/useAccountFromParams'
import { useMemo } from 'react'
import {
  formatAddress,
  apiMappingWithKeyListSelector,
  mappingsToAccountsMap,
  toAccountCodeString,
} from '@/app/[bankId]/accounts/accountHelpers'
import {
  Address,
  PackageDeliveryLabels,
  PackageTypeLabels,
} from '@/app/[bankId]/accounts/types'
import {
  DetailsSectionItem,
  DetailsSectionItemsRow,
} from '@/components/DetailsSection'
import Link from 'next/link'
import { StatementPackageSelectedAccounts } from '@/app/[bankId]/accounts/_components/StatementPackageSelectedAccounts'
import { useState } from 'react'
import { Select } from '@/components/Input/Select'
import { formValidators } from '@/api/formToApiSchema'
import { enumOptions } from '@/lib/validation/enumOptions'
import { Button as HeadlessButton } from '@headlessui/react'

export default function Page() {
  const route = useRoute()!

  // Use match to properly handle the route union and extract packageNumber
  const { routeParams, packageNumber } = match(route, {
    '/accounts/[effectiveMonth]/view/[accountCode]/statement-package/[packageNumber]': (data: any) => ({
      routeParams: data.params,
      packageNumber: data.params.packageNumber,
    }),
    _: (data: any) => ({
      routeParams: data.params,
      packageNumber: '',
    }),
  })

  const { account, accountCode, effectiveDate } =
    useAccountFromParams(routeParams)

  const [packageType, setPackageType] = useState<
    | 'ALL_ACCOUNTS'
    | 'COMPOSITE_ACCOUNTS'
    | 'DEPOSIT_ACCOUNTS'
    | 'SELECTED_ACCOUNTS'
  >('ALL_ACCOUNTS')
  const [isEditing, setIsEditing] = useState(false)

  const {
    data: statementPackages,
    isLoading,
    isError,
    error,
  } = useQuery(
    accountQueries('/listStatementPackages', {
      accountNumber: accountCode.accountNumber,
      applicationId: accountCode.applicationId,
      bankNumber: accountCode.bankNumber,
      effectiveDate,
    }),
  )

  // Fetch account mappings to get subAccounts
  const {
    data: accountMappingData,
    isLoading: mappingsLoading,
    isError: mappingsError,
  } = useQuery({
    ...accountQueries('/getAccountMappings', {
      code: toAccountCodeString(accountCode),
      effectiveDate,
    }),
    select: apiMappingWithKeyListSelector,
    enabled: !!account,
  })

  // Create subAccounts from mappings
  const allSubAccounts = useMemo(() => {
    if (!accountMappingData || !account) return []

    const accountsMap = mappingsToAccountsMap([
      {
        child: account,
        parent: null,
      },
      ...accountMappingData,
    ])

    const accountCodeString = toAccountCodeString(accountCode)
    const accountWithChildren = accountsMap[accountCodeString]

    return accountWithChildren?.children || []
  }, [accountMappingData, account, accountCode])

  // Filter subAccounts based on packageType
  const filteredSubAccounts = useMemo(() => {
    if (packageType === 'ALL_ACCOUNTS') {
      return allSubAccounts
    }

    if (packageType === 'COMPOSITE_ACCOUNTS') {
      return allSubAccounts.filter((account) => account.applicationId === 'C')
    }

    if (packageType === 'DEPOSIT_ACCOUNTS') {
      return allSubAccounts.filter((account) => account.applicationId === 'D')
    }

    // For SELECTED_ACCOUNTS, show all accounts since we don't have selection logic in readonly mode
    return allSubAccounts
  }, [allSubAccounts, packageType])

  if (isLoading || mappingsLoading) {
    return <>Loading...</>
  }

  if (isError) {
    return <>{error.message}</>
  }

  if (mappingsError) {
    return <>Error loading account mappings</>
  }

  if (!statementPackages || !account) {
    return <>There was an error</>
  }

  debugger

  // TODO NEED /getStatementPackage by account code and statement package number
  const statementPackageDetail = statementPackages.find(
    (item) =>
      item.statementPackage.statementPackageNumber.toString() === packageNumber,
  )

  if (!statementPackageDetail) {
    return <>There was an error</>
  }

  return (
    <>
      <InfoSection>
        <div className='flex items-center justify-between pb-4'>
          <div>
            <InfoSectionTitle>Statement package information</InfoSectionTitle>
          </div>
          <div className='flex gap-2'>
            <Button>Delete</Button>
            <Link
            href={`${routeTo('/accounts/[effectiveMonth]/edit/[accountCode]/statement-package/[packageNumber]', {
              ...routeParams,
              packageNumber,
            })}`}
            >
              <Button className='btn-primary text-white'>Edit</Button>
            </Link>
          </div>
        </div>
        <DetailsSectionItemsRow>
          <DetailsSectionItem
            label='Effective date'
            info={statementPackageDetail.statementPackage.effectiveDate}
          />
        </DetailsSectionItemsRow>
        <DetailsSectionItemsRow>
          <DetailsSectionItem
            label='Package code'
            info={
              statementPackageDetail.statementPackage.statementPackageNumber
            }
          />
          <DetailsSectionItem
            label='Delivery method'
            info={
              PackageDeliveryLabels[
                statementPackageDetail.statementPackage.packageDelivery
              ]
            }
          />
        </DetailsSectionItemsRow>
        <DetailsSectionItemsRow>
          <DetailsSectionItem
            label='Delivery address'
            info={formatAddress(statementPackageDetail.address as Address)}
          />
        </DetailsSectionItemsRow>
        <DetailsSectionItemsRow>
          <DetailsSectionItem
            label='Include accounts'
            info={
              <div className='flex items-center gap-2'>
                {!isEditing ?
                  <>
                    <span>{PackageTypeLabels[packageType]}</span>
                    <HeadlessButton
                      className='text-sm font-medium text-app-color-button-primary-bg hover:underline'
                      onClick={() => setIsEditing(true)}
                    >
                      Edit
                    </HeadlessButton>
                  </>
                : <div className='flex items-center items-baseline gap-2'>
                    <Select
                      name='packageType'
                      value={packageType}
                      className={'min-w-[200px]'}
                      required
                      options={enumOptions(
                        formValidators.statementPackage.shape.packageType,
                      )}
                      renderOption={(value) => PackageTypeLabels[value]}
                      renderSelected={(value) => PackageTypeLabels[value]}
                      onChange={(value) => {
                        setPackageType(value as typeof packageType)
                        setIsEditing(false)
                      }}
                    />
                    <HeadlessButton
                      className='text-sm font-medium text-gray-500 hover:underline'
                      onClick={() => setIsEditing(false)}
                    >
                      Cancel
                    </HeadlessButton>
                  </div>
                }
              </div>
            }
          />
        </DetailsSectionItemsRow>
      </InfoSection>
      <div className='mt-6'>
        <StatementPackageSelectedAccounts
          accountCode={accountCode}
          shortName={account.shortName}
          subAccounts={filteredSubAccounts}
          readonly={true}
          showKeyAccountBadge={true}
        />
      </div>
    </>
  )
}
