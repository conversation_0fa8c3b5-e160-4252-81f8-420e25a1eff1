'use client'

import { data } from '@/lib/unions/Union'
import { useRoute } from './routing'
import { useAccountFromParams } from '../../../_hooks/useAccountFromParams'
import { EditAccountHeader } from './_components/EditAccountHeader'
import { WarningRibbon } from '@/components/WarningRibbon'

export default function EditAccountLayout({
  children,
}: React.PropsWithChildren) {
  const route = useRoute()!
  const routeParams = data(route).params
  const account = useAccountFromParams(routeParams).account

  if (!account) return null

  return (
    <div className='flex flex-auto flex-col gap-3 overflow-y-scroll pt-8'>
      <EditAccountHeader account={account} />
      {account.applicationId === 'C' && !account.keyAccountCode && (
        <WarningRibbon message="This composite account doesn't currently have a key account" />
      )}
      <div className='mt-6 h-full'>{children}</div>
    </div>
  )
}
