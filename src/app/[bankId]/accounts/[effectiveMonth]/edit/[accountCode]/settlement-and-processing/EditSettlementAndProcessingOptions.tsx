'use client'

import {
  Account,
  AccountSearchItem,
  AccountCode,
  SettlementOption,
} from '@/app/[bankId]/accounts/types'
import { AccountTypeOverrideForm } from '@/api/formToApiSchema'
import { useFormMonthPicker } from '@/components/Form/useFormMonthPicker'
import { useFormSelect } from '@/components/Form/useFormSelect'
import { routeTo, useRoute } from '../routing'
import { useRouter } from 'next/navigation'
import { data } from '@/lib/unions/Union'
import { useState } from 'react'
import { ReactFormExtendedApi } from '@tanstack/react-form'
import { useQuery } from '@tanstack/react-query'
import { accountQueries } from '@/app/[bankId]/accounts/queries'
import {
  apiAccountSelector,
  searchMatchingAccounts,
  accountCodeToString,
  toAccountCodeString,
  formatAccountDisplay,
} from '@/app/[bankId]/accounts/accountHelpers'
import { ComboboxOption } from '@headlessui/react'
import { SearchCombo } from '@/app/[bankId]/accounts/(compositeAccount)/add/SearchCombo'

interface EditSettlementAndProcessingOptionsProps {
  form: ReactFormExtendedApi<AccountTypeOverrideForm>
  effectiveDate: string
  account: Account
}

export default function EditSettlementAndProcessingOptions({
  form,
  effectiveDate,
  account,
}: EditSettlementAndProcessingOptionsProps) {
  const router = useRouter()
  const route = useRoute()!
  const routeParams = data(route).params

  const { data: accounts } = useQuery({
    ...accountQueries('/getAccounts', {
      effectiveDate,
    }),
    select: (apiAccounts) =>
      apiAccounts.map((apiAccount) => ({
        ...apiAccountSelector(apiAccount),
        // don't actually need key account code for parentless open accounts,
        // but adding here for type safety
        keyAccountCode: null,
      })),
  })

  const settlementOptions: SettlementOption[] = [
    {
      label: formatAccountDisplay(account),
      value: true,
    },
    {
      label: 'Settle at child account level',
      value: false,
    },
  ]

  const getSettlementOptionLabel = (value: boolean | null): string => {
    const option = settlementOptions.find((opt) => opt.value === value)
    return option?.label || ''
  }

  const FormMonthPicker = useFormMonthPicker<AccountTypeOverrideForm>({
    form,
  })

  const Select = useFormSelect<AccountTypeOverrideForm>({
    form,
  })

  const [selectedAccountToCharge, setSelectedAccountToCharge] = useState<
    AccountSearchItem | undefined
  >()

  const getFilteredAccountsForCharge = (): Account[] => {
    if (!accounts) return []

    const currentSettlementAccount = form.getFieldValue(
      'isOverrideAsSettlementAccount',
    )

    if (account.applicationId === 'D' && currentSettlementAccount) {
      // if deposit account and settle at self, show all other deposit accounts
      return accounts.filter(
        (acc) => acc.applicationId === account.applicationId,
      )
    } else if (account.applicationId === 'C') {
      // if composite account, selecting a deposit is mandatory, so show all deposit accounts
      return accounts.filter((acc) => acc.applicationId === 'D')
    }

    return accounts
  }

  const handleSettlementAccountChange = (value: boolean | null) => {
    if (account.applicationId === 'D' && value) {
      // if deposit account and settle at self, set to current account
      form.setFieldValue('chargeAccountCode', toAccountCodeString(account))
      setSelectedAccountToCharge(account as AccountSearchItem)
    } else if (account.applicationId === 'C') {
      // if composite account, clear the selection
      form.setFieldValue('chargeAccountCode', null)
      setSelectedAccountToCharge(undefined)
    }
  }

  return (
    <div className='mt-4 flex flex-col gap-6'>
      <div className='flex gap-9'>
        <div className='flex flex-1 flex-col'>
          <FormMonthPicker
            name='effectiveDate'
            label='Effective date'
            required
            onChange={(effectiveMonth) => {
              router.push(
                routeTo(
                  '/accounts/[effectiveMonth]/edit/[accountCode]/settlement-and-processing',
                  { ...routeParams, effectiveMonth },
                ),
              )
            }}
          />
        </div>
        <div className='flex-1'></div>
      </div>
      <div className='flex gap-9'>
        <Select
          name='isOverrideAsSettlementAccount'
          label='Settlement Account'
          placeholder='No key account selected'
          required
          options={[true, false]}
          onChange={(value) => {
            handleSettlementAccountChange(value)
          }}
          renderOption={(value) => getSettlementOptionLabel(value)}
          renderSelected={(currentValue) => (
            <div>{getSettlementOptionLabel(currentValue)}</div>
          )}
        ></Select>

        <div className='flex-1'>
          <div className='flex flex-col'>
            <label className='mb-1 text-sm font-medium'>
              Account to Charge *
            </label>
            <SearchCombo
              displayValue={(selectedItem) =>
                selectedItem ?
                  formatAccountDisplay(selectedItem as Account)
                : ''
              }
              setSelectedItem={setSelectedAccountToCharge}
              placeholder='Search for account to charge'
              noResultsMessage='No accounts found.'
              onSearch={(searchQuery: string) => {
                const filteredAccounts = getFilteredAccountsForCharge()
                return searchMatchingAccounts(
                  searchQuery,
                  filteredAccounts,
                ).slice(0, 20)
              }}
              onSelection={(selectedItem: AccountSearchItem | undefined) => {
                if (selectedItem) {
                  form.setFieldValue(
                    'chargeAccountCode',
                    accountCodeToString(selectedItem as AccountCode),
                  )
                  setSelectedAccountToCharge(selectedItem)
                } else {
                  form.setFieldValue('chargeAccountCode', null)
                  setSelectedAccountToCharge(undefined)
                }
              }}
              renderOption={(item: AccountSearchItem) => {
                return (
                  <ComboboxOption
                    key={`dropdown-${item.accountNumber}-${item.applicationId}`}
                    value={item}
                    className='cursor-default select-none px-4 py-2 data-[focus]:bg-app-color-bg-brand-secondary data-[focus]:outline-none'
                  >
                    <div>{formatAccountDisplay(item as Account)}</div>
                  </ComboboxOption>
                )
              }}
            />
          </div>
        </div>
      </div>
    </div>
  )
}
