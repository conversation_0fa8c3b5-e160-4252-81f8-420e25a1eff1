'use client'

import {
  InfoSectionDescription,
  InfoSectionTitle,
} from '@/components/InfoSection'
import { EditAccountContent } from '../_components/EditAccountContent'
import { useRoute } from '../routing'
import { data } from '@/lib/unions/Union'
import { useAccountFromParams } from '@/app/[bankId]/accounts/_hooks/useAccountFromParams'

import { UserFieldListItem } from '@/app/[bankId]/accounts/types'
import {
  AccountTypeOverrideForm,
  formToApiSchemas,
  formValidators,
} from '@/api/formToApiSchema'
import { useForm } from '@tanstack/react-form'
import { useMutation } from '@tanstack/react-query'
import { accountMutation } from '@/app/[bankId]/accounts/mutations'
import { useRouter } from 'next/navigation'
import { routeTo as routeToViewAccount } from '../../../view/[accountCode]/routing'

import {
  accountToAccountCode,
  getUserFieldsListItemsFromConfigsAndSelections,
} from '@/app/[bankId]/accounts/accountHelpers'
import { useMemo } from 'react'
import { useUserFieldsRelatedEntities } from '@/app/[bankId]/accounts/_hooks/useUserFieldsRelatedEntities'
import { EditAccountFooter } from '../_components/EditAccountFooter'
import { useQuery } from '@tanstack/react-query'
import { accountQueries } from '@/app/[bankId]/accounts/queries'
import { toUTCDateString } from '@/lib/date'
import { components } from '@/api/schema'

import EditSettlementAndProcessingOptions from './EditSettlementAndProcessingOptions'
import EditSettlementOptions from './EditSettlementOptions'
import { EditProcessingOptions } from './EditProcessingOptions'

export default function Page() {
  const router = useRouter()
  const route = useRoute()!
  const routeParams = data(route).params
  const accountFromParams = useAccountFromParams(routeParams)
  const account = accountFromParams.account!
  const accountCode = accountToAccountCode(account)
  const effectiveDate = accountFromParams.effectiveDate

  // Fetch current accountTypeOverride data to pre-populate form
  const { data: currentAccountTypeOverride } = useQuery<
    components['schemas']['AccountTypeOverride']
  >(
    accountQueries('/getAccountTypeOverride', {
      bankNumber: account.bankNumber,
      applicationId: account.applicationId,
      accountNumber: account.accountNumber,
      effectiveDate: toUTCDateString(routeParams.effectiveMonth),
    }),
  )

  const { mutate: updateSettlementAndProcessingOptions } = useMutation(
    accountMutation('/updateSettlementProcessingOptions', {
      onSuccess: () => {
        router.push(
          routeToViewAccount(
            '/accounts/[effectiveMonth]/view/[accountCode]/settlement-and-processing',
            routeParams,
          ),
        )
      },
    }),
  )

  const { userFieldsConfigurations, userFieldSelections } =
    useUserFieldsRelatedEntities({
      effectiveDate,
      applicationId: account.applicationId,
      accountNumber: account.accountNumber,
      bankNumber: account.bankNumber,
    })

  useMemo<UserFieldListItem[]>(() => {
    return getUserFieldsListItemsFromConfigsAndSelections(
      accountCode,
      userFieldsConfigurations.data,
      userFieldSelections.data,
    ).map((originalUserField) => {
      const userField = { ...originalUserField }
      userField.selection.effectiveDate = effectiveDate
      return userField
    })
  }, [
    userFieldsConfigurations.data,
    userFieldSelections.data,
    effectiveDate,
    accountCode,
  ])

  const form = useForm<AccountTypeOverrideForm>({
    defaultValues: {
      accountCode: accountToAccountCode(account),
      effectiveDate: accountFromParams.effectiveDate,
      isOverrideAsSettlementAccount:
        currentAccountTypeOverride?.isOverrideAsSettlementAccount ?? true,
      chargeAccountCode: currentAccountTypeOverride?.chargeAccountCode ?? '',
      analysisResultOptionsPlanCode:
        currentAccountTypeOverride?.analysisResultOptionsPlanCode ?? null,
      balanceRequirementDefinitionCode:
        currentAccountTypeOverride?.balanceRequirementDefinitionCode ?? null,
      earningsCreditDefinitionCode:
        currentAccountTypeOverride?.earningsCreditDefinitionCode ?? null,
      interestRequirementDefinitionCode:
        currentAccountTypeOverride?.interestRequirementDefinitionCode ?? null,
      investableBalanceDefinitionCode:
        currentAccountTypeOverride?.investableBalanceDefinitionCode ?? null,
      reserveRequirementDefinitionCode:
        currentAccountTypeOverride?.reserveRequirementDefinitionCode ?? null,
      settlementCyclePlanCode:
        currentAccountTypeOverride?.settlementCyclePlanCode ?? null,
      statementCyclePlanCode:
        currentAccountTypeOverride?.statementCyclePlanCode ?? null,
      statementFormatPlanCode:
        currentAccountTypeOverride?.statementFormatPlanCode ?? null,
      statementMessagePlanCode:
        currentAccountTypeOverride?.statementMessagePlanCode ?? null,
      analysisResultOptionsPlanCodeExpiry:
        currentAccountTypeOverride?.analysisResultOptionsPlanCodeExpiry ?? null,
      balanceRequirementDefinitionCodeExpiry:
        currentAccountTypeOverride?.balanceRequirementDefinitionCodeExpiry ??
        null,
      earningsCreditDefinitionCodeExpiry:
        currentAccountTypeOverride?.earningsCreditDefinitionCodeExpiry ?? null,
      interestRequirementDefinitionCodeExpiry:
        currentAccountTypeOverride?.interestRequirementDefinitionCodeExpiry ??
        null,
      investableBalanceDefinitionCodeExpiry:
        currentAccountTypeOverride?.investableBalanceDefinitionCodeExpiry ??
        null,
      reserveRequirementDefinitionCodeExpiry:
        currentAccountTypeOverride?.reserveRequirementDefinitionCodeExpiry ??
        null,
      settlementCyclePlanCodeExpiry:
        currentAccountTypeOverride?.settlementCyclePlanCodeExpiry ?? null,
      statementCyclePlanCodeExpiry:
        currentAccountTypeOverride?.statementCyclePlanCodeExpiry ?? null,
      statementFormatPlanCodeExpiry:
        currentAccountTypeOverride?.statementFormatPlanCodeExpiry ?? null,
      statementMessagePlanCodeExpiry:
        currentAccountTypeOverride?.statementMessagePlanCodeExpiry ?? null,
    },
    validators: {
      onChange: formValidators.accountTypeOverride,
    },
    onSubmit: ({ value }) => {
      updateSettlementAndProcessingOptions(
        formToApiSchemas.accountTypeOverride.parse({
          ...value,
          accountTypeOverride: account,
        }),
      )
    },
  })

  return (
    <form
      className='flex h-full flex-col'
      onSubmit={(event) => {
        event.preventDefault()
        event.stopPropagation()
        form.handleSubmit()
      }}
    >
      <EditAccountContent>
        <InfoSectionTitle>Configure Settlement and Processing</InfoSectionTitle>
        <InfoSectionDescription className='-mt-3'>
          {`You can override the following settings for your composite account ${account.applicationId === 'C' ? 'composite' : 'deposit'} account.`}
        </InfoSectionDescription>
        <EditSettlementAndProcessingOptions
          form={form}
          effectiveDate={effectiveDate}
          account={account}
        />
        <EditSettlementOptions
          form={form}
          effectiveDate={effectiveDate}
          accountCode={routeParams.accountCode}
        />
        <EditProcessingOptions
          form={form}
          effectiveDate={effectiveDate}
          accountCode={routeParams.accountCode}
        />
      </EditAccountContent>

      <EditAccountFooter form={form} />
    </form>
  )
}
