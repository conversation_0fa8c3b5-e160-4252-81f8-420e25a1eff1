'use client'

import {
  InfoSectionDescription,
  InfoSubSectionTitle,
} from '@/components/InfoSection'
import { useMemo, useState } from 'react'
import { SortedTable } from '@/components/Table/SortedTable'
import { useStore } from '@tanstack/react-form'
import { formatToServerString, toUTCDateString } from '@/lib/date'
import { ToggleSwitch } from '@/components/Toggle'
import { useQuery } from '@tanstack/react-query'
import { accountQueries } from '@/app/[bankId]/accounts/queries'
import { transformToSettlementOptions } from '@/app/[bankId]/accounts/accountHelpers'
import { SelectSettlementOption } from '@/components/SettlementOptions/SelectSettlementOption'
import { SettlementOptionExpirationDate } from '@/components/SettlementOptions/SettlementOptionExpirationDate'
import { useSettlementOptionsData } from '@/components/SettlementOptions/useSettlementOptionsData'
import {
  SettlementOptionItem,
  SetSettlementOptionsProps,
  settlementOptions,
  SettlementOptionsColumnDef,
} from '@/app/[bankId]/accounts/types'

export default function EditSettlementOptions({
  form,
  effectiveDate,
  accountCode,
}: SetSettlementOptionsProps) {
  // The edit page uses a flat form structure where fields are at the root level
  const [formValues] = useStore(form.store, (state) => [state.values])

  // Parse accountCode to get individual components
  const [applicationId, bankNumber, accountNumber] = accountCode.split('-') as [
    'C' | 'D',
    string,
    string,
  ]

  // Fetch accountTypeOverride data to get default values
  const { data: accountTypeOverride } = useQuery(
    accountQueries('/getAccountTypeOverride', {
      bankNumber,
      applicationId,
      accountNumber,
      effectiveDate,
    }),
  )

  // Memoize the default settlement options to prevent recreation on every render
  const defaultSettlementOptions = useMemo(() => {
    return transformToSettlementOptions(accountTypeOverride)
  }, [accountTypeOverride])

  // Calculate toggle states based on current form values and default options
  const toggleStates = useMemo(() => {
    const states: Record<string, boolean> = {}
    settlementOptions.forEach((option) => {
      const fieldValue = formValues?.[option.field]
      const defaultOption = defaultSettlementOptions.find(
        (opt) => opt.field === option.field,
      )

      // Check if override should be enabled:
      // 1. If field already has a value, enable override
      // 2. If default value has a non-null plan, enable override
      const hasFieldValue =
        fieldValue !== null && fieldValue !== undefined && fieldValue !== ''
      const hasDefaultPlan =
        defaultOption?.planCode !== null &&
        defaultOption?.planCode !== undefined

      states[option.field] = hasFieldValue || hasDefaultPlan
    })
    return states
  }, [formValues, defaultSettlementOptions])

  // State to track user manual toggle changes (overrides the calculated state)
  const [manualToggleOverrides, setManualToggleOverrides] = useState<
    Record<string, boolean | undefined>
  >({})

  // Use shared settlement options data hook
  const { allOptionsMaps, isLoading: isLoadingSettlementOptions } =
    useSettlementOptionsData({
      effectiveDate,
    })

  const columns = useMemo<SettlementOptionsColumnDef[]>(() => {
    return [
      {
        header: 'Override',
        meta: {
          className: 'w-30  ml-6 mr-6',
        },
        cell: ({ row }) => {
          const field = row.original.field as SettlementOptionItem['field']
          // Use calculated toggle state, but allow manual override
          const calculatedState = toggleStates[field] || false
          const manualOverride = manualToggleOverrides[field]
          const isToggleEnabled =
            manualOverride !== undefined ? manualOverride : calculatedState

          return (
            <ToggleSwitch
              key={`${field}-toggle`}
              checked={isToggleEnabled}
              onChange={(checked: boolean) => {
                // Update manual toggle override
                setManualToggleOverrides((prev) => ({
                  ...prev,
                  [field]: checked,
                }))

                if (checked) {
                  // When enabling override, set the default plan code if available, otherwise empty
                  const defaultOption = defaultSettlementOptions.find(
                    (opt) => opt.field === field,
                  )
                  const defaultPlanCode = defaultOption?.planCode || ''
                  form.setFieldValue(field, defaultPlanCode)
                } else {
                  // If disabling override, clear the field value and expiry
                  form.setFieldValue(field, null)
                  form.setFieldValue(`${field}Expiry`, null)
                }
              }}
            />
          )
        },
      },
      {
        header: 'Settlement setting',
        accessorKey: 'name',
        meta: {
          className: 'ml-4 basis-full',
        },
      },
      {
        header: 'Plan',
        meta: {
          className: 'basis-full',
        },
        cell: ({ row }) => {
          const field = row.original.field as SettlementOptionItem['field']
          const fieldValue = formValues?.[field]
          const optionsMap = allOptionsMaps.get(field)
          // Use calculated toggle state, but allow manual override
          const calculatedState = toggleStates[field] || false
          const manualOverride = manualToggleOverrides[field]
          const isToggleEnabled =
            manualOverride !== undefined ? manualOverride : calculatedState

          // Get default value from accountTypeOverride
          const defaultOption = defaultSettlementOptions.find(
            (opt) => opt.field === field,
          )

          if (!optionsMap) {
            return <div className='min-w-80'>Loading options...</div>
          }

          return (
            <div className='min-w-80'>
              {isToggleEnabled ?
                <SelectSettlementOption
                  field={field}
                  value={fieldValue || ''}
                  optionsMap={optionsMap}
                  disabled={!isToggleEnabled}
                  defaultValue={defaultOption?.plan || 'Default'}
                  onChange={(value) => {
                    // Always allow changes when component is not disabled
                    form.setFieldValue(field, value)
                  }}
                />
              : <div>{defaultOption?.plan || 'Default'}</div>}
            </div>
          )
        },
      },
      {
        header: 'Expiration date',
        meta: {
          className: 'ml-4 basis-full',
        },
        cell: ({ row }) => {
          const field = row.original.field
          const expiryValue = formValues?.[`${field}Expiry`]
          // Use calculated toggle state, but allow manual override
          const calculatedState = toggleStates[field] || false
          const manualOverride = manualToggleOverrides[field]
          const isToggleEnabled =
            manualOverride !== undefined ? manualOverride : calculatedState

          return (
            <SettlementOptionExpirationDate
              isVisible={isToggleEnabled}
              expiry={expiryValue || undefined}
              disabled={!isToggleEnabled}
              onCheckboxChange={(checked: boolean) => {
                // Always allow changes when component is not disabled
                form.setFieldValue(
                  `${field}Expiry`,
                  checked ? null : toUTCDateString(new Date()),
                )
              }}
              onMonthPickerChange={(date: string) => {
                // Always allow changes when component is not disabled
                form.setFieldValue(`${field}Expiry`, formatToServerString(date))
              }}
            />
          )
        },
      },
    ]
  }, [
    allOptionsMaps,
    formValues,
    form,
    toggleStates,
    manualToggleOverrides,
    setManualToggleOverrides,
    defaultSettlementOptions,
  ])

  // Show loading state if data is not yet available
  if (isLoadingSettlementOptions) {
    return (
      <>
        <InfoSubSectionTitle>Settlement options</InfoSubSectionTitle>
        <InfoSectionDescription className='-mt-3'>
          These options are inherited from the account type of the key account.
        </InfoSectionDescription>
        <div>Loading settlement options...</div>
      </>
    )
  }

  // Show loading state if accountTypeOverride is not available
  // Note: Removed the loading check as it was causing infinite loading
  // The form should always have default values initialized

  return (
    <>
      <InfoSubSectionTitle>Settlement options</InfoSubSectionTitle>
      <InfoSectionDescription className='-mt-3'>
        These options are inherited from the account type of the key account.
      </InfoSectionDescription>

      <SortedTable
        data={settlementOptions}
        columns={columns}
        columnFilters={[]}
      />
    </>
  )
}
