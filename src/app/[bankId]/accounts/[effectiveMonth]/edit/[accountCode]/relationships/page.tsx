'use client'

import { useAccountFromParams } from '@/app/[bankId]/accounts/_hooks/useAccountFromParams'
import { accountQueries } from '@/app/[bankId]/accounts/queries'
import {
  InfoSection,
  InfoSectionDescription,
  InfoSectionTitle,
} from '@/components/InfoSection'
import { data } from '@/lib/unions/Union'
import { useQuery } from '@tanstack/react-query'
import { useRouter } from 'next/navigation'
import { EditRelationshipsContent } from '../_components/edit-relationships/EditRelationshipsContent'
import { useRoute } from '../routing'
import {
  accountCodeToString,
  apiMappingWithKeyListSelector,
} from '@/app/[bankId]/accounts/accountHelpers'
import { formatToServerString } from '@/lib/date'
import { HydratedAccountWithKeyAccountMappingForm } from '@/api/formToApiSchema'

export default function Page() {
  const route = useRoute()!
  const routeParams = data(route).params
  const accountFromParams = useAccountFromParams({
    ...routeParams,
    effectiveMonth: routeParams.effectiveMonth,
  })
  const [applicationId, bankNumber, accountNumber] =
    routeParams.accountCode.split('-') as ['C' | 'D', string, string]

  const accountCode = {
    applicationId,
    bankNumber,
    accountNumber,
  }
  const {
    data: accountMappingData,
    isLoading,
    isError,
    error,
  } = useQuery({
    ...accountQueries('/getAccountMappings', {
      code: accountCodeToString(accountCode),
      effectiveDate: formatToServerString(routeParams.effectiveMonth),
    }),
    select: apiMappingWithKeyListSelector,
  })
  const { data: leadAccountCode, isLoading: isLeadAccountCodeLoading } =
    useQuery(
      accountQueries('/getLeadCompositeAccountCode', {
        code: accountCodeToString(accountCode),
        effectiveDate: formatToServerString(routeParams.effectiveMonth),
      }),
    )
  // used as default in case no relationships found using account mapping

  if (isLoading || isLeadAccountCodeLoading) {
    return <>Loading...</>
  }

  if (isError) {
    return <>{error.message}</>
  }

  if (!accountFromParams.account) {
    return <>No Accounts Found</>
  }

  const initialNode: HydratedAccountWithKeyAccountMappingForm[] = [
    {
      child: {
        accountNumber: accountFromParams.account.accountNumber,
        bankNumber: accountFromParams.account.bankNumber,
        applicationId: accountFromParams.account.applicationId,
        shortName: accountFromParams.account.shortName,
        analysisAccountTypeCode:
          accountFromParams.account.analysisAccountTypeCode,
        branchCode: accountFromParams.account.branchCode,
        currencyCode: accountFromParams.account.currencyCode,
        effectiveDate: accountFromParams.account.effectiveDate,
        openDate: accountFromParams.account.openDate,
        primaryOfficerCode: accountFromParams.account.primaryOfficerCode,
        depositCategory: null,
        depositAccountTypeCode: null,
        secondaryOfficerCode: null,
        treasuryOfficerCode: null,
        accountStatus: null,
        closeDate: null,
        costCenter: '',
        customerSpecificPricingIndicator: null,
        processingIndicator: null,
        keyAccountCode: null,
        isKeyAccount: null,
      },
      parent: null,
    },
  ]

  return (
    <InfoSection className='mx-8 mb-auto'>
      <InfoSectionTitle>Account relationship</InfoSectionTitle>
      <InfoSectionDescription className='-mt-3'>
        Select deposit and composite accounts to add or remove as children of
        this account.
      </InfoSectionDescription>
      <EditRelationshipsContent
        leadAccountCode={leadAccountCode ?? accountFromParams.accountCode}
        accountMapping={accountMappingData ?? initialNode}
      />
    </InfoSection>
  )
}
