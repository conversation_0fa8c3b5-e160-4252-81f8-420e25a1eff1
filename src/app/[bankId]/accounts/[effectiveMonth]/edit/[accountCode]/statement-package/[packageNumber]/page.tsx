'use client'

import { useQuery } from '@tanstack/react-query'
import { match } from '@/lib/unions/match'
import { useRoute } from '../../routing'
import { routeTo as routeToView } from '@/app/[bankId]/accounts/[effectiveMonth]/view/[accountCode]/routing'
import { useAccountFromParams } from '@/app/[bankId]/accounts/_hooks/useAccountFromParams'
import { accountQueries } from '@/app/[bankId]/accounts/queries'
import { StatementPackageFormSchema } from '@/app/[bankId]/accounts/types'
import { EditStatementPackageForm } from './EditStatementPackageForm'
import { listSelector } from '@/api/selectors'
import { apiToFormSchemas } from '@/api/apiToFormSchema'

export default function EditStatementPackagePage() {
  const route = useRoute()!

  // Use match to properly handle the route union and extract packageNumber
  const { routeParams, packageNumber } = match(route, {
    '/accounts/[effectiveMonth]/edit/[accountCode]/statement-package/[packageNumber]': (data: any) => ({
      routeParams: data.params,
      packageNumber: data.params.packageNumber,
    }),
    _: (data: any) => ({
      routeParams: data.params,
      packageNumber: '',
    }),
  })

  const { accountCode, effectiveDate } = useAccountFromParams(routeParams)

  const {
    data: statementPackages,
    isLoading,
    isError,
    error,
  } = useQuery({
    ...accountQueries('/listStatementPackages', {
      accountNumber: accountCode.accountNumber,
      applicationId: accountCode.applicationId,
      bankNumber: accountCode.bankNumber,
      effectiveDate,
    }),
    select: listSelector(apiToFormSchemas.hydratedStatementPackage),
  })

  const { data: addresses, isLoading: isLoadingAddresses } = useQuery({
    ...accountQueries('/getAddresses', {
      accountNumber: accountCode.accountNumber,
      applicationId: accountCode.applicationId,
      bankNumber: accountCode.bankNumber,
      effectiveDate,
    }),
    select: listSelector(apiToFormSchemas.address),
  })
  debugger
  // Find the specific statement package
  const statementPackageDetail = statementPackages?.find(
    (item) =>
      item.statementPackage.statementPackageNumber.toString() === packageNumber,
  )
  debugger

  const handleSave = (_value: StatementPackageFormSchema) => {
    routeToView(
      '/accounts/[effectiveMonth]/view/[accountCode]/statement-package/[packageNumber]',
      {
        ...routeParams,
        packageNumber,
      },
    )
  }

  if (isLoading || isLoadingAddresses) {
    return <div>Loading...</div>
  }

  if (isError) {
    return <div className='text-red-600'>Error: {error?.message}</div>
  }

  if (!statementPackages || !statementPackageDetail || !addresses) {
    return <div>Statement package or addresses not found</div>
  }

  return (
    <EditStatementPackageForm
      statementPackageDetail={statementPackageDetail}
      addresses={addresses}
      statementPackages={statementPackages}
      onSave={handleSave}
    />
  )
}
