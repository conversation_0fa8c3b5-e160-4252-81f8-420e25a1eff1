'use client'
import SelectAccountsHierarchy from '@/app/[bankId]/accounts/_components/accounts-hierarchy/SelectAccountsHierarchy'

import { SelectAccountsList } from '@/app/[bankId]/accounts/_components/SelectAccountsList'
import { useAccountFromParams } from '@/app/[bankId]/accounts/_hooks/useAccountFromParams'
import {
  accountCodeToString,
  mappingsToAccountsMap,
} from '@/app/[bankId]/accounts/accountHelpers'
import { SelectAccountFormSchema } from '@/app/[bankId]/accounts/types'
import { useFormMonthPicker } from '@/components/Form/useFormMonthPicker'
import { SegmentedTab, SegmentedTabList } from '@/components/SegmentedTabList'
import { data } from '@/lib/unions/Union'
import { TabGroup, TabPanel, TabPanels } from '@headlessui/react'
import { useForm } from '@tanstack/react-form'
import { useRouter } from 'next/navigation'
import { routeTo, useRoute } from '../../routing'
import {
  AccountMutationProvider,
  SelectAccountModes,
} from '@/app/[bankId]/accounts/_context/AccountMutationContext'
import {
  AccountCode,
  HydratedAccountWithKeyAccountMappingForm,
} from '@/api/formToApiSchema'
import { ReactFlowProvider } from '@xyflow/react'

export const EditRelationshipsContent = ({
  leadAccountCode,
  accountMapping,
}: {
  leadAccountCode: AccountCode
  accountMapping: HydratedAccountWithKeyAccountMappingForm[]
}) => {
  const router = useRouter()

  const route = useRoute()!
  const routeParams = data(route).params
  const accountFromParams = useAccountFromParams(routeParams)
  const nestedAccounts = mappingsToAccountsMap([...(accountMapping ?? [])])

  const form = useForm<SelectAccountFormSchema>({
    defaultValues: {
      accountInfo: {
        ...accountFromParams.account!,
        effectiveDate: routeParams.effectiveMonth,
        useNextAvailableAccountNumber: false,
      },
      subAccounts:
        nestedAccounts?.[accountCodeToString(leadAccountCode)]?.children ?? [],
      demographics: {
        keyAccountCode: null,
        analysisAccountTypeCode: '',
        costCenter: '',
        branchCode: '',
        currencyCode: 'USD',
        depositAccountTypeCode: '',
        depositCategory: null,
        primaryOfficerCode: '',
        secondaryOfficerCode: null,
        treasuryOfficerCode: null,
        userFields: [],
        customerSpecificPricingIndicator: false,
      },
      skipKeyAccountSelection: false,
    },
  })

  const FormMonthPicker = useFormMonthPicker<SelectAccountFormSchema>({
    form,
  })

  return (
    <div className='mt-4 flex flex-col gap-6'>
      <div className='flex gap-9'>
        <div className='flex flex-1 flex-col'>
          <FormMonthPicker
            name='accountInfo.effectiveDate'
            label='Effective date'
            required
            onChange={(effectiveMonth) => {
              router.push(
                routeTo(
                  '/accounts/[effectiveMonth]/edit/[accountCode]/relationships',
                  {
                    ...routeParams,
                    effectiveMonth,
                  },
                ),
              )
            }}
          />
        </div>
      </div>
      <AccountMutationProvider
        mode={SelectAccountModes.EDIT}
        form={form}
        leadAccount={nestedAccounts?.[accountCodeToString(leadAccountCode)]}
      >
        <TabGroup className='flex flex-col gap-2'>
          <div className='flex flex-row gap-3'>
            <div>View</div>
            <SegmentedTabList>
              <SegmentedTab>List</SegmentedTab>
              <SegmentedTab>Graphical</SegmentedTab>
            </SegmentedTabList>
          </div>

          <TabPanels>
            <TabPanel>
              <SelectAccountsList form={form} />
            </TabPanel>
            <TabPanel>
              <ReactFlowProvider>
                <SelectAccountsHierarchy form={form} />
              </ReactFlowProvider>
            </TabPanel>
          </TabPanels>
        </TabGroup>
      </AccountMutationProvider>
    </div>
  )
}
