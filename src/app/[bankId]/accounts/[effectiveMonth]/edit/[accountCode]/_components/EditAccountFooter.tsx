'use client'

import { But<PERSON> } from '@/components/Button'
import { ReactFormExtendedApi } from '@tanstack/react-form'
import clsx from 'clsx'
import { useRouter } from 'next/navigation'

export function EditAccountFooter({
  form,
}: {
  form: ReactFormExtendedApi<any>
}) {
  const router = useRouter()
  return (
    <footer className='mt-4 flex justify-end gap-4 border-t bg-white px-12 py-10'>
      <Button
        className='btn mr-auto w-60'
        onClick={() => {
          router.back()
        }}
      >
        Cancel
      </Button>
      <form.Subscribe selector={(state) => state.canSubmit && state.isTouched}>
        {(enableSubmit) => (
          <Button
            type='submit'
            className={clsx(
              enableSubmit ? 'btn-primary' : 'btn-disabled',
              'w-60',
            )}
            disabled={!enableSubmit}
          >
            Save
          </Button>
        )}
      </form.Subscribe>
    </footer>
  )
}
