import { Account } from '@/app/[bankId]/accounts/types'
import { formatAccountCode } from '@/app/[bankId]/accounts/accountHelpers'

export function EditAccountHeader({ account }: { account: Account }) {
  return (
    <header className='mx-8'>
      <div className='text-2xl font-medium'>{`Edit ${account?.applicationId === 'C' ? 'composite' : 'deposit'} account`}</div>
      <div className='text-md flex gap-2'>
        <div className='font-medium'>{account.shortName}</div>
        <div className='text-app-color-secondary'>
          {formatAccountCode(account)}
        </div>
      </div>
    </header>
  )
}
