'use client'

import { useRoute, routeTo } from '../routing'
import { data } from '@/lib/unions/Union'
import { useAccountFromParams } from '@/app/[bankId]/accounts/_hooks/useAccountFromParams'
import { EditAccountContent } from '../_components/EditAccountContent'
import { useForm } from '@tanstack/react-form'
import {
  EditAccountInformationFormSchema,
  EditAccountInformationZodSchema,
} from '@/app/[bankId]/accounts/types'
import { useMutation } from '@tanstack/react-query'
import { accountMutation } from '@/app/[bankId]/accounts/mutations'
import { useRouter } from 'next/navigation'
import { routeTo as routeToViewAccount } from '../../../view/[accountCode]/routing'
import { useFormMonthPicker } from '@/components/Form/useFormMonthPicker'
import { useFormTextInput } from '@/components/Form/useFormTextInput'
import { TextInput } from '@/components/Input/TextInput'
import { formatAccountCode } from '@/app/[bankId]/accounts/accountHelpers'
import { EditAccountFooter } from '../_components/EditAccountFooter'
import {
  InfoSectionDescription,
  InfoSectionTitle,
} from '@/components/InfoSection'
import { formToApiSchemas } from '@/api/formToApiSchema'

export default function EditAccountInformation() {
  const router = useRouter()
  const route = useRoute()!
  const routeParams = data(route).params
  const accountFromParams = useAccountFromParams(routeParams)
  const account = accountFromParams.account!

  const { mutate: updateAccount } = useMutation(
    accountMutation('/updateAccount', {
      onSuccess: () => {
        router.push(
          routeToViewAccount(
            '/accounts/[effectiveMonth]/view/[accountCode]/details',
            routeParams,
          ),
        )
      },
    }),
  )

  const form = useForm<EditAccountInformationFormSchema>({
    defaultValues: {
      effectiveDate: accountFromParams.effectiveDate,
      shortName: account.shortName,
    },
    validators: {
      onChange: EditAccountInformationZodSchema,
    },
    onSubmit: async ({ value }) => {
      await updateAccount(
        formToApiSchemas.account.parse({
          ...account,
          ...value,
        }),
      )
    },
  })

  const FormMonthPicker = useFormMonthPicker<EditAccountInformationFormSchema>({
    form,
  })

  const FormInput = useFormTextInput<EditAccountInformationFormSchema>({ form })

  return (
    <form
      className='flex h-full flex-col'
      onSubmit={(event) => {
        event.preventDefault()
        event.stopPropagation()
        form.handleSubmit()
      }}
    >
      <EditAccountContent>
        <InfoSectionTitle>Account information</InfoSectionTitle>
        <InfoSectionDescription className='-mt-3'>
          {`Some basic information about this ${account.applicationId === 'C' ? 'composite' : 'deposit'} account.`}
        </InfoSectionDescription>
        <div className='mt-4 flex flex-col gap-6'>
          <div className='flex gap-9'>
            <div className='flex flex-1 flex-col'>
              <FormMonthPicker
                name='effectiveDate'
                label='Effective date'
                required
                onChange={(effectiveMonth) => {
                  router.push(
                    routeTo(
                      '/accounts/[effectiveMonth]/edit/[accountCode]/account-information',
                      { ...routeParams, effectiveMonth },
                    ),
                  )
                }}
              />
            </div>
            <div className='flex-1'></div>
          </div>
          <div className='flex gap-9'>
            <TextInput
              className='flex-1'
              label='Composite account number'
              readonly
              name='accountCode'
              value={formatAccountCode(account)}
            />

            <FormInput
              className='flex-1'
              name='shortName'
              label='Composite account name'
              placeholder='Must contain at most 20 digits'
              required
            />
          </div>
        </div>
      </EditAccountContent>
      <EditAccountFooter form={form} />
    </form>
  )
}
