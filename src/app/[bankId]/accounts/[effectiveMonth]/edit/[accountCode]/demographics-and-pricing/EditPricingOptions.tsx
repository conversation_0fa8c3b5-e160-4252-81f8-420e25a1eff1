import {
  EditDemographicsAndPricingFormSchemaType,
  PricingOptionsItem,
} from '@/app/[bankId]/accounts/types'
import {
  DetailsSectionSubtitle,
  DetailsSectionTitle,
} from '@/components/DetailsSection'
import { useStore } from '@tanstack/react-form'
import { SortedTable } from '@/components/Table/SortedTable'
import { useMemo } from 'react'
import { ColumnDef } from '@tanstack/react-table'
import { Select } from '@/components/Input/Select'

type EditPricingOptionsColumnDef = ColumnDef<PricingOptionsItem>

export function EditPricingOptions({
  form,
}: {
  form: EditDemographicsAndPricingFormSchemaType
}) {
  const [customerSpecificPricingIndicator] = useStore(form.store, (state) => [
    state.values.customerSpecificPricingIndicator,
  ])

  const pricingOptionsData = useMemo(() => {
    return [
      {
        name: 'Customer specific pricing indicator',
      },
    ]
  }, [])

  const columns = useMemo<EditPricingOptionsColumnDef[]>(() => {
    return [
      {
        header: 'Pricing option',
        accessorKey: 'name',
      },
      {
        header: 'Value',
        cell: ({ row }) => {
          return (
            <div className='min-w-64'>
              <Select
                className='mt-6'
                name='customerSpecificPricingIndicator'
                value={customerSpecificPricingIndicator}
                options={[true, false]}
                onChange={(value) => {
                  form.setFieldValue('customerSpecificPricingIndicator', value)
                }}
                renderOption={(value) => {
                  return value ? 'Yes' : 'No'
                }}
                renderSelected={(value) => {
                  return value ? 'Yes' : 'No'
                }}
              />
            </div>
          )
        },
      },
    ]
  }, [customerSpecificPricingIndicator, form])

  return (
    <>
      <div className='flex flex-row gap-1'>
        <DetailsSectionTitle>Pricing options</DetailsSectionTitle>
      </div>
      <DetailsSectionSubtitle>
        These settings are not synced with the key account.
      </DetailsSectionSubtitle>
      <SortedTable
        data={pricingOptionsData}
        columns={columns}
        columnFilters={[]}
      />
    </>
  )
}
