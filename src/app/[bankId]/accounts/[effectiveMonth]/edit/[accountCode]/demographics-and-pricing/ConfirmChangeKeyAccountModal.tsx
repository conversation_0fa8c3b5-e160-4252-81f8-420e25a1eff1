import {
  ExternalModalContextState,
  Modal,
  ModalCancelButton,
  ModalConfirmButton,
  ModalFooter,
  ModalTitle,
  ModalWindow,
} from '@/components/Modal'

export interface ConfirmChangeKeyAccountModalProps {
  onConfirm: () => void
}

export function ConfirmChangeKeyAccountModal({
  onConfirm,
  modalState,
}: ConfirmChangeKeyAccountModalProps & ExternalModalContextState) {
  return (
    <Modal modalState={modalState}>
      <ModalWindow className='w-[497px]'>
        <ModalTitle>Change key account</ModalTitle>
        <p className='text-app-color-secondary'>
          Changing key accounts may change both the demographic and pricing
          options and the settlement and processing options for this composite
          account.
        </p>
        <ModalFooter>
          <ModalCancelButton>Cancel</ModalCancelButton>
          <ModalConfirmButton
            className='btn-primary'
            onClick={(_, modalContext) => {
              onConfirm()
              modalContext.close()
            }}
          >
            Continue
          </ModalConfirmButton>
        </ModalFooter>
      </ModalWindow>
    </Modal>
  )
}
