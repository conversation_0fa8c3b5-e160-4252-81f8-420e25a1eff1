import {
  EditDemographicsAndPricingFormSchemaType,
  UserFieldListItem,
} from '@/app/[bankId]/accounts/types'
import {
  DetailsSectionSubtitle,
  DetailsSectionTitle,
} from '@/components/DetailsSection'
import { useStore } from '@tanstack/react-form'
import { SortedTable } from '@/components/Table/SortedTable'
import { useMemo } from 'react'
import { ColumnDef } from '@tanstack/react-table'
import { Select } from '@/components/Input/Select'
import { Tooltip } from '@/components/Tooltip'
import { InformationCircleIcon } from '@heroicons/react/24/outline'
import { TextInput } from '@/components/Input/TextInput'
import {
  formatToServerString,
  parseServerFormat,
  toUTCDateString,
} from '@/lib/date'
import { Checkbox } from '@/components/Checkbox'
import { MonthPicker } from '@/components/Filter/MonthPicker'

type UserFieldsColumnDef = ColumnDef<UserFieldListItem>

export function EditUserFields({
  form,
}: {
  form: EditDemographicsAndPricingFormSchemaType
}) {
  const [userFields] = useStore(form.store, (state) => [
    state.values.userFields,
  ])

  const columns = useMemo<UserFieldsColumnDef[]>(() => {
    return [
      {
        header: 'User field',
        accessorKey: 'configuration.name',
      },
      {
        header: 'Value',
        cell: ({ row }) => {
          if (row.original.configuration.fieldType === 'BOOLEAN') {
            return (
              <div className='basis-full'>
                <Select
                  className='mt-6'
                  name={'booleanUserField'}
                  value={
                    row.original.selection.isUnset ?
                      ''
                    : row.original.selection.booleanValue
                  }
                  options={[true, false]}
                  onChange={(value) => {
                    form.setFieldValue(
                      `userFields[${row.index}].selection.isUnset`,
                      false,
                    )
                    form.setFieldValue(
                      `userFields[${row.index}].selection.booleanValue`,
                      !!value,
                    )
                  }}
                  renderOption={(value) => {
                    return value ? 'Yes' : 'No'
                  }}
                  renderSelected={(value) => {
                    return value ? 'Yes' : 'No'
                  }}
                />
              </div>
            )
          }

          if (row.original.configuration.fieldType === 'DROPDOWN') {
            const options: number[] = []
            const optionsLabels: Record<number, string> = {}

            row.original.configuration.updatedDropdownOptions?.forEach(
              (option) => {
                options.push(option.code!)
                optionsLabels[option.code!] = option.value!
              },
            )

            return (
              <div className='basis-full'>
                <Select
                  className='mt-6'
                  name='dropdownUserField'
                  value={
                    row.original.selection.isUnset ?
                      ''
                    : row.original.selection.dropdownOptionCode
                  }
                  options={options}
                  onChange={(value) => {
                    form.setFieldValue(
                      `userFields[${row.index}].selection.isUnset`,
                      false,
                    )
                    form.setFieldValue(
                      `userFields[${row.index}].selection.dropdownOptionCode`,
                      value as number,
                    )
                  }}
                  renderOption={(value) => {
                    return optionsLabels[value as number]
                  }}
                  renderSelected={(value) => {
                    return optionsLabels[value as number]
                  }}
                />
              </div>
            )
          }

          if (row.original.configuration.fieldType === 'FREEFORM') {
            return (
              <div className='basis-full'>
                <TextInput
                  className='mt-6'
                  name='freeformUserField'
                  value={row.original.selection.freeformValue || ''}
                  placeholder={row.original.configuration.name}
                  onChange={(value) => {
                    form.setFieldValue(
                      `userFields[${row.index}].selection.isUnset`,
                      false,
                    )
                    form.setFieldValue(
                      `userFields[${row.index}].selection.freeformValue`,
                      value,
                    )
                  }}
                />
              </div>
            )
          }
        },
      },
      {
        header: 'Expiration date',
        cell: ({ row }) => {
          if (row.original.selection.isUnset) return

          const today = new Date()
          const expiry = row.original.selection.expiry

          const handleCheckboxChange = (checked: boolean) => {
            form.setFieldValue(
              `userFields[${row.index}].selection.expiry`,
              checked ? '' : toUTCDateString(today),
            )
            form.validateField(
              `userFields[${row.index}].selection.expiry`,
              'change',
            )
          }

          const handleDateChange = (date: string) => {
            form.setFieldValue(
              `userFields[${row.index}].selection.expiry`,
              formatToServerString(date),
            )
          }

          return (
            <div className='flex flex-row gap-3'>
              <Checkbox
                checked={!expiry}
                label='No expiration'
                onChange={handleCheckboxChange}
              />
              {expiry && (
                <MonthPicker
                  className='my-1 h-9 min-w-32 bg-white ring-1 ring-inset ring-gray-300'
                  initialDate={parseServerFormat(expiry)}
                  onDateChange={handleDateChange}
                />
              )}
            </div>
          )
        },
      },
      {
        header: 'Apply to child accounts',
        cell: ({ row }) => {
          return (
            !row.original.selection.isUnset && (
              <Checkbox
                className='ml-14'
                label={
                  row.original.selection.applyToChildAccounts ? 'Yes' : 'No'
                }
                checked={row.original.selection.applyToChildAccounts}
                onChange={(checked) => {
                  form.setFieldValue(
                    `userFields[${row.index}].selection.applyToChildAccounts`,
                    checked,
                  )
                }}
              />
            )
          )
        },
      },
    ]
  }, [form])

  return (
    <>
      <div className='flex flex-row gap-1'>
        <DetailsSectionTitle>User fields</DetailsSectionTitle>
        <Tooltip className='size-4 self-center' content='User fields'>
          <InformationCircleIcon />
        </Tooltip>
      </div>
      <DetailsSectionSubtitle>
        These settings are automatically synced with the key account.
      </DetailsSectionSubtitle>
      <SortedTable data={userFields} columns={columns} columnFilters={[]} />
    </>
  )
}
