import { NameCode } from '@/app/[bankId]/accounts/_components/NameCode'
import { useAccountRelatedEntities } from '@/app/[bankId]/accounts/_hooks/useAccountRelatedEntities'
import {
  selectOptionMapper,
  useCodesAndRenderOption,
} from '@/app/[bankId]/accounts/_hooks/useCodesAndRenderOption'
import {
  DepositCategoryLabels,
  EditDemographicsAndPricingFormSchema,
  EditDemographicsAndPricingFormSchemaType,
} from '@/app/[bankId]/accounts/types'
import {
  DetailsSectionItem,
  DetailsSectionTitle,
} from '@/components/DetailsSection'
import { useFormSelect } from '@/components/Form/useFormSelect'
import { useFormTextInput } from '@/components/Form/useFormTextInput'
import { InfoSectionDescription } from '@/components/InfoSection'
import { SelectOption } from '@/components/Input/Select'
import { data } from '@/lib/unions/Union'
import { useStore } from '@tanstack/react-form'
import { useRoute } from '../routing'
import { useBankOptions } from '@/app/[bankId]/configuration/bank-options/_hooks/useBankOptions'

export function EditDemographics({
  form,
  effectiveDate,
}: {
  form: EditDemographicsAndPricingFormSchemaType
  effectiveDate: string
}) {
  const route = useRoute()!
  const routeParams = data(route).params
  const bankId = routeParams.bankId
  const { bankOptions } = useBankOptions({ bankId, effectiveDate })

  const {
    accountTypes: { data: accountTypes },
    branches: { data: branches },
    officers: { data: officers },
  } = useAccountRelatedEntities({ effectiveDate })

  const [accountTypeCodes, renderAccountTypeOption] = useCodesAndRenderOption(
    accountTypes ?? [],
    (accountType) => accountType.accountTypeCode,
    (accountType) => (
      <NameCode
        name={accountType.description}
        code={accountType.accountTypeCode}
      />
    ),
  )

  const [branchCodes, renderBranchOption] = useCodesAndRenderOption(
    branches ?? [],
    (branch) => branch.code,
    (branch) => <NameCode name={branch.branchName} code={branch.code} />,
  )

  const [officerCodes, renderOfficerOption] = useCodesAndRenderOption(
    officers ?? [],
    (officer) => officer.code,
    (officer) => <NameCode name={officer.name} code={officer.code} />,
  )

  const [
    keyAccountCode,
    analysisAccountTypeCode,
    costCenter,
    branchCode,
    currencyCode,
    depositAccountTypeCode,
    depositCategory,
    primaryOfficerCode,
    secondaryOfficerCode,
    treasuryOfficerCode,
  ] = useStore(form.store, (state) => [
    state.values.keyAccountCode,
    state.values.analysisAccountTypeCode,
    state.values.costCenter,
    state.values.branchCode,
    state.values.currencyCode,
    state.values.depositAccountTypeCode,
    state.values.depositCategory,
    state.values.primaryOfficerCode,
    state.values.secondaryOfficerCode,
    state.values.treasuryOfficerCode,
  ])

  const Select = useFormSelect<EditDemographicsAndPricingFormSchema>({ form })

  const FormInput = useFormTextInput<EditDemographicsAndPricingFormSchema>({
    form,
  })

  return (
    <div className='flex flex-col gap-4'>
      <div className='my-4'>
        <DetailsSectionTitle>Demographics</DetailsSectionTitle>
        {keyAccountCode && (
          <InfoSectionDescription className='text-sm'>
            These settings are automatically synced with the key account.
          </InfoSectionDescription>
        )}
      </div>
      <div className='flex flex-col gap-2'>
        <div className='flex gap-9'>
          {keyAccountCode && bankOptions?.copyAccountTypeOfKeyAccount ?
            <DetailsSectionItem
              className='gap-1'
              label='Account type'
              info={renderAccountTypeOption(analysisAccountTypeCode)}
            />
          : <Select
              name='analysisAccountTypeCode'
              label='Account type'
              placeholder='Select account type'
              required
              renderSelected={renderAccountTypeOption}
              renderErrors={false}
            >
              {accountTypeCodes.map(
                selectOptionMapper(renderAccountTypeOption),
              )}
            </Select>
          }

          {keyAccountCode ?
            <DetailsSectionItem
              className='gap-1'
              label='Cost center'
              info={costCenter}
            />
          : <FormInput
              name='costCenter'
              label='Cost center'
              placeholder='Must contain at most 7 digits'
              required
            />
          }
        </div>

        <div className='flex gap-9'>
          {keyAccountCode ?
            <DetailsSectionItem
              className='gap-1'
              label='Branch'
              info={renderBranchOption(branchCode)}
            />
          : <Select
              name='branchCode'
              label='Branch'
              placeholder='Select account branch'
              required
              renderSelected={renderBranchOption}
            >
              {branchCodes.map(selectOptionMapper(renderBranchOption))}
            </Select>
          }

          {keyAccountCode ?
            <DetailsSectionItem
              className='gap-1'
              label='Currency'
              info={currencyCode}
            />
          : <FormInput name='currencyCode' label='Currency' disabled={true} />}
        </div>

        <div className='flex gap-9'>
          {keyAccountCode ?
            <DetailsSectionItem
              className='gap-1'
              label='Deposit type'
              info={depositAccountTypeCode}
            />
          : <FormInput
              name='depositAccountTypeCode'
              label='Deposit type'
              disabled={true}
            />
          }

          {keyAccountCode ?
            <DetailsSectionItem
              className='gap-1'
              label='Deposit category'
              info={depositCategory && DepositCategoryLabels[depositCategory]}
            />
          : <FormInput
              // TODO: Turn this into a Select component for depositCategory once we are required to make that field editable
              // name='depositCategory'
              name='depositCategory'
              label='Deposit category'
              disabled={true}
            />
          }
        </div>

        <div className='flex gap-9'>
          {keyAccountCode ?
            <DetailsSectionItem
              className='gap-1'
              label='Primary officer'
              info={renderOfficerOption(primaryOfficerCode)}
            />
          : <Select
              name='primaryOfficerCode'
              label='Primary officer'
              placeholder='Select primary officer'
              required
              renderSelected={renderOfficerOption}
            >
              {officerCodes.map(selectOptionMapper(renderOfficerOption))}
            </Select>
          }

          {keyAccountCode ?
            <DetailsSectionItem
              className='gap-1'
              label='Secondary officer'
              info={
                secondaryOfficerCode &&
                renderOfficerOption(secondaryOfficerCode)
              }
            />
          : <Select
              name='secondaryOfficerCode'
              label='Secondary officer'
              placeholder='Select secondary officer'
              renderSelected={renderOfficerOption}
            >
              {secondaryOfficerCode && (
                <SelectOption key='null' value={null}>
                  None
                </SelectOption>
              )}
              {officerCodes.map(selectOptionMapper(renderOfficerOption))}
            </Select>
          }
        </div>

        <div className='flex gap-9'>
          {keyAccountCode ?
            <DetailsSectionItem
              className='gap-1'
              label='Treasury officer'
              info={
                treasuryOfficerCode && renderOfficerOption(treasuryOfficerCode)
              }
            />
          : <Select
              name='treasuryOfficerCode'
              label='Treasury officer'
              placeholder='Select treasury officer'
              renderSelected={renderOfficerOption}
            >
              {treasuryOfficerCode && (
                <SelectOption key='null' value={null}>
                  None
                </SelectOption>
              )}
              {officerCodes.map(selectOptionMapper(renderOfficerOption))}
            </Select>
          }
          <div className='flex-1'></div>
        </div>
      </div>
    </div>
  )
}
