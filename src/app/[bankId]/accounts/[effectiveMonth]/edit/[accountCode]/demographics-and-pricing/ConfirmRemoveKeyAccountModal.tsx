import {
  ExternalModalContextState,
  Modal,
  ModalCancelButton,
  ModalConfirmButton,
  ModalFooter,
  ModalTitle,
  ModalWindow,
} from '@/components/Modal'
import { ExclamationTriangleIcon } from '@heroicons/react/24/outline'

export interface ConfirmRemoveKeyAccountModalProps {
  onConfirm: () => void
}

export function ConfirmRemoveKeyAccountModal({
  onConfirm,
  modalState,
}: ConfirmRemoveKeyAccountModalProps & ExternalModalContextState) {
  return (
    <Modal modalState={modalState}>
      <ModalWindow className='w-[600px]'>
        <ModalTitle>
          <div className='flex items-center justify-start gap-4'>
            <ExclamationTriangleIcon className='size-12 text-app-color-button-primary-error-bg' />
            <div>Remove key account from this composite account</div>
          </div>
        </ModalTitle>
        <p className='text-app-color-secondary'>
          This composite account will continue to use demographic and user field
          settings from its most recent key account, unless you update those
          settings or assign a new key account.
        </p>
        <ModalFooter>
          <ModalCancelButton>Cancel</ModalCancelButton>
          <ModalConfirmButton
            className='btn-alert'
            onClick={(_, modalContext) => {
              onConfirm()
              modalContext.close()
            }}
          >
            Remove
          </ModalConfirmButton>
        </ModalFooter>
      </ModalWindow>
    </Modal>
  )
}
