'use client'

import { useRouter } from 'next/navigation'
import { useRoute, routeTo } from '../routing'
import { routeTo as routeToViewAccount } from '../../../view/[accountCode]/routing'
import { data } from '@/lib/unions/Union'
import { useAccountFromParams } from '@/app/[bankId]/accounts/_hooks/useAccountFromParams'
import {
  EditDemographicsAndPricingFormSchema,
  EditDemographicsAndPricingZodSchema,
  UserFieldListItem,
} from '@/app/[bankId]/accounts/types'
import { useForm } from '@tanstack/react-form'
import { useMutation } from '@tanstack/react-query'
import {
  accountMutation,
  userFieldsMutation,
} from '@/app/[bankId]/accounts/mutations'
import { EditAccountContent } from '../_components/EditAccountContent'
import { useFormMonthPicker } from '@/components/Form/useFormMonthPicker'
import { EditAccountFooter } from '../_components/EditAccountFooter'
import { InfoSectionSeparator } from '@/components/InfoSection'
import { EditDemographics } from './EditDemographics'
import { EditKeyAccount } from './EditKeyAccount'
import { EditPricingOptions } from './EditPricingOptions'
import { useMemo } from 'react'
import {
  accountCodesAreEqual,
  accountToAccountCode,
  getUserFieldsListItemsFromConfigsAndSelections,
} from '@/app/[bankId]/accounts/accountHelpers'
import { useUserFieldsRelatedEntities } from '@/app/[bankId]/accounts/_hooks/useUserFieldsRelatedEntities'
import { EditUserFields } from './EditUserFields'
import { formToApiSchemas } from '@/api/formToApiSchema'

export default function EditDemographicsAndPricing() {
  const router = useRouter()
  const route = useRoute()!
  const routeParams = data(route).params
  const accountFromParams = useAccountFromParams(routeParams)
  const account = accountFromParams.account!
  const accountCode = accountToAccountCode(account)
  const effectiveDate = accountFromParams.effectiveDate

  const { userFieldsConfigurations, userFieldSelections } =
    useUserFieldsRelatedEntities({
      effectiveDate,
      applicationId: account.applicationId,
      accountNumber: account.accountNumber,
      bankNumber: account.bankNumber,
    })

  const userFields = useMemo<UserFieldListItem[]>(() => {
    return getUserFieldsListItemsFromConfigsAndSelections(
      accountCode,
      userFieldsConfigurations.data,
      userFieldSelections.data,
    ).map((originalUserField) => {
      const userField = { ...originalUserField }
      userField.selection.effectiveDate = effectiveDate
      return userField
    })
  }, [userFieldsConfigurations.data, userFieldSelections.data, effectiveDate])
  const { mutate: createUserFieldSelections } = useMutation(
    userFieldsMutation('/createUserFieldSelections', {
      // Do nothing on success
    }),
  )

  const { mutate: updateAccount } = useMutation(
    accountMutation('/updateAccount', {
      onSuccess: () => {
        router.push(
          routeToViewAccount(
            '/accounts/[effectiveMonth]/view/[accountCode]/details',
            routeParams,
          ),
        )
      },
    }),
  )

  // QUICK AND DIRTY HACK after https://bitbucket.fis.dev/projects/AFIN/repos/af_fpb/pull-requests/264/overview
  // removed keyAccountCode from Account. Might be cleaned up with https://jira.fis.dev/browse/REVCON-53.
  const { mutate: upsertKeyAccountMapping } = useMutation(
    accountMutation('/upsertKeyAccountMapping', {
      // Do nothing on success
    }),
  )
  const { mutate: removeKeyAccountMapping } = useMutation(
    accountMutation('/removeKeyAccountMapping', {
      // Do nothing on success
    }),
  )

  const form = useForm<EditDemographicsAndPricingFormSchema>({
    defaultValues: {
      ...account,
      effectiveDate: accountFromParams.effectiveDate,
      userFields: userFields ?? [],
    },
    validators: {
      onChange: EditDemographicsAndPricingZodSchema,
    },
    onSubmit: async ({ value }) => {
      // Update field user fields selections, update account,
      // redirect to account view, and show notification
      const { userFields, keyAccountCode, ...exceptSeparateData } = value

      const selections = userFields.map(
        (userField) => userField.selection,
      ) as UserFieldListItem['selection'][]

      if (selections.length > 0) {
        await createUserFieldSelections(
          selections.map((selection) =>
            formToApiSchemas.userFieldSelection.parse(selection),
          ),
        )
      }

      if (
        keyAccountCode &&
        // we added a key account
        (!account.keyAccountCode ||
          // we changed the key account
          (account.keyAccountCode &&
            !accountCodesAreEqual(account, account.keyAccountCode)))
      ) {
        await upsertKeyAccountMapping({
          accountCode,
          childAccountCode: keyAccountCode,
        })
      } else if (!keyAccountCode && account.keyAccountCode) {
        // we removed the key account
        await removeKeyAccountMapping(accountCode)
      }

      await updateAccount(
        formToApiSchemas.account.parse({
          ...account,
          ...exceptSeparateData,
        }),
      )
    },
  })

  const FormMonthPicker =
    useFormMonthPicker<EditDemographicsAndPricingFormSchema>({
      form,
    })

  return (
    <form
      className='flex h-full flex-col'
      onSubmit={(event) => {
        event.preventDefault()
        event.stopPropagation()
        form.handleSubmit()
      }}
    >
      <EditAccountContent>
        <div className='flex flex-col'>
          <div className='text-xl font-medium'>
            Demographic and pricing options
          </div>
        </div>
        <div className='mt-6 flex flex-col'>
          <div className='flex flex-col gap-4'>
            <div className='flex gap-9'>
              <div className='flex flex-1 flex-col'>
                <FormMonthPicker
                  name='effectiveDate'
                  label='Effective date'
                  required
                  onChange={(effectiveMonth) => {
                    router.push(
                      routeTo(
                        '/accounts/[effectiveMonth]/edit/[accountCode]/demographics-and-pricing',
                        { ...routeParams, effectiveMonth },
                      ),
                    )
                  }}
                />
              </div>
              <div className='flex-1'></div>
            </div>
            {accountCode.applicationId === 'C' && (
              <EditKeyAccount account={account} form={form} />
            )}
          </div>
          <InfoSectionSeparator />

          {/* Demographics */}
          <EditDemographics form={form} effectiveDate={effectiveDate} />
          <InfoSectionSeparator className='mb-3 mt-3' />

          {/* User Fields */}
          <EditUserFields form={form} />
          <InfoSectionSeparator className='mb-4 mt-6' />

          {/* Pricing Options */}
          <EditPricingOptions form={form} />
        </div>
      </EditAccountContent>
      <EditAccountFooter form={form} />
    </form>
  )
}
