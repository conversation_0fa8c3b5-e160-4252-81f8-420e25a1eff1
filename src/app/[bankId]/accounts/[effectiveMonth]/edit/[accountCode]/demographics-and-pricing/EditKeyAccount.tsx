import { NameCode } from '@/app/[bankId]/accounts/_components/NameCode'
import {
  accountCodesAreEqual,
  apiMappingWithKeyListSelector,
  formatAccountCode,
  mappingsToAccountsMap,
  toAccountCodeString,
} from '@/app/[bankId]/accounts/accountHelpers'
import { accountQueries } from '@/app/[bankId]/accounts/queries'
import {
  Account,
  AccountCode,
  BoundedAccountListItem,
  EditDemographicsAndPricingFormSchema,
  EditDemographicsAndPricingFormSchemaType,
} from '@/app/[bankId]/accounts/types'
import { useFormSelect } from '@/components/Form/useFormSelect'
import { SelectOption } from '@/components/Input/Select'
import { useStore } from '@tanstack/react-form'
import { useQuery } from '@tanstack/react-query'
import { JSX, useMemo } from 'react'
import {
  ConfirmChangeKeyAccountModal,
  ConfirmChangeKeyAccountModalProps,
} from './ConfirmChangeKeyAccountModal'
import { useExternalModalStateAndProps } from '@/components/Modal'
import {
  ConfirmRemoveKeyAccountModal,
  ConfirmRemoveKeyAccountModalProps,
} from './ConfirmRemoveKeyAccountModal'
import { useChangeKeyAccount } from '@/app/[bankId]/accounts/_hooks/useChangeKeyAccount'
import { useAccount } from '@/app/[bankId]/accounts/_hooks/useAccount'

type AccountOption = Pick<
  Account,
  'accountNumber' | 'bankNumber' | 'applicationId' | 'shortName'
> & {
  depth: number
}

export function EditKeyAccount({
  account,
  form,
}: {
  account: Account
  form: EditDemographicsAndPricingFormSchemaType
}) {
  const [effectiveDate, keyAccountCode] = useStore(form.store, (state) => [
    state.values.effectiveDate,
    state.values.keyAccountCode,
  ])
  const Select = useFormSelect<EditDemographicsAndPricingFormSchema>({ form })

  const { data: mappings } = useQuery({
    ...accountQueries('/getAccountMappings', {
      code: toAccountCodeString(account),
      effectiveDate,
    }),
    select: apiMappingWithKeyListSelector,
  })

  const [flattenedOptions, renderOption] = useMemo(() => {
    const accountsMap = mappingsToAccountsMap([...(mappings ?? [])])
    const nestedAccount = accountsMap[toAccountCodeString(account)]!
    const optionsMap: Record<string, AccountOption> = {}

    if (!nestedAccount)
      return [
        [] as AccountCode[],
        (() => <></>) as (accountCode: AccountCode) => JSX.Element,
      ]

    function flattenOptions(
      account: BoundedAccountListItem,
      depth: number,
    ): AccountCode[] {
      optionsMap[toAccountCodeString(account)] = {
        accountNumber: account.accountNumber,
        applicationId: account.applicationId,
        bankNumber: account.bankNumber,
        shortName: account.shortName,
        depth,
      }
      return [
        {
          accountNumber: account.accountNumber,
          applicationId: account.applicationId,
          bankNumber: account.bankNumber,
        },
        ...(
          account.children?.map((child) => flattenOptions(child, depth + 1)) ??
          []
        ).flat(Infinity),
      ] as AccountCode[]
    }

    function renderOption(accountCode: AccountCode) {
      const accountCodeString = toAccountCodeString(accountCode)
      const option = optionsMap[accountCodeString]
      return (
        <SelectOption
          key={accountCodeString}
          value={accountCode}
          focusClassNames='data-[focus]:bg-indigo-100'
        >
          <div className='flex'>
            <div className='flex-1 text-app-color-primary'>
              <div className={`pl-${(option.depth - 1) * 4}`}>
                {option.shortName}
              </div>
            </div>
            <div className='flex-1 text-app-color-secondary'>
              {formatAccountCode(accountCode)}
            </div>
          </div>
        </SelectOption>
      )
    }
    const flattenedOptions = flattenOptions(nestedAccount, 0).filter(
      (option) => !accountCodesAreEqual(option, account),
    )
    return [flattenedOptions, renderOption]
  }, [mappings, account])

  const { account: keyAccount, isLoading: isLoadingKeyAccount } = useAccount({
    accountCode: keyAccountCode,
    effectiveDate,
  })

  const { changeKeyAccountInEditForm } = useChangeKeyAccount({
    keyAccountCode,
    effectiveDate,
  })

  const [
    confirmChangeModalState,
    confirmChangeModalProps,
    setConfirmChangeModalProps,
  ] = useExternalModalStateAndProps<ConfirmChangeKeyAccountModalProps>({})

  const [
    confirmRemoveModalState,
    confirmRemoveModalProps,
    setConfirmRemoveModalProps,
  ] = useExternalModalStateAndProps<ConfirmRemoveKeyAccountModalProps>({})

  function onChange(
    newKeyAccountCode: AccountCode | null,
    handleChange: () => void,
  ) {
    if (newKeyAccountCode) {
      // we're changing the key account

      // callback for when we confirm change in ConfirmChangeKeyAccountModal
      function onConfirm() {
        changeKeyAccountInEditForm(newKeyAccountCode!, form)
        handleChange()
      }

      setConfirmChangeModalProps({ onConfirm })
      confirmChangeModalState.show()
    } else {
      // we're removing the key account

      // callback for when we confirm removal in ConfirmRemoveKeyAccountModal
      function onConfirm() {
        // no need to change any existing fields besides key account
        handleChange()
      }

      setConfirmRemoveModalProps({ onConfirm })
      confirmRemoveModalState.show()
    }
  }

  return (
    <>
      {flattenedOptions && !isLoadingKeyAccount && (
        <>
          <div className='flex gap-9'>
            <Select
              name='keyAccountCode'
              label='Key account'
              placeholder='No key account selected'
              renderSelected={() => (
                <NameCode
                  name={keyAccount!.shortName}
                  code={formatAccountCode(keyAccountCode!)}
                />
              )}
              onChangeDeferred={onChange}
            >
              {keyAccountCode && (
                <SelectOption
                  key='null'
                  value={null}
                  focusClassNames='data-[focus]:bg-indigo-100'
                >
                  Remove key account
                </SelectOption>
              )}
              {flattenedOptions?.map(renderOption)}
            </Select>
            <div className='flex-1'></div>
          </div>
          {confirmChangeModalProps && (
            <ConfirmChangeKeyAccountModal
              modalState={confirmChangeModalState}
              {...confirmChangeModalProps}
            />
          )}
          {confirmRemoveModalProps && (
            <ConfirmRemoveKeyAccountModal
              modalState={confirmRemoveModalState}
              {...confirmRemoveModalProps}
            />
          )}
        </>
      )}
    </>
  )
}
