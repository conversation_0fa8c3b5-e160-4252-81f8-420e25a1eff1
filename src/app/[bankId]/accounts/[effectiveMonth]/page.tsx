'use client'

import { SearchBar } from '@/components/SearchBar'
import {
  Breadcrumbs,
  BreadcrumbSegment,
} from '@/components/Breadcrumbs/Breadcrumbs'
import { Button } from '@/components/Button'
import { SimpleMultiselectFilter } from '@/components/Filter/SimpleMultiselectFilter'
import { formatToServerString } from '@/lib/date'
import { Menu, MenuButton } from '@headlessui/react'
import { ChevronDownIcon } from '@heroicons/react/24/outline'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { routeTo, useRoute } from './routing'
import { routeTo as accountsRootRoutTo } from '../routing'
import { Account, AccountCode } from '../types'
import { data } from '@/lib/unions/Union'
import { MonthPicker } from '@/components/Filter/MonthPicker'
import { useAccounts } from '../_hooks/useAccounts'
import AccountListPage from '../_components/AccountListPage'

const updateAccountStatusFilter = (status: string[] | null) => {
  console.debug(
    'TODO: AFIN-248 Filter by account status and effective date on Accounts page',
    status,
  )
}

const updateSearchAccount = (searchTerm: string) => {
  console.debug(
    'TODO: AFIN-247 Implement search by account name and account number',
    searchTerm,
  )
}

export default function Accounts() {
  const router = useRouter()
  const route = data(useRoute()!)
  const routeParams = route.params

  const effectiveDate = formatToServerString(routeParams.effectiveMonth)

  const { accounts, ...accountsResult } = useAccounts({ effectiveDate })

  const accountsData: Account[] = accounts ?? []

  const updateEffectiveDate = (monthYear: string) => {
    router.push(
      routeTo(
        '/accounts/[effectiveMonth]',
        {
          ...routeParams,
          effectiveMonth: monthYear,
        },
        route.query,
      ),
    )
  }

  if (accountsResult.status === 'error') {
    console.error(accountsResult.error)
    return `[${accountsResult.error.name}] ${accountsResult.error.message}`
  }

  return (
    <div className='mx-8 mt-8 flex flex-auto flex-col'>
      <SearchBar
        className='mb-4 max-w-96'
        defaultLabel='Search an account'
        onValueChange={updateSearchAccount}
      />

      <div className='mb-5 flex items-center'>
        <Breadcrumbs truncate={2}>
          <BreadcrumbSegment href={'#'}>All accounts</BreadcrumbSegment>
        </Breadcrumbs>

        <div className='ml-auto flex gap-2'>
          <Link href={accountsRootRoutTo('/accounts/add', routeParams)}>
            <Button className='btn-primary text-white'>
              Add composite account
            </Button>
          </Link>

          <Menu>
            <MenuButton className='btn inline-flex items-center gap-2 rounded-md border border-solid border-gray-200 px-3 py-1.5 data-[open]:bg-zinc-100'>
              Actions
              <ChevronDownIcon className='size-4' />
            </MenuButton>
          </Menu>
        </div>
      </div>

      <div className='mb-5 flex gap-2'>
        <MonthPicker
          className='border border-indigo-300 bg-indigo-100 hover:bg-indigo-200 focus-visible:outline-indigo-300'
          title='Select the effective date'
          initialDate={effectiveDate}
          showIcon={true}
          prefix={'View data effective on'}
          onDateChange={updateEffectiveDate}
        />

        <SimpleMultiselectFilter
          defaultLabel='Status: Open'
          name='accountStatus'
          options={['Open', 'Closed']}
          value={null}
          onValueChange={updateAccountStatusFilter}
        />

        <div className='ml-auto mt-1 font-light text-app-color-text-secondary'>
          {accountsData?.length ?
            accountsData.length === 1 ?
              '1 record'
            : `${accountsData.length} records`
          : '0 records'}
        </div>
      </div>
      <AccountListPage />
    </div>
  )
}
