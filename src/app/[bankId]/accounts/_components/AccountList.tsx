import {
  formatAccountCode,
  formatAccountCodeForRoute,
  getAccountTypeDescription,
  mappingsToAccountsMap,
  toAccountCodeString,
} from '@/app/[bankId]/accounts/accountHelpers'
import { routeTo, useRoute } from '@/app/[bankId]/accounts/routing'
import { Account, AccountListItem } from '@/app/[bankId]/accounts/types'
import { Checkbox } from '@/components/Checkbox'
import { ExpandedTable } from '@/components/Table/ExpandedTable'
import { formatToMonthYearFromDate } from '@/lib/date'
import { unwrap } from '@/lib/unions/unwrap'
import { Button } from '@headlessui/react'
import { ChevronDownIcon, ChevronRightIcon } from '@heroicons/react/24/outline'
import { ColumnDef } from '@tanstack/react-table'
import clsx from 'clsx'
import Link from 'next/link'
import { useMemo } from 'react'
import { AccountType } from '../../configuration/types'
import { HydratedAccountWithKeyAccountMappingForm } from '@/api/formToApiSchema'

type SelectAccountsColumnDef = ColumnDef<AccountListItem>

export function AccountList({
  account,
  accountMapping,
  accountTypes,
}: {
  account: Account
  accountMapping: HydratedAccountWithKeyAccountMappingForm[]
  accountTypes: AccountType[] | undefined
}) {
  const routeParams = unwrap(
    useRoute(),
    '/accounts/[effectiveMonth]/view/[accountCode]/relationships',
  )!.params!

  const items = useMemo(() => {
    const accountCode = toAccountCodeString(account)
    const accountsMap = mappingsToAccountsMap([...(accountMapping ?? [])])
    return accountsMap[accountCode] ? [accountsMap[accountCode]] : []
  }, [accountMapping])

  const columns: SelectAccountsColumnDef[] = useMemo(
    () => [
      {
        id: 'keyAccount',
        header: 'Key account',
        meta: {
          className: 'min-w-[130px]',
        },
        cell: ({ row }) => (
          <div className='px-6'>
            <Checkbox checked={row.original.isKeyAccount ?? false} />
          </div>
        ),
      },
      {
        id: 'shortName',
        accessorKey: 'shortName',
        header: 'Account',
        meta: {
          className: 'basis-1/3',
        },
        cell: ({ row, getValue }) => (
          <div
            style={{
              paddingLeft: `${
                row.id === '0' ? 0
                : row.getCanExpand() ? row.depth * 2
                : (row.depth + 1) * 2
              }rem`,
            }}
          >
            <div className='flex flex-row gap-2'>
              {row.getCanExpand() && (
                <Button
                  className={'h-6 w-6'}
                  onClick={row.getToggleExpandedHandler()}
                >
                  {row.getIsExpanded() ?
                    <ChevronDownIcon />
                  : <ChevronRightIcon />}
                </Button>
              )}
              <div
                className={clsx(
                  'flex flex-row text-nowrap',
                  row.id === '0' ? 'font-semibold' : undefined,
                )}
              >
                {row.id === '0' ?
                  getValue<string>()
                : <Link
                    className='font-bold text-indigo-600 underline'
                    href={routeTo(
                      '/accounts/[effectiveMonth]/view/[accountCode]/details',
                      {
                        ...routeParams,
                        accountCode: formatAccountCodeForRoute(row.original),
                      },
                    )}
                  >
                    {getValue<string>()}
                  </Link>
                }
              </div>
            </div>
          </div>
        ),
      },
      {
        id: 'accountNumber',
        accessorFn: (row) => formatAccountCode(row),
        header: 'Account number',
        meta: {
          className: 'min-w-[260px]',
        },
      },
      {
        id: 'accountType',
        header: 'Account type',
        meta: {
          className: 'min-w-[230px]',
        },
        cell: ({ row }) => (
          <div className='flex flex-col'>
            <div className={clsx(row.id === '0' ? 'font-semibold' : undefined)}>
              {getAccountTypeDescription(
                accountTypes,
                row.original.analysisAccountTypeCode,
              )}
            </div>
            <div className='text-sm text-app-color-secondary'>
              {row.original.analysisAccountTypeCode}
            </div>
          </div>
        ),
      },
      {
        id: 'currencyCode',
        accessorKey: 'currencyCode',
        header: 'Currency',
        meta: {
          className: 'min-w-[90px]',
        },
      },
      {
        id: 'openDate',
        accessorKey: 'openDate',
        header: 'Account open date',
        meta: {
          className: 'min-w-[170px]',
        },
        cell: ({ row, getValue }) => (
          <div className='flex w-full flex-row'>
            <div>{formatToMonthYearFromDate(getValue<string>())}</div>
          </div>
        ),
      },
    ],
    [accountTypes, routeParams],
  )

  return (
    <ExpandedTable
      data={items}
      columns={columns}
      getSubRows={(row) => row.children}
    />
  )
}
