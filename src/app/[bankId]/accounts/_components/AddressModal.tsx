import { useForm } from '@tanstack/react-form'
import {
  AccountCode,
  Address,
  AddressFormSchema,
  AddressFormSchemaDefaults,
  AddressFormZodSchema,
  StateCodeChoices,
} from '../types'
import { addressToForm, getNextAvailableNumber } from '../accountHelpers'
import { useFormTextInput } from '@/components/Form/useFormTextInput'
import { useFormSelect } from '@/components/Form/useFormSelect'
import { useMemo } from 'react'
import {
  ExternalModalContextState,
  Modal,
  ModalCancelButton,
  ModalConfirmButton,
  ModalFooter,
  ModalTitle,
  ModalWindow,
} from '@/components/Modal'

export interface AddressModalProps {
  address?: Address
  accountCode: AccountCode
  accountAddresses: Address[]
  onSave?: (form: AddressFormSchema) => void
}

export function AddressModal({
  address,
  accountAddresses,
  onSave,
  modalState,
}: AddressModalProps & ExternalModalContextState) {
  const nextAvailableAddressNumber = useMemo(
    () =>
      address?.addressNumber ??
      getNextAvailableNumber(
        accountAddresses.map((accountAddress) => accountAddress.addressNumber),
      ),
    [accountAddresses, address?.addressNumber],
  )

  const validateAddressNumberAvailable = async ({
    value,
  }: {
    value: number
  }) => {
    // no need to validate existing Address since addressNumber will be read-only
    if (address) return undefined

    const parsedValue = parseInt(value as unknown as string)

    // TODO: combine with async call to endpoint from 'AFIN-448 Address validation endpoint'
    const addressNumberExists = accountAddresses.find(
      (accountAddress) => accountAddress.addressNumber === parsedValue,
    )

    if (addressNumberExists) return 'Address code already in use'
  }

  const addressForm = useForm<AddressFormSchema>({
    defaultValues:
      address ?
        addressToForm(address) // editing existing address
      : {
          // adding new address
          ...AddressFormSchemaDefaults,
          addressNumber: nextAvailableAddressNumber,
        },
    validators: {
      onChange: AddressFormZodSchema,
    },
    onSubmit: ({ value }) => {
      if (onSave) {
        const parsed = AddressFormZodSchema.parse(value)
        onSave(parsed)
      }
      modalState?.close()
    },
  })

  const TextInput = useFormTextInput<AddressFormSchema>({ form: addressForm })
  const Select = useFormSelect<AddressFormSchema>({ form: addressForm })

  return (
    <Modal modalState={modalState}>
      <ModalWindow dataTestId='addresModal' zIndexStart={10}>
        <ModalTitle>{address ? 'Edit ' : 'Add a new '}address</ModalTitle>
        <p className='text-app-color-secondary'>
          This address will only apply to this composite account
        </p>

        <div className='mt-4 flex flex-col'>
          <TextInput
            name='addressNumber'
            label='Address code'
            required
            prefix='A - '
            readonly={!!address}
            validators={{
              onChangeAsyncDebounceMs: 500,
              onChangeAsync: validateAddressNumberAvailable,
            }}
          />
          <TextInput name='recipient' label='Recipient' required />
          <TextInput name='streetAddress' label='Street address' required />
          <TextInput name='streetAddress2' label='Apt/Suite' />
          <TextInput name='city' label='City' required />
          <div className='flex gap-4'>
            <TextInput
              name='zipCode'
              label='ZIP Code'
              required
              className='basis-full'
            />
            <Select
              name='state'
              label='State'
              required
              options={StateCodeChoices}
              className='basis-full'
            />
          </div>
        </div>
        <ModalFooter>
          <ModalCancelButton>Cancel</ModalCancelButton>
          <ModalConfirmButton
            onClick={addressForm.handleSubmit}
            className='btn-primary'
          >
            Save
          </ModalConfirmButton>
        </ModalFooter>
      </ModalWindow>
    </Modal>
  )
}
