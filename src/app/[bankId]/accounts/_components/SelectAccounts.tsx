'use client'

import { accountQueries } from '@/app/[bankId]/accounts/queries'
import {
  InfoSection,
  InfoSectionDescription,
  InfoSectionTitle,
} from '@/components/InfoSection'
import { SegmentedTab, SegmentedTabList } from '@/components/SegmentedTabList'
import {
  ComboboxOption,
  TabGroup,
  TabPanel,
  TabPanels,
} from '@headlessui/react'
import { ArrowUpTrayIcon } from '@heroicons/react/24/outline'
import { useStore } from '@tanstack/react-form'
import { useQuery, useQueryClient, UseQueryResult } from '@tanstack/react-query'
import Link from 'next/link'
import { useEffect } from 'react'
import {
  accountCodesAreEqual,
  apiAccountSelector,
  apiMappingWithKeyListSelector,
  formatAccountCode,
  mappingsToAccountsMap,
  searchMatchingAccounts,
  toAccountCodeString,
} from '../accountHelpers'
import {
  AccountCode,
  AccountSearchItem,
  SelectAccountFormSchemaType,
  UserFieldListItem,
} from '../types'
import { SearchCombo } from '../(compositeAccount)/add/SearchCombo'
import { SelectAccountsList } from './SelectAccountsList'
import SelectAccountsHierarchy from './accounts-hierarchy/SelectAccountsHierarchy'
import { useAccountRelatedEntities } from '../_hooks/useAccountRelatedEntities'
import { NameCode } from './NameCode'
import { useConfigurations } from '@/app/[bankId]/configuration/_hooks/useConfigurations'
import {
  AccountMutationProvider,
  SelectAccountModes,
} from '../_context/AccountMutationContext'
import { ReactFlowProvider } from '@xyflow/react'
import { HydratedUserFieldConfigurationForm } from '@/api/formToApiSchema'

interface SelectAccountsProps {
  isVisible: boolean
  form: SelectAccountFormSchemaType
}

export function SelectAccounts({ isVisible, form }: SelectAccountsProps) {
  const [
    effectiveDate,
    subAccounts,
    accountNumber,
    applicationId,
    bankNumber,
    userFields,
  ] = useStore(form.store, (state) => [
    state.values.accountInfo.effectiveDate,
    state.values.subAccounts,
    state.values.accountInfo.accountNumber,
    state.values.accountInfo.applicationId,
    state.values.accountInfo.bankNumber,
    state.values.demographics.userFields,
  ])

  // TODO [Accounts] Move create accounts data fetching to its own module
  const getParentlessAndOpenAccountsResponse = useQuery({
    ...accountQueries('/getParentlessOpenAccounts', {
      effectiveDate,
    }),
    select: (apiAccounts) =>
      apiAccounts.map((apiAccount) => ({
        ...apiAccountSelector(apiAccount),
        // don't actually need key account code for parentless open accounts,
        // but adding here for type safety
        keyAccountCode: null,
      })),
  })

  const { accountTypes, branches, officers } = useAccountRelatedEntities({
    effectiveDate,
  })

  const {
    userFieldConfigurations,
    analysisResultOptions,
    cycleDefinitions,
    earningsCreditDefinitions,
    investableBalanceDefinitions,
    requiredBalanceDefinitions,
    statementFormatPlans,
  } = useConfigurations({ effectiveDate })

  const queryClient = useQueryClient()

  useEffect(() => {
    if (
      userFieldConfigurations.status === 'success' &&
      userFieldConfigurations.data
    ) {
      if (userFields.length === 0) {
        const newUserFields: UserFieldListItem[] =
          userFieldConfigurations.data.map(
            (configuration: HydratedUserFieldConfigurationForm) => {
              const selection: UserFieldListItem['selection'] = {
                applicationId,
                accountNumber,
                bankNumber,
                effectiveDate,
                userFieldCode: configuration.code,
                applyToChildAccounts: false,
                isUnset: true,
                booleanValue: null,
                dropdownOptionCode: null,
                expiry: null,
                freeformValue: null,
              }
              return {
                configuration: configuration,
                selection: selection,
              }
            },
          )
        form.setFieldValue('demographics.userFields', newUserFields)
      } else {
        if (
          userFields[0].selection.applicationId !== applicationId ||
          userFields[0].selection.accountNumber !== accountNumber ||
          userFields[0].selection.effectiveDate !== effectiveDate ||
          userFields[0].selection.bankNumber !== bankNumber
        ) {
          const newUserFields = userFields.map((userField) => {
            userField.selection.applicationId = applicationId
            userField.selection.accountNumber = accountNumber
            userField.selection.bankNumber = bankNumber
            userField.selection.effectiveDate = effectiveDate
            return userField
          })
          form.setFieldValue('demographics.userFields', newUserFields)
        }
      }
    }
  }, [
    form,
    accountTypes,
    branches,
    officers,
    applicationId,
    accountNumber,
    effectiveDate,
    userFields,
    userFieldConfigurations,
  ])

  if (!isVisible) {
    return
  }

  const queryResults: UseQueryResult<any>[] = [
    accountTypes,
    branches,
    officers,
    getParentlessAndOpenAccountsResponse,
    userFieldConfigurations,
    analysisResultOptions,
    cycleDefinitions,
    earningsCreditDefinitions,
    investableBalanceDefinitions,
    requiredBalanceDefinitions,
    statementFormatPlans,
  ]

  if (queryResults.find((queryResult) => queryResult.status === 'pending')) {
    return 'Loading...'
  }

  const queryResultError = queryResults.find(
    (queryResult) => queryResult.status === 'error',
  )
  if (queryResultError) {
    throw queryResultError.error
  }

  const parentlessAndOpenAccounts =
    getParentlessAndOpenAccountsResponse.data ?? []

  return (
    <InfoSection className='mb-10 h-full gap-0'>
      <InfoSectionTitle>Select child accounts</InfoSectionTitle>
      <InfoSectionDescription>
        Select deposit and composite accounts to add as children of this
        account.
      </InfoSectionDescription>
      <AccountMutationProvider mode={SelectAccountModes.CREATE} form={form}>
        <TabGroup className='flex flex-col gap-2'>
          <div className='flex flex-row'>
            <div className='z-20 mb-4 flex w-[540px]'>
              <SearchCombo
                placeholder='Add accounts by searching for their name or number'
                noResultsMessage='No accounts found.'
                onSearch={(searchQuery: string) => {
                  const results = searchMatchingAccounts(
                    searchQuery,
                    parentlessAndOpenAccounts,
                  )
                  return results
                    .filter((account) => {
                      const isAccountInSubAccounts = !!subAccounts.find(
                        (subAccount) =>
                          accountCodesAreEqual(
                            subAccount as AccountCode,
                            account as AccountCode,
                          ),
                      )
                      return !isAccountInSubAccounts
                    })
                    .slice(0, 20)
                }}
                onSelection={async (
                  selectedItem: AccountSearchItem | undefined,
                ) => {
                  if (!selectedItem) {
                    return
                  }

                  const selectedAccount = parentlessAndOpenAccounts.filter(
                    (account) =>
                      accountCodesAreEqual(
                        account as AccountCode,
                        selectedItem as AccountCode,
                      ),
                  )[0]

                  if (selectedAccount) {
                    const selectedAccountCode =
                      toAccountCodeString(selectedAccount)
                    const mappings = await queryClient.fetchQuery(
                      accountQueries('/getAccountMappings', {
                        code: selectedAccountCode,
                        effectiveDate,
                      }),
                    )

                    const accountsMap = mappingsToAccountsMap([
                      {
                        child: selectedAccount,
                        parent: null,
                      },
                      ...apiMappingWithKeyListSelector(mappings ?? []),
                    ])
                    form.setFieldValue(
                      'subAccounts',
                      subAccounts.concat(accountsMap[selectedAccountCode]),
                    )
                  }
                }}
                renderOption={(item: AccountSearchItem) => {
                  return (
                    <ComboboxOption
                      key={`${item.applicationId}${item.accountNumber}`}
                      value={item}
                      className='cursor-default select-none px-4 py-2 data-[focus]:bg-app-color-bg-brand-secondary data-[focus]:outline-none'
                    >
                      <NameCode
                        name={item.shortName}
                        code={formatAccountCode(item as AccountCode)}
                      />
                    </ComboboxOption>
                  )
                }}
              />
            </div>

            {/* TODO: AFIN-261 Implement bulk import of child accounts on Add accounts view */}
            <Link
              className='w-1/8 ml-auto flex max-h-6 flex-row gap-1 font-semibold text-app-color-fg-brand-primary hover:underline'
              href='/#'
            >
              <ArrowUpTrayIcon className='mt-1 h-4 w-4' />
              Bulk add child accounts
            </Link>
          </div>

          <div className='flex flex-row gap-3'>
            <div>View</div>
            <SegmentedTabList>
              <SegmentedTab>List</SegmentedTab>
              <SegmentedTab>Graphical</SegmentedTab>
            </SegmentedTabList>
          </div>

          <TabPanels>
            <TabPanel>
              <SelectAccountsList form={form} />
            </TabPanel>
            <TabPanel>
              <ReactFlowProvider>
                <SelectAccountsHierarchy form={form} />
              </ReactFlowProvider>
            </TabPanel>
          </TabPanels>
        </TabGroup>
      </AccountMutationProvider>
    </InfoSection>
  )
}
