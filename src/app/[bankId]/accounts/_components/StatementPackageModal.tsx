import React, { useCallback, useMemo, useState } from 'react'
import {
  AccountCode,
  Address,
  AddressCode,
  AddressFormSchema,
  BoundedAccountListItem,
  HydratedStatementPackage,
  PackageDeliveryLabels,
  PackageTypeLabels,
  StatementPackageFormDefaults,
  StatementPackageFormSchema,
  StatementPackageFormZodSchema,
} from '../types'
import { DialogPanel } from '@headlessui/react'
import { Button } from '@/components/Button'
import { Button as HeadlessButton } from '@headlessui/react'
import { useForm } from '@tanstack/react-form'
import {
  InfoSection,
  InfoSectionDescription,
  InfoSectionTitle,
} from '@/components/InfoSection'
import { useFormMonthPicker } from '@/components/Form/useFormMonthPicker'
import { useFormTextInput } from '@/components/Form/useFormTextInput'
import { useFormSelect } from '@/components/Form/useFormSelect'
import {
  accountCodeToString,
  addressCodeToString,
  addressToAddressCode,
  formatAddress,
  formatAddressNumber,
  formToAddress,
  getNextAvailableNumber,
} from '../accountHelpers'
import { StatementPackageSelectedAccounts } from './StatementPackageSelectedAccounts'
import { SelectOption } from '@/components/Input/Select'
import { AddressModal, AddressModalProps } from './AddressModal'
import {
  ExternalModalContextState,
  Modal,
  ModalDialog,
  useExternalModalStateAndProps,
} from '@/components/Modal'
import { toUTCDateString } from '@/lib/date'
import { formValidators } from '@/api/formToApiSchema'
import { enumOptions } from '@/lib/validation/enumOptions'
export interface StatementPackageModalInstanceProps {
  statementPackage?: HydratedStatementPackage
  onSave?: (form: StatementPackageFormSchema) => void
  existingStatementPackageNumbers: number[]
}

export type StatementPackageModalProps = StatementPackageModalInstanceProps & {
  shortName: string
  accountCode: AccountCode
  accountAddresses: Address[]
  setAccountAddresses: (accountAddreses: Address[]) => void
  keyAccountAddresses: Address[]
  subAccounts: BoundedAccountListItem[]
}

interface AddressOptionProps {
  address: Address
  editable: boolean
  fromKeyAccount: boolean
  onEdit?: (address: Address) => void
}

function AddressOption(props: AddressOptionProps) {
  return (
    <div className='flex items-center justify-start gap-4'>
      <div>{formatAddress(props.address)}</div>
      <div className='flex items-center gap-2 font-light'>
        <div>
          {`${props.address.applicationId}-${formatAddressNumber(props.address.addressNumber)}`}
        </div>
        {props.fromKeyAccount && (
          <div className='text-sm'> from key account</div>
        )}
      </div>
      {props.editable ?
        <HeadlessButton
          className='ml-auto text-sm font-medium text-app-color-button-primary-bg'
          onClick={() => props.onEdit?.(props.address)}
        >
          Edit
        </HeadlessButton>
      : null}
    </div>
  )
}

export function StatementPackageModal({
  statementPackage: hydratedStatementPackage,
  shortName,
  accountCode,
  keyAccountAddresses,
  accountAddresses,
  setAccountAddresses,
  existingStatementPackageNumbers,
  subAccounts,
  onSave,
  modalState,
}: StatementPackageModalProps & ExternalModalContextState) {
  const { statementPackage, selectedAccounts, address } =
    hydratedStatementPackage ?? {}

  const accountCodeString = useMemo(
    () => accountCodeToString(accountCode),
    [accountCode],
  )

  const [canAddNewAddress, setCanAddNewAddress] = useState<boolean>(true)

  const nextAvailableStatementPackageNumber = useMemo(
    () =>
      statementPackage?.statementPackageNumber ??
      getNextAvailableNumber(existingStatementPackageNumbers),
    [existingStatementPackageNumbers, statementPackage],
  )

  const statementPackageForm = useForm<StatementPackageFormSchema>({
    defaultValues:
      statementPackage ?
        {
          // editing existing statement package
          effectiveDate: statementPackage.effectiveDate,
          statementPackageNumber: statementPackage.statementPackageNumber,
          packageDelivery: statementPackage.packageDelivery,
          addressCode: address!,
          packageType: statementPackage.packageType,
          selectedAccounts: selectedAccounts ?? [],
          existingStatementPackageNumbers,
        }
      : {
          // adding new statement package
          ...StatementPackageFormDefaults,
          existingStatementPackageNumbers,
          statementPackageNumber: nextAvailableStatementPackageNumber,
          // if key account only has 1 address and there are no addresses for this account,
          // default to that key account address
          ...(keyAccountAddresses.length === 1 &&
            accountAddresses.length === 0 && {
              addressCode: addressToAddressCode(keyAccountAddresses[0]),
            }),
        },
    validators: {
      onChange: StatementPackageFormZodSchema,
    },
    onSubmit: ({ value }) => {
      if (onSave) {
        const parsed = StatementPackageFormZodSchema.parse(value)
        onSave(parsed)
      }
      modalState?.close()
    },
  })

  const allAddresses = useMemo(() => {
    return [...keyAccountAddresses, ...accountAddresses]
  }, [keyAccountAddresses, accountAddresses])
  const [addressModalState, addressModalProps, setAddressModalProps] =
    useExternalModalStateAndProps<AddressModalProps>({})

  const showAddAddress = useCallback(() => {
    setAddressModalProps({
      accountCode,
      accountAddresses,
      onSave: (addressForm: AddressFormSchema) => {
        const newAddressToSave = {
          ...formToAddress(addressForm),
          accountCode,
          effectiveDate: toUTCDateString(new Date()),
        }
        statementPackageForm.setFieldValue(
          'addressCode',
          addressToAddressCode(newAddressToSave),
        )
        setAccountAddresses(accountAddresses.concat(newAddressToSave))
        setCanAddNewAddress(false)
      },
    })
    addressModalState.show()
  }, [
    setAddressModalProps,
    accountCode,
    accountAddresses,
    setAccountAddresses,
    statementPackageForm,
    setCanAddNewAddress,
    addressModalState,
  ])

  const showEditAddress = useCallback(
    (addressToEdit: Address) => {
      setAddressModalProps({
        address: addressToEdit,
        accountCode,
        accountAddresses,
        onSave: (addressForm: AddressFormSchema) => {
          const editedAddress = {
            ...formToAddress(addressForm),
            accountCode,
            effectiveDate: toUTCDateString(new Date()),
          }
          setAccountAddresses(
            accountAddresses.map((existingAddress) =>
              existingAddress.addressNumber === addressToEdit.addressNumber ?
                editedAddress
              : existingAddress,
            ),
          )
          statementPackageForm.setFieldValue(
            'addressCode',
            addressToAddressCode(editedAddress),
          )
        },
      })
      addressModalState.show()
    },
    [
      setAddressModalProps,
      accountCode,
      accountAddresses,
      setAccountAddresses,
      statementPackageForm,
      addressModalState,
    ],
  )

  const FormMonthPicker = useFormMonthPicker<StatementPackageFormSchema>({
    form: statementPackageForm,
  })
  const FormInput = useFormTextInput<StatementPackageFormSchema>({
    form: statementPackageForm,
  })
  const Select = useFormSelect<StatementPackageFormSchema>({
    form: statementPackageForm,
  })

  return (
    <Modal modalState={modalState}>
      <ModalDialog>
        <div className='fixed inset-0 z-10 w-screen overflow-y-auto'>
          <DialogPanel
            transition
            className='data-[closed]:transform-[scale(95%)] flex min-h-screen flex-col bg-zinc-100 duration-300 ease-out data-[closed]:opacity-0'
            data-testid='statementPackageModal'
          >
            <div className='mx-24 mt-8 flex flex-col gap-4'>
              <header className='text-2xl font-medium'>
                {statementPackage ? 'Edit' : 'Add'} statement package
              </header>
              <InfoSection className='h-full gap-0'>
                <div>
                  <InfoSectionTitle>Statement package</InfoSectionTitle>
                  <InfoSectionDescription>
                    Configure delivery settings and select which accounts to
                    include inthe statement package.
                  </InfoSectionDescription>
                </div>

                <div className='grid grid-cols-2 gap-0 gap-x-20'>
                  <div className='mb-4'>
                    <FormMonthPicker
                      name='effectiveDate'
                      label='Effective date *'
                    />
                  </div>

                  <FormInput
                    className='col-start-1'
                    name='statementPackageNumber'
                    label='Package code'
                    required
                    readonly={!!hydratedStatementPackage}
                  />

                  <Select
                    name='packageDelivery'
                    label='Delivery method'
                    placeholder='Select delivery method'
                    required
                    options={enumOptions(
                      formValidators.statementPackage.shape.packageDelivery,
                    )}
                    renderOption={(value) => PackageDeliveryLabels[value]}
                    renderSelected={(value) => PackageDeliveryLabels[value]}
                  />

                  <div className='col-span-2 mb-4 flex flex-col gap-0'>
                    <Select
                      name='addressCode'
                      label='Delivery address'
                      required
                      disabled={allAddresses.length === 0}
                      renderSelected={(value: AddressCode) => {
                        const address: Address | undefined =
                          value.addressNumber > 0 ?
                            allAddresses.find(
                              (address) =>
                                addressCodeToString(value) ===
                                addressCodeToString(
                                  addressToAddressCode(address),
                                ),
                            )
                          : undefined
                        return address ?
                            <AddressOption
                              address={address}
                              editable={false}
                              fromKeyAccount={
                                accountCodeToString(value.accountCode) !==
                                accountCodeString
                              }
                            />
                          : <div className='text-zinc-400'>
                              Select delivery address
                            </div>
                      }}
                      renderErrors={false}
                    >
                      {allAddresses.map((address) => {
                        const fromThisAccount =
                          accountCodeToString(address.accountCode) ===
                          accountCodeString
                        const addressCode = addressToAddressCode(address)
                        return (
                          <SelectOption
                            key={addressCodeToString(addressCode)}
                            value={addressCode}
                            focusClassNames='data-[focus]:bg-indigo-100'
                          >
                            <AddressOption
                              address={address}
                              editable={fromThisAccount}
                              onEdit={showEditAddress}
                              fromKeyAccount={!fromThisAccount}
                            />
                          </SelectOption>
                        )
                      })}
                    </Select>
                    <div className='mt-1 text-sm'>
                      {canAddNewAddress ?
                        <HeadlessButton
                          className='font-medium text-app-color-button-primary-bg'
                          onClick={() => showAddAddress()}
                        >
                          Add a new address
                        </HeadlessButton>
                      : <span>&nbsp;</span>}
                    </div>
                  </div>

                  <Select
                    name='packageType'
                    label='Include accounts'
                    required
                    options={enumOptions(
                      formValidators.statementPackage.shape.packageType,
                    )}
                    renderOption={(value) => PackageTypeLabels[value]}
                    renderSelected={(value) => PackageTypeLabels[value]}
                    onChange={(value, form) => {
                      if (value !== 'SELECTED_ACCOUNTS')
                        // clear selected accounts
                        form.setFieldValue('selectedAccounts', [])
                    }}
                  />

                  <div className='col-span-2'>
                    <StatementPackageSelectedAccounts
                      accountCode={accountCode}
                      shortName={shortName}
                      subAccounts={subAccounts}
                      form={statementPackageForm}
                    />
                  </div>
                </div>
              </InfoSection>
            </div>
            <footer className='mt-auto flex justify-end gap-4 border-t bg-white px-12 py-10'>
              <Button
                className='btn w-60'
                onClick={(e) => {
                  modalState?.close()
                }}
              >
                Cancel
              </Button>
              <Button
                className='btn-primary w-60'
                onClick={statementPackageForm.handleSubmit}
              >
                Save
              </Button>
            </footer>
          </DialogPanel>
        </div>
        {addressModalProps && (
          <AddressModal modalState={addressModalState} {...addressModalProps} />
        )}
      </ModalDialog>
    </Modal>
  )
}
