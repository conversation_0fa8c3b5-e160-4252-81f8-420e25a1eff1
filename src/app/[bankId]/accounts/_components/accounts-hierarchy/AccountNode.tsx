import { useStore } from '@tanstack/react-form'
import { Handle, NodeProps, Position, type Node } from '@xyflow/react'
import clsx from 'clsx'
import { memo, useMemo } from 'react'
import {
  formatAccountCode,
  handleRemoveAccount,
  isAccountKeyAccount,
} from '../../accountHelpers'
import {
  AccountCode,
  AccountListItem,
  SelectAccountFormSchemaType,
} from '../../types'
import SelectAccountsHierarchyPopover from './SelectAccountsHierarchyPopover'
import { useChangeKeyAccount } from '../../_hooks/useChangeKeyAccount'
import {
  SelectAccountModes,
  useAccountMutationContext,
} from '../../_context/AccountMutationContext'
import { AccountNodeProvider } from '../../_context/AccountNodeContext'

export type AccountNode = Node<
  {
    accountListItem: AccountListItem
    form: SelectAccountFormSchemaType
    isRoot: boolean
  },
  'selectorNode'
>

export function AccountListItemNode({
  data,
}: {
  data: {
    keyAccount: AccountCode
    accountListItem: AccountListItem
    isRoot: boolean
  }
}) {
  const isComposite = !data.isRoot && data.accountListItem.applicationId === 'C'

  return (
    <div
      className={clsx(
        'rounded-md px-2 py-2 shadow-md',
        data.isRoot ? 'w-[250px] border-[3px] border-black bg-indigo-100'
        : isComposite ? 'w-[250px] border-2 border-black bg-white'
        : 'w-[250px] border-2 border-zinc-200 bg-zinc-50',
      )}
    >
      <div className='mx-2 min-w-min text-left'>
        <div className='text-nowrap text-sm font-bold'>
          {data.accountListItem.shortName}
        </div>
        <div className='flex flex-row gap-2'>
          <div className='text-sm text-gray-500'>
            {formatAccountCode(data.accountListItem as AccountCode)}
          </div>
          {isAccountKeyAccount(data.keyAccount, data.accountListItem) && (
            <div className='max-w-24 rounded-md bg-indigo-600 px-2 pt-0.5 text-xs font-light text-indigo-100'>
              Key account
            </div>
          )}
        </div>
      </div>
      <Handle type='target' position={Position.Top} className='opacity-0' />
      <Handle type='source' position={Position.Bottom} className='opacity-0' />
    </div>
  )
}

function AccountNode({ data }: NodeProps<AccountNode>) {
  const [effectiveDate, subAccounts, keyAccountCode] = useStore(
    data.form.store,
    (state) => [
      state.values.accountInfo.effectiveDate,
      state.values.subAccounts,
      state.values.demographics.keyAccountCode,
    ],
  )
  const { mode } = useAccountMutationContext()

  const { changeKeyAccountInCreateForm } = useChangeKeyAccount({
    keyAccountCode,
    effectiveDate: effectiveDate,
  })

  const onSelectKeyAccount = () => {
    switch (mode) {
      case SelectAccountModes.CREATE:
        changeKeyAccountInCreateForm(data.accountListItem, data.form)
        break
      case SelectAccountModes.EDIT:
        console.log('EDIT')
        break
      default:
        throw 'Need a valid mode CREATE | EDIT'
    }
  }

  const onRemoveAccount = () => {
    switch (mode) {
      case SelectAccountModes.CREATE:
        handleRemoveAccount(
          data.accountListItem,
          data.form,
          subAccounts,
          keyAccountCode,
        )
        break
      case SelectAccountModes.EDIT:
        console.log('EDIT')
        break
      default:
        throw 'Need a valid mode CREATE | EDIT'
    }
  }

  const accountNode = useMemo(
    () => (
      <AccountListItemNode
        data={{
          keyAccount: {
            applicationId: keyAccountCode?.applicationId as 'C' | 'D',
            accountNumber: keyAccountCode?.accountNumber as string,
            bankNumber: keyAccountCode?.bankNumber as string,
          },
          ...data,
        }}
      />
    ),
    [data, keyAccountCode?.accountNumber, keyAccountCode?.applicationId],
  )

  return data.isRoot && mode === SelectAccountModes.CREATE ?
      accountNode
    : <AccountNodeProvider currentAccount={data.accountListItem}>
        <SelectAccountsHierarchyPopover
          AccountsNode={accountNode}
          onSelectKeyAccount={onSelectKeyAccount}
          onRemoveAccount={onRemoveAccount}
        />
      </AccountNodeProvider>
}

export default memo(AccountNode)
