import dagre from '@dagrejs/dagre'
import {
  Background,
  BackgroundVariant,
  ConnectionLineType,
  Controls,
  CoordinateExtent,
  Edge,
  Node,
  Position,
  ReactFlow,
  useEdgesState,
  useNodesState,
  useReactFlow,
  XYPosition,
} from '@xyflow/react'
import { useEffect, useMemo } from 'react'

import '@xyflow/react/dist/style.css'

import { AccountListItemNode } from '@/app/[bankId]/accounts/_components/accounts-hierarchy/AccountNode'
import {
  formatAccountCode,
  getDepth,
  mappingsToAccountsMap,
} from '../../accountHelpers'
import { HydratedAccountWithKeyAccountMappingForm } from '@/api/formToApiSchema'
import {
  AccountCode,
  AccountListItem,
  BoundedAccountListItem,
} from '../../types'

const nodeExtent: CoordinateExtent = [
  [0, 0],
  [100000, 100000],
]

function getLead(accounts: Record<string, BoundedAccountListItem>) {
  let lead
  let max = 0

  for (const account in accounts) {
    const depth = getDepth(accounts[account])
    if (depth > max) {
      lead = accounts[account]
      max = depth
    }
  }
  return lead
}
const initialNodes: Node[] = []
const initialEdges: Edge[] = []
function ViewAccountsHierarchy({
  accountMapping,
}: {
  accountMapping: HydratedAccountWithKeyAccountMappingForm[]
}) {
  const leadAccount = getLead(
    mappingsToAccountsMap([...(accountMapping ?? [])]),
  )

  const proOptions = { hideAttribution: true }

  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes)
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges)
  useEffect(() => {
    const reInitNodes: Node[] = []
    const reInitEdges: Edge[] = []
    const position: XYPosition = { x: 0, y: 0 }

    if (leadAccount) {
      // handle the parent account
      reInitNodes.push({
        id: formatAccountCode({
          applicationId: leadAccount.applicationId,
          accountNumber: leadAccount.accountNumber,
          bankNumber: leadAccount.bankNumber,
        }),
        data: {
          accountListItem: {
            shortName: leadAccount.shortName,
            applicationId: leadAccount.applicationId,
            accountNumber: leadAccount.accountNumber,
          } as AccountListItem,
          isRoot: true,
        },
        // position will be set by dager
        position,
        type: 'accountNode',
      } as Node)

      // push parent edges
      leadAccount?.children?.forEach((initParentChild) => {
        reInitEdges.push({
          id: formatAccountCode(initParentChild as AccountCode),
          source: formatAccountCode({
            applicationId: leadAccount.applicationId,
            accountNumber: leadAccount.accountNumber,
            bankNumber: leadAccount.bankNumber,
          }),
          target: formatAccountCode(initParentChild as AccountCode),
          className: 'stroke-black stroke-2',
          type: 'smoothstep',
        })
      })
    }
    const set = new Set()

    const transformData = (subAccountParam: BoundedAccountListItem[]) => {
      subAccountParam.forEach((subAccount, idx) => {
        if (!set.has(formatAccountCode(subAccount as AccountCode))) {
          reInitNodes.push({
            id: formatAccountCode(subAccount as AccountCode),
            data: {
              accountListItem: subAccount as AccountListItem,
              isRoot: false,
            },
            // position will be set by dager
            position,
            type: 'accountNode',
          } as Node)
          if (subAccount.children) {
            subAccount.children.forEach((subAccountChild) => {
              reInitEdges.push({
                id: formatAccountCode(subAccountChild as AccountCode),
                source: formatAccountCode(subAccount as AccountCode),
                target: formatAccountCode(subAccountChild as AccountCode),
                className: 'stroke-black stroke-2',
                type: 'smoothstep',
              })
              transformData(subAccount.children!)
            })
          }
          set.add(formatAccountCode(subAccount as AccountCode))
        }
      })
    }
    if (leadAccount && leadAccount.children) {
      transformData(leadAccount.children)
    }

    setEdges(reInitEdges)
    const dagreGraph = new dagre.graphlib.Graph()
    dagreGraph.setDefaultEdgeLabel(() => ({}))

    dagreGraph.setGraph({
      rankdir: 'TB',
      edgesep: 20,
      ranksep: 100,
      nodesep: 10,
    })

    reInitNodes.forEach((node) => {
      dagreGraph.setNode(node.id, { width: 250, height: 50 })
    })

    reInitEdges.forEach((edge) => {
      dagreGraph.setEdge(edge.source, edge.target, { curve: 0.2 })
    })

    dagre.layout(dagreGraph)

    const layoutedNodes = reInitNodes.map((node) => {
      const nodeWithPosition = dagreGraph.node(node.id)

      return {
        ...node,
        targetPosition: Position.Top,
        sourcePosition: Position.Bottom,
        position: {
          x: nodeWithPosition.x,
          y: nodeWithPosition.y,
        },
      }
    })
    setNodes(layoutedNodes)
  }, [setEdges, setNodes, initialNodes, initialEdges])

  return (
    <>
      {nodes.length > 0 && (
        <div className='h-[550px]'>
          <ReactFlow
            nodes={nodes}
            edges={edges}
            onNodesChange={onNodesChange}
            onEdgesChange={onEdgesChange}
            connectionLineType={ConnectionLineType.SmoothStep}
            nodeExtent={nodeExtent}
            proOptions={proOptions}
            nodeTypes={{
              accountNode: AccountListItemNode,
            }}
            fitView
          >
            <Background color='#aaa' variant={BackgroundVariant.Dots} />
            <Controls />
          </ReactFlow>
        </div>
      )}
    </>
  )
}

export default ViewAccountsHierarchy
