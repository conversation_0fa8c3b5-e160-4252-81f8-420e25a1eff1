import React, { memo } from 'react'
import { Popover, PopoverButton, PopoverPanel } from '@headlessui/react'
import { Button } from '@/components/Button'
import {
  SelectAccountModes,
  useAccountMutationContext,
} from '../../_context/AccountMutationContext'
import { AddAccountModal } from '../relationship-modal/AddAccountModal'
import { RemoveAccountModal } from '../relationship-modal/RemoveAccountModal'
import { If } from '@/components/If'
import { KeyAccountModal } from '../relationship-modal/KeyAccountModal'
import { MoveAccountModal } from '../relationship-modal/MoveAccountModal'
import { useAccountNodeContext } from '../../_context/AccountNodeContext'
import { useStore } from '@tanstack/react-form'
import { accountCodesAreEqual } from '../../accountHelpers'
import { RemoveKeyAccountModal } from '../relationship-modal/RemoveKeyAccountModal'

interface SelectAccountHierarchyPopoverProps {
  AccountsNode: React.JSX.Element
  onSelectKeyAccount: () => void
  onRemoveAccount: () => void
}

function SelectAccountsHierarchyPopover({
  AccountsNode,
  onSelectKeyAccount,
  onRemoveAccount,
}: SelectAccountHierarchyPopoverProps) {
  const { mode, form } = useAccountMutationContext()

  const { currentAccount } = useAccountNodeContext()
  const {
    accountInfo: { keyAccountCode },
  } = useStore(form.store, (state) => state.values)

  return (
    <Popover className='inline-flex'>
      <PopoverButton role='button'>{AccountsNode}</PopoverButton>
      <PopoverPanel
        anchor='bottom start'
        className='flex min-w-[280px] flex-col rounded-md border bg-white text-sm'
      >
        <If true={mode === SelectAccountModes.CREATE}>
          <Button
            onClick={() => onSelectKeyAccount()}
            className='flex items-center gap-1 border-none p-3 hover:bg-indigo-100'
          >
            Select as key account
          </Button>
          <Button
            onClick={() => {}}
            className='flex items-center gap-1 border-none p-3 hover:bg-indigo-100'
          >
            View account details
          </Button>
          <Button
            onClick={() => onRemoveAccount()}
            className='flex items-center gap-1 border-none p-3 hover:bg-indigo-100'
          >
            Remove
          </Button>
        </If>
        <If true={mode === SelectAccountModes.EDIT}>
          <AddAccountModal />
          <MoveAccountModal />
          <RemoveAccountModal />
          {(
            !!keyAccountCode &&
            accountCodesAreEqual(currentAccount, keyAccountCode)
          ) ?
            <RemoveKeyAccountModal />
          : <KeyAccountModal />}
        </If>
      </PopoverPanel>
    </Popover>
  )
}

export default memo(SelectAccountsHierarchyPopover)
