import dagre from '@dagrejs/dagre'
import {
  Background,
  BackgroundVariant,
  Controls,
  CoordinateExtent,
  Position,
  ReactFlow,
  XYPosition,
  useEdgesState,
  useNodesState,
  useReactFlow,
  type Edge,
  type Node,
} from '@xyflow/react'
import { useEffect } from 'react'

import '@xyflow/react/dist/style.css'

import { useStore } from '@tanstack/react-form'
import { formatAccountCode } from '../../accountHelpers'
import {
  AccountCode,
  AccountListItem,
  BoundedAccountListItem,
  SelectAccountFormSchemaType,
} from '../../types'
import AccountNode from './AccountNode'
import { useAccountMutationContext } from '../../_context/AccountMutationContext'

interface SelectAccountHierarchyProps {
  form: SelectAccountFormSchemaType
}

const nodeExtent: CoordinateExtent = [
  [0, 0],
  [100000, 100000],
]
const initialNodes: Node[] = []
const initialEdges: Edge[] = []

function SelectAccountsHierarchy({ form }: SelectAccountHierarchyProps) {
  const {
    accountInfo: { shortName, accountNumber, applicationId, bankNumber },
    subAccounts,
  } = useStore(form.store, (state) => state.values)

  const { leadAccount } = useAccountMutationContext()

  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes)
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges)

  const proOptions = { hideAttribution: true }

  useEffect(() => {
    const reInitNodes: Node[] = []
    const reInitEdges: Edge[] = []
    const position: XYPosition = { x: 0, y: 0 }

    const topAccount =
      leadAccount ? leadAccount : (
        { shortName, accountNumber, applicationId, bankNumber }
      )

    // handle the parent account
    reInitNodes.push({
      id: formatAccountCode({
        applicationId: topAccount.applicationId,
        accountNumber: topAccount.accountNumber,
        bankNumber: topAccount.bankNumber,
      }),
      data: {
        accountListItem: {
          shortName: topAccount.shortName,
          applicationId: topAccount.applicationId,
          accountNumber: topAccount.accountNumber,
        } as AccountListItem,
        form: form,
        isRoot: true,
      },
      // position will be set by dager
      position,
      type: 'accountNode',
    } as Node)

    // push parent edges
    subAccounts.forEach((initParentChild) => {
      reInitEdges.push({
        id: formatAccountCode(initParentChild as AccountCode),
        source: formatAccountCode({
          applicationId: topAccount.applicationId,
          accountNumber: topAccount.accountNumber,
          bankNumber: topAccount.bankNumber,
        }),
        target: formatAccountCode(initParentChild as AccountCode),
        className: 'stroke-black stroke-2',
        type: 'smoothstep',
      })
    })
    const set = new Set()

    const transformData = (subAccountParam: BoundedAccountListItem[]) => {
      subAccountParam.forEach((subAccount, idx) => {
        if (!set.has(formatAccountCode(subAccount as AccountCode))) {
          reInitNodes.push({
            id: formatAccountCode(subAccount as AccountCode),
            data: {
              accountListItem: subAccount as AccountListItem,
              form: form,
              isRoot: false,
            },
            // position will be set by dager
            position,
            type: 'accountNode',
          } as Node)
          if (subAccount.children) {
            subAccount.children.forEach((subAccountChild) => {
              reInitEdges.push({
                id: formatAccountCode(subAccountChild as AccountCode),
                source: formatAccountCode(subAccount as AccountCode),
                target: formatAccountCode(subAccountChild as AccountCode),
                className: 'stroke-black stroke-2',
                type: 'smoothstep',
              })
              transformData(subAccount.children!)
            })
          }
          set.add(formatAccountCode(subAccount as AccountCode))
        }
      })
    }
    transformData(subAccounts)
    setEdges(reInitEdges)
    const dagreGraph = new dagre.graphlib.Graph()
    dagreGraph.setDefaultEdgeLabel(() => ({}))

    dagreGraph.setGraph({
      rankdir: 'TB',
      edgesep: 20,
      ranksep: 100,
      nodesep: 10,
    })

    reInitNodes.forEach((node) => {
      dagreGraph.setNode(node.id, { width: 250, height: 50 })
    })

    reInitEdges.forEach((edge) => {
      dagreGraph.setEdge(edge.source, edge.target, { curve: 0.2 })
    })

    dagre.layout(dagreGraph)

    const layoutedNodes = reInitNodes.map((node) => {
      const nodeWithPosition = dagreGraph.node(node.id)

      return {
        ...node,
        targetPosition: Position.Top,
        sourcePosition: Position.Bottom,
        position: {
          x: nodeWithPosition.x,
          y: nodeWithPosition.y,
        },
      }
    })

    setNodes(layoutedNodes)
  }, [
    subAccounts,
    shortName,
    accountNumber,
    applicationId,
    form,
    setEdges,
    setNodes,
  ])

  return (
    <>
      {nodes.length > 0 && (
        <div className='h-[600px] basis-full'>
          <ReactFlow
            nodes={nodes}
            edges={edges}
            onNodesChange={onNodesChange}
            onEdgesChange={onEdgesChange}
            nodeExtent={nodeExtent}
            proOptions={proOptions}
            nodeTypes={{
              accountNode: AccountNode,
            }}
            fitView
          >
            <Background color='#aaa' variant={BackgroundVariant.Dots} />
            <Controls />
          </ReactFlow>
        </div>
      )}
    </>
  )
}

export default SelectAccountsHierarchy
