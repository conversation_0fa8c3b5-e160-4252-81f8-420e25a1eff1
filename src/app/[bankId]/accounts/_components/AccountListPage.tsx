'use client'

import { SortedTable } from '@/components/Table/SortedTable'
import { formatToMonthYearFromDate, formatToServerString } from '@/lib/date'
import { data } from '@/lib/unions/Union'
import { useQuery } from '@tanstack/react-query'
import { ColumnDef } from '@tanstack/react-table'
import { useRouter } from 'next/navigation'
import { useMemo } from 'react'
import { accountTypeQueries } from '../../configuration/account-type/queries'
import { useRoute } from '../[effectiveMonth]/routing'
import { useAccounts } from '../_hooks/useAccounts'
import { formatAccountCode, formatAccountCodeForRoute } from '../accountHelpers'
import { routeTo as accountsRootRoutTo } from '../routing'
import { Account, AccountCode } from '../types'
import DoNotReuseLoadingSpinnerForAccountList from '@/app/_components/svgComponents/DoNotReuseLoadingSpinnerForAccountList'

type AccountsListColumnDef = ColumnDef<Account>

export default function AccountListPage() {
  const router = useRouter()
  const route = data(useRoute()!)
  const routeParams = route.params

  const effectiveDate = formatToServerString(routeParams.effectiveMonth)

  const { accounts, ...accountsResult } = useAccounts({ effectiveDate })

  const getAccountTypesResponse = useQuery(
    accountTypeQueries('/getAccountTypes', {
      effectiveDate,
    }),
  )

  const accountsData: Account[] = accounts ?? []

  const columns = useMemo<AccountsListColumnDef[]>(() => {
    return [
      {
        header: 'Account name',
        accessorKey: 'shortName',
      },
      {
        header: 'Account number',
        accessorFn: (account) => formatAccountCode(account),
      },
      {
        header: 'Account type',
        cell: ({ row }) => (
          <div className='flex flex-col'>
            <div className='text-nowrap'>
              {
                (getAccountTypesResponse.data ?? []).find(
                  (accountType) =>
                    accountType.accountTypeCode ===
                    row.original.analysisAccountTypeCode,
                )?.description
              }
            </div>
            <div className='text-sm text-app-color-secondary'>
              {row.original.analysisAccountTypeCode}
            </div>
          </div>
        ),
      },
      {
        header: 'Key account',
        cell: ({ row }) => {
          return (
            row.original.keyAccountCode &&
            formatAccountCode(row.original.keyAccountCode)
          )
        },
      },
      {
        header: 'Account open date',
        accessorKey: 'openDate',
        cell: ({ getValue }) => formatToMonthYearFromDate(getValue<Date>()),
      },
    ]
  }, [getAccountTypesResponse])

  const columnFilters = useMemo(() => [], [])

  if (
    accountsResult.status === 'pending' ||
    getAccountTypesResponse.status === 'pending'
  ) {
    return (
      <div className='opacity-50'>
        <SortedTable
          data={[]}
          columns={columns}
          columnFilters={columnFilters}
          handleRowDoubleClick={({ original: row }) => {
            if (row.accountNumber && row.applicationId && row.effectiveDate) {
              router.push(
                accountsRootRoutTo(
                  '/accounts/[effectiveMonth]/view/[accountCode]/details',
                  {
                    ...routeParams,
                    accountCode: formatAccountCodeForRoute(row as AccountCode),
                  },
                ),
              )
            } else {
              console.error('Account not available')
            }
          }}
        />
        <div className='flex h-full w-full items-center justify-center bg-white p-8'>
          <div role='status'>
            <DoNotReuseLoadingSpinnerForAccountList />
            <span className='sr-only'>Loading...</span>
          </div>
        </div>
      </div>
    )
  }

  if (accountsResult.status === 'error') {
    console.error(accountsResult.error)
    return `[${accountsResult.error.name}] ${accountsResult.error.message}`
  }

  return (
    <>
      <SortedTable
        data={accountsData}
        columns={columns}
        columnFilters={columnFilters}
        handleRowDoubleClick={({ original: row }) => {
          if (row.accountNumber && row.applicationId && row.effectiveDate) {
            router.push(
              accountsRootRoutTo(
                '/accounts/[effectiveMonth]/view/[accountCode]/details',
                {
                  ...routeParams,
                  accountCode: formatAccountCodeForRoute(row as AccountCode),
                },
              ),
            )
          } else {
            console.error('Account not available')
          }
        }}
      />
    </>
  )
}
