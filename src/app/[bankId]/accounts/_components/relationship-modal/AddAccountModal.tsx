import { Button } from '@/components/Button'
import {
  Modal,
  ModalCancelButton,
  ModalConfirmButton,
  Modal<PERSON>ooter,
  ModalTitle,
  ModalWindow,
  useExternalModalStateAndProps,
} from '@/components/Modal'
import { SearchCombo } from '../../(compositeAccount)/add/SearchCombo'
import {
  accountCodesAreEqual,
  apiAccountSelector,
  formatAccountCode,
  searchMatchingAccounts,
  toAccountCodeString,
} from '../../accountHelpers'
import { AccountCode, AccountSearchItem } from '../../types'
import { accountQueries } from '../../queries'
import { ComboboxOption } from '@headlessui/react'
import { NameCode } from '../NameCode'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import { useAccountMutationContext } from '../../_context/AccountMutationContext'
import { useStore } from '@tanstack/react-form'
import { formatToServerString, toUIFormat } from '@/lib/date'
import { useState } from 'react'
import { PlusIcon } from '@heroicons/react/24/outline'
import { useAccountNodeContext } from '../../_context/AccountNodeContext'

export const AddAccountModal = () => {
  const [modalState] = useExternalModalStateAndProps({})
  const { form, addMapping, leadAccount } = useAccountMutationContext()
  const { currentAccount } = useAccountNodeContext()
  const queryClient = useQueryClient()

  const [selectedItem, setSelectedItem] = useState<
    AccountSearchItem | undefined
  >()
  const {
    accountInfo: { shortName, accountNumber, applicationId, effectiveDate },
    subAccounts,
  } = useStore(form.store, (state) => state.values)

  const getParentlessAndOpenAccountsResponse = useQuery({
    ...accountQueries('/getParentlessOpenAccounts', {
      effectiveDate: toUIFormat(effectiveDate),
    }),
    select: (apiAccounts) =>
      apiAccounts.map((apiAccount) => ({
        ...apiAccountSelector(apiAccount),
        // don't actually need key account code for parentless open accounts,
        // but adding here for type safety
        keyAccountCode: null,
      })),
  })

  const parentlessAndOpenAccounts =
    getParentlessAndOpenAccountsResponse.data ?? []

  return (
    <>
      <Button
        onClick={() => modalState.show()}
        className='flex items-center gap-1 border-none p-3 hover:bg-indigo-100'
      >
        <div className='flex items-center justify-between gap-2'>
          <span>
            <PlusIcon className='h-4 w-4' />
          </span>
          <div>Add account to this relationship</div>
        </div>
      </Button>
      <Modal modalState={modalState}>
        <ModalWindow className='w-[497px]'>
          <ModalTitle>Add account to this relationship</ModalTitle>
          <p className='text-app-color-secondary'>
            If adding a composite account, this will also add its children to
            the relationship.
          </p>

          <div className='z-20 flex flex-col py-4'>
            <p className='pb-2'>Select account</p>

            <SearchCombo
              displayValue={(selectedItem) => selectedItem?.shortName}
              setSelectedItem={setSelectedItem}
              placeholder='Add accounts by searching for their name or number'
              noResultsMessage='No accounts found.'
              onSearch={(searchQuery: string) => {
                const results = searchMatchingAccounts(
                  searchQuery,
                  parentlessAndOpenAccounts,
                )
                return results
                  .filter((account) => {
                    const isAccountInSubAccounts = !!subAccounts.find(
                      (subAccount) =>
                        accountCodesAreEqual(
                          subAccount as AccountCode,
                          account as AccountCode,
                        ),
                    )
                    return (
                      !isAccountInSubAccounts &&
                      (leadAccount ?
                        !accountCodesAreEqual(
                          account as AccountCode,
                          leadAccount,
                        )
                      : true)
                    )
                  })
                  .slice(0, 20)
              }}
              onSelection={async (
                selectedItem: AccountSearchItem | undefined,
              ) => {
                if (!selectedItem) {
                  return
                }
              }}
              renderOption={(item: AccountSearchItem) => {
                return (
                  <ComboboxOption
                    key={`dropdown-${item.accountNumber}-${item.applicationId}`}
                    value={item}
                    className='cursor-default select-none px-4 py-2 data-[focus]:bg-app-color-bg-brand-secondary data-[focus]:outline-none'
                  >
                    <NameCode
                      name={item.shortName}
                      code={formatAccountCode(item as AccountCode)}
                    />
                  </ComboboxOption>
                )
              }}
            />
          </div>
          <ModalFooter>
            <ModalCancelButton>Cancel</ModalCancelButton>
            <ModalConfirmButton
              disabled={selectedItem === undefined}
              className={
                selectedItem === undefined ? 'btn-disabled' : 'btn-primary'
              }
              onClick={async (_, modalContext) => {
                if (selectedItem) {
                  await addMapping.mutate({
                    childAccountCode: selectedItem as AccountCode,
                    parentAccountCode: currentAccount,
                    effectiveDate: toUIFormat(effectiveDate),
                  })

                  setSelectedItem(undefined)
                  modalContext.close()
                }
              }}
            >
              Add
            </ModalConfirmButton>
          </ModalFooter>
        </ModalWindow>
      </Modal>
    </>
  )
}
