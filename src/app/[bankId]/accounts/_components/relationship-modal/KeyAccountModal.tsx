import { Button } from '@/components/Button'
import {
  Modal,
  ModalCancelButton,
  ModalConfirmButton,
  Modal<PERSON>ooter,
  ModalTitle,
  ModalWindow,
  useExternalModalStateAndProps,
} from '@/components/Modal'
import { StarIcon } from '@heroicons/react/24/outline'
import { useStore } from '@tanstack/react-form'
import { useAccountMutationContext } from '../../_context/AccountMutationContext'
import { toUIFormat } from '@/lib/date'
import { useAccountNodeContext } from '../../_context/AccountNodeContext'

export const KeyAccountModal = () => {
  const [modalState] = useExternalModalStateAndProps({})
  const { form, setKeyAccount } = useAccountMutationContext()
  const {
    accountInfo: {
      shortName,
      accountNumber,
      applicationId,
      bankNumber,
      effectiveDate,
      keyAccountCode,
    },
    subAccounts,
  } = useStore(form.store, (state) => state.values)

  const { currentAccount } = useAccountNodeContext()

  return (
    <>
      <Button
        onClick={() => modalState.show()}
        className='flex items-center gap-1 border-none p-3 hover:bg-indigo-100'
      >
        <div className='flex items-center justify-between gap-2'>
          <span>
            <StarIcon className='h-4 w-4' />
          </span>
          <div>Select as key account</div>
        </div>
      </Button>
      <Modal modalState={modalState}>
        <ModalWindow className='w-[497px]'>
          <ModalTitle>Change key account</ModalTitle>
          <p className='text-app-color-secondary'>
            Changing key accounts may change both the demographic and pricing
            options and settlement and processing options for this composite
            account.
          </p>
          <ModalFooter>
            <ModalCancelButton>Cancel</ModalCancelButton>
            <ModalConfirmButton
              className='btn-primary'
              onClick={async (_, modalContext) => {
                await setKeyAccount.mutate({
                  childAccountCode: {
                    accountNumber: currentAccount.accountNumber,
                    applicationId: currentAccount.applicationId,
                    bankNumber: currentAccount.bankNumber,
                  },
                  effectiveDate: toUIFormat(effectiveDate),
                  parentAccountCode: {
                    accountNumber,
                    applicationId,
                    bankNumber,
                  },
                })
                modalContext.close()
              }}
            >
              Yes
            </ModalConfirmButton>
          </ModalFooter>
        </ModalWindow>
      </Modal>
    </>
  )
}
