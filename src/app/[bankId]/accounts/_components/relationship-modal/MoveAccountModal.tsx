import { Button } from '@/components/Button'
import {
  Modal,
  ModalCancelButton,
  ModalConfirmButton,
  Modal<PERSON>ooter,
  ModalTitle,
  ModalWindow,
  useExternalModalStateAndProps,
} from '@/components/Modal'
import { toUIFormat } from '@/lib/date'
import { ComboboxOption } from '@headlessui/react'
import { ArrowRightStartOnRectangleIcon } from '@heroicons/react/24/outline'
import { useStore } from '@tanstack/react-form'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import { useState } from 'react'
import { SearchCombo } from '../../(compositeAccount)/add/SearchCombo'
import { useAccountMutationContext } from '../../_context/AccountMutationContext'
import { useAccountNodeContext } from '../../_context/AccountNodeContext'
import {
  accountCodesAreEqual,
  apiAccountSelector,
  formatAccountCode,
  searchMatchingAccounts,
} from '../../accountHelpers'
import { accountQueries } from '../../queries'
import { AccountCode, AccountSearchItem } from '../../types'
import { NameCode } from '../NameCode'

export const MoveAccountModal = () => {
  const [modalState] = useExternalModalStateAndProps({})
  const { form, updateMapping } = useAccountMutationContext()
  const { currentAccount } = useAccountNodeContext()

  const [selectedItem, setSelectedItem] = useState<
    AccountSearchItem | undefined
  >()
  const {
    accountInfo: { shortName, accountNumber, applicationId, effectiveDate },
    subAccounts,
  } = useStore(form.store, (state) => state.values)

  let { data: accounts } = useQuery({
    ...accountQueries('/getAccounts', {
      effectiveDate: toUIFormat(effectiveDate),
    }),
    select: (apiAccounts) =>
      apiAccounts.map((apiAccount) => ({
        ...apiAccountSelector(apiAccount),
        // don't actually need key account code for parentless open accounts,
        // but adding here for type safety
        keyAccountCode: null,
      })),
  })

  accounts = accounts ?? []

  return (
    <>
      <Button
        onClick={() => modalState.show()}
        className='flex items-center gap-1 border-none p-3 hover:bg-indigo-100'
      >
        <div className='flex items-center justify-between gap-2'>
          <span>
            <ArrowRightStartOnRectangleIcon className='h-4 w-4' />
          </span>
          <div>Move to another relationship</div>
        </div>
      </Button>
      <Modal modalState={modalState}>
        <ModalWindow className='w-[497px]'>
          <ModalTitle>Move to another composite account</ModalTitle>
          <p className='text-app-color-secondary'>
            If this is a composite account, all of its children will be moved to
            the new account.
          </p>

          <div className='z-20 flex flex-col py-4'>
            <p className='pb-2'>Move to account</p>

            <SearchCombo
              displayValue={(selectedItem) => selectedItem?.shortName}
              setSelectedItem={setSelectedItem}
              placeholder='Add accounts by searching for their name or number'
              noResultsMessage='No accounts found.'
              onSearch={(searchQuery: string) => {
                const results = searchMatchingAccounts(searchQuery, accounts)
                return results
                  .filter((account) => {
                    return (
                      account.applicationId === 'C' &&
                      !accountCodesAreEqual(
                        currentAccount,
                        account as AccountCode,
                      )
                    )
                  })
                  .slice(0, 20)
              }}
              onSelection={async (
                selectedItem: AccountSearchItem | undefined,
              ) => {
                if (!selectedItem) {
                  return
                }
              }}
              renderOption={(item: AccountSearchItem) => {
                return (
                  <ComboboxOption
                    key={`dropdown-${item.accountNumber}-${item.applicationId}`}
                    value={item}
                    className='cursor-default select-none px-4 py-2 data-[focus]:bg-app-color-bg-brand-secondary data-[focus]:outline-none'
                  >
                    <NameCode
                      name={item.shortName}
                      code={formatAccountCode(item as AccountCode)}
                    />
                  </ComboboxOption>
                )
              }}
            />
          </div>
          <ModalFooter>
            <ModalCancelButton>Cancel</ModalCancelButton>
            <ModalConfirmButton
              disabled={selectedItem === undefined}
              className={
                selectedItem === undefined ? 'btn-disabled' : 'btn-primary'
              }
              onClick={async (_, modalContext) => {
                if (selectedItem) {
                  await updateMapping.mutate({
                    parentAccountCode: selectedItem as AccountCode,
                    childAccountCodes: [currentAccount as AccountCode],
                    effectiveDate: toUIFormat(effectiveDate),
                  })
                  setSelectedItem(undefined)
                  modalContext.close()
                }
              }}
            >
              Move
            </ModalConfirmButton>
          </ModalFooter>
        </ModalWindow>
      </Modal>
    </>
  )
}
