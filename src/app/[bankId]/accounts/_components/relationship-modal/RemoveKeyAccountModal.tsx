import { Button } from '@/components/Button'
import {
  Modal,
  ModalCancelButton,
  ModalConfirmButton,
  Modal<PERSON>ooter,
  ModalTitle,
  ModalWindow,
  useExternalModalStateAndProps,
} from '@/components/Modal'
import {
  ExclamationTriangleIcon,
  StarIcon,
  XMarkIcon,
} from '@heroicons/react/24/outline'
import { useStore } from '@tanstack/react-form'
import { useAccountMutationContext } from '../../_context/AccountMutationContext'
import { toUIFormat } from '@/lib/date'
import { useAccountNodeContext } from '../../_context/AccountNodeContext'
import { DialogTitle } from '@headlessui/react'

export const RemoveKeyAccountModal = () => {
  const [modalState] = useExternalModalStateAndProps({})
  const { form, removeKeyAccount } = useAccountMutationContext()
  const {
    accountInfo: { accountNumber, applicationId, bankNumber },
  } = useStore(form.store, (state) => state.values)

  return (
    <>
      <Button
        onClick={() => modalState.show()}
        className='flex items-center gap-1 border-none p-3 hover:bg-indigo-100'
      >
        <div className='flex items-center justify-between gap-2'>
          <span>
            <XMarkIcon className='h-4 w-4' />
          </span>
          <div>Deselect as key account</div>
        </div>
      </Button>
      <Modal modalState={modalState}>
        <ModalWindow className='w-[497px] border-2 border-red-400'>
          <ModalTitle className='pb-2'>
            <div>
              <ExclamationTriangleIcon className='h-9 w-9 text-red-700' />
            </div>
          </ModalTitle>

          <DialogTitle
            as='h2'
            className='mb-2 flex items-center text-lg font-medium'
          >
            Deselect as key account
          </DialogTitle>
          <p className='text-app-color-secondary'>
            Changing key accounts may change both the demographic and pricing
            options and settlement and processing options for this composite
            account.
          </p>
          <ModalFooter>
            <ModalCancelButton>Cancel</ModalCancelButton>
            <ModalConfirmButton
              className='btn-alert'
              onClick={async (_, modalContext) => {
                await removeKeyAccount.mutate({
                  accountNumber,
                  applicationId,
                  bankNumber,
                })
                modalContext.close()
              }}
            >
              Remove
            </ModalConfirmButton>
          </ModalFooter>
        </ModalWindow>
      </Modal>
    </>
  )
}
