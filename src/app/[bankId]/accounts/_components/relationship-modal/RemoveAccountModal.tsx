import { Button } from '@/components/Button'
import {
  Modal,
  ModalCancelButton,
  ModalConfirmButton,
  Modal<PERSON>ooter,
  ModalTitle,
  ModalWindow,
  useExternalModalStateAndProps,
} from '@/components/Modal'
import { toUIFormat } from '@/lib/date'
import { ExclamationTriangleIcon, TrashIcon } from '@heroicons/react/24/outline'
import { useStore } from '@tanstack/react-form'
import { useQueryClient } from '@tanstack/react-query'
import { useAccountMutationContext } from '../../_context/AccountMutationContext'
import { useAccountNodeContext } from '../../_context/AccountNodeContext'
import { DialogTitle } from '@headlessui/react'

export const RemoveAccountModal = () => {
  const { form, removeMapping } = useAccountMutationContext()
  const {
    accountInfo: { shortName, accountNumber, applicationId, effectiveDate },
    subAccounts,
  } = useStore(form.store, (state) => state.values)
  const [modalState] = useExternalModalStateAndProps({})
  const { currentAccount } = useAccountNodeContext()
  const modalTitle =
    currentAccount.isKeyAccount ?
      'Remove key account from this composite account'
    : 'Remove account from this relationship'
  const modalCopy =
    currentAccount.isKeyAccount ?
      'This composite account will continue to use demographic and user field settings from its most recent key account, unless you update those settings or assign a new key account.'
    : 'If removing a composite account, this will also remove its children from the relationship.'
  return (
    <>
      <Button
        onClick={() => modalState.show()}
        className='flex items-center gap-1 border-none p-3 hover:bg-indigo-100'
      >
        <div className='flex items-center justify-between gap-2'>
          <span>
            <TrashIcon className='h-4 w-4' />
          </span>
          <div>Remove from this relationship</div>
        </div>
      </Button>
      <Modal modalState={modalState}>
        <ModalWindow className='w-[497px] border-2 border-red-400'>
          <ModalTitle className='pb-2'>
            <div>
              <ExclamationTriangleIcon className='h-9 w-9 text-red-700' />
            </div>
          </ModalTitle>
          <DialogTitle
            as='h2'
            className='mb-2 flex items-center text-lg font-medium'
          >
            {modalTitle}
          </DialogTitle>
          <p className='text-app-color-secondary'>{modalCopy}</p>

          <ModalFooter>
            <ModalCancelButton>Cancel</ModalCancelButton>
            <ModalConfirmButton
              className='btn-alert'
              onClick={async (_, modalContext) => {
                await removeMapping.mutate({
                  accountCodes: [
                    {
                      accountNumber: currentAccount.accountNumber,
                      applicationId: currentAccount.applicationId,
                      bankNumber: currentAccount.bankNumber,
                    },
                  ],
                  leadAccount: {
                    accountNumber: currentAccount.accountNumber,
                    applicationId: currentAccount.applicationId,
                    bankNumber: currentAccount.bankNumber,
                  },
                  effectiveDate: toUIFormat(effectiveDate),
                })
                modalContext.close()
              }}
            >
              Remove
            </ModalConfirmButton>
          </ModalFooter>
        </ModalWindow>
      </Modal>
    </>
  )
}
