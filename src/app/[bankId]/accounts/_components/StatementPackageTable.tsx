import { SortedTable } from '@/components/Table/SortedTable'
import {
  Address,
  HydratedStatementPackage,
  PackageDeliveryLabels,
} from '../types'
import { ReactNode, useMemo } from 'react'
import { ColumnDef } from '@tanstack/react-table'
import { formatAddress, formatAddressNumber } from '../accountHelpers'
import { formatToMonthYearFromDate } from '@/lib/date'

export interface StatementPackageTableProps {
  data: HydratedStatementPackage[]
  renderActions?: (rowOriginal: HydratedStatementPackage) => ReactNode
}

type StatementPackagesColumnDef = ColumnDef<
  HydratedStatementPackage | undefined
>

export function StatementPackageTable({
  data,
  renderActions,
}: StatementPackageTableProps) {
  const columns = useMemo<StatementPackagesColumnDef[]>(() => {
    return [
      {
        header: 'Code',
        accessorFn: (row) => row?.statementPackage?.statementPackageNumber,
        meta: {
          className: 'text-sm px-6 min-w-[90px]',
        },
        enableSorting: false,
      },
      {
        header: 'Delivery method',
        accessorFn: (row) =>
          PackageDeliveryLabels[row?.statementPackage?.packageDelivery!],
        meta: {
          className: 'text-sm px-6 min-w-[180px]',
        },
        enableSorting: false,
      },
      {
        header: 'Delivery address',
        accessorFn: (row) => row?.address,
        meta: {
          className: 'text-sm basis-full px-6',
        },
        enableSorting: false,
        cell: ({ getValue }) => {
          const address = getValue() as Address | undefined
          return (
            <>
              <div className='flex flex-col'>
                <div className='text-nowrap'>
                  {address ? formatAddress(address) : undefined}
                </div>
                <div className='text-xs'>
                  {address ?
                    `${address.applicationId} - ${formatAddressNumber(address.addressNumber)}`
                  : undefined}
                </div>
              </div>
            </>
          )
        },
      },
      {
        header: 'Effective date',
        accessorFn: (row) => row?.statementPackage.effectiveDate,
        meta: {
          className: 'text-sm px-6 min-w-[240px] items-center',
        },
        enableSorting: false,
        cell: ({ getValue, row }) =>
          !!getValue() ?
            <>
              <div>{formatToMonthYearFromDate(getValue() as Date)}</div>
              {renderActions?.(row.original!)}
            </>
          : undefined,
      },
    ]
  }, [renderActions])

  const columnFilters = useMemo(() => [], [])

  return (
    <SortedTable
      data={data.length > 0 ? data : [undefined]}
      columns={columns}
      columnFilters={columnFilters}
    />
  )
}
