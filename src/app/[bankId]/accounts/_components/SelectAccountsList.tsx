'use client'

import { Checkbox } from '@/components/Checkbox'
import { ExpandedTable } from '@/components/Table/ExpandedTable'
import { formatToMonthYearFromDate, formatToServerString } from '@/lib/date'
import { Button } from '@headlessui/react'
import {
  ChevronDownIcon,
  ChevronRightIcon,
  TrashIcon,
} from '@heroicons/react/24/outline'
import { useStore } from '@tanstack/react-form'
import { ColumnDef } from '@tanstack/react-table'
import { clsx } from 'clsx'
import { useMemo } from 'react'
import {
  formatAccountCode,
  getAccountTypeDescription,
  handleRemoveAccount,
  isAccountKeyAccount,
} from '../accountHelpers'
import { AccountListItem, SelectAccountFormSchemaType } from '../types'
import { useAccountRelatedEntities } from '../_hooks/useAccountRelatedEntities'
import { useChangeKeyAccount } from '../_hooks/useChangeKeyAccount'
import {
  SelectAccountModes,
  useAccountMutationContext,
} from '../_context/AccountMutationContext'
import SelectAccountsHierarchyPopover from './accounts-hierarchy/SelectAccountsHierarchyPopover'
import { EllipsisVerticalIcon } from '@heroicons/react/24/solid'
import { If } from '@/components/If'
import { AccountNodeProvider } from '../_context/AccountNodeContext'

type SelectAccountsColumnDef = ColumnDef<AccountListItem>

export interface SelectAccountsListProps {
  form: SelectAccountFormSchemaType
}

export function SelectAccountsList({ form }: SelectAccountsListProps) {
  const {
    accountInfo: {
      applicationId,
      accountNumber,
      bankNumber,
      shortName,
      openDate,
      effectiveDate,
      isKeyAccount,
    },
    demographics: { analysisAccountTypeCode, currencyCode, keyAccountCode },
    subAccounts,
  } = useStore(form.store, (state) => state.values)

  const {
    accountTypes: { data: accountTypes },
  } = useAccountRelatedEntities({ effectiveDate })

  const { changeKeyAccountInCreateForm } = useChangeKeyAccount({
    keyAccountCode,
    effectiveDate: formatToServerString(effectiveDate),
  })
  const { mode } = useAccountMutationContext()
  const columns = useMemo<SelectAccountsColumnDef[]>(() => {
    return [
      {
        id: 'keyAccount',
        header: 'Key account',
        meta: {
          className: 'min-w-[130px]',
        },
        cell: ({ row }) => (
          <div className='px-8'>
            {row.id != '0' && (
              <Checkbox
                checked={!!isAccountKeyAccount(keyAccountCode, row.original)}
                onChange={() => {
                  if (mode === SelectAccountModes.CREATE) {
                    changeKeyAccountInCreateForm(row.original, form)
                  }
                }}
              />
            )}
          </div>
        ),
      },
      {
        id: 'shortName',
        accessorKey: 'shortName',
        header: 'Account',
        meta: {
          className: 'basis-full',
        },
        cell: ({ row, getValue }) => (
          <div
            style={{
              paddingLeft: `${
                row.id === '0' ? 0
                : row.getCanExpand() ? row.depth * 2
                : (row.depth + 1) * 2
              }rem`,
            }}
          >
            <div className='flex flex-row gap-2'>
              {row.getCanExpand() && (
                <Button
                  className={'h-6 w-6'}
                  onClick={row.getToggleExpandedHandler()}
                >
                  {row.getIsExpanded() ?
                    <ChevronDownIcon />
                  : <ChevronRightIcon />}
                </Button>
              )}
              <div
                className={clsx(
                  'flex flex-row text-nowrap',
                  row.id === '0' ? 'font-semibold' : undefined,
                )}
              >
                {getValue<string>()}
                {!!isAccountKeyAccount(keyAccountCode, row.original) && (
                  <div className='ml-2 rounded-md bg-indigo-600 px-2 pt-[1px] text-sm font-light text-indigo-100'>
                    Key account
                  </div>
                )}
              </div>
            </div>
          </div>
        ),
      },
      {
        id: 'accountNumber',
        accessorFn: (row) => formatAccountCode(row),
        header: 'Account number',
        meta: {
          className: 'min-w-[260px]',
        },
        cell: ({ row, getValue }) => (
          <div className={clsx(row.id === '0' && 'font-semibold')}>
            {getValue<string>()}
          </div>
        ),
      },
      {
        id: 'accountType',
        header: 'Account type',
        meta: {
          className: 'min-w-[230px]',
        },
        cell: ({ row }) => (
          <div className='flex flex-col'>
            <div className={clsx(row.id === '0' ? 'font-semibold' : undefined)}>
              {getAccountTypeDescription(
                accountTypes,
                row.original.analysisAccountTypeCode,
              )}
            </div>
            <div className='text-sm text-app-color-secondary'>
              {row.original.analysisAccountTypeCode}
            </div>
          </div>
        ),
      },
      {
        id: 'currencyCode',
        accessorKey: 'currencyCode',
        header: 'Currency',
        meta: {
          className: 'min-w-[90px]',
        },
        cell: ({ row, getValue }) => (
          <div className={clsx(row.id === '0' ? 'font-semibold' : undefined)}>
            {getValue<string>()}
          </div>
        ),
      },
      {
        id: 'openDate',
        accessorKey: 'openDate',
        header: 'Account open date',
        meta: {
          className: 'min-w-[170px]',
        },
        cell: ({ row, getValue }) => (
          <div className='flex w-full flex-row justify-between'>
            <div className={clsx(row.id === '0' ? 'font-semibold' : undefined)}>
              {formatToMonthYearFromDate(getValue<string>())}
            </div>
            <If true={mode === SelectAccountModes.CREATE}>
              {row.depth == 0 && row.id != '0' && (
                <Button
                  className={
                    'ml-auto h-6 w-6 text-app-color-secondary hover:text-app-color-primary'
                  }
                  onClick={() =>
                    handleRemoveAccount(
                      row.original,
                      form,
                      subAccounts,
                      keyAccountCode,
                    )
                  }
                >
                  <TrashIcon />
                </Button>
              )}
            </If>
            <If true={mode === SelectAccountModes.EDIT}>
              <div className='flex items-center rounded-md p-[0.5px] text-app-color-secondary hover:bg-indigo-100 hover:text-app-color-primary'>
                <AccountNodeProvider currentAccount={row.original}>
                  <SelectAccountsHierarchyPopover
                    AccountsNode={<EllipsisVerticalIcon className='h-4 w-4' />}
                    onSelectKeyAccount={function (): void {
                      throw new Error('Function not implemented.')
                    }}
                    onRemoveAccount={function (): void {
                      throw new Error('Function not implemented.')
                    }}
                  />
                </AccountNodeProvider>
              </div>
            </If>
          </div>
        ),
      },
    ]
  }, [
    keyAccountCode,
    subAccounts,
    form,
    accountTypes,
    changeKeyAccountInCreateForm,
  ])

  const allRows = useMemo<AccountListItem[]>(() => {
    return [
      {
        applicationId,
        accountNumber,
        bankNumber,
        shortName,
        analysisAccountTypeCode,
        openDate,
        currencyCode,
        isKeyAccount,
      },
      ...subAccounts,
    ]
  }, [
    applicationId,
    accountNumber,
    shortName,
    analysisAccountTypeCode,
    openDate,
    currencyCode,
    isKeyAccount,
    subAccounts,
  ])

  return (
    <ExpandedTable
      data={allRows}
      columns={columns}
      getSubRows={(row) => row.children}
    />
  )
}
