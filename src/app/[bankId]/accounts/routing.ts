import { defineRoute, defineRoutes } from '@/lib/defineRoute'
import { compositeAccountAdd } from './(compositeAccount)/add/routing'
import { accountsEffectiveDate } from './[effectiveMonth]/routing'
import {
  viewAccountDetails,
  viewAccountRelationships,
  viewAccountStatements,
  viewAccount,
} from './[effectiveMonth]/view/[accountCode]/routing'

const accounts = defineRoute('accounts')

export const [useRoute, routeTo] = defineRoutes(
  accounts,
  compositeAccountAdd,
  accountsEffectiveDate,
  viewAccount,
  viewAccountDetails,
  viewAccountStatements,
  viewAccountRelationships,
)
