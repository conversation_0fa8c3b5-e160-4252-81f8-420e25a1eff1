'use client'

import {
  InfoSection,
  InfoSectionTitle,
  InfoSectionSeparator,
  InfoSectionDescription,
} from '@/components/InfoSection'
import { CreateCompositeAccountFormSchemaType } from '../../types'
import { SetProcessingOptions } from './SetProcessingOptions'
import { SetSettlementOptions } from './SetSettlementOptions'
import { SetOverrideAsSettlementAccount } from './SetOverrideAsSettlementAccount'
import { useStore } from '@tanstack/react-form'
import { NameCode } from '../../_components/NameCode'

interface ConfigureSettlementAndProcessingProps {
  isVisible: boolean
  form: CreateCompositeAccountFormSchemaType
}

export function ConfigureSettlementAndProcessing({
  isVisible,
  form,
}: ConfigureSettlementAndProcessingProps) {
  const [shortName, accountNumber, applicationId] = useStore(
    form.store,
    (state) => [
      state.values.accountInfo.shortName,
      state.values.accountInfo.accountNumber,
      state.values.accountInfo.applicationId,
    ],
  )

  return (
    isVisible && (
      <InfoSection className='mb-10 h-full gap-0'>
        <InfoSectionTitle>Configure settlement and processing</InfoSectionTitle>
        <InfoSectionDescription>
          You can override the following settings for your composite account.
        </InfoSectionDescription>

        <div className='grid grid-cols-2'>
          {/* Settlement account number */}
          <SetOverrideAsSettlementAccount form={form} />

          {/* Account to charge number */}
          {/* TODO: Implement Account to charge selector */}
          <div className='mb-4 mt-1 flex flex-col gap-1'>
            <div className='text-sm font-medium'>Account to charge *</div>
            <div className='flex flex-row gap-1 font-semibold'>
              <NameCode
                name={shortName}
                code={`${applicationId}-${accountNumber}`}
              />
            </div>
          </div>
        </div>
        <InfoSectionSeparator />

        {/* Settlement options */}
        <SetSettlementOptions form={form} />
        <InfoSectionSeparator />

        {/* Processing options */}
        <SetProcessingOptions form={form} />
      </InfoSection>
    )
  )
}
