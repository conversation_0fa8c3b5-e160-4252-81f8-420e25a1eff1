'use client'

import {
  InfoSection,
  InfoSectionSeparator,
  InfoSectionTitle,
} from '@/components/InfoSection'
import { Button } from '@headlessui/react'
import { useStore } from '@tanstack/react-form'
import { useState } from 'react'
import { CreateCompositeAccountFormSchemaType } from '../../types'
import { ConfigureDemographics } from './ConfigureDemographics'
import { ConfigurePricingOptions } from './ConfigurePricingOptions'
import { SelectKeyAccountCombo } from './SelectKeyAccount'
import { SetUserFieldsSelections } from './SetUserFieldsSelections'
import { NameCode } from '../../_components/NameCode'
import { useAccount } from '../../_hooks/useAccount'
import { formatAccountCode } from '../../accountHelpers'

interface ConfigureDemographicsAndPricingProps {
  isVisible: boolean
  form: CreateCompositeAccountFormSchemaType
}

export function ConfigureDemographicsAndPricing({
  isVisible,
  form,
}: ConfigureDemographicsAndPricingProps) {
  const [effectiveDate, keyAccountCode, skipKeyAccountSelection] = useStore(
    form.store,
    (state) => [
      state.values.accountInfo.effectiveDate,
      state.values.demographics.keyAccountCode,
      state.values.skipKeyAccountSelection,
    ],
  )

  const { account: keyAccount } = useAccount({
    accountCode: keyAccountCode,
    effectiveDate,
  })

  const [isKeyAccountSelectionOpen, setIsKeyAccountSelectionOpen] =
    useState(false)

  return (
    isVisible && (
      <InfoSection className='mb-10 h-full gap-0'>
        <InfoSectionTitle>
          Configure demographics and pricing options
        </InfoSectionTitle>

        {/* Key account name and number */}
        {isKeyAccountSelectionOpen ?
          <div className='w-[540px]'>
            <SelectKeyAccountCombo
              form={form}
              postSelectionCallback={() => setIsKeyAccountSelectionOpen(false)}
            />
          </div>
        : <div className='mb-4 mt-1 flex flex-col gap-1'>
            <div className='text-sm font-medium'>Key account *</div>
            <div className='flex flex-row gap-1 font-semibold'>
              <NameCode
                name={
                  skipKeyAccountSelection ?
                    'Key account selection skipped'
                  : (keyAccount?.shortName ?? '')
                }
                code={keyAccountCode ? formatAccountCode(keyAccountCode) : ''}
              />
              <Button
                className='text-indigo-600 hover:cursor-pointer hover:underline'
                onClick={(e) => {
                  setIsKeyAccountSelectionOpen(true)
                }}
              >
                Change
              </Button>
            </div>
          </div>
        }
        <InfoSectionSeparator />

        {/* Demographics */}
        <ConfigureDemographics form={form} />
        <InfoSectionSeparator />

        {/* User fields */}
        <SetUserFieldsSelections form={form} />
        <InfoSectionSeparator />

        {/* Pricing options */}
        <ConfigurePricingOptions form={form} />
      </InfoSection>
    )
  )
}
