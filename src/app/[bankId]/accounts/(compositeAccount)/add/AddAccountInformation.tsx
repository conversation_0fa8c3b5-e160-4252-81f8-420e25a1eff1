'use client'

import {
  InfoSection,
  InfoSectionDescription,
  InfoSectionTitle,
} from '@/components/InfoSection'
import { Checkbox } from '@/components/Checkbox'
import { useFormTextInput } from '@/components/Form/useFormTextInput'
import { TextInput } from '@/components/Input/TextInput'
import { useStore } from '@tanstack/react-form'
import { useQuery } from '@tanstack/react-query'
import { accountQueries } from '../../queries'
import {
  CompositeAccountFormSchema,
  CreateCompositeAccountFormSchemaType,
} from '../../types'
import { useFormMonthPicker } from '@/components/Form/useFormMonthPicker'
import { useRoute } from './routing'
import { data } from '@/lib/unions/Union'
import { formatToServerString } from '@/lib/date'

interface AddAccountInformationProps {
  isVisible: boolean
  form: CreateCompositeAccountFormSchemaType
}

export function AddAccountInformation({
  isVisible,
  form,
}: AddAccountInformationProps) {
  const route = useRoute()!
  const routeParams = data(route).params

  const [useNextAvailableAccountNumber, effectiveDate] = useStore(
    form.store,
    (state) => [
      state.values.accountInfo.useNextAvailableAccountNumber,
      state.values.accountInfo.effectiveDate,
    ],
  )

  const nextAvailableAccountNumberResponse = useQuery(
    accountQueries('/getNextAvailableAccountNumber', {
      effectiveDate,
    }),
  )

  const { nextAvailableAccountNumber = '' } =
    nextAvailableAccountNumberResponse.data ?? {}

  const FormMonthPicker = useFormMonthPicker<CompositeAccountFormSchema>({
    form,
  })

  const FormInput = useFormTextInput<CompositeAccountFormSchema>({
    form,
  })

  return (
    <InfoSection className={isVisible ? 'mb-10 h-full gap-0' : 'hidden'}>
      <InfoSectionTitle>Add account information</InfoSectionTitle>
      <InfoSectionDescription>
        Fill out some basic information about this composite account.
      </InfoSectionDescription>

      <div className='mt-5 flex flex-row gap-9'>
        <div className='flex w-full flex-1 flex-col'>
          <FormMonthPicker
            name='accountInfo.effectiveDate'
            label='Effective date'
            required
            onChange={(monthYear: string) => {
              form.setFieldValue('accountInfo.effectiveDate', monthYear)
            }}
          />
        </div>

        <FormInput
          className='flex-1'
          name='accountInfo.bankNumber'
          label='Bank'
          required
        />
      </div>

      <div className='flex flex-row gap-9'>
        <FormInput
          name='accountInfo.shortName'
          label='Composite account name'
          placeholder='3 to 24 characters'
          required
        />

        <div className='flex w-full flex-1 flex-col'>
          <FormInput
            name='accountInfo.accountNumber'
            label='Composite account number'
            placeholder='Must contain at most 20 digits'
            required
            readonly={useNextAvailableAccountNumber}
          />

          <form.Field name='accountInfo.useNextAvailableAccountNumber'>
            {(field) => {
              return (
                <Checkbox
                  className='my-2'
                  label='Use next best available number'
                  value={field.state.value?.toString()}
                  onChange={(event) => {
                    if (!useNextAvailableAccountNumber) {
                      form.setFieldValue(
                        'accountInfo.accountNumber',
                        nextAvailableAccountNumber,
                      )
                      form.validateField('accountInfo.accountNumber', 'change')
                    } else {
                      form.setFieldValue('accountInfo.accountNumber', '')
                    }
                    field.handleChange(event)
                  }}
                  disabled={
                    nextAvailableAccountNumberResponse.status !== 'success'
                  }
                />
              )
            }}
          </form.Field>
        </div>
      </div>
    </InfoSection>
  )
}
