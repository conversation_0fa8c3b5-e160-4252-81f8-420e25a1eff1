'use client'

import {
  Combobox,
  ComboboxButton,
  ComboboxInput,
  ComboboxOption,
  ComboboxOptions,
  Label,
} from '@headlessui/react'
import { ChevronDownIcon } from '@heroicons/react/24/solid'
import { useState } from 'react'

interface TypeaheadComboProps<T> {
  label: string
  placeholder: string
  autoFocus?: boolean
  options: T[]
  value: T | null
  filterFn: (query: string, items: T[]) => T[]
  renderOption: (option: T) => React.ReactElement
  renderValue: (option: T | undefined) => string
  onSelection: (option: T | null) => void
}

export function TypeaheadCombo<T>({
  label,
  placeholder,
  autoFocus,
  options,
  value,
  filterFn,
  renderOption,
  renderValue,
  onSelection,
}: TypeaheadComboProps<T>) {
  const [query, setQuery] = useState('')
  const [selectedOption, setSelectedOption] = useState(value)

  const filteredOptions = filterFn(query, options)

  return (
    <Combobox
      as='div'
      value={selectedOption}
      onChange={(option) => {
        setQuery('')
        onSelection(option)
        setSelectedOption(option)
      }}
    >
      <Label className='text-sm font-medium'>{label}</Label>
      <div className='relative w-full'>
        <ComboboxInput
          autoFocus={!!autoFocus}
          className='border-1.5 my-1 block h-9 w-full rounded-md py-2 pl-3 pr-2 text-app-color-primary shadow-sm ring-1 ring-inset ring-zinc-300 focus:ring-1 focus:ring-inset focus:ring-indigo-600'
          onChange={(event) => setQuery(event.target.value)}
          onBlur={() => setQuery('')}
          displayValue={renderValue}
          placeholder={placeholder}
        />
        <ComboboxButton className='absolute inset-y-0 right-0 flex items-center rounded-r-md px-2 focus:outline-none'>
          <ChevronDownIcon className='size-5' aria-hidden='true' />
        </ComboboxButton>

        {filteredOptions.length > 0 && (
          <ComboboxOptions className='absolute -mt-1 max-h-32 w-full scroll-py-0 overflow-y-auto rounded-md border-0 bg-white p-1.5 py-2 text-base shadow-sm ring-1 ring-inset ring-zinc-300 focus:ring-1 focus:ring-inset'>
            {filteredOptions.map((option) => renderOption(option))}
          </ComboboxOptions>
        )}
      </div>
    </Combobox>
  )
}
