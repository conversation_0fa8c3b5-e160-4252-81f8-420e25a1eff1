'use client'

import { useRoute } from '@/app/[bankId]/accounts/routing'
import { Step, Stepper } from '@/components/Stepper'
import { Tooltip } from '@/components/Tooltip'
import {
  firstDayOfMonth,
  formatToMonthYearFromDate,
  toServerFormat,
} from '@/lib/date'
import { data } from '@/lib/unions/Union'
import { InformationCircleIcon } from '@heroicons/react/24/outline'
import { useForm } from '@tanstack/react-form'
import { useMutation } from '@tanstack/react-query'
import { useRouter } from 'next/navigation'
import { useMemo, useState } from 'react'
import {
  formatAccountCodeForRoute,
  formToCompositeAccountCreateRequest,
} from '../../accountHelpers'
import { accountMutation } from '../../mutations'
import {
  CompositeAccountFormDefaults,
  CompositeAccountFormSchema,
  CompositeAccountZodSchema,
  CreateCompositeAccountStepID,
  SelectAccountFormSchemaType,
} from '../../types'
import { AddAccountInformation } from './AddAccountInformation'
import { ConfigureDemographicsAndPricing } from './ConfigureDemographicsAndPricing'
import { ConfigureSettlementAndProcessing } from './ConfigureSettlementAndProcessing'
import { ConfigureStatementPackages } from './ConfigureStatementPackages'
import { CreateCompositeAccountFooter } from './CreateCompositeAccountFooter'
import { SelectAccounts } from '../../_components/SelectAccounts'
import { SelectKeyAccount } from './SelectKeyAccount'
import { routeTo } from '../../[effectiveMonth]/view/[accountCode]/routing'

const stepperIndex: Record<CreateCompositeAccountStepID, number> = {
  [CreateCompositeAccountStepID.step1_AddAccountInformation]: 0,
  [CreateCompositeAccountStepID.step2_SelectChildAccounts]: 1,
  [CreateCompositeAccountStepID.step3_SelectKeyAccount]: 2,
  [CreateCompositeAccountStepID.step3_ConfigureDemographicsAndPricing]: 2,
  [CreateCompositeAccountStepID.step4_ConfigureSettlementAndProcessing]: 3,
  [CreateCompositeAccountStepID.step5_ConfigureStatementPackages]: 4,
}

export default function CreateCompositeAccount() {
  const router = useRouter()
  const route = data(useRoute()!).params
  const firstDayOfCurrentMonth = useMemo(
    () => toServerFormat(firstDayOfMonth(new Date())),
    [],
  )

  const [currentStepId, setCurrentStepId] = useState(
    CreateCompositeAccountStepID.step1_AddAccountInformation,
  )

  const { mutate: createCompositeAccount } = useMutation(
    accountMutation('/createCompositeAccount', {
      onSuccess: (data) => {
        router.push(
          routeTo('/accounts/[effectiveMonth]/view/[accountCode]/details', {
            bankId: route.bankId,
            effectiveMonth: formatToMonthYearFromDate(data.effectiveDate),
            accountCode: formatAccountCodeForRoute(data),
          }),
        )
      },
    }),
  )
  const form = useForm<CompositeAccountFormSchema>({
    defaultValues: {
      ...CompositeAccountFormDefaults,
      accountInfo: {
        ...CompositeAccountFormDefaults.accountInfo,
        effectiveDate: firstDayOfCurrentMonth,
        openDate: firstDayOfCurrentMonth,
      },
    },
    onSubmit: async ({ value }) => {
      const request = formToCompositeAccountCreateRequest(value)
      await createCompositeAccount(request)
    },
    validators: {
      onChange: CompositeAccountZodSchema,
      onSubmit: CompositeAccountZodSchema,
    },
  })

  const steps = [
    {
      name: 'Add account information',
    },
    {
      name: 'Select child accounts',
    },
    {
      name: 'Configure demographics and pricing options',
    },
    {
      name: 'Configure settlement and processing',
    },
    {
      name: 'Configure statement packages',
    },
  ] as Step[]

  return (
    <div className={'flex flex-auto flex-col overflow-y-scroll'}>
      <div className='mx-8 mt-8 flex flex-auto flex-col gap-3'>
        <header className='flex flex-col gap-5'>
          <div className='flex flex-row gap-1'>
            <div className='text-xl font-semibold'>Add a composite account</div>
            <Tooltip
              className='mt-1 size-6 self-center'
              topOffset={-2}
              content='Add a composite account'
            >
              <InformationCircleIcon />
            </Tooltip>
          </div>
          <div className='items-center rounded-xl border bg-white px-6 py-5'>
            <Stepper
              steps={steps}
              currentStepIndex={stepperIndex[currentStepId]}
            />
          </div>
        </header>

        <form
          className='flex max-h-full flex-auto flex-col'
          onSubmit={(event) => {
            event.preventDefault()
            event.stopPropagation()
            form.handleSubmit()
          }}
        >
          {/* Step 1 */}
          <AddAccountInformation
            isVisible={
              currentStepId ===
              CreateCompositeAccountStepID.step1_AddAccountInformation
            }
            form={form}
          />

          {/* Step 2 */}
          <SelectAccounts
            isVisible={
              currentStepId ===
              CreateCompositeAccountStepID.step2_SelectChildAccounts
            }
            form={form as unknown as SelectAccountFormSchemaType}
          />

          {/* Step 3 */}
          <SelectKeyAccount
            isVisible={
              currentStepId ===
              CreateCompositeAccountStepID.step3_SelectKeyAccount
            }
            form={form}
          />

          <ConfigureDemographicsAndPricing
            isVisible={
              currentStepId ===
              CreateCompositeAccountStepID.step3_ConfigureDemographicsAndPricing
            }
            form={form}
          />

          {/* Step 4 */}
          <ConfigureSettlementAndProcessing
            isVisible={
              currentStepId ===
              CreateCompositeAccountStepID.step4_ConfigureSettlementAndProcessing
            }
            form={form}
          />

          {/* Step 5 */}
          <ConfigureStatementPackages
            isVisible={
              currentStepId ===
              CreateCompositeAccountStepID.step5_ConfigureStatementPackages
            }
            form={form}
          />
        </form>
      </div>
      <CreateCompositeAccountFooter
        currentStepId={currentStepId}
        setCurrentStepId={setCurrentStepId}
        form={form}
      />
    </div>
  )
}
