'use client'

import { But<PERSON>, ComboboxOption } from '@headlessui/react'
import { useStore } from '@tanstack/react-form'
import { CreateCompositeAccountFormSchemaType } from '../../types'
import { useMemo, useState } from 'react'
import { Select } from '@/components/Input/Select'
import { NameCode } from '../../_components/NameCode'

interface SetOverrideAsSettlementAccountProps {
  form: CreateCompositeAccountFormSchemaType
}

export function SetOverrideAsSettlementAccount({
  form,
}: SetOverrideAsSettlementAccountProps) {
  const [
    isOverrideAsSettlementAccount,
    applicationId,
    accountNumber,
    shortName,
  ] = useStore(form.store, (state) => [
    state.values.accountTypeOverride.isOverrideAsSettlementAccount,
    state.values.accountInfo.applicationId,
    state.values.accountInfo.accountNumber,
    state.values.accountInfo.shortName,
  ])

  const accountCode = useMemo(() => {
    return `${applicationId}-${accountNumber}`
  }, [applicationId, accountNumber])

  const [isEditing, setIsEditing] = useState(false)

  return !isEditing ?
      <ShowOverrideAsSettlementAccountValue
        isOverrideAsSettlementAccount={!!isOverrideAsSettlementAccount}
        accountCode={accountCode}
        shortName={shortName}
        onChange={() => setIsEditing(true)}
      />
    : <EditOverrideAsSettlementAccountValue
        isOverrideAsSettlementAccount={!!isOverrideAsSettlementAccount}
        accountCode={accountCode}
        shortName={shortName}
        onChange={(value: boolean) => {
          form.setFieldValue(
            'accountTypeOverride.isOverrideAsSettlementAccount',
            value,
          )
          setIsEditing(false)
        }}
      />
}

function ShowOverrideAsSettlementAccountValue({
  isOverrideAsSettlementAccount,
  accountCode,
  shortName,
  onChange,
}: {
  isOverrideAsSettlementAccount: boolean
  accountCode: string
  shortName: string
  onChange: () => void
}) {
  return (
    <div className='mb-4 mt-1 flex flex-col gap-1'>
      <div className='text-sm font-medium'>Settlement account *</div>
      <div className='flex flex-row gap-1 font-semibold'>
        {isOverrideAsSettlementAccount ?
          <NameCode name={shortName} code={accountCode} />
        : 'Settle at the child account level'}
        <Button
          className='text-indigo-600 hover:cursor-pointer hover:underline'
          onClick={onChange}
        >
          Change
        </Button>
      </div>
    </div>
  )
}

function EditOverrideAsSettlementAccountValue({
  isOverrideAsSettlementAccount,
  accountCode,
  shortName,
  onChange,
}: {
  isOverrideAsSettlementAccount: boolean
  accountCode: string
  shortName: string
  onChange: (value: boolean) => void
}) {
  return (
    <div className='mb-4 mt-1 flex flex-col gap-1'>
      <div className='text-sm font-medium'>Settlement account *</div>
      <Select
        className={'-mb-10 w-auto max-w-[500px] pr-4'}
        name={'isOverrideAsSettlementAccount'}
        value={isOverrideAsSettlementAccount}
        options={[true, false]}
        onChange={onChange}
        renderOption={(value) => {
          return value ?
              <NameCode name={shortName} code={accountCode} />
            : 'Settle at the child account level'
        }}
        renderSelected={(value) => {
          return value ?
              <NameCode name={shortName} code={accountCode} />
            : 'Settle at the child account level'
        }}
      />
    </div>
  )
}
