'use client'

import {
  InfoSection,
  InfoSectionDescription,
  InfoSectionTitle,
} from '@/components/InfoSection'
import { ComboboxOption } from '@headlessui/react'
import { useStore } from '@tanstack/react-form'
import { useMemo } from 'react'
import { formatAccountCode } from '../../accountHelpers'
import {
  AccountCode,
  AccountSearchItem,
  BoundedAccountListItem,
  CreateCompositeAccountFormSchemaType,
  SelectAccountFormSchemaType,
} from '../../types'
import { TypeaheadCombo } from './TypeaheadCombo'
import { NameCode } from '../../_components/NameCode'
import { useChangeKeyAccount } from '../../_hooks/useChangeKeyAccount'
import { useAccount } from '../../_hooks/useAccount'

interface SelectKeyAccountProps {
  isVisible: boolean
  form: CreateCompositeAccountFormSchemaType
}

export function SelectKeyAccount({ isVisible, form }: SelectKeyAccountProps) {
  return (
    isVisible && (
      <InfoSection className='mb-10 h-full gap-0'>
        <InfoSectionTitle>
          Configure demographics and pricing options
        </InfoSectionTitle>
        <InfoSectionDescription>
          Select a key account to apply its settings to the composite account.
          You can edit these setings.
        </InfoSectionDescription>
        <SelectKeyAccountCombo form={form} />
      </InfoSection>
    )
  )
}

interface SelectKeyAccountComboProps {
  postSelectionCallback?: () => void
  form: CreateCompositeAccountFormSchemaType
}

export function SelectKeyAccountCombo({
  form,
  postSelectionCallback,
}: SelectKeyAccountComboProps) {
  const [effectiveDate, subAccounts, keyAccountCode, skipKeyAccountSelection] =
    useStore(form.store, (state) => [
      state.values.accountInfo.effectiveDate,
      state.values.subAccounts,
      state.values.demographics.keyAccountCode,
      state.values.skipKeyAccountSelection,
    ])

  const { account: keyAccount } = useAccount({
    accountCode: keyAccountCode,
    effectiveDate,
  })

  const { changeKeyAccountInCreateForm } = useChangeKeyAccount({
    keyAccountCode,
    effectiveDate,
  })

  const skipKeyAccountOption: AccountSearchItem = useMemo(
    () => ({
      shortName: `Don't select key account`,
      accountNumber: '',
      applicationId: undefined,
      bankNumber: '',
      isKeyAccount: false,
    }),
    [],
  )

  const flattenedSubAccounts: AccountSearchItem[] = useMemo(
    // Flatten the subAccounts structure keeping the same rendering order
    // displayed in the Select child accounts table.
    () => {
      function flatChildren(
        account: BoundedAccountListItem,
      ): AccountSearchItem[] {
        return [
          {
            shortName: account.shortName,
            accountNumber: account.accountNumber,
            applicationId: account.applicationId,
            bankNumber: account.bankNumber,
            isKeyAccount: account.isKeyAccount,
          },
          ...(account.children?.map(flatChildren) ?? []),
        ] as AccountSearchItem[]
      }
      return [
        skipKeyAccountOption,
        ...(subAccounts
          .map(flatChildren)
          .flat(Infinity) as AccountSearchItem[]),
      ]
    },
    [subAccounts, skipKeyAccountOption],
  )

  return (
    <div className='w-[540px]'>
      <TypeaheadCombo
        autoFocus={true}
        label='Key account *'
        placeholder='Select key account'
        options={flattenedSubAccounts}
        value={
          keyAccount ?
            {
              shortName: keyAccount.shortName,
              accountNumber: keyAccount.accountNumber,
              applicationId: keyAccount.applicationId,
              bankNumber: keyAccount.bankNumber,
              isKeyAccount: keyAccount.isKeyAccount,
            }
          : skipKeyAccountSelection ?
            skipKeyAccountOption
          : null
        }
        filterFn={filterAccountsByNameOrCode}
        renderOption={renderKeyAccountOption}
        renderValue={renderKeyAccountValue}
        onSelection={(option) => {
          if (option) {
            if (option === skipKeyAccountOption) {
              form.setFieldValue('skipKeyAccountSelection', true)
              form.setFieldValue('demographics.keyAccountCode', null)
            } else {
              form.setFieldValue('skipKeyAccountSelection', false)
              changeKeyAccountInCreateForm(
                {
                  applicationId: option.applicationId!,
                  accountNumber: option.accountNumber,
                  bankNumber: option.bankNumber,
                },
                form as unknown as SelectAccountFormSchemaType,
              )
            }
          }
          postSelectionCallback && postSelectionCallback()
        }}
      />
    </div>
  )
}

function filterAccountsByNameOrCode(query: string, items: AccountSearchItem[]) {
  const lowQuery = query.toLowerCase()
  const filtered = items.filter((item) => {
    const name = item.shortName.toLowerCase()
    const number = item.accountNumber.toLowerCase()
    const nameAndCode =
      `${name} ${formatAccountCode(item as AccountCode)}`.toLowerCase()
    return (
      name.includes(lowQuery) ||
      number.includes(lowQuery) ||
      nameAndCode.includes(lowQuery)
    )
  })
  return filtered
}

function renderKeyAccountOption(item: AccountSearchItem) {
  const itemAccountCode =
    item.applicationId && item.accountNumber ?
      formatAccountCode(item as AccountCode)
    : item.accountNumber
  return (
    <ComboboxOption
      key={`${item.applicationId}${item.accountNumber}`}
      value={item}
      disabled={item.isKeyAccount ?? false}
      className='cursor-default select-none px-4 py-2 data-[disabled]:bg-gray-200 data-[focus]:bg-app-color-bg-brand-secondary data-[focus]:outline-none'
    >
      <div className='flex justify-between'>
        <NameCode name={item.shortName} code={itemAccountCode} />
        {item.isKeyAccount && (
          <div className='max-w-24 rounded-md bg-indigo-600 px-2 pt-0.5 text-xs font-light text-indigo-100'>
            Key account
          </div>
        )}
      </div>
    </ComboboxOption>
  )
}

function renderKeyAccountValue(item: AccountSearchItem | undefined) {
  return (
    !item ? ''
    : item.applicationId && item.accountNumber ?
      `${item.shortName} ${formatAccountCode(item as AccountCode)}`
    : `${item.shortName}`
  )
}
