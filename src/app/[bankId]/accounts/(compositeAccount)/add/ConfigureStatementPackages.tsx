'use client'

import {
  InfoSection,
  InfoSectionDescription,
  InfoSectionTitle,
} from '@/components/InfoSection'
import { Button } from '@/components/Button'
import { useExternalModalStateAndProps } from '@/components/Modal'
import { PencilSquareIcon, TrashIcon } from '@heroicons/react/24/outline'
import { useStore } from '@tanstack/react-form'
import { useQuery } from '@tanstack/react-query'
import { useCallback, useMemo } from 'react'
import {
  StatementPackageModal,
  StatementPackageModalInstanceProps,
} from '../../_components/StatementPackageModal'
import { StatementPackageTable } from '../../_components/StatementPackageTable'
import {
  formToStatementPackageRequest,
  requestToHydratedStatementPackage,
} from '../../accountHelpers'
import { accountQueries } from '../../queries'
import {
  AccountCode,
  Address,
  CreateCompositeAccountFormSchemaType,
  HydratedStatementPackage,
} from '../../types'
import { apiToFormSchemas } from '@/api/apiToFormSchema'
import { listSelector } from '@/api/selectors'

interface RenderActionsProps {
  hydratedStatementPackage: HydratedStatementPackage
  showEditModal: (hydratedStatementPackage: HydratedStatementPackage) => void
  removeStatementPackage: (statementPackageNumber: number) => void
}

function RenderActions({
  hydratedStatementPackage,
  showEditModal,
  removeStatementPackage,
}: RenderActionsProps) {
  return (
    <div className='ml-auto flex'>
      <Button
        className={
          'border-0 text-app-color-secondary hover:text-app-color-primary'
        }
        onClick={() => {
          if (hydratedStatementPackage) showEditModal(hydratedStatementPackage)
        }}
      >
        <PencilSquareIcon className='size-4' />
      </Button>
      <Button
        className={
          'border-0 text-app-color-secondary hover:text-app-color-primary'
        }
        onClick={() => {
          removeStatementPackage(
            hydratedStatementPackage.statementPackage.statementPackageNumber!,
          )
        }}
      >
        <TrashIcon className='size-4' />
      </Button>
    </div>
  )
}

interface ConfigureStatementPackagesProps {
  isVisible: boolean
  form: CreateCompositeAccountFormSchemaType
}

export function ConfigureStatementPackages({
  isVisible,
  form,
}: ConfigureStatementPackagesProps) {
  const [
    accountNumber,
    bankNumber,
    shortName,
    effectiveDate,
    subAccounts,
    keyAccountCode,
    statementPackages,
    accountAddresses,
  ] = useStore(form.store, (state) => [
    state.values.accountInfo.accountNumber,
    state.values.accountInfo.bankNumber,
    state.values.accountInfo.shortName,
    state.values.accountInfo.effectiveDate,
    state.values.subAccounts,
    state.values.demographics.keyAccountCode,
    state.values.statementPackages,
    state.values.addresses,
  ])

  const accountCode: AccountCode = useMemo(
    () => ({ applicationId: 'C', accountNumber, bankNumber }),
    [accountNumber],
  )

  const keyAccountAddresses = useQuery({
    ...accountQueries('/getAddresses', {
      effectiveDate,
      applicationId: keyAccountCode?.applicationId!,
      accountNumber: keyAccountCode?.accountNumber!,
      bankNumber: keyAccountCode?.bankNumber!,
    }),
    enabled: !!keyAccountCode,
    select: listSelector(apiToFormSchemas.address),
  }).data

  const setAccountAddresses = useCallback(
    (accountAddresses: Address[]) => {
      form.setFieldValue('addresses', accountAddresses)
    },
    [form],
  )

  const existingStatementPackageNumbers = useMemo(
    () =>
      statementPackages.map(
        (statementPackageRequest) =>
          statementPackageRequest.statementPackageNumber,
      ),
    [statementPackages],
  )

  const [modalState, modalInstanceProps, setModalInstanceProps] =
    useExternalModalStateAndProps<StatementPackageModalInstanceProps>({})

  const showAddModal = useCallback(() => {
    setModalInstanceProps({
      existingStatementPackageNumbers,
      onSave: (value) => {
        form.setFieldValue(
          'statementPackages',
          statementPackages.concat(
            formToStatementPackageRequest(value, accountCode),
          ),
        )
      },
    })
    modalState.show()
  }, [
    setModalInstanceProps,
    existingStatementPackageNumbers,
    form,
    statementPackages,
    accountCode,
    modalState,
  ])

  const showEditModal = useCallback(
    (statementPackage: HydratedStatementPackage) => {
      setModalInstanceProps({
        statementPackage,
        existingStatementPackageNumbers: existingStatementPackageNumbers.filter(
          (statementPackageNumber) =>
            statementPackageNumber !==
            statementPackage.statementPackage.statementPackageNumber,
        ),
        onSave: (value) => {
          form.setFieldValue(
            'statementPackages',
            statementPackages.map((statementPackageRequest) =>
              (
                value.statementPackageNumber ===
                statementPackageRequest.statementPackageNumber
              ) ?
                // replace existing statement package we just edited
                formToStatementPackageRequest(value, accountCode)
              : statementPackageRequest,
            ),
          )
        },
      })
      modalState.show()
    },
    [
      setModalInstanceProps,
      existingStatementPackageNumbers,
      form,
      statementPackages,
      accountCode,
      modalState,
    ],
  )

  const removeStatementPackage = useCallback(
    (statementPackageNumber: number) => {
      form.setFieldValue(
        'statementPackages',
        statementPackages.filter(
          (statementPackageRequest) =>
            statementPackageRequest.statementPackageNumber !==
            statementPackageNumber,
        ),
      )
    },
    [form, statementPackages],
  )

  const hydratedStatementPackages = useMemo(
    () =>
      statementPackages.map((request) =>
        requestToHydratedStatementPackage(request, [
          ...(keyAccountAddresses ?? []),
          ...accountAddresses,
        ]),
      ),
    [statementPackages, keyAccountAddresses, accountAddresses],
  )

  return (
    isVisible && (
      <>
        <InfoSection className='mb-10 h-full gap-0'>
          <div className='flex items-center'>
            <div>
              <InfoSectionTitle>Configure statement packages</InfoSectionTitle>
              <InfoSectionDescription>
                This step is optional. You can create multiple statement
                packages.
              </InfoSectionDescription>
            </div>
            <Button
              className='btn-primary ml-auto'
              onClick={() => {
                showAddModal()
              }}
            >
              Add a statement package
            </Button>
          </div>
          <StatementPackageTable
            data={hydratedStatementPackages}
            renderActions={(
              hydratedStatementPackage: HydratedStatementPackage,
            ) => (
              <RenderActions
                hydratedStatementPackage={hydratedStatementPackage}
                showEditModal={showEditModal}
                removeStatementPackage={removeStatementPackage}
              />
            )}
          />
        </InfoSection>
        {modalInstanceProps && (
          <StatementPackageModal
            modalState={modalState}
            {...modalInstanceProps}
            accountCode={accountCode}
            shortName={shortName}
            accountAddresses={accountAddresses}
            setAccountAddresses={setAccountAddresses}
            keyAccountAddresses={keyAccountAddresses ?? []}
            subAccounts={subAccounts}
          />
        )}
      </>
    )
  )
}
