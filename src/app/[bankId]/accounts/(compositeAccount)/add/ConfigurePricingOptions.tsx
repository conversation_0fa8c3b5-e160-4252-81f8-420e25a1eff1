'use client'
import {
  InfoSectionDescription,
  InfoSubSectionTitle,
} from '@/components/InfoSection'
import { Select } from '@/components/Input/Select'
import { SortedTable } from '@/components/Table/SortedTable'
import { Tooltip } from '@/components/Tooltip'
import { InformationCircleIcon } from '@heroicons/react/24/outline'
import { useStore } from '@tanstack/react-form'
import { ColumnDef } from '@tanstack/react-table'
import { useMemo } from 'react'
import {
  CreateCompositeAccountFormSchemaType,
  PricingOptionsItem,
} from '../../types'

type PricingOptionsColumnDef = ColumnDef<PricingOptionsItem>

interface ConfigurePricingOptionsProps {
  form: CreateCompositeAccountFormSchemaType
}

export function ConfigurePricingOptions({
  form,
}: ConfigurePricingOptionsProps) {
  const {
    demographics: { customerSpecificPricingIndicator },
  } = useStore(form.store, (state) => state.values)
  const pricingOptionsData = useMemo(() => {
    return [
      {
        name: 'Customer specific pricing indicator',
      },
    ]
  }, [])

  const columns = useMemo<PricingOptionsColumnDef[]>(() => {
    return [
      {
        header: 'Pricing option',
        accessorKey: 'name',
      },
      {
        header: 'Value',
        cell: ({ row }) => {
          return (
            <div className='min-w-64'>
              <Select
                className='mt-6'
                name='customerSpecificPricingIndicator'
                value={customerSpecificPricingIndicator}
                options={[true, false]}
                onChange={(value) => {
                  form.setFieldValue(
                    'demographics.customerSpecificPricingIndicator',
                    value,
                  )
                }}
                renderOption={(value) => {
                  return value ? 'Yes' : 'No'
                }}
                renderSelected={(value) => {
                  return value ? 'Yes' : 'No'
                }}
              />
            </div>
          )
        },
      },
    ]
  }, [customerSpecificPricingIndicator, form])
  return (
    <>
      <InfoSubSectionTitle>
        Pricing options
        <Tooltip className='size-6 self-center' content='Pricing options'>
          <InformationCircleIcon />
        </Tooltip>
      </InfoSubSectionTitle>
      <InfoSectionDescription className='-mt-3'>
        These settings are not synced with the key account.
      </InfoSectionDescription>

      <SortedTable
        data={pricingOptionsData}
        columns={columns}
        columnFilters={[]}
      />
    </>
  )
}
