'use client'
import {
  InfoSectionDescription,
  InfoSubSectionTitle,
} from '@/components/InfoSection'
import { Checkbox } from '@/components/Checkbox'
import { MonthPicker } from '@/components/Filter/MonthPicker'
import { Select } from '@/components/Input/Select'
import { TextInput } from '@/components/Input/TextInput'
import { SortedTable } from '@/components/Table/SortedTable'
import { Tooltip } from '@/components/Tooltip'
import {
  parseMonthPickerFormat,
  parseServerFormat,
  toServerFormat,
} from '@/lib/date'
import { InformationCircleIcon } from '@heroicons/react/24/outline'
import { useStore } from '@tanstack/react-form'
import { ColumnDef } from '@tanstack/react-table'
import { useMemo } from 'react'
import {
  CreateCompositeAccountFormSchemaType,
  UserFieldListItem,
} from '../../types'

type UserFieldsColumnDef = ColumnDef<UserFieldListItem>

interface ConfigureUserFieldsProps {
  form: CreateCompositeAccountFormSchemaType
}

export function SetUserFieldsSelections({ form }: ConfigureUserFieldsProps) {
  const {
    demographics: { userFields },
  } = useStore(form.store, (state) => state.values)

  const columns = useMemo<UserFieldsColumnDef[]>(() => {
    return [
      {
        header: 'User field',
        accessorKey: 'configuration.name',
      },
      {
        header: 'Value',
        cell: ({ row }) => {
          if (row.original.configuration.fieldType === 'BOOLEAN') {
            return (
              <div className='basis-full'>
                <Select
                  className='mt-6'
                  name={'booleanUserField'}
                  value={
                    row.original.selection.isUnset ?
                      ''
                    : row.original.selection.booleanValue
                  }
                  options={[true, false]}
                  onChange={(value) => {
                    form.setFieldValue(
                      `demographics.userFields[${row.index}].selection.isUnset`,
                      false,
                    )
                    form.setFieldValue(
                      `demographics.userFields[${row.index}].selection.booleanValue`,
                      !!value,
                    )
                  }}
                  renderOption={(value) => {
                    return value ? 'Yes' : 'No'
                  }}
                  renderSelected={(value) => {
                    return value ? 'Yes' : 'No'
                  }}
                />
              </div>
            )
          }

          if (row.original.configuration.fieldType === 'DROPDOWN') {
            const options: number[] = []
            const optionsLabels: Record<number, string> = {}

            row.original.configuration.updatedDropdownOptions?.forEach(
              (option) => {
                options.push(option.code!)
                optionsLabels[option.code!] = option.value!
              },
            )

            return (
              <div className='basis-full'>
                <Select
                  className='mt-6'
                  name='dropdownUserField'
                  value={
                    row.original.selection.isUnset ?
                      ''
                    : row.original.selection.dropdownOptionCode
                  }
                  options={options}
                  onChange={(value) => {
                    form.setFieldValue(
                      `demographics.userFields[${row.index}].selection.isUnset`,
                      false,
                    )
                    form.setFieldValue(
                      `demographics.userFields[${row.index}].selection.dropdownOptionCode`,
                      value as number,
                    )
                  }}
                  renderOption={(value) => {
                    return optionsLabels[value as number]
                  }}
                  renderSelected={(value) => {
                    return optionsLabels[value as number]
                  }}
                />
              </div>
            )
          }

          if (row.original.configuration.fieldType === 'FREEFORM') {
            return (
              <div className='basis-full'>
                <TextInput
                  className='mt-6'
                  name='freeformUserField'
                  value={row.original.selection.freeformValue || ''}
                  placeholder={row.original.configuration.name}
                  onChange={(value) => {
                    form.setFieldValue(
                      `demographics.userFields[${row.index}].selection.isUnset`,
                      false,
                    )
                    form.setFieldValue(
                      `demographics.userFields[${row.index}].selection.freeformValue`,
                      value,
                    )
                  }}
                />
              </div>
            )
          }
        },
      },
      {
        header: 'Expiration date',
        cell: ({ row }) => {
          if (row.original.selection.isUnset) return

          const today = new Date()
          const expiry = row.original.selection.expiry

          const handleCheckboxChange = (checked: boolean) => {
            form.setFieldValue(
              `demographics.userFields[${row.index}].selection.expiry`,
              checked ? '' : toServerFormat(today),
            )
          }

          const handleDateChange = (date: string) => {
            form.setFieldValue(
              `demographics.userFields[${row.index}].selection.expiry`,
              toServerFormat(parseMonthPickerFormat(date)),
            )
          }

          return (
            <div className='flex flex-row gap-3'>
              <Checkbox
                checked={!expiry}
                label='No expiration'
                onChange={handleCheckboxChange}
              />
              {expiry && (
                <MonthPicker
                  className='my-1 h-9 min-w-32 bg-white ring-1 ring-inset ring-gray-300'
                  initialDate={parseServerFormat(expiry)}
                  onDateChange={handleDateChange}
                />
              )}
            </div>
          )
        },
      },
      {
        header: 'Apply to child accounts',
        cell: ({ row }) => {
          return (
            !row.original.selection.isUnset && (
              <Checkbox
                className='ml-14'
                label={
                  row.original.selection.applyToChildAccounts ? 'Yes' : 'No'
                }
                checked={row.original.selection.applyToChildAccounts}
                onChange={(checked) => {
                  form.setFieldValue(
                    `demographics.userFields[${row.index}].selection.applyToChildAccounts`,
                    checked,
                  )
                }}
              />
            )
          )
        },
      },
    ]
  }, [form])
  return (
    <>
      <InfoSubSectionTitle>
        User fields
        <Tooltip className='size-6 self-center' content='User fields'>
          <InformationCircleIcon />
        </Tooltip>
      </InfoSubSectionTitle>
      <InfoSectionDescription className='-mt-3'>
        These settings are not automatically synced with the key account.
      </InfoSectionDescription>

      <SortedTable data={userFields} columns={columns} columnFilters={[]} />
    </>
  )
}
