'use client'

import { InputGroup } from '@/components/Input/InputGroup'
import { Combobox, ComboboxInput, ComboboxOptions } from '@headlessui/react'
import { MagnifyingGlassIcon } from '@heroicons/react/20/solid'
import React, { useState } from 'react'

export interface SearchComboProps<T> {
  placeholder: string
  noResultsMessage: string
  setSelectedItem?: (selectedItem: T | undefined) => void
  displayValue?: (item: T) => string
  onSearch: (searchQuery: string) => T[]
  onSelection: (selectedItem: T | undefined) => void
  renderOption: (resultItem: T) => React.ReactElement
}

export function SearchCombo<T>({
  placeholder,
  noResultsMessage,
  setSelectedItem,
  displayValue,
  onSearch,
  onSelection,
  renderOption,
}: SearchComboProps<T>) {
  const [searchQuery, setSearchQuery] = useState('')
  const [resultItems, setResultItems] = useState<T[]>([])

  return (
    <InputGroup className='w-full'>
      <MagnifyingGlassIcon className='h-4 w-4' />
      <Combobox
        onChange={(selectedItem: T | null) => {
          setSelectedItem && setSelectedItem(selectedItem || undefined)
          onSelection(selectedItem ?? undefined)
          setSearchQuery('')
          setResultItems([])
        }}
        onClose={() => {
          setSearchQuery('')
          setResultItems([])
        }}
      >
        <ComboboxInput
          autoFocus
          className='h-9 w-full rounded-md border-0 p-1.5 text-app-color-primary shadow-sm ring-1 ring-inset ring-zinc-300 focus:ring-1 focus:ring-inset focus:ring-indigo-600'
          placeholder={placeholder}
          displayValue={displayValue}
          onChange={(e) => {
            const newSearchQuery = e.target.value
            const newResultItems = onSearch(newSearchQuery)
            setSearchQuery(newSearchQuery)
            setResultItems(newResultItems)
          }}
        />

        {resultItems.length > 0 && (
          <ComboboxOptions
            className='absolute -mt-1 max-h-64 w-full scroll-py-0 overflow-y-auto rounded-md border-0 bg-white p-1.5 py-2 text-base shadow-sm ring-1 ring-inset ring-zinc-300 focus:ring-1 focus:ring-inset'
            static
          >
            {resultItems.map((item) => renderOption(item))}
          </ComboboxOptions>
        )}

        {searchQuery !== '' && resultItems.length === 0 && (
          <div className='absolute -mt-1 w-full rounded-md border-0 bg-white px-6 py-4 text-base text-app-color-secondary shadow-sm ring-1 ring-inset ring-zinc-300 focus:ring-1 focus:ring-inset'>
            {noResultsMessage}
          </div>
        )}
      </Combobox>
    </InputGroup>
  )
}
