'use client'

import {
  InfoSectionDescription,
  InfoSubSectionTitle,
} from '@/components/InfoSection'
import {
  CreateCompositeAccountFormSchemaType,
  SettlementOptionItem,
  settlementOptions,
  SettlementOptionsColumnDef,
} from '../../types'
import { useMemo } from 'react'
import { SortedTable } from '@/components/Table/SortedTable'
import { useStore } from '@tanstack/react-form'
import { formatToServerString, toUTCDateString } from '@/lib/date'
import { useConfigurations } from '@/app/[bankId]/configuration/_hooks/useConfigurations'
import { SelectSettlementOption } from '@/components/SettlementOptions/SelectSettlementOption'
import { SettlementOptionExpirationDate } from '@/components/SettlementOptions/SettlementOptionExpirationDate'

interface SetSettlementOptionsProps {
  form: CreateCompositeAccountFormSchemaType
}

export function SetSettlementOptions({ form }: SetSettlementOptionsProps) {
  const [accountTypeOverride, effectiveDate] = useStore(form.store, (state) => [
    state.values.accountTypeOverride,
    state.values.accountInfo.effectiveDate,
  ])

  const {
    analysisResultOptions: { data: analysisResultOptions },
    cycleDefinitions: { data: cycleDefinitions },
  } = useConfigurations({ effectiveDate })

  const analysisResultOptionsMap = useMemo<Map<string, string>>(() => {
    const optionsMap: Map<string, string> = new Map()
    analysisResultOptions!.forEach((option) => {
      const code = `${option.code}`
      const name = `${option.name}`
      optionsMap.set(code, name)
    })
    return optionsMap
  }, [analysisResultOptions])

  const settlementCycleOptionsMap = useMemo<Map<string, string>>(() => {
    const optionsMap: Map<string, string> = new Map()
    cycleDefinitions!.forEach((option) => {
      const code = `${option.code}`
      const name = `${option.description}`
      if (option.cycleType === 'SETTLEMENT') {
        optionsMap.set(code, name)
      }
    })
    return optionsMap
  }, [cycleDefinitions])

  const allOptionsMaps = useMemo<
    Map<SettlementOptionItem['field'], Map<string, string>>
  >(() => {
    const optionsMap: Map<
      SettlementOptionItem['field'],
      Map<string, string>
    > = new Map()
    optionsMap.set('analysisResultOptionsPlanCode', analysisResultOptionsMap)
    optionsMap.set('settlementCyclePlanCode', settlementCycleOptionsMap)
    return optionsMap
  }, [analysisResultOptionsMap, settlementCycleOptionsMap])

  const columns = useMemo<SettlementOptionsColumnDef[]>(() => {
    return [
      {
        header: 'Settlement setting',
        accessorKey: 'name',
        meta: {
          className: 'ml-4 basis-full',
        },
      },
      {
        header: 'Plan',
        meta: {
          className: 'basis-full',
        },
        cell: ({ row }) => {
          const field = row.original.field as SettlementOptionItem['field']
          const fieldValue = accountTypeOverride[field]
          return (
            <div className='min-w-80'>
              <SelectSettlementOption
                field={field}
                value={fieldValue ? fieldValue : ''}
                optionsMap={allOptionsMaps.get(field)!}
                onChange={(value) => {
                  form.setFieldValue(`accountTypeOverride.${field}`, value)
                }}
              />
            </div>
          )
        },
      },
      {
        header: 'Expiration date',
        meta: {
          className: 'ml-4 basis-full',
        },
        cell: ({ row }) => {
          const field = row.original.field
          const fieldValue = accountTypeOverride[field]
          const expiryValue = accountTypeOverride[`${field}Expiry`]
          return (
            <SettlementOptionExpirationDate
              isVisible={!!fieldValue}
              expiry={expiryValue}
              onCheckboxChange={(checked: boolean) => {
                form.setFieldValue(
                  `accountTypeOverride.${field}Expiry`,
                  checked ? undefined : toUTCDateString(new Date()),
                )
              }}
              onMonthPickerChange={(date: string) => {
                form.setFieldValue(
                  `accountTypeOverride.${field}Expiry`,
                  formatToServerString(date),
                )
              }}
            />
          )
        },
      },
    ]
  }, [allOptionsMaps, accountTypeOverride, form])
  return (
    <>
      <InfoSubSectionTitle>Settlement options</InfoSubSectionTitle>
      <InfoSectionDescription className='-mt-3'>
        These options are inherited from the account type of the key account.
      </InfoSectionDescription>

      <SortedTable
        data={settlementOptions}
        columns={columns}
        columnFilters={[]}
      />
    </>
  )
}
