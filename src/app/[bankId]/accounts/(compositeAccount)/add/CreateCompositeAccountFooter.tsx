'use client'

import { But<PERSON> } from '@/components/Button'
import { unwrap } from '@/lib/unions/unwrap'
import { useStore } from '@tanstack/react-form'
import Link from 'next/link'
import { useCallback } from 'react'
import { routeTo } from '../../routing'
import {
  CompositeAccountZodSchema,
  CreateCompositeAccountFormSchemaType,
  CreateCompositeAccountStepID,
} from '../../types'
import { useRoute } from './routing'
import { formatToMonthYearFromDate } from '@/lib/date'

interface CreateCompositeAccountFooterProps {
  form: CreateCompositeAccountFormSchemaType
  currentStepId: number
  setCurrentStepId: (value: number) => void
}

export function CreateCompositeAccountFooter({
  form,
  currentStepId,
  setCurrentStepId,
}: CreateCompositeAccountFooterProps) {
  const route = unwrap(useRoute(), '/accounts/add')

  const [accountInfo, demographics] = useStore(
    form.store,
    ({ values: { accountInfo, demographics } }) => [accountInfo, demographics],
  )

  const showBack = () => {
    return (
      currentStepId != CreateCompositeAccountStepID.step1_AddAccountInformation
    )
  }

  const showNext = () => {
    return (
      currentStepId !=
      CreateCompositeAccountStepID.step5_ConfigureStatementPackages
    )
  }

  const showCreate = () => {
    return (
      currentStepId ==
      CreateCompositeAccountStepID.step5_ConfigureStatementPackages
    )
  }

  const isNextEnabled = useCallback(() => {
    if (
      currentStepId == CreateCompositeAccountStepID.step1_AddAccountInformation
    ) {
      return CompositeAccountZodSchema.shape.accountInfo.safeParse(accountInfo)
        .success
    }

    if (
      currentStepId ==
      CreateCompositeAccountStepID.step3_ConfigureDemographicsAndPricing
    ) {
      return CompositeAccountZodSchema.shape.demographics.safeParse(
        demographics,
      ).success
    }

    return true
  }, [currentStepId, accountInfo, demographics])

  const goBack = () => {
    switch (currentStepId) {
      case CreateCompositeAccountStepID.step3_ConfigureDemographicsAndPricing:
        setCurrentStepId(
          !demographics.keyAccountCode ?
            CreateCompositeAccountStepID.step3_SelectKeyAccount
          : CreateCompositeAccountStepID.step2_SelectChildAccounts,
        )
        break
      default:
        setCurrentStepId(currentStepId - 1)
        break
    }
  }

  const goNext = () => {
    switch (currentStepId) {
      case CreateCompositeAccountStepID.step2_SelectChildAccounts:
        setCurrentStepId(
          !demographics.keyAccountCode ?
            CreateCompositeAccountStepID.step3_SelectKeyAccount
          : CreateCompositeAccountStepID.step3_ConfigureDemographicsAndPricing,
        )
        break
      default:
        setCurrentStepId(currentStepId + 1)
        break
    }
  }

  if (!route)
    throw Error(
      'CreateCompositeAccountFooter must be used on the /accounts/add route.',
    )

  return (
    <footer className='flex justify-end gap-4 border-t bg-white px-12 py-10'>
      <Link
        href={routeTo('/accounts/[effectiveMonth]', {
          effectiveMonth: formatToMonthYearFromDate(new Date()),
          bankId: route.params.bankId,
        })}
        className='mr-auto'
      >
        <Button className='btn w-60'>Cancel</Button>
      </Link>

      {showBack() && (
        <Button
          type='button'
          className='btn w-60'
          onClick={() => {
            goBack()
          }}
        >
          Back
        </Button>
      )}

      {showNext() && (
        <Button
          type='button'
          className={isNextEnabled() ? 'btn-primary w-60' : 'btn-disabled w-60'}
          disabled={!isNextEnabled()}
          onClick={() => {
            goNext()
          }}
        >
          Next
        </Button>
      )}

      {showCreate() && (
        <Button
          type='button'
          className='btn-primary w-60'
          onClick={() => {
            form.validate('submit')
            form.handleSubmit()
          }}
        >
          Create
        </Button>
      )}
    </footer>
  )
}
