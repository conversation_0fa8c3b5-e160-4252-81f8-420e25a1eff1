import { AccountMutation } from './mutations'
import { Tag } from '@/lib/unions/Union'
import { withNotifications } from '@/components/Notifications'

export const messages: {
  [T in Tag<AccountMutation>]: string
} = {
  '/createCompositeAccount': 'Composite account successfully created.',
  '/updateAccount': 'Account successfully updated.',
  '/upsertKeyAccountMapping': '',
  '/removeKeyAccountMapping': '',
  '/addAccountMapping': 'Account added to relationship',
  '/removeAccountMappings': 'Account removed from relationship',
  '/updateAccountMappings': 'Account updated',
  '/setKeyAccount': 'Key account selected',
  '/removeKeyAccount': 'Key account removed',
  '/updateSettlementProcessingOptions':
    'updated settlement and processing options',
  '/createAddress': 'Address successfully created',
}

export const notifications = withNotifications(messages)
