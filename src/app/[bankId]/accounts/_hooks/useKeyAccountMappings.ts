import { useQuery } from '@tanstack/react-query'
import { accountQueries } from '../queries'
import { useMemo } from 'react'
import { Account } from '../types'
import { accountCodesAreEqual } from '../accountHelpers'

// QUICK AND DIRTY HACK after https://bitbucket.fis.dev/projects/AFIN/repos/af_fpb/pull-requests/264/overview
// removed keyAccountCode from Account. Might be cleaned up with https://jira.fis.dev/browse/REVCON-53.
export function useKeyAccountMappings() {
  const { data: keyAccountMappings, ...restResult } = useQuery(
    accountQueries('/listAllKeyAccountMappings'),
  )

  return useMemo(
    () => ({
      keyAccountMappings,
      withKeyAccountCode: (account: Account): Account => ({
        ...account,
        keyAccountCode: account.keyAccountCode ?? null,
      }),
      ...restResult,
    }),
    [keyAccountMappings, restResult],
  )
}
