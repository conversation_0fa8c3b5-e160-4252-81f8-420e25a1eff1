'use client'

import { SelectOption } from '@/components/Input/Select'
import { ReactNode, useMemo } from 'react'

export function useCodesAndRenderOption<T>(
  objects: T[],
  codeMapper: (obj: T) => string,
  labelMapper: (obj: T) => ReactNode,
): [string[], (code: string | null) => ReactNode] {
  return useMemo(() => {
    const codeMap: Record<string, ReactNode> = objects.reduce(
      (previous, current) => ({
        ...previous,
        [codeMapper(current)]: labelMapper(current),
      }),
      {},
    )
    return [
      Object.keys(codeMap),
      (code: string | null) => code && codeMap[code],
    ]
  }, [codeMapper, labelMapper, objects])
}

export function selectOptionMapper(
  labelMapper: (code: string) => ReactNode,
): (code: string) => ReactNode {
  return function toSelectOption(code: string) {
    return (
      <SelectOption
        key={code}
        value={code}
        focusClassNames='data-[focus]:bg-indigo-100'
      >
        {labelMapper(code)}
      </SelectOption>
    )
  }
}
