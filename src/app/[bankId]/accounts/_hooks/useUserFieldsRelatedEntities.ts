import { useQuery, UseQueryResult } from '@tanstack/react-query'
import { accountQueries } from '../queries'
import { userFieldQueries } from '../../configuration/user-fields/queries'
import { listSelector } from '@/api/selectors'
import { apiToFormSchemas } from '@/api/apiToFormSchema'
import {
  HydratedUserFieldConfigurationForm,
  UserFieldSelectionForm,
} from '@/api/formToApiSchema'

interface UserFieldsRelatedEntities {
  userFieldsConfigurations: UseQueryResult<HydratedUserFieldConfigurationForm[]>
  userFieldSelections: UseQueryResult<UserFieldSelectionForm[]>
}

export function useUserFieldsRelatedEntities({
  effectiveDate,
  applicationId,
  accountNumber,
  bankNumber,
}: {
  effectiveDate: string
  applicationId: 'C' | 'D'
  accountNumber: string
  bankNumber: string
}): UserFieldsRelatedEntities {
  return {
    userFieldsConfigurations: useQuery({
      ...userFieldQueries('/getUserFieldsConfigurations'),
      select: listSelector(apiToFormSchemas.hydratedUserFieldConfiguration),
    }),
    userFieldSelections: useQuery({
      ...accountQueries('/getUserFieldSelections', {
        effectiveDate: effectiveDate,
        applicationId: applicationId,
        accountNumber: accountNumber,
        bankNumber: bankNumber,
      }),
      select: listSelector(apiToFormSchemas.userFieldSelection),
    }),
  }
}
