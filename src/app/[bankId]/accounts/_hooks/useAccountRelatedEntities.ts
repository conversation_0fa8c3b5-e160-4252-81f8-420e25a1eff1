import { useQuery } from '@tanstack/react-query'
import { getLastDayOfMonthString } from '@/lib/date'
import { accountTypeQueries } from '../../configuration/account-type/queries'
import { branchQueries } from '../../configuration/branches/queries'
import { officerQueries } from '../../configuration/officer/queries'
import { apiToFormSchemas } from '@/api/apiToFormSchema'

import { listSelector } from '@/api/selectors'

export function useAccountRelatedEntities({
  effectiveDate,
}: {
  effectiveDate: string
}) {
  return {
    accountTypes: useQuery({
      ...accountTypeQueries('/getAccountTypes', {
        effectiveDate: getLastDayOfMonthString(effectiveDate),
      }),
      select: listSelector(apiToFormSchemas.accountType),
    }),
    branches: useQuery(
      branchQueries('/getBranches', {
        effectiveDate: getLastDayOfMonthString(effectiveDate),
      }),
    ),
    officers: useQuery(
      officerQueries('/getOfficers', {
        effectiveDate: getLastDayOfMonthString(effectiveDate),
      }),
    ),
  }
}
