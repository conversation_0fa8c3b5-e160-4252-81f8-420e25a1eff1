import { useMemo } from 'react'
import { AccountCode } from '../types'
import { accountToAccountCode, apiAccountSelector } from '../accountHelpers'
import { accountQueries } from '../queries'
import { useQuery } from '@tanstack/react-query'

export function useAccount({
  accountCode,
  effectiveDate,
}: {
  accountCode: AccountCode | null | undefined
  effectiveDate: string
}) {
  const { data: account, ...accountResult } = useQuery({
    ...accountQueries('/getAccount', {
      ...(accountCode ?
        accountToAccountCode(accountCode)
      : { applicationId: 'C', accountNumber: '', bankNumber: '' }),
      effectiveDate,
    }),
    enabled: !!accountCode,
    select: apiAccountSelector,
  })

  return useMemo(
    () => ({
      account: account ?? undefined,
      ...accountResult,
      isLoading: accountResult.isLoading,
      isError: accountResult.isError,
      error: accountResult.error,
    }),
    [account, accountResult],
  )
}
