'use client'

import { RouteParamsRecord } from '@/lib/defineRoute'
import { useMemo } from 'react'
import { toUTCDateString } from '@/lib/date'
import { useAccount } from './useAccount'

export function useAccountFromParams(
  routeParams: RouteParamsRecord<['effectiveMonth', 'accountCode']>,
) {
  const {
    accountCode,
    applicationId,
    accountNumber,
    bankNumber,
    effectiveDate,
  } = useMemo(() => {
    const [applicationId, bankNumber, accountNumber] =
      routeParams.accountCode.split('-') as ['C' | 'D', string, string]
    const accountCode = { applicationId, accountNumber, bankNumber }
    return {
      accountCode,
      applicationId,
      bankNumber,
      accountNumber,
      // Because account demographics can change intra-month via daily demographic updates from the deposit cores,
      // we must always evaluate account information as of the end of the month to match the logic used in analysis.
      effectiveDate: toUTCDateString(routeParams.effectiveMonth),
    }
  }, [routeParams])

  const { account, ...accountResult } = useAccount({
    accountCode,
    effectiveDate,
  })

  const { account: keyAccount, ...keyAccountResult } = useAccount({
    accountCode: account?.keyAccountCode,
    effectiveDate,
  })

  return {
    account,
    accountCode,
    applicationId,
    accountNumber,
    keyAccount,
    effectiveDate,
    ...accountResult,
    isLoading: accountResult.isLoading || keyAccountResult.isLoading,
    isError: accountResult.isError || keyAccountResult.isError,
    error: accountResult.error || keyAccountResult.error,
  }
}
