import {
  Account,
  AccountCode,
  EditDemographicsAndPricingFormSchemaType,
  SelectAccountFormSchemaType,
} from '../types'
import { useMemo } from 'react'
import { useAccounts } from './useAccounts'
import { accountCodesAreEqual, accountToAccountCode } from '../accountHelpers'

const updateOnKeyAccountChange = [
  'analysisAccountTypeCode',
  'costCenter',
  'branchCode',
  'currencyCode',
  'primaryOfficerCode',
  'secondaryOfficerCode',
  'treasuryOfficerCode',
] as const
type UpdateOnKeyAccountChange = (typeof updateOnKeyAccountChange)[number]

function resolveKeyAccountValue(
  newKeyAccount: Account,
  fieldName: keyof Account,
): any {
  // TODO: When live-linking is enabled, this may not be a simple lookup on newKeyAccount.
  // If newKeyAccount is a composite, we will require more complicated logic to resolve any
  // further key account linkages to find the value we want to display in the form
  return newKeyAccount[fieldName]
}

export function useChangeKeyAccount({
  keyAccountCode,
  effectiveDate,
}: {
  keyAccountCode: AccountCode | null | undefined
  effectiveDate: string
}) {
  const { accounts } = useAccounts({ effectiveDate })

  return useMemo(() => {
    function changeKeyAccountInCreateForm(
      newKeyAccountCode: AccountCode,
      form: SelectAccountFormSchemaType,
    ) {
      const newKeyAccount = accounts?.find((account) =>
        accountCodesAreEqual(newKeyAccountCode, account),
      )!
      if (
        !!keyAccountCode &&
        accountCodesAreEqual(newKeyAccountCode, keyAccountCode)
      ) {
        form.setFieldValue('demographics.keyAccountCode', null)
      } else {
        form.setFieldValue('skipKeyAccountSelection', false)
        form.setFieldValue(
          'demographics.keyAccountCode',
          accountToAccountCode(newKeyAccount),
        )

        updateOnKeyAccountChange.forEach(
          (fieldName: UpdateOnKeyAccountChange) => {
            form.setFieldValue(
              `demographics.${fieldName}`,
              resolveKeyAccountValue(newKeyAccount, fieldName),
            )
            form.validateField(`demographics.${fieldName}`, 'change')
          },
        )
      }
    }

    function changeKeyAccountInEditForm(
      newKeyAccountCode: AccountCode,
      form: EditDemographicsAndPricingFormSchemaType,
    ) {
      const newKeyAccount = accounts?.find((account) =>
        accountCodesAreEqual(newKeyAccountCode!, account),
      )!

      updateOnKeyAccountChange.forEach(
        (fieldName: UpdateOnKeyAccountChange) => {
          form.setFieldValue(
            fieldName,
            resolveKeyAccountValue(newKeyAccount, fieldName),
          )
          form.validateField(fieldName, 'change')
        },
      )
    }

    return { changeKeyAccountInCreateForm, changeKeyAccountInEditForm }
  }, [keyAccountCode, accounts])
}
