import { useQuery } from '@tanstack/react-query'
import { accountQueries } from '../queries'
import { useKeyAccountMappings } from './useKeyAccountMappings'
import { useMemo } from 'react'
import { apiAccountListSelector } from '../accountHelpers'
import { formatToServerString } from '@/lib/date'

export function useAccounts({ effectiveDate }: { effectiveDate: string }) {
  const { data, ...restResult } = useQuery({
    ...accountQueries('/getAccounts', {
      effectiveDate: formatToServerString(effectiveDate),
    }),
    select: apiAccountListSelector,
  })

  // QUICK AND DIRTY HACK after https://bitbucket.fis.dev/projects/AFIN/repos/af_fpb/pull-requests/264/overview
  // removed keyAccountCode from Account. Might be cleaned up with https://jira.fis.dev/browse/REVCON-53.
  const { keyAccountMappings, withKeyAccountCode } = useKeyAccountMappings()

  return useMemo(
    () => ({
      accounts: keyAccountMappings && data?.map(withKeyAccountCode),
      ...restResult,
    }),
    [data, keyAccountMappings, withKeyAccountCode, restResult],
  )
}
