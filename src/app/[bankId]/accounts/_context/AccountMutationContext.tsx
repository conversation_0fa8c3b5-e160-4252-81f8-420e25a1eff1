import {
  useMutation,
  UseMutationResult,
  useQueryClient,
} from '@tanstack/react-query'
import { accountMutation } from '../mutations'
import { createContext, ReactNode, useContext } from 'react'
import { Account, AccountCode } from '@/api/formToApiSchema'
import { AccountListItem, SelectAccountFormSchemaType } from '../types'
import { accountCodeToString } from '../accountHelpers'
import { toUIFormat } from '@/lib/date'
import { components } from '@/api/schema'

export const SelectAccountModes = {
  CREATE: 'CREATE',
  EDIT: 'EDIT',
} as const

export type SelectAccountMode = keyof typeof SelectAccountModes

type RelationshipMutationContextType = {
  mode: SelectAccountMode
  form: SelectAccountFormSchemaType
  removeMapping: UseMutationResult<
    {
      child: components['schemas']['AccountWithKey']
      parent?: components['schemas']['AccountWithKey']
    }[],
    Error,
    {
      accountCodes: AccountCode[]
      effectiveDate: string
      leadAccount: AccountCode
    },
    unknown
  >
  addMapping: UseMutationResult<
    {
      child: Account
      parent?: Account
    }[],
    Error,
    {
      childAccountCode: AccountCode
      effectiveDate: string
      parentAccountCode: AccountCode
    },
    unknown
  >
  updateMapping: UseMutationResult<
    {
      child: components['schemas']['Account']
      parent?: components['schemas']['Account']
    }[],
    Error,
    {
      childAccountCodes: components['schemas']['AccountCode'][]
      effectiveDate: string
      parentAccountCode: components['schemas']['AccountCode']
    },
    unknown
  >
  setKeyAccount: UseMutationResult<
    unknown,
    Error,
    {
      childAccountCode: components['schemas']['AccountCode']
      effectiveDate: string
      parentAccountCode: components['schemas']['AccountCode']
    },
    unknown
  >
  removeKeyAccount: UseMutationResult<
    unknown,
    Error,
    components['schemas']['AccountCode'],
    unknown
  >
  leadAccount?: AccountListItem
}

export const AccountMutationContext = createContext<
  RelationshipMutationContextType | undefined
>(undefined)

export const useAccountMutationContext = () => {
  const context = useContext(AccountMutationContext)
  if (!context) {
    throw new Error('useMutationContext must be used within a MutationProvider')
  }
  return context
}

type AccountMutationProviderProps = {
  children: ReactNode
  mode: SelectAccountMode
  form: SelectAccountFormSchemaType
  leadAccount?: AccountListItem
}

export const AccountMutationProvider = ({
  children,
  mode,
  form,
  leadAccount,
}: AccountMutationProviderProps) => {
  const queryClient = useQueryClient()
  const updateMapping = useMutation(
    accountMutation('/updateAccountMappings', {
      onSuccess: () => {
        queryClient.invalidateQueries({
          queryKey: [
            '/getAccountMappings',
            accountCodeToString(form.getFieldValue('accountInfo')),
            toUIFormat(form.getFieldValue('accountInfo.effectiveDate')),
          ],
        })
        queryClient.invalidateQueries({
          queryKey: ['/listAllKeyAccountMappings'],
        })
        queryClient.invalidateQueries({
          queryKey: [
            '/getLeadCompositeAccountCode',
            accountCodeToString(form.getFieldValue('accountInfo')),
            toUIFormat(form.getFieldValue('accountInfo.effectiveDate')),
          ],
        })
      },
      onError: () => console.log('ERROR'),
    }),
  )

  const addMapping = useMutation(
    accountMutation('/addAccountMapping', {
      onSuccess: () =>
        queryClient.invalidateQueries({
          queryKey: [
            '/getAccountMappings',
            accountCodeToString(form.getFieldValue('accountInfo')),
            toUIFormat(form.getFieldValue('accountInfo.effectiveDate')),
          ],
        }),
      onError: () => console.log('ERROR'),
    }),
  )

  const removeMapping = useMutation(
    accountMutation('/removeAccountMappings', {
      onSuccess: () => {
        queryClient.invalidateQueries({
          queryKey: [
            '/getAccountMappings',
            accountCodeToString(form.getFieldValue('accountInfo')),
            toUIFormat(form.getFieldValue('accountInfo.effectiveDate')),
          ],
        })
        queryClient.invalidateQueries({
          queryKey: [
            '/getParentlessOpenAccounts',
            toUIFormat(form.getFieldValue('accountInfo.effectiveDate')),
          ],
        })
        queryClient.invalidateQueries({
          queryKey: [
            '/getLeadCompositeAccountCode',
            accountCodeToString(form.getFieldValue('accountInfo')),
            toUIFormat(form.getFieldValue('accountInfo.effectiveDate')),
          ],
        })
      },
      onError: () => console.log('ERROR'),
    }),
  )

  const setKeyAccount = useMutation(
    accountMutation('/setKeyAccount', {
      onSuccess: () => {
        const clearAccountCache = form.getFieldValue('accountInfo')
        queryClient.invalidateQueries({
          queryKey: [
            '/getAccountMappings',
            accountCodeToString(form.getFieldValue('accountInfo')),
            toUIFormat(form.getFieldValue('accountInfo.effectiveDate')),
          ],
        })
        queryClient.invalidateQueries({
          queryKey: [
            '/getAccount',
            clearAccountCache.applicationId,
            clearAccountCache.accountNumber,
            clearAccountCache.bankNumber,
            toUIFormat(clearAccountCache.effectiveDate),
          ],
        })
      },
      onError: () => console.log('ERROR'),
    }),
  )

  const removeKeyAccount = useMutation(
    accountMutation('/removeKeyAccount', {
      onSuccess: () => {
        const clearAccountCache = form.getFieldValue('accountInfo')
        queryClient.invalidateQueries({
          queryKey: [
            '/getAccountMappings',
            accountCodeToString(clearAccountCache),
            toUIFormat(clearAccountCache.effectiveDate),
          ],
        })
        queryClient.invalidateQueries({
          queryKey: [
            '/getAccount',
            clearAccountCache.applicationId,
            clearAccountCache.accountNumber,
            clearAccountCache.bankNumber,
            toUIFormat(clearAccountCache.effectiveDate),
          ],
        })
      },
      onError: () => console.log('ERROR'),
    }),
  )

  return (
    <AccountMutationContext.Provider
      value={{
        mode,
        updateMapping,
        addMapping,
        removeMapping,
        form,
        setKeyAccount,
        removeKeyAccount,
        leadAccount,
      }}
    >
      {children}
    </AccountMutationContext.Provider>
  )
}
