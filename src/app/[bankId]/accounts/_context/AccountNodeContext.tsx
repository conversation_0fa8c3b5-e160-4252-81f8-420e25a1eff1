import { createContext, ReactNode, useContext } from 'react'
import { AccountListItem } from '../types'

type AccountNodeContextType = {
  currentAccount: AccountListItem
}

export const AccountNodeContext = createContext<
  AccountNodeContextType | undefined
>(undefined)

export const useAccountNodeContext = () => {
  const context = useContext(AccountNodeContext)
  if (!context) {
    throw new Error(
      'useAccountMutationContext must be used within a AccountNodeProvider',
    )
  }
  return context
}

type AccountNodeProviderProps = {
  children: ReactNode
  currentAccount: AccountListItem
}

export const AccountNodeProvider = ({
  children,
  currentAccount,
}: AccountNodeProviderProps) => {
  return (
    <AccountNodeContext.Provider value={{ currentAccount }}>
      {children}
    </AccountNodeContext.Provider>
  )
}
