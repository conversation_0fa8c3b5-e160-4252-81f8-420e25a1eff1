export const DashChartHeader = ({
  label,
  cta,
  isEmptyState,
}: {
  label: string
  cta: string
  isEmptyState?: boolean
}) => {
  return (
    <div
      className={`my-0 flex w-[calc(100%-${isEmptyState ? '0px' : '40px'})] justify-between`}
    >
      <div className='text-left text-base font-medium'>{label}</div>
      <div className='flex min-w-[160px] items-center justify-center rounded-lg border border-zinc-300 px-3 hover:cursor-pointer hover:bg-indigo-100'>
        <span className='px-1 text-xs'>{cta}</span>
      </div>
    </div>
  )
}
