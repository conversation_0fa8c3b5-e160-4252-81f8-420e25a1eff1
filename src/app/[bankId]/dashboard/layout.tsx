'use client'

import React from 'react'
import { CalendarDateRangeIcon } from '@heroicons/react/24/outline'

import { Filter } from '@/components/Filter/Filter'
import { Dash<PERSON>hartHeader } from './DashChartHeader'
import { NotificationList } from '../../_components/NotificationsList/NotificationsList'

import { HorizontalBarStackedChart } from '../../_components/Charts/HorizontalBarStackedChart'
import { VerticalBarTabbedChart } from '../../_components/Charts/VerticalBarTabbedChart'
import { BasicLineChart } from '../../_components/Charts/BasicLineChart'
import { CombinationBarAndLineChart } from '../../_components/Charts/CombinationBarAndLineChart'

// Mock Data
import {
  balanceTrendCategories,
  balanceTrendSeries,
  fileNotifications,
  grossRevCategories,
  grossRevSeries,
  resultsTrendCategories,
  resultsTrendSeries,
  revenueLossCategories,
  revenueLossSeries,
} from './mockData'

import messages from './messages.json'
const { MainTitles, ChartsTitles } = messages

import { useQuery } from '@tanstack/react-query'
import { query } from '../services/queries'
import { startOfTheMonth, toServerFormat } from '@/lib/date'
import { EmptyState } from '@/app/_components/EmptyState'
import { useRouter, useParams } from 'next/navigation'

export default function Dashboard() {
  // TODO: just for the sake of the demo, making request to server API
  // to trigger the authorization dialog
  const data = useQuery(
    query('/getServiceCatalogMapping', {
      effectiveDate: toServerFormat(startOfTheMonth()),
    }),
  )

  const { bankId } = useParams()

  // TODO: Temporary logic to display demo charts for Bank 537. Otherwise, display
  // empty state for all other banks until analysis is ready.
  const isTemporaryEmptyState = false

  return (
    <div className='h-full min-w-full overflow-auto p-8'>
      {/* Notification List Section */}
      <section className='py-3'>
        <div className='text-left text-lg font-medium'>
          {MainTitles.NotificationsSection}
        </div>
        <div className='py-1.5 text-sm text-zinc-500'>
          {'Last Update: 08/31/2024 11:59 PM'}
        </div>
        <NotificationList
          notifications={isTemporaryEmptyState ? [] : fileNotifications}
        />
      </section>

      {/* Charts Section */}
      <section className='py-3'>
        {/* Title + Date Range */}
        <div className='text-left text-lg font-medium'>
          {MainTitles.VisualizationsSection}
        </div>
        <div className='my-3 flex'>
          <Filter
            className='border border-indigo-300 bg-white hover:bg-indigo-200 focus-visible:outline-indigo-300'
            label='Jan 2025 - Mar 2025'
            icon={<CalendarDateRangeIcon />}
            disabled
            inUse
          ></Filter>
        </div>

        {/* Chart 1 */}
        <div className='my-2 rounded-lg border border-zinc-300 bg-white p-3'>
          <DashChartHeader
            label={ChartsTitles.GrossRevenue.Title}
            cta={ChartsTitles.GrossRevenue.CtaLabel}
            isEmptyState={isTemporaryEmptyState}
          />
          {isTemporaryEmptyState ?
            <EmptyState />
          : <HorizontalBarStackedChart
              series={grossRevSeries}
              categories={grossRevCategories}
            />
          }
        </div>

        {/* Charts 2 & 3 */}
        <div className='my-6 flex w-full justify-between gap-x-5'>
          <div className='flex-1 rounded-lg border border-zinc-300 bg-white p-3'>
            <DashChartHeader
              label={ChartsTitles.ResultTrends.Title}
              cta={ChartsTitles.ResultTrends.CtaLabel}
              isEmptyState={isTemporaryEmptyState}
            />
            {isTemporaryEmptyState ?
              <EmptyState classNames='h-72' />
            : <VerticalBarTabbedChart
                series={resultsTrendSeries}
                categories={resultsTrendCategories}
              />
            }
          </div>
          <div className='flex-1 rounded-lg border border-zinc-300 bg-white p-3'>
            <DashChartHeader
              label={ChartsTitles.BalanceTrends.Title}
              cta={ChartsTitles.BalanceTrends.CtaLabel}
              isEmptyState={isTemporaryEmptyState}
            />
            {isTemporaryEmptyState ?
              <EmptyState classNames='h-72' />
            : <BasicLineChart
                series={balanceTrendSeries}
                categories={balanceTrendCategories}
              />
            }
          </div>
        </div>

        {/* Chart 4 */}
        <div className='my-6 rounded-lg border border-zinc-300 bg-white p-3'>
          <DashChartHeader
            label={ChartsTitles.RevenueLoss.Title}
            cta={ChartsTitles.RevenueLoss.CtaLabel}
            isEmptyState={isTemporaryEmptyState}
          />
          {isTemporaryEmptyState ?
            <EmptyState classNames='h-72' />
          : <CombinationBarAndLineChart
              series={revenueLossSeries}
              categories={revenueLossCategories}
            />
          }
        </div>
      </section>
    </div>
  )
}
