export const fileNotifications = [
  {
    id: 1,
    message: 'REJECT - ACCOUNT CLOSED',
    fileName: 'R-10035 Demographic Changes',
    date: '08/29/2024',
    read: false,
  },
  {
    id: 2,
    message: 'Demographic file',
    fileName: 'R-10201 Product Transaction Activity',
    date: '08/25/2024',
    read: false,
  },
  {
    id: 3,
    message: 'INVALID PRODUCT CODE - PRODUCT POSTED',
    fileName: 'R-10201 Product Transaction Activity',
    date: '08/12/2024',
    read: false,
  },
  {
    id: 4,
    message: 'Lockbox',
    fileName: 'File Missing',
    date: '08/12/2024',
    read: false,
  },
  {
    id: 5,
    message: 'REJECT - ACCOUNT NOT FOUND',
    fileName: 'R-10201 Product Transaction Activity',
    date: '08/12/2024',
    read: true,
  },
  {
    id: 6,
    message: 'REJECT - ACCOUNT NOT FOUND',
    fileName: 'R-10201 Product Transaction Activity',
    date: '08/12/2024',
    read: true,
  },
  {
    id: 7,
    message: 'REJECT - ACCOUNT NOT FOUND',
    fileName: 'R-10201 Product Transaction Activity',
    date: '08/12/2024',
    read: true,
  },
]

export const grossRevCategories = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun']
export const grossRevSeries = [
  {
    name: 'General Account',
    data: [44, 55, 41, 37, 22, 43],
  },
  {
    name: 'Depository',
    data: [53, 32, 33, 52, 13, 43],
  },
  {
    name: 'Wire',
    data: [12, 17, 11, 9, 15, 11],
  },
  {
    name: 'Lockbox',
    data: [9, 7, 5, 8, 6, 9],
  },
  {
    name: 'ACH',
    data: [25, 12, 19, 32, 25, 24],
  },
  {
    name: 'Remote Deposit Capture',
    data: [25, 12, 19, 32, 25, 24],
  },
  {
    name: 'Treasury Management',
    data: [25, 12, 19, 32, 25, 24],
  },
]

export const resultsTrendCategories = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun']
export const resultsTrendSeries = [
  {
    name: 'Revenue collected',
    data: [7000, 5230, 10000, 12500, 14000, 23000],
  },
  {
    name: 'Earning allowance',
    data: [6800, 7400, 5500, 9200, 7100, 8300],
  },
  {
    name: 'Credit waiver fees',
    data: [7200, 5800, 9300, 6500, 8900, 7600],
  },
]

export const balanceTrendCategories = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun']
export const balanceTrendSeries = [
  {
    name: 'Revenue collected',
    data: [7200, 5800, 9300, 6500, 8900, 7600],
  },
]

export const revenueLossCategories = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun']
export const revenueLossSeries = [
  {
    name: 'Realization',
    type: 'line',
    data: [80, 90, 90, 65, 75, 40],
  },
  {
    name: 'Service Waivers',
    type: 'bar',
    data: [130, 170, 105, 100, 105, 70],
  },
  {
    name: 'Override Pricing',
    type: 'bar',
    data: [90, 55, 105, 50, 55, 50],
  },
]
