export const DEBOUNCE_FOR_USER_INPUTS_MS = 500

export const configLinks = [
  { route: 'bank-options', label: 'Bank options' },
  { route: 'schedule-definition', label: 'Schedule definition' },
  { route: 'statement-formats', label: 'Statement format' },
  { route: 'statement-message', label: 'Statement message' },
  { route: 'investable-balance', label: 'Investable balance' },
  { route: 'required-balance', label: 'Required balance' },
  { route: 'earnings-credit', label: 'Earning credit definition' },
  { route: 'reserve-requirements', label: 'Reserve requirement' },
  { route: 'analysis-result-options', label: 'Analysis results options' },
  { route: 'account-type', label: 'Account types' },
  { route: 'index-rates', label: 'Index rates' },
  { route: 'user-fields', label: 'User fields' },
  { route: 'branches', label: 'Branch' },
  { route: 'officer', label: 'Officer' },
  // todo: users and roles
  // { route: 'users-and-roles', label: 'Users and roles' },
]
