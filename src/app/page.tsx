'use client'

import { authRedirectUrlPersistenceKey } from '@/lib/http/authMiddleware'
import { sessionStorageForKey } from '@/lib/persistence/sessionStorage'
import { useRouter } from 'next/navigation'
import { useEffect } from 'react'

/**
 * Handles the termination of the login flow.
 *
 * Whenever an API request gets a 401 response, we will initiate a login flow. This
 * flow starts with the Revenue Connect backend, redirects to Infinity IdP, back
 * to Revenue Connect backend (which sets a session cookie), and finally here.
 *
 * This page handles two scenarios:
 *
 * 1. We were able to perform login via a popup window. In this case the page
 * was loaded in the popup window and all it needs to do is close the window to
 * complete the flow.
 *
 * 2. We were unable to perform the login via a popup window, so we had to do
 * a full-page redirect. In this case, we will find a redirectUrl in sessionStorage
 * and navigate to it.
 *
 * @returns
 */
export default function LoginHandlerPage() {
  const router = useRouter()
  const [getRedirectUrl] = sessionStorageForKey<string>(
    authRedirectUrlPersistenceKey,
  )
  const redirectUrl = getRedirectUrl?.()

  useEffect(() => {
    if (redirectUrl) {
      router.replace(redirectUrl)
    }
  }, [redirectUrl, router])

  if (redirectUrl) return 'Redirecting...'

  return (
    <div className='relative isolate flex h-screen w-full items-center justify-center'>
      <h1 className='text-lg'>
        Close this window to resume your Revenue Connect session.
      </h1>
    </div>
  )
}
