import { priceType, PriceType, ServiceType } from '@/api/zodSchemas'
import {
  EffectiveDateRequestBody as EffectiveDateRequestBodyForApi,
  VersionableEntityId as VersionableEntityIdForApi,
} from '@/api/formToApiSchema'
import { <PERSON><PERSON><PERSON>, ReactForm<PERSON>pi } from '@tanstack/react-form'

// Price type by service type is based on the data from the following doc:
// https://fisglobal.sharepoint.com/:x:/r/teams/PlatformsandEnterpriseProductOrg-AFEngineering/Shared%20Documents/AF%20Engineering/SNOWWHITE%20(XAA%20in%20the%20cloud)/Product%20Documents/Services/Fields%20by%20Service%20Type%20-%20Volume%20Based.xlsx?d=w9bc4252a7d624fbba12e1d54ee01a9ca&csf=1&web=1&e=jIrMUT
export const PRICE_TYPES_BY_SERVICE_TYPE: Record<ServiceType, PriceType[]> = {
  BALANCE_BASED: priceType.options,
  PRE_PRICED: [],
  RECURRING: ['NOT_PRICED', 'UNIT_PRICED', 'FLAT_FEE'],
  SERVICE_SET: priceType.exclude(['INDEXED', 'PERCENTAGE']).options,
  VOLUME_BASED: priceType.exclude(['INDEXED', 'PERCENTAGE']).options,
}

export function validPriceTypeOrNull(
  serviceType: ServiceType,
  priceType: PriceType | null,
) {
  if (
    priceType &&
    PRICE_TYPES_BY_SERVICE_TYPE[serviceType].includes(priceType)
  ) {
    return priceType
  }

  return null
}

export type VersionableEntityId = VersionableEntityIdForApi

export type EffectiveDateRequestBody = EffectiveDateRequestBodyForApi

export type FormSchemaType<T> = ReactFormApi<T> & FormApi<T>
