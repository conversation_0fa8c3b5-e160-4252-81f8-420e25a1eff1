@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 245, 245, 245;
  --background-end-rgb: 255, 255, 255;
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

@layer components {
  .btn {
    @apply h-10 bg-white text-base font-semibold hover:bg-zinc-100;
  }

  .btn-primary {
    @apply h-10 bg-indigo-600 text-base font-semibold text-white hover:bg-indigo-500;
  }

  .btn-alert {
    @apply h-10 bg-app-color-button-primary-error-bg text-base font-semibold hover:bg-app-color-button-primary-error-bg-hover;
  }

  .btn-disabled {
    @apply h-10 bg-gray-400 text-base font-semibold text-white;
  }

  input,
  textarea,
  select {
    @apply text-base !important;
  }
}

@layer base {
  /* reseting default popover styles */
  :popover-open {
    position: absolute;
    inset: unset;
    padding: 0;
    background-color: unset;
  }
  /* remove arrows from input[type=number] */
  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
  input[type='number'] {
    -moz-appearance: textfield;
  }
}

/* 
ColorPickerInput width override for Coloris Color Picker
.clr-field is from '@melloware/coloris/dist/coloris.css'
*/
.clr-field {
  @apply w-full;
}
