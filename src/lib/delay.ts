export type Delayed<Args extends unknown[], R> = {
  (...args: Args): Promise<R>
  cancel: () => void
}

/**
 * Delay a given function by some number of milliseconds. Returns the delayed
 * function.
 *
 * The delay is cancellable -- if the delayed function has not yet executed,
 * calling `fn.cancel()` will prevent the delayed function from executing.
 *
 * When canceled, the Promise returned by the delayed function will never
 * resolve.
 *
 * Only the most-recent invocation of the delayed function can be cancelled. If
 * the delayed function is invoked twice, and the delayed time has not yet
 * elapsed when `cancel()` is called, _only the second invocation will be
 * cancelled_.
 *
 * @example
 * ```ts
 * const fnToDelay = () => 42
 * const delayByOneSecond = delay(1000)
 *
 * const delayedFn = delayByOneSecond(fnToDelay)
 *
 * const delayedResult: Promise<number> = delayedFn()
 *
 * // After calling `cancel()`, `delayedResult` will never resolve.
 * delayedFn.cancel()
 * ```
 *
 * @param ms Number of milliseconds by which to delay function execution
 * @returns Delayed function
 */
export function delay(
  ms: number,
): <Args extends unknown[], R>(fn: (...args: Args) => R) => Delayed<Args, R> {
  return <Args extends unknown[], R>(fn: (...args: Args) => R) => {
    let timeout: ReturnType<typeof setTimeout> | undefined = undefined
    const delayedFn = (...args: Args) =>
      new Promise<R>((r) => {
        timeout = setTimeout(() => r(fn(...args)), ms)
      })

    return Object.assign(delayedFn, {
      cancel: () => {
        timeout && clearTimeout(timeout)
      },
    })
  }
}
