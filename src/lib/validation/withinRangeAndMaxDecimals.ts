import { RefinementCtx } from 'zod'
import Decimal from 'decimal.js'
/**
 * Validate max digits for floats.
 * @example
 * ```ts
 * import Decimal from 'decimal.js'
 * 
 * const indexRateSchema = z
     .preprocess((input) => {
       if (typeof input === 'string' || typeof input === 'number') {
         return new Decimal(input)
       }
     }, z.instanceof(Decimal))
     .superRefine(withinRangeAndMaxDecimals(0, 100, 6))
     .transform((val) => val.toNumber()),
 * ```
 */
export function withinRangeAndMaxDecimals<TValue extends Decimal>(
  min: number,
  max: number,
  decimals: number,
) {
  return (value: TValue, ctx: RefinementCtx) => {
    if (value.lt(min) || value.gt(max)) {
      ctx.addIssue({
        code: 'custom',
        message: `Number must be between ${min} and ${max}`,
      })
    }
    if (value.dp() > decimals) {
      ctx.addIssue({
        code: 'custom',
        message: `Number must have at most ${decimals} decimal places`,
      })
    }
  }
}
