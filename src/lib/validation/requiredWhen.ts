import {
  DeepKeys,
  DeepValue,
  FieldApi,
  FieldValidateFn,
  FieldValidators,
} from '@tanstack/react-form'

export const requiredWhen = <Schema, Name extends DeepKeys<Schema>>(
  predicate: (
    value: DeepValue<Schema, Name>,
    fieldApi: FieldApi<Schema, Name>,
  ) => boolean,
): FieldValidators<Schema, any> => {
  const validation: FieldValidateFn<Schema, Name> = ({ value, fieldApi }) => {
    const isRequired = predicate(value, fieldApi)
    const hasExistingError = fieldApi.getMeta().errors.includes('Required')

    if (isRequired) {
      return value == undefined && !hasExistingError ? 'Required' : undefined
    }

    return undefined
  }

  return {
    onChange: validation,
    onMount: validation,
  }
}
