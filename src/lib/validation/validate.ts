import { z } from 'zod'

/**
 * Validate a value against a Zod schema, returning the list of error messages if
 * the schema fails to parse the value.
 *
 * This is useful in conjuction with TanStack Form, when running more complex
 * validations on field values -- particularly when `z.refine()` is required.
 *
 * @example
 *```ts
 *<FormDatePicker
 * validators={{
 *   onChange: ({ value: expirationDate, fieldApi }) =>
 *     parse(
 *       expirationDate,
 *       z.date().refine(
 *           (value) => {
 *             // any logic that require fieldApi
 *           }
 *         ),
 *     ),
 * }}
 * />
 *```
 */
export function validate<T>(value: T, schema: z.ZodType<T>) {
  const result = schema.safeParse(value)
  if (!result.success) {
    return result.error.issues.map((issue) => issue.message).join(',')
  }
}
