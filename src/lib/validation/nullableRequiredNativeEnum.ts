import {
  EnumLike,
  RawCreateParams,
  z,
  Zod<PERSON>ffects,
  ZodNative<PERSON>num,
  ZodNullable,
} from 'zod'

/**
 * Given an enum type `T`, produces a schema type `T | null` that when validated
 * will fail if the value is `null`.
 *
 * This is useful for dropdown selection fields where the dropdown options come
 * from an enum, but we do not want to pre-select any of the options -- i.e. we
 * want the default value to be `null`. But we still want the field to be
 * required, so it should fail validation if the value is still `null`.
 *
 * @param values A native TypeScript enum
 * @param params
 * @returns
 */
export function nullableRequiredNativeEnum<T extends EnumLike>(
  values: T,
  params?: RawCreateParams,
): ZodEffects<ZodNullable<ZodNativeEnum<T>>> {
  return nullableRequired(z.nativeEnum(values, params))
}

export function nullableRequired<T extends z.ZodTypeAny>(type: T) {
  return type.nullable().transform((value, ctx) => {
    if (value === null) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Required',
      })
    }
    return value
  })
}
