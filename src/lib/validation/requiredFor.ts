import { DeepK<PERSON>s, DeepValue, FieldValidators } from '@tanstack/react-form'

/**
 * Create a Field validation that will require a non-null (and non-undefined)
 * value, only if some other form field value is one of a given list of `forValues`.
 *
 * @returns
 */
export const requiredFor =
  <Schema>() =>
  <Name extends DeepKeys<Schema>>(
    name: Name,
    forValues: DeepValue<Schema, Name>[],
    except?: boolean,
  ): FieldValidators<Schema, Name> => {
    return {
      onChangeListenTo: [name],
      onChange: ({ value, fieldApi }) => {
        const forValue = fieldApi.form.getFieldValue(name)

        const isRequired =
          (except && !forValues.includes(forValue)) ||
          (!except && forValues.includes(forValue))

        return isRequired && value == undefined ? 'Required' : undefined
      },
    }
  }

export const requiredForExcept =
  <Schema>() =>
  <Name extends DeepKeys<Schema>>(
    name: Name,
    forValues: DeepValue<Schema, Name>[],
  ) =>
    requiredFor<Schema>()(name, forValues, true)
