import { SafeParseReturnType, ZodType, ZodTypeDef } from 'zod'

/**
 * The same as <PERSON><PERSON>'s `.safeParse`, except you must provide a value assignable to
 * the schema's input type.
 *
 * @param schema Any Zod schema
 * @param value It's type must be assignable to the Zod schema input type.
 * @returns
 */
export function typeSafeParse<Input, Def extends ZodTypeDef, Output>(
  schema: ZodType<Output, Def, Input>,
  value: Input,
): SafeParseReturnType<Input, Output> {
  return schema.safeParse(value)
}
