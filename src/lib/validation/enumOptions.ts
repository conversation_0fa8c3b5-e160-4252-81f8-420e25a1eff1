import { z, Zod<PERSON>ny, Zod<PERSON>ffects, ZodEnum, ZodNullable } from 'zod'

export function enumOptions<T extends [string, ...string[]]>(
  value: ZodEffects<ZodNullable<ZodEnum<T>>, unknown, unknown> | ZodEnum<T>,
): T {
  return 'innerType' in value ?
      value.innerType().unwrap().options
    : (value as ZodEnum<T>).options
}

export type EnumLabels<T extends z.ZodType<any, any, any>> = Record<
  NonNullable<z.infer<T>>,
  string
>
