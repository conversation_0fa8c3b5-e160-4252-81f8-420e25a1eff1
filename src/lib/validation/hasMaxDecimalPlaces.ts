import { RefinementCtx } from 'zod'

/**
 * Validate max fraction for number.
 * @example
 * ```ts
 * const feeOrPriceSchema = z.string().superRefine(hasMaxDecimalPlaces(5))
 * ```
 */
export function hasMaxDecimalPlaces<TValue extends string | number>(
  max: number,
  message: string = `Fractional length should be less or equal to ${max} digits.`,
) {
  return (value: TValue | null, ctx: RefinementCtx) => {
    let isValid = false

    if (typeof value === 'string') {
      const fractionLength = (value.split('.')[1] ?? '').length
      isValid = fractionLength <= max
    }

    if (typeof value === 'number') {
      const maybeInteger = value * Math.pow(10, max)
      isValid = Number.isInteger(maybeInteger)
    }

    if (!isValid && value != null) {
      ctx.addIssue({
        code: 'custom',
        message,
      })
    }
  }
}
