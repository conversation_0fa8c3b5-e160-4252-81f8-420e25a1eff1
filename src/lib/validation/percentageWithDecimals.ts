import { z } from 'zod'
import Decimal from 'decimal.js'
import { withinRangeAndMaxDecimals } from './withinRangeAndMaxDecimals'

// validates floating-point numbers
// decimals in JS are stored as binary fractions, leading to small rounding errors
// ex: 8.12 can be 8.120000000000001 --> breaking 6 decimal-digit validation
export const percentageWithDecimals = z
  .preprocess((input) => {
    if (typeof input === 'string' || typeof input === 'number') {
      return new Decimal(input)
    }
  }, z.instanceof(Decimal))
  .superRefine(withinRangeAndMaxDecimals(0, 100, 6))
  .transform((val) => val.toNumber())
