export type UnaryFunction<A, B = void> = (arg: A) => B

/**
 * Compose functions.
 *
 * This utility is also commonly called `pipe` (e.g. in rxjs).
 *
 * @param ...fns Any number of functions to compose
 */
export function compose<T1, T2>(
  fn1: UnaryFunction<T1, T2>,
): UnaryFunction<T1, T2>
export function compose<T1, T2, T3>(
  fn1: UnaryFunction<T1, T2>,
  fn2: UnaryFunction<T2, T3>,
): UnaryFunction<T1, T3>
export function compose<T1, T2, T3, T4>(
  fn1: UnaryFunction<T1, T2>,
  fn2: UnaryFunction<T2, T3>,
  fn3: UnaryFunction<T3, T4>,
): UnaryFunction<T1, T4>
export function compose<T1, T2, T3, T4, T5>(
  fn1: UnaryFunction<T1, T2>,
  fn2: UnaryFunction<T2, T3>,
  fn3: UnaryFunction<T3, T4>,
  fn4: UnaryFunction<T4, T5>,
): UnaryFunction<T1, T5>
export function compose(
  ...fns: UnaryFunction<unknown, unknown>[]
): UnaryFunction<unknown, unknown> {
  return fns.reduce((a, b) => (arg) => b(a(arg)))
}
