import { mapObject } from './mapObject'

export type ObjectIsoMapper<T extends {}> = {
  [K in keyof T]?: (value: T[K]) => T[K]
}

/**
 * Map from one object to another of the same type. That is, the mapped object
 * has the exact same keys as the original object, and each key's value has the
 * same type as that key's value in the original object.
 *
 * @example
 * ```ts
 * const obj = { foo: 'bar' }
 * const mapped: { foo: string } = isoMapObject(obj, { foo: (bar) => `${bar} baz` })
 * ```
 *
 * @param object
 * @param mapper
 * @returns
 */
export function isoMapObject<T extends {}>(
  object: T,
  mapper: ObjectIsoMapper<T>,
): T {
  return {
    ...object,
    ...mapObject(mapper, ([key, map]) => {
      return [key, map ? map(object[key]) : object[key]]
    }),
  }
}
