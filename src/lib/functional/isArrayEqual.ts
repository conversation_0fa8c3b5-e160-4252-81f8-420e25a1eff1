/**
 * Creates a predicate that determines if two arrays are equal.
 *
 * @param comparator
 * @returns
 */
export function isArrayEqual<T extends Array<unknown> | ReadonlyArray<unknown>>(
  comparator: (a: T[number], b: T[number]) => boolean = (a, b) => a === b,
): (firstArray: T, secondArray: T) => boolean {
  return (firstArray, secondArray) => {
    return (
      firstArray.length === secondArray.length &&
      firstArray.every((a, i) => comparator(a, secondArray[i]))
    )
  }
}
