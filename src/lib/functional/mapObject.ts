import { entries } from './entries'
import { fromEntries } from './fromEntries'

export type ObjectMapper<T extends {}> = (
  entry: [keyof T, T[keyof T]],
) => [keyof T, unknown]

export type MappedObject<T extends {}, M extends ObjectMapper<T>> = {
  [K in keyof T]: ReturnType<M>[1]
}

/**
 * Map from one object to another with the same keys, retaining more detailed
 * type information.
 *
 * @example
 * ```ts
 * const obj = { foo: 'bar' }
 * const mapped: { foo: number } = mapObject(obj, ([key, value]) => [key, value.length])
 * ```
 *
 * @param object
 * @param mapper
 * @returns
 */
export function mapObject<T extends {}, M extends ObjectMapper<T>>(
  object: T,
  mapper: M,
): MappedObject<T, M> {
  return fromEntries(entries(object).map(mapper))
}
