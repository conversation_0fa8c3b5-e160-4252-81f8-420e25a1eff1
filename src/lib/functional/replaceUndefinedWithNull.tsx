import { mapObject } from '@/lib/functional/mapObject'

type UndefinedTo<PERSON>ull<T> = T extends undefined ? null : T
type ReplaceUndefinedWithNull<T> = Required<{
  [Key in keyof T]: UndefinedToNull<T[Key]>
}>

export function replaceUndefined<PERSON><PERSON>Null<T extends {}>(
  obj: T,
): ReplaceUndefinedWithNull<T> {
  return mapObject(obj, (prop) => prop ?? null) as ReplaceUndefinedWithNull<T>
}
