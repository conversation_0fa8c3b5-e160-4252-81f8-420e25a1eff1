import { compose } from './functional/compose'

const CURRENT_LOCALE = 'en-US'

const intlUSD = Intl.NumberFormat(CURRENT_LOCALE, {
  currency: 'USD',
  style: 'currency',
  maximumFractionDigits: 6,
  minimumFractionDigits: 2,
})

const intlVolume = Intl.NumberFormat(CURRENT_LOCALE)

function tryParseFloat(value: string | number | undefined | null) {
  return typeof value === 'string' ? parseFloat(value) : value
}

function formatWith(intlFormat: Intl.NumberFormat) {
  return function formatter(value: number | undefined | null) {
    return value == undefined ? value : intlFormat.format(value)
  }
}

export const formatUSD = compose(tryParseFloat, formatWith(intlUSD))
export const formatVolume = compose(tryParseFloat, formatWith(intlVolume))
