import { defineErrorClass } from '../errors'
import { OkResponse } from './fetchErrorMiddleware'
import { FetchMiddleware } from './fetchImpl'

/**
 * Fetch middleware that parses HTTP Response bodies as JSON.
 *
 * If the Response body cannot be parsed as JSON, this will throw `InvalidResponseJson`.
 *
 * @param input
 * @param init
 * @returns
 */
export function jsonMiddleware(): FetchMiddleware<unknown, OkResponse> {
  return (next) => async (request) => {
    const response = await next(request)

    try {
      const json: unknown = await response.json()
      return json
    } catch (err) {
      const cause = err instanceof Error ? err : undefined
      throw new InvalidResponseJson(
        `The response body from \`${request.url}\` could not be parsed as JSON.`,
        { cause },
      )
    }
  }
}

export const InvalidResponseJson = defineErrorClass('InvalidResponseJson')
