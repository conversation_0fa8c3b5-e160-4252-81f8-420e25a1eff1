import { compose } from '../functional/compose'
import { Middleware, withMiddleware } from '../middleware/withMiddleware'
import { authMiddleware } from './authMiddleware'
import { fetchErrorMiddleware } from './fetchErrorMiddleware'
import { jsonMiddleware } from './jsonMiddleware'

/**
 * Describes a middleware function that operates on `Requests` to produce some
 * `Promise<Resp>`, where `Resp` is typically a `Response`.
 */
export type FetchMiddleware<Resp = Response, NextResp = Resp> = Middleware<
  Request,
  Promise<Resp>,
  Request,
  Promise<NextResp>
>

/**
 * Compose FetchMiddleware to produce a fetch implementation with the following
 * features:
 *
 * 1. Consistent error behavior (e.g. HTTP status codes >= 400 throw).
 * 2. Response bodies are parsed as JSON.
 * 3. 401 responses initiate a login flow.
 */
export const fetchImpl = withMiddleware(
  (request: Request) => fetch(request),
  compose(fetchErrorMiddleware(), authMiddleware(), jsonMiddleware()),
)
