const metricsUtils = (route: string) => {
  const calls: Array<unknown> = []
  let startTime = 0
  let numAttempts = 0
  const baseDelay = 1000
  const maxRetries = 3

  const expoBackoff = (attempts: number) => {
    numAttempts = attempts
    if (numAttempts >= maxRetries) {
      endTimer(true)
    } else {
      endTimer(false)
    }
    return Math.min(baseDelay * 2 ** attempts, 30 * baseDelay)
  }

  const linearBackoff = (attempts: number) => {
    numAttempts = attempts
    return attempts * baseDelay
  }

  const process = () => {
    const data = {
      [route]: calls,
    }
    // TODO: Send to metrics service/endpoint
    console.log('Metrics Data: ', data)
  }

  const startTimer = () => {
    startTime = performance.now()
  }

  const endTimer = (complete: boolean) => {
    const elapsed = performance.now() - startTime
    calls.push({
      numAttempts,
      elapsed,
    })
    if (complete) {
      process()
    }
  }

  return {
    maxRetries,
    startTimer,
    endTimer,
    expoBackoff,
    linearBackoff,
  }
}

export default metricsUtils
