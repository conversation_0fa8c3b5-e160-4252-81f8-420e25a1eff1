import { defineErrorClass } from '../errors'
import { FetchMiddleware } from './fetchImpl'

export type OkResponse = Response & { ok: true }

export function isOkResponse(response: Response): response is OkResponse {
  return response.ok
}

/**
 * Fetch middleware which normalizes the native `fetch` error behavior.
 *
 * Responses with status codes >= 400 will result in a thrown error. The error
 * type will be either `HttpClientError` (for codes >= 400 and < 500) or
 * `HttpServerError` (for codes >= 500). The error will include a `cause` containing
 * the raw text of the Response body.
 *
 * If `fetch` itself throws an error, it will be re-thrown as `FailedToFetch`.
 *
 * @returns
 */
export function fetchErrorMiddleware(): FetchMiddleware<OkResponse, Response> {
  return (next) => async (request) => {
    let response: Response
    try {
      response = await next(request)
    } catch (err) {
      const cause = err instanceof Error ? err : undefined
      throw new FailedToFetch(
        `Encountered an error which prevented fetching of \`${request.url}\``,
        { cause },
      )
    }

    if (!isOkResponse(response)) {
      const status = response.status
      const message = await response
        .text()
        .catch(
          () =>
            'Response body could not be decoded. The Content-Encoding header may be invalid or incorrect.',
        )
      const cause = new Error(message)

      const ErrorConstructor =
        status === 401 ? HttpUnauthorizedError
        : status >= 500 ? HttpServerError
        : HttpClientError

      throw new ErrorConstructor(`[${status}]`, { cause })
    }

    return response
  }
}

export const FailedToFetch = defineErrorClass('FailedToFetch')
export const HttpUnauthorizedError = defineErrorClass('HttpUnauthorizedError')
export const HttpClientError = defineErrorClass('HttpClientError')
export const HttpServerError = defineErrorClass('HttpServerError')
