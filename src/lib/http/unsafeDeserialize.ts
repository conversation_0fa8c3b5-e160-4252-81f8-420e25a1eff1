/**
 * Unsafely cast a value to a given type `T`.
 *
 * This is intended for use only when deserializing JSON data that has a known
 * shape, described by the given type `T`.
 *
 * If there is _any chance_ that a value may _not_ be of type `T`, this function
 * should **not** be used. In those cases, write a custom type guard that proves
 * the value is of type `T` and use that instead.
 *
 * @param value The result of parsing a Response body as JSON -- e.g. obtained
 * from calling `fetchJson`.
 * @returns The input value cast to the given type `T`
 */
export function unsafeDeserialize<T>(value: unknown): T {
  return value as T
}

export function unsafeAsyncDeserialize<T>(value: Promise<unknown>): Promise<T> {
  return value as Promise<T>
}
