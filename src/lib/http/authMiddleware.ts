import { defineErrorClass } from '../errors'
import { sessionStorageFor<PERSON>ey } from '../persistence/sessionStorage'
import { HttpUnauthorizedError, OkResponse } from './fetchErrorMiddleware'
import { FetchMiddleware } from './fetchImpl'

const authPath = '/auth'

export const authRedirectUrlPersistenceKey = 'authRedirectUrl'

export function authMiddleware(): FetchMiddleware<OkResponse> {
  const [_, setRedirectUrl] = sessionStorageForKey(
    authRedirectUrlPersistenceKey,
  )

  return (next) => async (request) => {
    // We're not in a React component here, so we can't use a hook like `usePathname`
    // here -- getting the bankId this way enforces that all API requests originate
    // from the browser (and not from the NextJS Node server). This is what we want.
    const bankId = encodeURI(
      globalThis.document?.location.pathname.split('/')[1],
    )

    // All requests require a `bankId` query parameter.
    const url = new URL(request.url)
    url.searchParams.set('bankId', bankId)
    const modifiedRequest = await createModifiedRequest(url, request)

    try {
      const response = await next(modifiedRequest)

      // If response is successful and window has an opener with the same location origin,
      // close the popup.
      try {
        if (
          globalThis.opener?.location?.origin === globalThis.location.origin
        ) {
          globalThis.close()
        }
      } catch {
        // Origin could be blocked by the secuirty context,
        // just silently ignoring the error,
        // no need to close any popup windows.
      }

      return response
    } catch (err) {
      if (!(err instanceof HttpUnauthorizedError)) throw err

      const authUrl = new URL(authPath, new URL(url).origin)
      authUrl.searchParams.set('bankId', bankId)

      const maybeLoggedIn = await openLoginPopup(authUrl)

      // We were unable to open the login popup for some reason -- fallback to
      // full page redirect.
      if (maybeLoggedIn === false) {
        if (globalThis.document?.location) {
          if (setRedirectUrl) setRedirectUrl(globalThis.document.location.href)
          globalThis.document.location = authUrl.href
          return new Response(null, { status: 204 }) as OkResponse
        } else {
          throw new CannotAuthenticateError(
            `[${bankId}] Cannot redirect, 'globalThis.document' not found.`,
          )
        }

        // The login popup has closed, but we can't be sure that we're
        // authenticated (e.g. user could've closed the popup without completing
        // the login flow). We will retry the original request -- callers should
        // not retry 401s.
      } else {
        return next(await createModifiedRequest(url, request))
      }
    }
  }
}

/**
 * Modifying Requests is really annoying.
 *
 * @param url
 * @param request
 * @returns
 */
async function createModifiedRequest(
  url: URL,
  request: Request,
): Promise<Request> {
  return new Request(url, {
    // Request body's are single-use -- so we have to clone it.
    body: await request.clone().blob(),
    cache: request.cache,
    credentials: request.credentials,
    headers: request.headers,
    integrity: request.integrity,
    keepalive: request.keepalive,
    method: request.method,
    mode: request.mode,
    redirect: request.redirect,
    referrer: request.referrer,
    referrerPolicy: request.referrerPolicy,
    signal: request.signal,
  })
}

const popupWidth = 680
const popupHeight = 580

/**
 * Open a new window for the user to perform a login flow.
 *
 * @param url
 * @returns
 */
function openLoginPopup(url: URL): Promise<boolean> {
  return new Promise((resolve) => {
    if (typeof globalThis.open !== 'function') return resolve(false)

    const thisLeft = globalThis.screenLeft ?? globalThis.screenX
    const thisTop = globalThis.screenTop ?? globalThis.screenY
    const thisWidth = globalThis.innerWidth
    const thisHeight = globalThis.innerHeight

    const left = (thisWidth - popupWidth) / 2 + thisLeft
    const top = (thisHeight - popupHeight) / 2 + thisTop

    const authWindowHandle = globalThis.open(
      url,
      // Giving the window a name means that we will never open multiple popups,
      // we'll just reload the already-opened window.
      'Revenue Connect Login',
      `popup,left=${left},top=${top},width=${popupWidth},height=${popupHeight}`,
    )

    if (!authWindowHandle) return resolve(false)

    authWindowHandle.focus()

    const interval = setInterval(() => {
      if (authWindowHandle.closed) {
        clearInterval(interval)
        resolve(true)
      }
    }, 500)
  })
}

export const CannotAuthenticateError = defineErrorClass(
  'CannotAuthenticateError',
)
