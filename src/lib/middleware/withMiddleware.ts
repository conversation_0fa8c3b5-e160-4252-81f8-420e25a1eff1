import { UnaryFunction } from '../functional/compose'
import {
  CombinedParameters,
  CombinedReturnType,
  FunctionUnion,
} from '../unions/combine'

export type Middleware<In, Out, NextIn = In, NextOut = Out> = (
  next: UnaryFunction<NextIn, NextOut>,
) => UnaryFunction<In, Out>

/**
 * Compose a function with some "middleware."
 *
 * This becomes useful in combination with the `compose` function -- the middleware
 * passed here may be composed of many separately-defined functions, each with
 * their own purpose, and together they can modify the behavior of the given `fn`.
 *
 * @example
 * ```ts
 * const myFn = (str: string) => {
 *   console.log('myFn input:', str)
 *
 *   const output = str.length
 *
 *   console.log('myFn output:', output)
 *   return output
 * }
 *
 * function intToString<In>(): Middleware<In, string, In, number> {
 *   return (next) => (input) => {
 *     console.log('intToString input:', input)
 *
 *     const output = next(input).toString()
 *
 *     console.log('intToString output:', output)
 *     return output
 *   }
 * }
 *
 * function concatenate<Out>(s: string): Middleware<string, Out> {
 *   return (next) => (input) => {
 *     console.log('concatenate input:', input)
 *
 *     const output = next(input + s)
 *
 *     console.log('concatenate output:', output)
 *     return output
 *   }
 * }
 *
 * const myModifiedFn = withMiddleware(
 *   myFn,
 *   compose(
 *     intToString(),
 *     concatenate(' middleware!'),
 *   ),
 * )
 *
 * assert(myModifiedFn('hello'), '17')
 *
 * // prints:
 * // > concatenate input: 'hello'
 * // > intToString input: 'hello middleware!'
 * // > myFn input: 'hello middleware!'
 * // > myFn output: 17
 * // > intToString output: '17'
 * // > concatenate output: '17'
 *
 * ```
 *
 * > _Note: this middleware __is not__ NextJS middleware -- NextJS middleware runs
 * > on the server and is totally unrelated._
 *
 * @param fn Any unary function
 * @param middleware A "middleware" function that will wrap the inner `fn`.
 * @returns
 */
export function withMiddleware<FnIn, FnOut, In = FnIn, Out = FnOut>(
  fn: UnaryFunction<FnIn, FnOut>,
  middleware: Middleware<In, Out, FnIn, FnOut>,
): UnaryFunction<In, Out> {
  return middleware(fn)
}

/**
 * Create middleware which performs a side-effect.
 *
 * The side-effect is executed after the next middleware in the chain, and is
 * passed both input and output.
 *
 * @example
 * ```ts
 * const myFn = (str: string) => str.length
 *
 * const modifiedFn = withMiddleware(
 *   myFn,
 *   sideEffect((str: string) => {
 *     console.log('the string is:', str)
 *   })
 * )
 *
 * modifiedFn('hello')
 *
 * // prints: `the string is: 'hello'`
 *
 * ```
 *
 * @param sideEffect
 * @returns
 */
export function sideEffect<NextIn, NextOut>(
  sideEffect: UnaryFunction<[NextIn, NextOut], void>,
): Middleware<NextIn, NextOut> {
  return (next) => (input) => {
    const output = next(input)
    sideEffect([input, output])
    return output
  }
}

/**
 * Create a middleware which transforms its input before passing it to the next
 * middleware in the chain.
 *
 * @example
 * ```ts
 * const myFn = (str: string) => str.length
 *
 * const modifiedFn = withMiddleware(
 *   myFn,
 *   inputMiddleware((n: number) => n.toString())
 * )
 *
 * // Our middleware converts 42 to the string '42' before calling `myFn`.
 * assert(modifiedFn(42), 2)
 *
 * ```
 *
 * @param transform
 * @returns
 */
export function inputMiddleware<Out, NextIn, In = NextIn>(
  transform: (input: In) => NextIn,
): Middleware<In, Out, NextIn, Out> {
  return (next) => (input) => next(transform(input))
}

/**
 * Create a middleware which transforms the output of the next middleware in
 * the chain.
 *
 * @example
 * ```ts
 * const myFn = (str: string) => str.length
 *
 * const modifiedFn = withMiddleware(
 *   myFn,
 *   outputMiddleware((n) => n * 2)
 * )
 *
 * // Our middleware doubles the output of `myFn`.
 * assert(modifiedFn('hello'), 10)
 *
 * ```
 *
 * @param transform
 * @returns
 */
export function outputMiddleware<In, Out, NextOut>(
  transform: (output: NextOut) => Out,
): Middleware<In, Out, In, NextOut> {
  return (next) => (input) => transform(next(input))
}

/**
 * Apply middleware to a function created by a call to `combine` -- which takes
 * a `Union` of functions and returns a function with a `Union` argument.
 *
 * This is simply a helper to provide the typing for such middleware. It just
 * returns the middleware passed to it, unchanged.
 *
 * @example
 * ```ts
 * type MyUnion =
 *   | FunctionUnion<'a', string, string>
 *   | FunctionUnion<'b', number, number>
 *
 * const myMiddleware = combineMiddleware<MyUnion>(
 *
 *   // Now `argument` has type `Union<'a', string> | Union<'b', number>`
 *   (next) => (argument) => {
 *
 *     // Our middleware can transform the argument, matching on each union
 *     // variant and returning an argument of the same type.
 *     return next(matchIdentity(argument, {
 *       a: (str) => str.toUpperCase(),
 *       b: (n) => n * n,
 *     }))
 *   }
 * )
 * ```
 *
 * @param middleware
 * @returns
 */
export function combineMiddleware<U extends FunctionUnion>(
  middleware: Middleware<CombinedParameters<[U]>, CombinedReturnType<[U]>>,
): Middleware<CombinedParameters<[U]>, CombinedReturnType<[U]>> {
  return middleware
}
