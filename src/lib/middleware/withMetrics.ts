import metricsUtils from '@/lib/http/metricsUtils'
import { Data, Tag, tag, Union } from '../unions/Union'
import { Middleware, outputMiddleware } from './withMiddleware'
import { UseMutationOptions, UseQueryOptions } from '@tanstack/react-query'
import { isoMap } from '../unions/isoMap'

type AsyncFn = (...args: any[]) => Promise<unknown>

/**
 * Mutation or Query middleware which adds metrics to the queryFn/mutationFn.
 *
 * @returns A middleware which may be passed to `defineQueries` or `defineMutations`
 * to add metrics to any of the associated queries or mutations.
 */
export function withMetrics<
  Request extends Union<any, unknown>,
  Options extends Union<
    Tag<Request>,
    UseMutationOptions<any, Error, any> | UseQueryOptions<any, Error, any, any>
  >,
>(): Middleware<Request, Options> {
  return outputMiddleware((options) => {
    const uri = tag(options)
    const { startTimer, endTimer, maxRetries, expoBackoff } = metricsUtils(uri)

    const measureAsyncFn =
      <F extends AsyncFn>(fn: F) =>
      async (...args: Parameters<F>): Promise<Awaited<ReturnType<F>>> => {
        startTimer()
        try {
          const result = await (fn(...args) as ReturnType<F>)
          endTimer(true)
          return result
        } catch (error) {
          endTimer(true)
          throw error
        }
      }

    return isoMap(options, {
      _: (options: Data<Options>) => {
        const retry = options.retry

        const modifiedOptions: Data<Options> = {
          ...options,
          retry: (failureCount, error) => {
            if (failureCount > maxRetries) return false
            if (typeof retry === 'function') return retry(failureCount, error)
            if (typeof retry === 'number') return failureCount < retry
            return retry ?? false
          },
          retryDelay: expoBackoff,
        }

        if ('queryFn' in options && typeof options.queryFn === 'function') {
          return {
            ...modifiedOptions,
            queryFn: measureAsyncFn(options.queryFn),
          }
        }
        if ('mutationFn' in options && options.mutationFn) {
          return {
            ...modifiedOptions,
            mutationFn: measureAsyncFn(options.mutationFn),
          }
        }
      },
    })
  })
}
