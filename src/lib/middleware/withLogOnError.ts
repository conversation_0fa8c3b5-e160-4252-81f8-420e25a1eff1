import { AnyMutation } from '../state/defineMutation'
import { isoMap } from '../unions/isoMap'
import { Union } from '../unions/Union'
import { combineMiddleware, outputMiddleware } from './withMiddleware'
import { UnaryFunction } from '../functional/compose'
import { isoMapObject } from '../functional/isoMapObject'

/**
 * Mutation middleware that adds logging to the `onError` UseMutationOption.
 *
 * @param log Optionally pass in a function which will be called with an error.
 * Defaults to `console.error`.
 * @returns
 */
export const withLogOnError = <U extends Union<any, AnyMutation>>(
  log: UnaryFunction<unknown, void> = console.error.bind(console),
) =>
  combineMiddleware<U>(
    outputMiddleware((options) => {
      return isoMap(options, {
        _: (options: ReturnType<AnyMutation>) =>
          isoMapObject(options, {
            onError: (onError) => (error, request, context) => {
              log(error)
              return onError?.(error, request, context)
            },
          }),
      })
    }),
  )
