'use client'

import { Union, data, variant } from '@/lib/unions/Union'
import {
  ReadonlyURLSearchParams,
  useParams,
  usePathname,
  useSearchParams,
} from 'next/navigation'
import { IfOptional } from './types/ifOptional'

type NextRouteParams = ReturnType<typeof useParams>

type RouteParams = readonly string[]
export type RouteParamsRecord<Params extends RouteParams> = {
  bankId: string
} & {
  [K in Params[number]]: string
}
type QueryParams = Record<string, unknown>

type ParamsToHref<Params extends RouteParams> =
  Params extends (
    readonly [infer Head extends string, ...infer Tail extends RouteParams]
  ) ?
    `/[${Head}]${ParamsToHref<Tail>}`
  : ''

type RouteDefinition<
  Href extends string = string,
  Params extends RouteParams = RouteParams,
  Query extends QueryParams = QueryParams,
> = {
  href: Href
  use: UseRoute<Href, Params, Query>
  to: RouteTo<Params, Query>
}

type UseRoute<
  Href extends string = string,
  Params extends RouteParams = [],
  Query extends QueryParams = {},
> = (
  pathname: string,
  routeParams: NextRouteParams,
  queryParams: ReadonlyURLSearchParams,
) =>
  | undefined
  | Union<
      Href,
      {
        params: RouteParamsRecord<Params>
        query: Query
      }
    >

type RouteTo<
  Params extends RouteParams = [],
  Query extends QueryParams = {},
> = (params: RouteParamsRecord<Params>, query: Query) => string

/**
 * Define an application route, including any dynamic route segments (i.e. route
 * params) and query params.
 *
 * @example
 * ```ts
 *
 * const myRoute = defineRoute(
 *   '/my-route',
 *   ['id'],
 *   (searchParams) => ({ foo: searchParams.get('foo') })
 * )
 *
 * const href = myRoute.to({ id: 'me' }, { foo: 'bar' })
 * assert(href, '/my-route/me?foo=bar')
 *
 * // Or, more commonly:
 *
 * const [ useRoute, routeTo ] = defineRoutes(myRoute)
 * const href = routeTo('/my-route', { id: 'me' }, { foo: 'bar' })
 * ```
 *
 *
 * @param tag A unique string identifier for this route. It should be equal to
 * the route's path, without any dynamic route segments.
 * @param paramKeys A list of strings identifying the route's dynamic segments.
 * @param parseQuery A function which may return a well-typed object containing
 * data parsed from the URL search params.
 * @returns A RouteDefinition which may be passed to `defineRoutes` to enable
 * using the route's parameters and generating hrefs to this route.
 */
export function defineRoute<
  Tag extends string = string,
  const Params extends RouteParams = [],
  Query extends QueryParams = {},
>(
  tag: Tag,
  paramKeys: Params | readonly [] = [],
  parseQuery:
    | ((query: ReadonlyURLSearchParams) => Query | false)
    | (() => {}) = () => ({}),
): RouteDefinition<`/${Tag}${ParamsToHref<Params>}`, Params, Query> {
  const href = [`/${tag}`, ...paramKeys.map((key) => `[${key}]`)].join(
    '/',
  ) as `/${Tag}${ParamsToHref<Params>}`

  return {
    href: href,

    use: (pathname, routeParams, queryParams) => {
      if (!matchesRoute(pathname, href)) return undefined
      const query = parseQuery(queryParams) as Query | false
      if (!query) return undefined

      // Every route extracts `bankId` from the params.
      const params = Object.fromEntries(
        ['bankId', ...paramKeys].map((param) => {
          return [param, routeParams[param] as string]
        }),
      ) as RouteParamsRecord<Params>

      return variant<
        `/${Tag}${ParamsToHref<Params>}`,
        {
          params: RouteParamsRecord<Params>
          query: Query
        }
      >(href, { params, query })
    },

    to: (params, query) => {
      return `${interpolateRouteParams(href, params)}?${queryToString(query)}`
    },
  }
}

/**
 * Define a nested route.
 *
 * Given a parent route and a child route, this will return a new RouteDefinition
 * that concatenates the parent and child routes and merges their parameters.
 *
 * @example
 * ```ts
 * const servicesRoute = defineRoute('/services')
 *
 * const catalogRoute = defineChildRoute(
 *   servicesRoute,
 *   defineRoute('view', ['serviceCode']),
 * )
 *
 * const href = catalogRoute.to({ serviceCode: 'foo' })
 * assert(href, '/services/view/foo')
 * ```
 *
 * @param parent A parent route, created by `defineRoute`
 * @param child A child route, created by `defineRoute`
 * @returns A new route definition which concatenates the parent and child
 * routes and merges their parameters.
 */
export function defineChildRoute<
  ParentTag extends string = string,
  const ParentParams extends RouteParams = [],
  ParentQuery extends QueryParams = {},
  ChildTag extends string = string,
  const ChildParams extends RouteParams = [],
  ChildQuery extends QueryParams = {},
>(
  parent: RouteDefinition<ParentTag, ParentParams, ParentQuery>,
  child: RouteDefinition<ChildTag, ChildParams, ChildQuery>,
): RouteDefinition<
  `${ParentTag}${ChildTag}`,
  [...ParentParams, ...ChildParams],
  ParentQuery & ChildQuery
> {
  const href = `${parent.href}${child.href}` as const
  return {
    href,

    use: (pathname, routeParams, queryParams) => {
      const parentRoute = parent.use(pathname, routeParams, queryParams)
      if (!parentRoute) return undefined

      const { params: parentParams, query: parentQuery } = data(parentRoute)

      const childPathname =
        '/' + pathname.split('/').slice(parent.href.split('/').length).join('/')
      const childRoute = child.use(childPathname, routeParams, queryParams)
      if (!childRoute) return undefined

      const { params, query } = data(childRoute)

      return variant<
        `${ParentTag}${ChildTag}`,
        {
          routable: true
          href: `${ParentTag}${ChildTag}`
          params: RouteParamsRecord<[...ParentParams, ...ChildParams]>
          query: ParentQuery & ChildQuery
        }
      >(href, {
        routable: true,
        href,
        params: { ...parentParams, ...params },
        query: { ...parentQuery, ...query },
      })
    },

    to: (params, query) => {
      return `${interpolateRouteParams(href, params)}?${queryToString(query)}`
    },
  }
}

// SAFETY: The last type parameter in RouteDefinition is used in a contravariant
// position -- that is, if we ask for a type that extends RouteDefinition<_, __, T>,
// then T must be assignable to the extended type. This is opposite of how extend
// works with covariant type positions (which is more intuitive).
//
// In the `defineRoutes` method, this means we must be careful not to assume
// anything about the shape of the `Query` type parameter (which we're setting
// to `any` here).
type AnyRouteDefinition = RouteDefinition<string, RouteParams, any>

/**
 * Given a list of defined routes, produce a pair of functions that may be used
 * to obtain route parameters and generate route hrefs, respectively.
 *
 * The first in the returned pair of functions is typically called `useRoute`,
 * and it is a React hook that returns a Union containing strongly-typed
 * route parameters, depending on the current state of the application's URL.
 *
 * The second function is typically called `routeTo` and it takes in strongly-typed
 * arguments corresponding to one of the defined routes and produces a string
 * href to that route.
 *
 * @example
 * ```ts
 * const [ useRoute, routeTo ] = defineRoutes(
 *   firstRoute,
 *   secondRoute,
 *   ...
 * )
 *
 * // `useRoute` returns a Union, we can match on it to obtain route data
 * // for each possible route.
 * match(useRoute(), {
 *   'firstRoute': () => { ... },
 *   'secondRoute': () => { ... },
 * })
 *
 * // The second argument to `routeTo` will be strongly-typed based on the the
 * // first argument, which names the desired route.
 * routeTo('firstRoute', firstRouteParams)
 * ```
 *
 * @param definitions A list of RouteDefinitions, each obtained by calling `defineRoute`
 * or `defineChildRoute`.
 * @returns A pair of functions, `useRoute` and `routeTo`, that can be used to
 * obtain route parameters and generate route hrefs, respectively.
 */
export function defineRoutes<const D extends readonly AnyRouteDefinition[]>(
  ...definitions: D
) {
  const use = defineUseRoute(...definitions)

  return [
    function useRoute() {
      // Remove the bankId from the pathname, so that all our defined query
      // hrefs (which don't include bankId) can match the pathname.
      //
      // (The bankId is still present in routeParams, and will still get parsed
      // into the route.)
      const pathname = '/' + usePathname().split('/').slice(2).join('/')
      const routeParams = useParams()
      const queryParams = useSearchParams()
      return use(pathname, routeParams, queryParams)
    },
    defineRouteTo(...definitions),
  ] as const
}

function defineRouteTo<const D extends readonly RouteDefinition[]>(
  ...definitions: D
) {
  return <Href extends D[number]['href']>(
    ...[href, params, query]: IfOptional<
      Parameters<Extract<D[number], { href: Href }>['to']>[1]
    > extends true ?
      [
        href: Href,
        params: Parameters<Extract<D[number], { href: Href }>['to']>[0],
        query?: Parameters<Extract<D[number], { href: Href }>['to']>[1],
      ]
    : [
        href: Href,
        params: Parameters<Extract<D[number], { href: Href }>['to']>[0],
        query: Parameters<Extract<D[number], { href: Href }>['to']>[1],
      ]
  ) => {
    const definition = definitions.find((d) => d.href === href)!
    return definition.to(params, query ?? {})
  }
}

function defineUseRoute<const D extends readonly RouteDefinition[]>(
  ...definitions: D
) {
  return (
    pathname: string,
    routeParams: NextRouteParams,
    queryParams: ReadonlyURLSearchParams,
  ): ReturnType<D[number]['use']> => {
    // Attempt to match the longest href's first -- this means we'll match a
    // more specific route before a less specific one.
    for (const definition of definitions.toSorted(
      (a, b) => b.href.length - a.href.length,
    )) {
      const route = definition.use(pathname, routeParams, queryParams)
      if (route) return route as ReturnType<D[number]['use']>
    }

    // SAFETY: The return type of the RouteDefinition['use'] function includes
    // undefined, so it is assignable to the return type.
    return undefined as ReturnType<D[number]['use']>
  }
}

const segmentRegex = /\[([a-zA-Z0-9-_]+)\]/

function interpolateRouteParams(
  href: string,
  params: Record<string, string>,
): string {
  return `/[bankId]${href}`
    .split('/')
    .map((segment) => {
      const match = segment.match(segmentRegex)
      if (!match || !(match[1] in params)) return segment
      return params[match[1]]
    })
    .join('/')
}

function matchesRoute(pathname: string, href: string): boolean {
  const pathSegments = pathname.split('/')
  return href.split('/').every((segment, i) => {
    const pathSegment = pathSegments[i]
    if (typeof pathSegment === 'undefined') return false
    if (segmentRegex.test(segment)) return true
    return segment === pathSegment
  })
}

function queryToString(query: QueryParams): string {
  const searchParams = new URLSearchParams()
  for (const [key, value] of Object.entries(query)) {
    if (typeof value === 'undefined') continue
    searchParams.set(key, (value ?? 'null').toString())
  }
  return searchParams.toString()
}
