'use client'

type Serializable =
  | string
  | number
  | boolean
  | null
  | Array<Serializable>
  | { [k: string | number]: Serializable }

/**
 * Retrieves and stores serializable values in sessionStorage.
 *
 * @param key - The key under which the value is stored in sessionStorage.
 * @returns - A tuple containing:
 * - A function to retrieve the value from sessionStorage. Once this function is returned the value will automatically be removed from sessionStorage.
 * - A function to store the value in sessionStorage.
 *
 * If session storage is not available, the function will return [undefined, undefined]
 */
export function sessionStorageForKey<Value extends Serializable = Serializable>(
  key: string,
): [
  (() => Value | undefined) | undefined,
  ((value: Value) => void) | undefined,
] {
  if (!globalThis.sessionStorage) return [undefined, undefined]

  return [
    () => {
      try {
        const storedValue: Value | undefined =
          JSON.parse(globalThis.sessionStorage.getItem(key) ?? 'null') ??
          undefined
        return storedValue
      } catch (_) {
        return undefined
      } finally {
        globalThis.sessionStorage.removeItem(key)
      }
    },
    (value: Value) => {
      globalThis.sessionStorage.setItem(key, JSON.stringify(value))
    },
  ]
}
