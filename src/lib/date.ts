import { format, formatISO, parseISO, set, endOfMonth } from 'date-fns'

export function toUIFormat(
  date: Date | string,
  formatStr: 'yyyy-MM-dd' | 'yyyy-MM' = 'yyyy-MM-dd',
) {
  return format(
    date instanceof Date ? date : parseServerFormat(date),
    formatStr,
  )
}

export function startOfTheMonth() {
  return set(new Date(), {
    hours: 0,
    seconds: 0,
    minutes: 0,
    milliseconds: 0,
    date: 1,
  })
}

export function toServerFormat(date: Date) {
  return formatISO(date, { representation: 'date' })
}

export function parseServerFormat(date: string) {
  return parseISO(date)
}

function toUTCString(date: Date | string): string {
  const toFormat = date instanceof Date ? date : parseServerFormat(date)
  // if we don't convert to UTC here, we will get unintended discrepancies across time zones
  const inUTC = Date.UTC(
    toFormat.getUTCFullYear(),
    toFormat.getUTCMonth(),
    toFormat.getUTCDate(),
  )
  return new Date(inUTC).toISOString()
}

export function toUTCYearMonth(date: Date | string): string {
  return toUTCString(date).substring(0, 7)
}

export const formatToMonthYearFromDate = toUTCYearMonth

export function toUTCDateString(date: Date | string) {
  return toUTCString(date).substring(0, 10)
}

export function formatToMonthYearFromInts(
  monthValue: number,
  yearValue: number,
) {
  const toFormat = new Date(yearValue, monthValue - 1)
  return format(toFormat, 'yyyy-MM')
}

export function parseMonthPickerFormat(date: string) {
  const [year, month] = date.split('-').map(Number)
  return new Date(year, month - 1, 1)
}

export function formatToServerString(monthYearString: string) {
  return monthYearString.length === 7 ?
      `${monthYearString}-01`
    : monthYearString
}

export function getLastDayOfMonth(date: Date | string) {
  return endOfMonth(date instanceof Date ? date : parseServerFormat(date))
}

export function getLastDayOfMonthString(date: Date | string) {
  return format(getLastDayOfMonth(date), 'yyyy-MM-dd')
}

export function firstDayOfMonth(date: Date) {
  return new Date(date.getFullYear(), date.getMonth(), 1)
}

export function monthNameToShort(monthName: string) {
  if (monthName.toLowerCase().startsWith('sept')) {
    return monthName.substring(0, 4)
  }
  return monthName.length > 3 ? monthName.substring(0, 3) : monthName
}
