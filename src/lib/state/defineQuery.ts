import { QueryKey, queryOptions, UseQueryOptions } from '@tanstack/react-query'
import {
  Body,
  OpenApiClient,
  ErrResponse,
  OkResponse,
  throwClientError,
} from '../openapi/client'
import { HttpMethod, OpenApiSchema } from '../openapi/types'
import { compose, UnaryFunction } from '../functional/compose'
import { Union, variant } from '../unions/Union'
import { Middleware } from '../middleware/withMiddleware'
import {
  CombinedParameters,
  CombinedReturnType,
  combineWithMiddleware,
  destructureCombined,
} from '../unions/combine'
import { HttpUnauthorizedError } from '../http/fetchErrorMiddleware'
import { ReplaceNever } from '../types/replaceNever'

/**
 * Design goals for `defineQuery`:
 *
 * 1. Build on `createClient` to integrate generated OpenAPI types with TanStack
 * Query -- provide an opinionated pattern for `useQuery` options that only
 * requires a definition for queryKey.
 *
 * 2. Everything about a query should be defined in one place -- no need to define
 * the API endpoint string in one place, the fetch function in another place, the
 * queryKeys somewhere else. It should all go in one place, with little boilerplate
 * or repetition.
 *
 * 3. Allow queries to share behavior via composition. We were already doing
 * ad-hoc composition, but that gets hard to manage as we add behaviors.
 */

type QueryOptions<
  Operation extends OpenApiSchema[string],
  Method extends HttpMethod,
> = UseQueryOptions<
  ReplaceNever<OkResponse<Operation[Method]>, void>,
  ErrResponse<Operation[Method]> | Error,
  ReplaceNever<OkResponse<Operation[Method]>, void>
>

export type Query<
  Operation extends OpenApiSchema[string],
  Method extends HttpMethod,
> = UnaryFunction<
  ReplaceNever<Body<Operation[Method]>, void>,
  QueryOptions<Operation, Method>
>

// SAFETY: This type should only be used as a type constraint. The `any` types
// are necessary because they are used in contravariant positions.
export type AnyQuery = Query<Record<HttpMethod, any>, HttpMethod>

export type DefineQueryOptions<
  Operation extends OpenApiSchema[string],
  Method extends HttpMethod,
> = Omit<QueryOptions<Operation, Method>, 'queryFn' | 'queryKey'>

/**
 * Define a re-usable, strongly-typed query.
 *
 * A query represents read-only access to a piece of server state.
 *
 * Once a query is defined, it can be passed to `defineQueries` along with other
 * defined queries that may share common behavior.
 *
 * > _Note: we currently hardcode the POST http method, and we expect all our
 * > APIs to be POST endpoints -- we could easily accept a `method` parameter,
 * > but we've omitted it for convenience.
 *
 * @example
 * ```ts
 * const client = createClient<paths>()
 *
 * const searchQuery = defineQuery(
 *   client
 *   '/search',
 *   'post',
 *   (searchTerm) => [ searchTerm ],
 * )
 *
 * // The defined query can be used directly...
 *
 * const query = data(searchQuery)
 * const queryResult = useQuery(query('some search term'))
 *
 * // Or, more commonly, combined with other queries:
 *
 * const query = defineQueries([
 *   searchQuery,
 *   // more queries can be added here.
 * ])
 *
 * const queryResult = useQuery(query('/search', 'some search term'))
 *
 * ```
 *
 * @param client An `OpenApiClient` capable of making typed fetch requests.
 * @param path A string indicating the specific API path for this query.
 * @param key An optional function taking the request as an argument and returning a `QueryKey`.
 * Will be appended to `path` to form the fully-qualified query key.
 * @param options Optionally provide additional `useQuery` options.
 * @returns A `Union<Path, Query>` which can be passed to `defineQueries`.
 */
export function defineQuery<
  Paths extends OpenApiSchema,
  Path extends keyof Paths & string,
>(
  client: OpenApiClient<Paths>,
  path: Path,
  key:
    | ((request: ReplaceNever<Body<Paths[Path]['post']>, void>) => QueryKey)
    | QueryKey = [],
  options: DefineQueryOptions<Paths[Path], 'post'> = {},
): Union<Path, Query<Paths[Path], 'post'>> {
  return variant(path, (request) => {
    const queryKey: QueryKey =
      typeof key === 'function' ? [path, ...key(request)] : [path, ...key]

    const defaultMaxRetry = 3 // this is TanStack Query's default value.
    const retry = options?.retry

    return queryOptions({
      ...options,
      retry: (failureCount, error) => {
        if (error instanceof HttpUnauthorizedError) return false
        if (typeof retry === 'undefined') return failureCount < defaultMaxRetry
        if (typeof retry === 'function') return retry(failureCount, error)
        if (typeof retry === 'number') return failureCount < retry
        return retry
      },
      queryKey,
      queryFn: () => compose(client(path, 'post'), throwClientError)(request),
    })
  })
}

/**
 * Group defined queries together so they can share common functionality.
 *
 * This common functionality is expressed as "middleware" that may be composed
 * and added to the queries to modify their behavior.
 *
 * > _Note: this middleware __is not__ NextJS middleware -- NextJS middleware runs
 * > on the server and is totally unrelated to queries._
 *
 * @example
 * ```ts
 * const catalogQuery = definePostQuery(
 *   client,
 *   '/getCatalog',
 * )
 *
 * const serviceQuery = defineQuery(
 *   client,
 *   '/getService',
 *   'post',
 *   (serviceCode) => [ serviceCode ],
 * )
 *
 * const query = defineQueries(
 *   [
 *     catalogQuery,
 *     serviceQuery,
 *   ],
 *   // This simple middleware matches on the request union type, and modifies
 *   // all serviceCodes passed to the `getService` API.
 *   (next) => (request) => {
 *     return next(isoMap(request, {
 *       '/getService': (serviceCode) => serviceCode.toUpperCase(),
 *       _: (request) => request,
 *     }))
 *   },
 * )
 * ```
 *
 * @param queries
 * @param middleware
 * @returns
 */
export const defineQueries = <const U extends Union<string, AnyQuery>[]>(
  queries: U,
  middleware?: Middleware<CombinedParameters<U>, CombinedReturnType<U>>,
) => destructureCombined(combineWithMiddleware(queries, middleware))
