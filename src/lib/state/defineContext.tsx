import React, {
  Context,
  createContext,
  Dispatch,
  useContext,
  useReducer,
} from 'react'
import { match, Matcher } from '../unions/match'
import { data, Data, tag, Tag, Union, variant } from '../unions/Union'
import { IfOptional } from '../types/ifOptional'

type ContextMutation<M extends readonly ((data: any) => Union)[]> = ReturnType<
  M[number]
>

/**
 * Define local state using a React Context.
 *
 * > _Note: never put server state into a Context -- server state should always
 * > be managed using `defineQuery` and `defineMutation`. `defineContext` is only
 * > for state that never leaves the browser.
 *
 * @param name The name of the Context, for debugging and error messaging.
 * @param initialState Initial value of the Context state.
 * @param _mutations A tuple of supported mutations that may be performed on the
 * state -- this argument is only used for type inference.
 * @param reducer A `Matcher` that can handle each possible state mutation.
 * @returns
 */
export function defineContext<
  State,
  const Mutations extends readonly ((data: any) => Union)[],
>(
  name: string,
  initialState: State,
  _mutations: Mutations,
  reducer: Matcher<
    {
      [T in Tag<ContextMutation<Mutations>>]: Union<
        T,
        [State, Data<ContextMutation<Mutations>, T>]
      >
    }[Tag<ContextMutation<Mutations>>],
    State
  >,
): [
  () => State,
  () => Dispatch<ContextMutation<Mutations>>,
  React.FunctionComponent<React.PropsWithChildren<{ state?: State }>>,
] {
  const Context = createContext<State>(initialState)
  const DispatchContext = createContext<Dispatch<ContextMutation<Mutations>>>(
    () => {},
  )

  return [
    createUseContext(name, Context),
    createUseContext(`${name}Dispatch`, DispatchContext),
    ({ children, state: componentInitialState }) => {
      const [state, dispatch] = useReducer<State, [ContextMutation<Mutations>]>(
        (previousState, action) => {
          const actionTag = tag(action)
          const pair = variant(actionTag, [
            previousState,
            data(action),
          ] as const)

          // SAFETY: In order for the `reducer` argument to be properly typed, we
          // need to be more specific (by using a mapped type over the possible
          // tags of the Mutations) -- but this prevents the type-checker from
          // deciding that `reducer'`s `Matcher<mapped type>` type is assignable
          // to `match`'s type constraint for it's matcher argument. This is safe
          // as long as `typeof pair` is assignable to `Union<T, [State, Data<ContextMutation<Mutations>, T>]>`.
          //
          // The type-checker also has a hard time deciding that `MatcherReturn`
          // of a `Matcher` that always returns a single type `State` is equivalent
          // to `State` -- the return type of `match` here is `State`.
          return match(pair, reducer as Matcher<typeof pair, State>) as State
        },
        componentInitialState ?? initialState,
      )
      return (
        <Context.Provider value={state}>
          <DispatchContext.Provider value={dispatch}>
            {children}
          </DispatchContext.Provider>
        </Context.Provider>
      )
    },
  ]
}

export const defineContextMutation =
  <T extends string>(tag: T) =>
  <A = void,>(...[data]: IfOptional<A> extends true ? [data?: A] : [data: A]) =>
    variant<T, A>(tag, data as A)

/**
 * Helper function to create custom `useContext` helpers with better error reporting.
 */
function createUseContext<S>(
  contextName: string,
  Context: Context<S>,
): () => S {
  return function useDefinedContext(): S {
    const context = useContext(Context)
    if (typeof context === 'undefined') {
      throw new Error(
        `Cannot use ${contextName} context because the calling Component is not ` +
          `within a ${contextName} Provider.`,
      )
    }
    return context
  }
}
