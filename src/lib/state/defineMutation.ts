import {
  InvalidateQueryFilters,
  UseMutationOptions,
  useQueryClient,
} from '@tanstack/react-query'
import { compose, UnaryFunction } from '../functional/compose'
import {
  Body,
  OpenApiClient,
  OkResponse,
  throwClientError,
} from '../openapi/client'
import { HttpMethod, OpenApiSchema } from '../openapi/types'
import { Union, variant } from '../unions/Union'
import {
  CombinedParameters,
  CombinedReturnType,
  combineWithMiddleware,
  destructureCombined,
} from '../unions/combine'
import { Middleware, withMiddleware } from '../middleware/withMiddleware'
import { HttpUnauthorizedError } from '../http/fetchErrorMiddleware'
import { ReplaceNever } from '../types/replaceNever'

type MutationOptions<Request = void, Response = unknown> = UseMutationOptions<
  Response,
  Error,
  Request
>

export type Mutation<Request = void, Response = unknown> = UnaryFunction<
  Omit<MutationOptions<Request, Response>, 'mutationFn'>,
  MutationOptions<Request, Response>
>

// SAFETY: This type should only be used as a type constraint. The `any` types
// are necessary because they are used in contravariant positions.
export type AnyMutation = Mutation<any, any>

type DefineMutationOptions<
  Operation extends OpenApiSchema[string],
  Method extends HttpMethod = 'post',
  Request = Body<Operation[Method]>,
  Response = OkResponse<Operation[Method]>,
> = Omit<MutationOptions<Request, Response>, 'mutationFn'> & {
  middleware?: Middleware<
    Request,
    Promise<Response>,
    ReplaceNever<Body<Operation[Method]>, void>,
    Promise<ReplaceNever<OkResponse<Operation[Method]>, void>>
  >
  invalidate?:
    | InvalidateQueryFilters
    | InvalidateQueryFilters[]
    | (() => InvalidateQueryFilters | InvalidateQueryFilters[] | undefined)
    | ((
        request: Request,
        response?: Response,
        error?: Error,
      ) => InvalidateQueryFilters | InvalidateQueryFilters[] | undefined)
}

/**
 * Define a re-usable, strongly-typed mutation.
 *
 * A mutation modifies a piece of server state.
 *
 * Once a mutation is defined, it can be passed to `defineMutations` along with
 * other defined mutations that may share common behavior.
 *
 * > _Note: we currently hardcode the POST http method, and we expect all our
 * > APIs to be POST endpoints -- we could easily accept a `method` parameter,
 * > but we've omitted it for convenience.
 *
 * @example
 * ```ts
 * const client = createClient<paths>()
 *
 * const createCategory = defineMutation(
 *   client,
 *   '/createCategory',
 *   'post',
 *   {
 *     invalidate: query('/listCategories'),
 *   },
 * )
 *
 * // The defined mutation can be used directly...
 *
 * const mutation = data(createCategory)
 * const { mutateAsync } = useMutation(mutation())
 * const response = await mutateAsync()
 *
 * // Or, more commonly, combined with other mutations:
 *
 * const mutation = defineMutations([
 *   createCategory,
 *   // more mutations can be added here.
 * ])
 *
 * const { mutateAsync } = useMutation(mutation('/api/createCategory'))
 * const response = await mutateAsync(category)
 * ```
 *
 * @param client An `OpenApiClient` capable of making typed fetch requests.
 * @param path String indicating the specific API path for this mutation.
 * @param options May contain any `useMutation` options, except for `mutationFn`.
 * @param options.middleware Optionally wrap the API call with middleware -- useful
 * for transforming request/response types from one type to another.
 * @param options.invalidate InvalidateQueryFilters, or a function which may
 * return InvalidateQueryFilters -- these are passed to `queryClient.invalidateQueries`
 * to invalidate any queries.
 * @returns A `Union<Uri, Mutation>` which can be passed to `defineMutations`.
 */
export function defineMutation<
  Paths extends OpenApiSchema,
  Path extends keyof Paths & string,
  Request = Body<Paths[Path]['post']>,
  Response = OkResponse<Paths[Path]['post']>,
>(
  client: OpenApiClient<Paths>,
  path: Path,
  {
    middleware,
    invalidate,
    ...defaultOptions
  }: DefineMutationOptions<Paths[Path], 'post', Request, Response> = {},
): Union<Path, Mutation<Request, Response>> {
  const useMutationOptions: Mutation<Request, Response> = (options) => {
    const queryClient = useQueryClient()
    const onSettled = options?.onSettled ?? defaultOptions.onSettled

    const api = compose(client(path, 'post'), throwClientError)
    const mutationFn =
      middleware ?
        withMiddleware(api, middleware)
      : (api as UnaryFunction<Request, Promise<Response>>)

    const defaultMaxRetry = 3 // this is TanStack Query's default value.
    const retry = options?.retry ?? defaultOptions?.retry

    return {
      ...defaultOptions,
      ...options,
      retry: (failureCount, error) => {
        if (error instanceof HttpUnauthorizedError) return false
        if (typeof retry === 'undefined') return failureCount < defaultMaxRetry
        if (typeof retry === 'function') return retry(failureCount, error)
        if (typeof retry === 'number') return failureCount < retry
        return retry
      },
      mutationFn: compose(mutationFn),
      onSettled: (response, error, request, context) => {
        if (invalidate) {
          const filters =
            typeof invalidate === 'function' ?
              invalidate(request, response, error || undefined)
            : invalidate

          if (filters) {
            const allFilters = Array.isArray(filters) ? filters : [filters]
            for (const filters of allFilters) {
              queryClient.invalidateQueries(filters)
            }
          }
        }
        return onSettled?.(response, error, request, context)
      },
    }
  }
  return variant(path, useMutationOptions)
}

/**
 * Group defined mutations together so they can share common functionality.
 *
 * This common functionality is expressed as "middleware" that may be composed
 * and added to the mutations to modify their behavior.
 *
 * > _Note: this middleware __is not__ NextJS middleware -- NextJS middleware runs
 * > on the server and is totally unrelated to queries._
 *
 * @example
 * ```ts
 * const createCategory = defineMutation(
 *   client,
 *   '/createCategory',
 *   { invalidate: query('/api/getCatalog') },
 * )
 *
 * const updateCategory = defineQuery(
 *   '/api/updateCategory',
 *   simpleMutationFn<Category, UpdateCategoryResponse>,
 *   {
 *     invalidate: query('/api/getCatalog'),
 *   },
 * )
 *
 * const useDefinedMutation = defineMutations(
 *   [
 *     createCategory,
 *     updateCategory,
 *   ],
 *   // This simple middleware modifies the options returned by the defined
 *   // queries, adding retries.
 *   (next) => (queryClient) => {
 *     const options = next(queryClient)
 *     return matchIdentity(options, {
 *       _: override<Data<typeof options>>({
 *         retry: () => 3,
 *       })
 *     })
 *   },
 * )
 * ```
 *
 * @param mutations
 * @param middleware
 * @returns
 */
export const defineMutations = <const U extends Union<string, AnyMutation>[]>(
  queries: U,
  middleware?: Middleware<CombinedParameters<U>, CombinedReturnType<U>>,
) => destructureCombined(combineWithMiddleware(queries, middleware))
