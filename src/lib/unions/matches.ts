import { Variant, Tag, Union, tag as getTag } from './Union'

/**
 * Matches a single member of a Union.
 *
 * @example
 * ```ts
 * type Ok<T> = Union<'Ok', T>
 * type Err<E> = Union<'Err', E>
 * type Result<T, E> = Ok<T> | Err<E>
 *
 * function doSomethingWithResult(result: Result<string, Error>) {
 *   if (matches(result, 'Ok')) {
 *     const data: string = variant(result)
 *   } else {
 *     console.error(variant(result))
 *   }
 * }
 * ```
 *
 * @param union A member of a Union
 * @param tag A string literal, naming one member of the union.
 * @returns Narrows the type of `union` to the specific member specified by `T`.
 */
export function matches<U extends Union, T extends Tag<U>>(
  union: U | undefined,
  tag: T,
): union is Variant<U, T> {
  return typeof union !== 'undefined' && tag === getTag(union)
}
