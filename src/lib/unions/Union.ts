// It's important that we do not export these symbols -- this effectively makes
// the Union's [TAG] and [DATA] fields private.
const TAG = Symbol('TAG')
const DATA = Symbol('DATA')

/**
 * A tagged union (known in other languages as a discriminated union, enum, or sum
 * type) -- a general-purpose tool for writing type-safe code.
 *
 * A tagged union consists of two parts:
 *
 * - the "tag", which is a string literal uniquely identifying the union member.
 * - the "data", which can be any type.
 *
 * It's useful to define a standard property to use as the tag, and a standard
 * property to contain the data. That is what this type does.
 *
 * Tagged unions become interesting when you define, well, a union of them. Like
 * so:
 *
 * ```ts
 * type CatalogParams = { categoryCode: string }
 * type ServiceParams = { parentCategoryCode: string }
 *
 * type Route = Union<'Catalog', CatalogParams>
 *            | Union<'Service', ServiceParams>
 * ```
 *
 * We refer to each type within the union as a "variant." In this case we have
 * two variants -- one has the tag `'Catalog'` with the data type `CatalogParams`,
 * the other has the tag type `'Service'` with data type `ServiceParams`.
 *
 * Now we can benefit from this standardized way of defining tagged unions. We
 * can use various helper types to do things like this:
 *
 * ```ts
 * // The `Tag` and `Data` helper types are documented elsewhere.
 * function routeTo<T extends Tag<Route>>(tag: T, params: Data<Route, T>): string {
 *   ...
 * }
 *
 * // This is strongly-typed! The first argument only accepts the strings 'Catalog'
 * // or 'Service' -- and the second argument's type depends on which string is provided.
 * const href = routeTo('Catalog', { categoryCode })
 * const href = routeTo('Service', { parentCategoryCode })
 * ```
 *
 * There's much more that can be done with Union -- feel free to explore
 * additional reading on using tagged unions in TypeScript, in other languages
 * (a lot of this is inspired by Rust enums and pattern matching), or in general.
 */
export type Union<T extends string = string, V = unknown> = {
  [TAG]: T
  [DATA]: V
}

/**
 * Given a Union `U`, extract those variants with tags assignable to `T`.
 */
export type Variant<U extends Union, T extends Tag<U> = Tag<U>> = Extract<
  U,
  { [TAG]: T }
>

/**
 * Given a Union `U`, exclude all variants with tags assignable to `T`.
 */
export type ExcludeVariant<U extends Union, T extends Tag<U>> = Exclude<
  U,
  { [TAG]: T }
>

/**
 * Create a Union variant.
 *
 * @param tag A string to uniquely identify this member of a Union
 * @param data Any type containing data to be associated with the given tag.
 * @returns A Union<T, V> object.
 */
export function variant<T extends string>(tag: T): Union<T, void>
export function variant<T extends string, V>(tag: T, data: V): Union<T, V>
export function variant(tag: string, data?: unknown): Union {
  return { [TAG]: tag, [DATA]: data }
}

/**
 * Given a Union `U`, get the type of its tag.
 *
 * This will almost always be a string literal, or union of string literals.
 */
export type Tag<U extends Union> = U[typeof TAG]

/**
 * Return the union member's tag.
 *
 * @param union A member of a Union
 * @returns The value of the union member's tag.
 */
export function tag<U extends Union>(union: U): Tag<U> {
  return union[TAG]
}

/**
 * Given a Union `U`, extract those types with tags assignable to `T` and
 * get the type of their data.
 *
 * @example
 * ```ts
 * type Union = Union<'a', string> | Union<'b', number>
 * type UnionData = Data<Union> // has type `string | number`
 * type AData = Data<Union, 'a'> // has type `string`
 * ```
 */
export type Data<U extends Union, T extends Tag<U> = Tag<U>> = Extract<
  U,
  { [TAG]: T }
>[typeof DATA]

/**
 * Return the union member's data.
 *
 * @param union A variant of a Union
 * @returns The data contained in the `union`.
 */
export function data<U extends Union>(union: U): Data<U> {
  return union[DATA]
}

/**
 * Product a union of `Union` types from a union of `string` types.
 *
 * @example
 * ```ts
 * // Produces: `Union<'foo', unknown> | Union<'bar', unknown>`
 * type MyUnion = IntoUnion<'foo' | 'bar'>
 * ```
 */
export type IntoUnion<T extends string, V = unknown> = {
  [K in T]: Union<K, V>
}[T]

/**
 * Sometimes it may be useful for a class to also be a Union.
 *
 * This is a [class mixin](https://www.typescriptlang.org/docs/handbook/mixins.html)
 * that implements the Union interface on top of a given class.
 *
 * The new class that is produced is abstract -- it must be extended and given
 * an implementation of the `data` getter, as the mixin has no way to know
 * where the superclass will store its data.
 *
 * @example
 * Let's say we wish to define a custom Error class, and we want to be able to
 * use `Union` features like `match` with it.
 *
 * If we were just defining our custom Error in terms of the `Union` type, it
 * would look like this:
 * ```ts
 * type MyError = Union<'MyError', string>
 * ```
 * We're using `string` for the data type because `MyError` will carry an error
 * message.
 *
 * But, of course, just using the `Union` type doesn't give us any of the `Error`
 * behavior we want.
 *
 * So we can use the mixin:
 * ```ts
 * class MyError extends UnionMixin('MyError', Error)<string>() {
 *   readonly name = 'MyError'
 *   get data(): string { return this.message }
 * }
 * ```
 * When we write `UnionMixin('MyError', Error)<string>()` we are saying: "mix
 * the `Union<'MyError', string>` type into the `Error` class."
 *
 * We then have to extend the mixin class to provide an implementation for the
 * `data` getter. And we're done!
 *
 * @param tag A string to uniquely identify this member of a Union.
 * @param Superclass The class to which we wish to add the Union interface.
 * @returns
 */
export function UnionMixin<
  T extends string,
  S extends new (...args: any[]) => {},
>(tag: T, Superclass: S) {
  return <D>() => {
    abstract class Union extends Superclass {
      [TAG]: T = tag;
      // SAFETY: we never give out a reference to the DATA symbol, so nobody
      // can ever access this property -- it's here only to inform the type-checker
      // of this Union's data type.
      [DATA]!: D
      get tag(): T {
        return this[TAG]
      }
      abstract get data(): D
    }
    return Union
  }
}
