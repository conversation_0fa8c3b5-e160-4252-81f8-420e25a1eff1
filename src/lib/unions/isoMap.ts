import { match } from './match'
import {
  Data,
  ExcludeVariant,
  tag,
  Tag,
  Union,
  variant,
  Variant,
} from './Union'

type IsoMatcher<U extends Union> = {
  [T in Tag<U>]: (data: Data<U, T>, variant: Variant<U, T>) => typeof data
}

type PartialIsoMatcher<
  U extends Union,
  M extends Partial<IsoMatcher<U>> = {},
> = M & {
  _: (
    data: Data<ExcludeVariant<U, Extract<Tag<U>, keyof M>>>,
    variant: Variant<ExcludeVariant<U, Extract<Tag<U>, keyof M>>>,
  ) => typeof data
}

/**
 * Isomorphic map between two Union variants.
 *
 * Exactly like `map` except each method of the matcher object must return
 * a value of the ___same type___ as its argument.
 *
 * This results in a return value of the same type as the input `union`.
 *
 * @param union
 * @param matcher
 * @returns
 */
export function isoMap<
  U extends Union,
  M extends IsoMatcher<U> | PartialIsoMatcher<U>,
>(union: U, matcher: M): U {
  const matchedTag = tag(union)
  const data = match(union, matcher)

  // SAFETY: We require that `matcher` must return the same type as it receives,
  // so for every union variant the matcher will return a value of the same type
  // as that variant's data -- so it is safe to cast the resulting union to `U`.
  return variant(matchedTag, data) as U
}
