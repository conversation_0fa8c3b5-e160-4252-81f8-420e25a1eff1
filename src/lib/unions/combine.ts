import { UnaryFunction } from '../functional/compose'
import { Middleware, withMiddleware } from '../middleware/withMiddleware'
import { IfOptional } from '../types/ifOptional'
import { split } from './split'
import { tag, Tag, Union, data, Data, Variant, variant } from './Union'

export type FunctionUnion<
  T extends string = string,
  A = any,
  R = unknown,
> = Union<T, UnaryFunction<A, R>>

export type Combined<U extends readonly FunctionUnion[]> = <
  T extends Tag<U[number]>,
>(
  argument: CombinedParameters<U, T>,
) => CombinedReturnType<U, T>

export type CombinedParameters<
  U extends readonly FunctionUnion[],
  T extends Tag<U[number]> = Tag<U[number]>,
> = Variant<
  {
    [K in keyof U]: {
      [T in Tag<U[K]>]: Union<T, Parameters<Data<U[K], T>>[0]>
    }[Tag<U[K]>]
  }[number],
  T
>

export type CombinedReturnType<
  U extends readonly FunctionUnion[],
  T extends Tag<U[number]> = Tag<U[number]>,
> = Variant<
  {
    [K in keyof U]: {
      [T in Tag<U[K]>]: Union<T, ReturnType<Data<U[K], T>>>
    }[Tag<U[K]>]
  }[number],
  T
>

/**
 * Combine a union of functions into a function that takes a union argument.
 *
 * @example
 * ```ts
 * const a = makeVariant('a', (s: string) => s.length)
 * const b = makeVariant('b', (n: number) => n*n)
 *
 * const c = combine(a, b)
 *
 * const resultA: string = c(makeVariant('a', 'foo')) // type checks!
 * console.log(resultA) // prints "3"
 *
 * const resultB: number = c(makeVariant('b', 8)) // type checks!
 * console.log(resultB) // prints "64"
 *
 * const badResult: number = c(makeVariant('b', 8)) // type error! string is not assignable to number.
 *
 * ```
 *
 * @param unionOfFns A Union where each member is a UnaryFunction.
 * @returns A single function which takes a Union as an argument.
 */
export function combine<const U extends readonly FunctionUnion[]>(
  ...unionOfFns: U
): Combined<U> {
  return <T extends Tag<U[number]>>(argument: CombinedParameters<U, T>) => {
    const t = tag(argument)
    const fn = unionOfFns.find((fn) => tag(fn) === t)
    // If this happens, the caller is either doing an unsafe cast somewhere, or
    // (less likely) there is a bug in the Combined type that allowed passing in
    // an argument with an unknown Tag.
    if (!fn) {
      throw new Error(
        `Combined function was given an argument with tag ${t}, but ` +
          `none of the function members accepts this argument.`,
      )
    }
    return variant(t, data(fn)(data(argument))) as CombinedReturnType<U, T>
  }
}

/**
 * Combine a union of functions into a function that takes a union argument, and
 * then apply some middleware to that function.
 *
 * The middleware cannot change the input or output type of the combined function.
 *
 * @param union
 * @param middleware
 * @returns
 */
export function combineWithMiddleware<const U extends readonly FunctionUnion[]>(
  union: U,
  middleware: Middleware<CombinedParameters<U>, CombinedReturnType<U>> = (n) =>
    (a) =>
      n(a),
): Combined<U> {
  return withMiddleware(combine(...union), middleware) as Combined<U>
}

/**
 * Combine a union of functions into a function that takes a union argument, apply
 * middleware, and then "split" it back into a union of functions.
 *
 * The return value will have the same type as the input uniob.
 *
 * @param union
 * @param middleware
 * @returns
 */
export function combineAndSplitWithMiddleware<U extends FunctionUnion>(
  union: U,
  middleware: Middleware<CombinedParameters<[U]>, CombinedReturnType<[U]>> = (
      n,
    ) =>
    (a) =>
      n(a),
): U {
  return split(tag(union), withMiddleware(combine(union), middleware)) as U
}

/**
 * Convert a function created by `combine` -- which takes a `Union` and returns
 * a `Union` -- into a function that takes two arguments, `Tag` and `Data`, and
 * returns the `Data` of contained in the original `Union`.
 *
 * This is a convenience wrapper to make `Combined` functions easier to call.
 *
 * @param combined
 */
export function destructureCombined<const U extends readonly FunctionUnion[]>(
  combined: Combined<U>,
): <const T extends Tag<CombinedParameters<U>>>(
  // This looks complicated, but it's just here so that if the `data` type contains
  // `void` or `undefined`, callers may omit the second argument.
  ...[tag, data]: IfOptional<Data<CombinedParameters<U>, T>> extends true ?
    [tag: T, data?: Data<CombinedParameters<U>, T>]
  : [tag: T, data: Data<CombinedParameters<U>, T>]
) => Data<CombinedReturnType<U>, T> {
  return <const T extends Tag<CombinedParameters<U>>>(
    tag: T,
    argument?: Data<CombinedParameters<U>, T>,
  ): Data<CombinedReturnType<U>, T> => data(combined(variant(tag, argument)))
}
