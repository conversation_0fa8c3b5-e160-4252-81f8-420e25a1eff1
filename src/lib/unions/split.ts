import { FunctionUnion } from './combine'
import { data, Data, Tag, Union, variant } from './Union'

type Split<T extends string, F extends (union: Union<T, any>) => Union<T>> = {
  [T in Tag<Parameters<F>[0]>]: FunctionUnion<
    T,
    Data<Parameters<F>[0], T>,
    Data<ReturnType<F>, T>
  >
}[Tag<Parameters<F>[0]>]

/**
 * Given a function which takes a Union argument, return a `Union` variant containing
 * the function.
 *
 * The function contained in the `Union` will no longer take a `Union` argument,
 * but instead simply the corresponding `Data`.
 *
 * This can be seen as the opposite of the `combine` function. The following
 * identity holds:
 *
 * ```ts
 * let union = variant('a', (a: string) => a)
 * union = split('a', combine(union))
 * ```
 *
 * @param tag
 * @param fnOfUnions
 * @returns
 */
export function split<
  T extends string,
  F extends (union: Union<T, any>) => Union<T>,
>(tag: T, fnOfUnions: F): Split<T, F> {
  return variant(tag, (argument) => {
    return data(fnOfUnions(variant(tag, argument)))
  })
}
