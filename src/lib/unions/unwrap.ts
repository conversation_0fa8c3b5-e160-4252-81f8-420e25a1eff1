import { Tag, Union, Data, tag as getTag, data } from './Union'

/**
 * Unwrap a specific variant of a Union, returning its data.
 *
 * If the given union variant is not the desired variant, as specified by the given
 * tag, this returns undefined.
 *
 * @example
 * ```ts
 * type Ok<T> = Union<'Ok', T>
 * type Err<E> = Union<'Err', E>
 * type Result<T, E> = Ok<T> | Err<E>
 *
 * function doSomethingWithResult(result: Result<string, Error>) {
 *   const ok: string | undefined = unwrap(result, 'Ok')
 * }
 * ```
 *
 * @param union A variant of a Union (or undefined, for convenience)
 * @param tag A string literal, naming one variant of the union.
 * @returns If `union` is the variant named by `tag`, returns the `union`'s data,
 * otherwise returns undefined.
 */
export function unwrap<U extends Union, T extends Tag<U>>(
  union: U | undefined,
  tag: T,
): Data<U, T> | undefined {
  if (typeof union === 'undefined' || tag !== getTag(union)) return undefined
  return data(union)
}

/**
 * Unwrap a specific variant of a Union, returning its data.
 *
 * If the given union variant is not the desired variant, as specified by the given
 * tag, the provided default value will be returned.
 *
 * @example
 * ```ts
 * type Ok<T> = Union<'Ok', T>
 * type Err<E> = Union<'Err', E>
 * type Result<T, E> = Ok<T> | Err<E>
 *
 * function doSomethingWithResult(result: Result<string, Error>) {
 *   const ok: string = unwrapOrElse(result, 'Ok', 'Not Ok')
 * }
 * ```
 *
 * @param union A variant of a Union (or undefined, for convenience)
 * @param tag A string literal, naming one variant of the union.
 * @param or A default value to be returned if `union` is not the desired variant.
 * @returns If `union` is the variant named by `tag`, returns the `union`'s variant,
 * otherwise returns `orElse`.
 */
export function unwrapOr<U extends Union, T extends Tag<U>>(
  union: U | undefined,
  tag: T,
  or: Data<U, T>,
): Data<U, T> {
  if (typeof union === 'undefined' || tag !== getTag(union)) return or
  return data(union)
}
