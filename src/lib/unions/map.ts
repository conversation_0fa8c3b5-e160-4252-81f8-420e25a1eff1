import { ExcludeVariant, tag, Tag, Union, variant } from './Union'
import { match, Matcher, PartialMatcher } from './match'

type MappedMatcherReturn<
  U extends Union,
  M extends Matcher<U> | PartialMatcher<U>,
> =
  | {
      [K in Tag<U>]: K extends keyof M ?
        M[K] extends (...args: any[]) => infer R ?
          Union<K, R>
        : never
      : never
    }[Tag<U>]
  | {
      [K in Tag<ExcludeVariant<U, Extract<Tag<U>, keyof M>>>]: M['_'] extends (
        (...args: any[]) => infer R
      ) ?
        Union<K, R>
      : never
    }[Tag<ExcludeVariant<U, Extract<Tag<U>, keyof M>>>]

/**
 * Map between two Union variants.
 *
 * Exactly like `match`, except the return value is still a Union.
 *
 * @param union
 * @param matcher
 * @returns
 */
export function map<U extends Union, M extends Matcher<U> | PartialMatcher<U>>(
  union: U,
  matcher: M,
): MappedMatcherReturn<U, M> {
  const matchedTag = tag(union)
  const data = match(union, matcher)
  return variant(matchedTag, data) as MappedMatcherReturn<U, M>
}
