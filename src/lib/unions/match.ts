import { Variant, tag, Tag, Union, data, Data, ExcludeVariant } from './Union'

export type Matcher<U extends Union, D = unknown> = {
  [T in Tag<U>]: (value: Data<U, T>, variant: Variant<U, T>) => D
}

export type PartialMatcher<
  U extends Union,
  M extends Partial<Matcher<U>> = {},
> = M & {
  _: (
    data: Data<ExcludeVariant<U, Extract<Tag<U>, keyof M>>>,
    variant: Variant<ExcludeVariant<U, Extract<Tag<U>, keyof M>>>,
  ) => unknown
}

type MatcherReturn<U extends Union, M extends Matcher<U> | PartialMatcher<U>> =
  M[keyof M] extends (...args: any[]) => infer R ? R : never

/**
 * Match against the variants of a Union.
 *
 * All variants must be handled, either individually or by using a catch-all
 * handler.
 *
 * @example
 * ```ts
 * type Ok<T> = Union<'Ok', T>
 * type Err<E> = Union<'Err', E>
 * type Result<T, E> = Ok<T> | Err<E>
 *
 * function doSomethingWithResult(result: Result<string, Error>): number {
 *   return match(result, {
 *     Ok: (value) => value.length,
 *     Err: (error) => {
 *       console.error(error)
 *       return 0
 *     },
 *   })
 * }
 *
 * type MyUnion = Union<'A', string> | Union<'B', number> | Union<'C', Date>
 *
 * function doSomethingWithMyUnion(union: MyUnion): string {
 *   return match(union, {
 *     A: (str) => str.toUpperCase(),
 *     _: (value) => value.toString().toUpperCase(),
 *   })
 * }
 * ```
 *
 * @param union Union, each of its members must be handled by the matcher.
 * @param matcher An object with a function property for each variant in the union.
 * @returns The return value of whichever matcher function was executed
 */
export function match<
  U extends Union,
  M extends Matcher<U> | PartialMatcher<U>,
>(union: U, matcher: M): MatcherReturn<U, M> {
  const matchedTag = tag(union)
  const matchFn =
    matcher[matchedTag as keyof M] ?? ('_' in matcher && matcher['_'])

  // This should never happen -- if it does, either the caller is doing some
  // unsafe casting or (less likely) there's a bug in the Matcher or PartialMatcher
  // types that allowed the caller to omit a variant.
  if (!matchFn) {
    throw new Error(
      `Union variant ${matchedTag} cannot be handled by \`match\`. ` +
        `The provided matcher only matches variants ${Object.keys(matcher).join(', ')}.`,
    )
  }

  // SAFETY: We're calling a function which is a property of `M`, so it must be
  // assignable to MatcherReturn<U, M>. And we need this return type so that
  // we get the specific type returned by `M` rather than just the `unknown` we
  // would get if we just let tsc assume `M` = `Matcher`.
  return (matchFn as Matcher<U>[Tag<U>])(
    data(union),
    union as Variant<U, Tag<U>>,
  ) as MatcherReturn<U, M>
}
