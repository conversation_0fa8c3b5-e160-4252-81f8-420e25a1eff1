import { UnionMixin } from './unions/Union'

/**
 * Define a custom Error class. These classes are proper `Error` subclasses, and
 * also implement the `Union` interface -- this allows them to be used in
 * union types to take advantage of helper functions like `match`.
 *
 * @example
 * ```
 * const MyError = defineErrorClass('MyError')
 * const YourError = defineErrorClass('YourError')
 *
 * type OurError = InstanceType<typeof MyError>
 *               | InstanceType<typeof YourError>
 *
 * function handleErrors(error: OurError): void {
 *   match(error, {
 *     'MyError': () => { ... },
 *     'YourError': () => { ... },
 *   })
 * }
 * ```
 *
 * @param name The name of the Error class
 * @returns A constructor which is a subclass of `Error`, and produces instances
 * which are `instanceof Error` and also implement `Union`.
 */
export function defineErrorClass<const Name extends string>(name: Name) {
  return class DefinedError extends UnionMixin(name, Error)<string>() {
    readonly name = name
    get data(): string {
      return this.message
    }
  }
}
