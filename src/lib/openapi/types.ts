export type HttpMethod =
  | 'get'
  | 'put'
  | 'post'
  | 'delete'
  | 'options'
  | 'head'
  | 'patch'
  | 'trace'
export type OkStatus = 200 | 201 | 202 | 203 | 204 | 206 | 207 | '2XX'
export type ErrStatus =
  | 500
  | 501
  | 502
  | 503
  | 504
  | 505
  | 506
  | 507
  | 508
  | 510
  | 511
  | '5XX'
  | 400
  | 401
  | 402
  | 403
  | 404
  | 405
  | 406
  | 407
  | 408
  | 409
  | 410
  | 411
  | 412
  | 413
  | 414
  | 415
  | 416
  | 417
  | 418
  | 420
  | 421
  | 422
  | 423
  | 424
  | 425
  | 426
  | 427
  | 428
  | 429
  | 430
  | 431
  | 444
  | 450
  | 451
  | 497
  | 498
  | 499
  | '4XX'
export type MediaType = 'application/json'
export type OpenApiSchema = Record<string, { [method in HttpMethod]?: {} }>

export type OpenApiAdHocSchema<Request, Response> = {
  [path: string]: {
    post: {
      requestBody: { content: { [M in MediaType]: Request } }
      responses: { 200: { content: { [M in MediaType]: Response } } }
    }
  }
}
