import { Data, Union, variant } from '../unions/Union'
import {
  ErrStatus,
  HttpMethod,
  MediaType,
  OkStatus,
  OpenApiAdHocSchema,
  OpenApiSchema,
} from './types'
import { unsafeSerialize } from '../http/unsafeSerialize'
import { unsafeDeserialize } from '../http/unsafeDeserialize'
import { match } from '../unions/match'
import { fetchImpl } from '../http/fetchImpl'
import { ReplaceNever } from '../types/replaceNever'

export type Body<Operation> =
  Operation extends { requestBody: {} } ? BodyContent<Operation['requestBody']>
  : never

type BodyContent<Body> =
  'content' extends keyof Body ?
    MediaType extends keyof Body['content'] ?
      Body['content'][MediaType]
    : never
  : never

type ResponseWithStatus<Operation, Status> =
  Operation extends { responses: Record<number, {}> } ?
    {
      [K in keyof Operation['responses']]: BodyContent<
        Operation['responses'][K]
      >
    }[keyof Operation['responses'] & Status]
  : never

export type OkResponse<Operation> = ResponseWithStatus<Operation, OkStatus>

export type ErrResponse<Operation> = ResponseWithStatus<Operation, ErrStatus>

export type OpenApiResponse<Operation> =
  | Union<'Ok', ReplaceNever<OkResponse<Operation>, void>>
  | Union<'Err', ReplaceNever<ErrResponse<Operation>, void>>

/**
 * Make strongly-typed fetch requests based on an API schema definition generated
 * by the `openapi-typescript` library.
 */
export type OpenApiClient<Paths extends OpenApiSchema> = <
  Path extends keyof Paths & string,
  Method extends HttpMethod,
>(
  path: Path,
  method: Method,
) => (
  body: ReplaceNever<Body<Paths[Path][Method]>, void>,
) => Promise<OpenApiResponse<Paths[Path][Method]>>

/**
 * Create a strongly-typed fetch client using types generated by the
 * `openapi-typescript` library.
 *
 * Currently we only expect to support POST requests that contain a request body
 * and no other parameters.
 *
 * If we ever need to expand support to include e.g. query, header, or path
 * parameters we'll need quite a bit more logic to conform to the OpenApi spec --
 * the details can be seen in the `openapi-fetch` codebase.
 *
 * @param options
 * @param options.baseUrl A base URL used for all requests.
 * @returns OpenApiClient
 */
export const createClient =
  <Paths extends OpenApiSchema>({
    baseUrl,
  }: {
    baseUrl?: string
  } = {}): OpenApiClient<Paths> =>
  <Path extends keyof Paths & string, Method extends HttpMethod>(
    path: Path,
    method: Method,
  ) =>
  async (body: ReplaceNever<Body<Paths[Path][Method]>, void>) => {
    const serializedBody = body ? unsafeSerialize(body) : undefined

    try {
      const extraHeaders: { [key: string]: string } = {}
      if (
        process.env.NEXT_PUBLIC_QA_BYPASS_HEADER_NAME &&
        process.env.NEXT_PUBLIC_QA_BYPASS_HEADER_VALUE
      ) {
        extraHeaders[process.env.NEXT_PUBLIC_QA_BYPASS_HEADER_NAME] =
          process.env.NEXT_PUBLIC_QA_BYPASS_HEADER_VALUE
      }

      const response = await fetchImpl(
        new Request(`${baseUrl ?? ''}${path}`, {
          body: serializedBody,
          credentials: 'include',
          headers: { 'Content-Type': 'application/json', ...extraHeaders },
          method,
        }),
      )
      return variant(
        'Ok',
        unsafeDeserialize<ReplaceNever<OkResponse<Paths[Path][Method]>, void>>(
          response,
        ),
      )
    } catch (error) {
      return variant(
        'Err',
        unsafeDeserialize<ReplaceNever<ErrResponse<Paths[Path][Method]>, void>>(
          error,
        ),
      )
    }
  }

/**
 * Create an ad-hoc client to support APIs that are not yet defined in the
 * OpenAPI schema.
 *
 * > _Note: This should only be used temporarily until all APIs are defined by
 * > an OpenAPI schema -- at that point this function will be removed and all
 * > uses replaced with `createClient`._
 *
 * @returns
 */
export const createAdHocClient = <Request = void, Response = unknown>() =>
  createClient<OpenApiAdHocSchema<Request, Response>>()

/**
 * The OpenAPI client returns a response type which is a Union of the success and
 * error types.
 *
 * This method will extract the success type and return it, or throw the error
 * type. This can be convenient when interacting with code that expects errors
 * to be returned via a rejected Promise.
 *
 * @param response
 * @returns
 */
export async function throwClientError<
  Paths extends OpenApiSchema,
  Path extends keyof Paths & string,
  Method extends HttpMethod,
>(
  response: Promise<OpenApiResponse<Paths[Path][Method]>>,
): Promise<Data<OpenApiResponse<Paths[Path][Method]>, 'Ok'>> {
  return match(await response, {
    Ok: (value) => value,
    Err: (err) => {
      throw err
    },
  })
}
