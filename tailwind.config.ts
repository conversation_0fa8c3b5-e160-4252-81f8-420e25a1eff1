import type { Config } from 'tailwindcss'

const config: Config = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      fontSize: {
        base: ['0.875rem', '1.25rem'],
      },
      flexBasis: {
        '1/8': '12.5%',
      },
      backgroundImage: {
        'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
        'gradient-conic':
          'conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))',
      },
      // Semantic colors for reference: https://www.figma.com/design/8FMDmlreLh9TY4ClBDtzus/Unify-Design-System-(Foundations)?node-id=0-1&t=edjyJdE0rEagcmQC-0
      colors: {
        'app-color': {
          primary: '#182230',
          secondary: '#667085',
          'fg-error-primary': '#B42318',
          'bg-secondary': '#F9FAFB',
          'bg-disabled': '#D0D5DD',
          'text-primary-800': '#182230',
          'text-secondary-hover': '#61646C',
          'button-primary-error-bg': '#B42318',
          'button-primary-error-bg-hover': '#D92D20',
          'fg-success-secondary': '#079455',
          'bg-success-primary': '#ECFDF3',
          'bg-brand-primary': '#EEF2FF',
          'bg-brand-secondary': '#E0E7FF',
          'bg-brand-solid': '#4F46E5',
          'fg-brand-primary': '#4F46E5',
          'button-primary-bg': '#4F46E5',
          'text-secondary': '#667085',
          'border-error': '#FDA29B',
          'bg-error-primary': '#FEF3F2',
          'bg-warning-primary': '#FFFAEB',
          'fg-warning-secondary': '#DC6803',
        },
      },
    },
  },
  plugins: [require('@tailwindcss/forms')],
}
export default config
