import { test, expect } from '@playwright/test'

test('has title', async ({ page }) => {
  await page.goto('/999')
  await expect(page).toHaveTitle('Revenue Connect')
})

test('navigate to Service catalog', async ({ page }) => {
  await page.goto('/999')

  // Click the get started link.
  await page.getByRole('link', { name: 'Service catalog' }).click()
  await expect(page).toHaveURL(
    /999\/services\/[0-9]{4}-[0-9]{2}-[0-9]{2}\/catalog\/root\?view=column/,
  )
})
