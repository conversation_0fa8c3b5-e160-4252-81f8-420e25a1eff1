import { Locator, <PERSON> } from '@playwright/test'

export function maintainFocus(locator: Locator, page: Page): Locator {
  let currentLocator = locator
  return new Proxy(locator, {
    get(_, property: keyof Locator) {
      if (property !== 'press') return currentLocator[property]

      const press: Locator['press'] = async (key, options) => {
        if (key !== 'Tab') return currentLocator.press(key, options)
        await currentLocator.press(key, options)
        currentLocator = page.locator('*:focus')
      }
      return press
    },
  })
}
