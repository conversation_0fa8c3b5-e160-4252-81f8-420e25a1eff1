import { ServiceCatalogPageObject } from '@/app/[bankId]/services/[effectiveDate]/catalog/_testing/ServiceCatalogPageObject'
import test, { expect } from '@playwright/test'

// ToDo: Disabled until the test strategy for the environments will be defined
test.skip('Service catalog filters', () => {
  test.beforeEach(
    ServiceCatalogPageObject.provider(async (page) => {
      await page.goto('list')
    }),
  )

  test(
    'when a filter value is selected, it correctly filters the list view.',
    ServiceCatalogPageObject.provider(async (page) => {
      const list = page.getView('list')
      await list.selectCategory('ACH AUTO FUND SERVICES')
      await list.selectCategory('PASS-THROUGH')

      const numBeforeFiltering = await list.getNumVisibleServices()
      await list.filter('Service type', ['Volume based'])
      const numAfterFiltering = await list.getNumVisibleServices()

      expect(numAfterFiltering).not.toBeUndefined()
      expect(numBeforeFiltering).toBeGreaterThan(numAfterFiltering!)
    }),
  )

  test(
    'when a filter value is selected, this value persists across catalog navigation.',
    ServiceCatalogPageObject.provider(async (page) => {
      const list = page.getView('list')
      await list.selectCategory('ACH AUTO FUND SERVICES')
      await list.selectCategory('PASS-THROUGH')

      await list.filter('Service type', ['Volume based'])
      const numAfterFiltering = await list.getNumVisibleServices()
      expect(numAfterFiltering).not.toBeUndefined()

      await list.selectCategory('DEPOSIT ADMINISTRATION FEE')
      await list.selectCategory('PASS-THROUGH')

      await expect(list.numVisibleServices()).toContainText(
        `${numAfterFiltering}`,
      )
    }),
  )
})
