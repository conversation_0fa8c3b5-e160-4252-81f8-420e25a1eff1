import { ColumnViewPageObject } from '@/app/[bankId]/services/[effectiveDate]/catalog/_testing/ColumnViewPageObject'
import { ServiceCatalogPageObject } from '@/app/[bankId]/services/[effectiveDate]/catalog/_testing/ServiceCatalogPageObject'
import { test, expect } from '@playwright/test'

// ToDo: Disabled until the test strategy for the environments will be defined
test.skip('Product catalog column view', () => {
  test.beforeEach(
    ServiceCatalogPageObject.provider(async (page) => {
      await page.goto('column')
    }),
  )

  test(
    'Displays root-level categories in the first column',
    ColumnViewPageObject.provider(async (page) => {
      await expect(page.columnLinks(0)).toHaveCount(26)
    }),
  )

  test(
    'Displays the correct count of categories and products',
    ColumnViewPageObject.provider(async (page) => {
      await expect(page.columnCounts(0, { categories: 26 })).toBeVisible()
    }),
  )

  test.describe('After navigation into a root-level category', () => {
    test.beforeEach(
      ColumnViewPageObject.provider(
        async (page) => await page.gotoCategory('ACH AUTO FUND SERVICES'),
      ),
    )

    test('Updates the route', async ({ page }) => {
      await expect(page).toHaveURL(
        /\/services\/[0-9]{4}-[0-9]{2}-[0-9]{2}\/catalog\/AAF\?/,
      )
    })

    test(
      'Displays contents of the root-level category in the second column',
      ColumnViewPageObject.provider(async (page) => {
        const expectedText = [
          'PASS-THROUGH',
          'NON-PRODUCT',
          'LBX NATIONAL',
          'LBX NY WHOLESALE',
          'LBX NY RETAIL',
        ]
        await expect(page.columnLinks(1)).toContainText(expectedText)
      }),
    )

    test(
      'Displays the correct count of categories and products',
      ColumnViewPageObject.provider(async (page) => {
        await expect(
          page.columnCounts(1, { categories: 2, products: 3 }),
        ).toBeVisible()
      }),
    )
  })
})
