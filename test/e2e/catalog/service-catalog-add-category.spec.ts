import { test, expect } from '@playwright/test'
import { messages } from '@/app/[bankId]/services/[effectiveDate]/catalog/notifications'
import { ColumnViewPageObject } from '@/app/[bankId]/services/[effectiveDate]/catalog/_testing/ColumnViewPageObject'
import { ServiceCatalogPageObject } from '@/app/[bankId]/services/[effectiveDate]/catalog/_testing/ServiceCatalogPageObject'
import { maintainFocus } from '../maintain-focus'

// ToDo: Disabled until the test strategy for the environments will be defined
test.skip('Product catalog add category', () => {
  test.beforeEach(
    ServiceCatalogPageObject.provider(async (page) => {
      await page.goto('column')
    }),
  )

  test(
    'When "Add category" is clicked the "Add a product category" modal appears',
    ServiceCatalogPageObject.provider(async (page) => {
      const modal = await page.addCategory()
      await modal.isVisible()
    }),
  )

  test.describe('When a category is added', () => {
    test.beforeEach(
      ServiceCatalogPageObject.provider(async (page) => {
        const columnView = page.getView('column')
        await columnView.gotoCategory('ACH AUTO FUND SERVICES')
        await columnView.columnHasContents(1) // Ensure we've completed navigation into Category A.
        await page.addCategory({ name: 'New category', code: 'NEW' })
      }),
    )

    test(
      'notification is visible',
      ColumnViewPageObject.provider(async (page) => {
        await expect(
          page.getNotification(messages['/addServiceCategory']),
        ).toBeVisible()
      }),
    )
  })

  test(
    '"Add a product category" modal can be navigated with the keyboard',
    ServiceCatalogPageObject.provider(async (page) => {
      const modal = await page.addCategory()
      const focused = maintainFocus(modal.locator, page.page)
      await focused.press('Tab') // X
      await focused.press('Tab') // Category name
      await focused.fill('New category')
      await focused.press('Tab') // Category code
      await focused.fill('NEW')
      await focused.press('Tab') // Cancel
      await focused.press('Tab') // Add
      await expect(focused).toBeEnabled()
      await focused.press('Enter')

      await expect(
        page.getNotification(messages['/addServiceCategory']),
      ).toBeVisible()
    }),
  )
})
