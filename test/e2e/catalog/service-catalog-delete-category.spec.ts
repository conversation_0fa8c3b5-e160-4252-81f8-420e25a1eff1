import { ColumnViewPageObject } from '@/app/[bankId]/services/[effectiveDate]/catalog/_testing/ColumnViewPageObject'
import { messages } from '@/app/[bankId]/services/[effectiveDate]/catalog/notifications'
import { ServiceCatalogPageObject } from '@/app/[bankId]/services/[effectiveDate]/catalog/_testing/ServiceCatalogPageObject'
import { test, expect } from '@playwright/test'

// ToDo: Disabled until the test strategy for the environments will be defined
test.skip('when empty category is deleted', () => {
  test.beforeEach(
    ServiceCatalogPageObject.provider(async (page) => {
      await page.goto('column')
      await page.getView('column').deleteCategory(0, 'Empty Category A')
    }),
  )

  test(
    'category should disappear from the columns list',
    ColumnViewPageObject.provider(async (page) => {
      await expect(
        page.columnLinks(1).getByText('Empty Category A'),
      ).not.toBeVisible()
    }),
  )

  test(
    'notification should appear',
    ServiceCatalogPageObject.provider(async (page) => {
      await expect(
        page.getNotification('Category successfully deleted.'),
      ).toBeVisible()
    }),
  )
})

test.skip('attempt to delete non empty category', () => {
  test.beforeEach(
    ServiceCatalogPageObject.provider(async (page) => {
      await page.goto('column')
    }),
  )

  test(
    'delete button should be disabled',
    ColumnViewPageObject.provider(async (page) => {
      const categoryMenu = await page.openCategoryMenu(
        0,
        'ACH AUTO FUND SERVICES',
      )
      expect(await categoryMenu.deleteIsDisabled()).toBeTruthy()
    }),
  )

  test(
    'show tooltip with explanation',
    ColumnViewPageObject.provider(async (page) => {
      const categoryMenu = await page.openCategoryMenu(
        0,
        'ACH AUTO FUND SERVICES',
      )
      await categoryMenu.hoverDeleteButton()

      await expect(
        page.page.getByRole('tooltip', {
          name: 'Category must be empty to be deleted',
        }),
      ).toBeVisible()
    }),
  )
})
