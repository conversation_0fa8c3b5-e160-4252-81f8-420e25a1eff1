import { expect, test } from '@playwright/test'
import { messages } from '@/app/[bankId]/services/[effectiveDate]/catalog/notifications'
import { ServiceCatalogPageObject } from '@/app/[bankId]/services/[effectiveDate]/catalog/_testing/ServiceCatalogPageObject'
import { ColumnViewPageObject } from '@/app/[bankId]/services/[effectiveDate]/catalog/_testing/ColumnViewPageObject'

// ToDo: Disabled until the test strategy for the environments will be defined
test.skip('Product catalog edit category', () => {
  test.beforeEach(
    ServiceCatalogPageObject.provider(async (page) => {
      await page.goto('column')
    }),
  )

  test.describe('when category is updated', async () => {
    test.beforeEach(
      ColumnViewPageObject.provider(async (page) => {
        await page.editCategory(0, 'ACH AUTO FUND SERVICES', {
          name: 'Edit Cat Name',
          code: 'EDT',
        })
      }),
    )

    test(
      'notification is visible',
      ServiceCatalogPageObject.provider(async (page) => {
        await expect(
          page.getNotification(messages['/updateServiceCategory']),
        ).toBeVisible()
      }),
    )

    test(
      'changes should appear on a column list',
      ColumnViewPageObject.provider(async (page) => {
        await expect(
          page.columnLinks(0).getByText('Edit Cat Name'),
        ).toBeVisible()
      }),
    )
  })
})
