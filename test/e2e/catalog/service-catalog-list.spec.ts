import { messages } from '@/app/[bankId]/services/[effectiveDate]/catalog/notifications'
import { ServiceCatalogPageObject } from '@/app/[bankId]/services/[effectiveDate]/catalog/_testing/ServiceCatalogPageObject'
import { test, expect } from '@playwright/test'

// ToDo: Disabled until the test strategy for the environments will be defined
test.skip('Product catalog list view', () => {
  test.beforeEach(
    ServiceCatalogPageObject.provider(async (page) => {
      await page.goto('list')
    }),
  )

  test.describe('Viewing all nested products', () => {
    test.beforeEach(
      ServiceCatalogPageObject.provider(async (page) => {
        await page.getView('list').viewAllNestedProducts()
      }),
    )

    test(
      'the Category column appears',
      ServiceCatalogPageObject.provider(async (page) => {
        await expect(
          page.getView('list').getColumnHeader('Category'),
        ).toBeVisible()
      }),
    )

    test(
      'the first row is a Service',
      ServiceCatalogPageObject.provider(async (page) => {
        await expect(
          page.getView('list').getRowAndCellByIndex(0, 0),
        ).toHaveText(/^HEALTHCARE/)
      }),
    )

    test(
      'the number of Services is displayed',
      ServiceCatalogPageObject.provider(async (page) => {
        const numVisibleServices = await page
          .getView('list')
          .getNumVisibleServices()
        expect(numVisibleServices).not.toBeUndefined()
      }),
    )
  })

  test.describe('Deleting a category', () => {
    test.describe('when the category is empty', () => {
      test.beforeEach(
        ServiceCatalogPageObject.provider(async (page) => {
          await page.getView('list').deleteCategory('Empty Category B')
        }),
      )

      test(
        'notification should appear.',
        ServiceCatalogPageObject.provider(async (page) => {
          await expect(
            page.getNotification('Category successfully deleted.'),
          ).toBeVisible()
        }),
      )
    })

    test.describe('when the category is not empty', () => {
      test(
        'the delete button is disabled.',
        ServiceCatalogPageObject.provider(async (page) => {
          const categoryMenu = await page
            .getView('list')
            .openCategoryMenu('GENERAL ACH SERVICE')
          expect(await categoryMenu.deleteIsDisabled()).toBeTruthy()
        }),
      )
    })
  })
})
