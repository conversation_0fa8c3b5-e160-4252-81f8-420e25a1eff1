import { PricingTableViewPageObject } from '@/app/[bankId]/pricing/[effectiveDate]/(table-view)/_testing/PricingTableViewPageObject'
import test from '@playwright/test'

test.describe('Pricing', () => {
  test.beforeEach(PricingTableViewPageObject.provider((page) => page.goto()))

  test(
    'Navigation between "Price list" and "Promotions" tabs',
    PricingTableViewPageObject.provider(async (page) => {
      await page.viewPromotions()
      await page.viewPriceList()
    }),
  )

  test(
    'Navigation to "Add price list"',
    PricingTableViewPageObject.provider(async (page) => {
      const priceListTable = await page.viewPriceList()
      await priceListTable.addPriceList()
    }),
  )
})
