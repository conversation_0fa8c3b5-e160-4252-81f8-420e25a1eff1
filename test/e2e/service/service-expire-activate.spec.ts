import { MOCK_DATA } from '@/app/api/v1/mocks/MockData'
import { ServiceDetailsPageObject } from '@/app/[bankId]/services/[effectiveDate]/(service)/_testing/ServiceDetailsPageObject'
import { expect, test } from '@playwright/test'

const testService =
  MOCK_DATA.get('ServiceCatalog').getAll().serviceByCode['B23']

// Expire and reactivate service feature is disabled
test.skip('When service is expired', () => {
  test.beforeEach(
    ServiceDetailsPageObject.provider(async (page) => {
      await page.goto({
        version: testService.effectiveDate,
        serviceCode: testService.code,
      })

      const expireModal = await page.expire()

      const datePicker = await expireModal.openExpireDate()
      await datePicker.selectDay(1)
      await datePicker.apply()

      await expireModal.expireService()
    }),
  )

  test(
    '"Expired" badge should be displayed on the service timeline',
    ServiceDetailsPageObject.provider(async (page) => {
      await expect(page.getButton('Reactivate')).toBeVisible()
      await expect(page.getServiceTimeline().getByText('Expired')).toBeVisible()
    }),
  )

  test.describe('after service is reactivated', () => {
    test.beforeEach(
      ServiceDetailsPageObject.provider(async (page) => {
        const reactivate = await page.reactivate()

        await reactivate.selectDay('Select the service effective date', 2)
        await reactivate.toggleHasExpirationDate()
        await reactivate.selectDay('Expiration date', 3)
        await reactivate.activateService()
      }),
    )

    test(
      'service timeline should display updated date range without "Expired" badge',
      ServiceDetailsPageObject.provider(async (page) => {
        await expect(page.getButton('Expire')).toBeVisible()

        await expect(
          page.getServiceTimeline().getByText('Expired'),
        ).not.toBeVisible()

        await expect(
          page
            .getServiceTimeline()
            .getByText(
              new RegExp('[\\d]{4}-[\\d]{2}-02 - [\\d]{4}-[\\d]{2}-03'),
            ),
        ).toBeVisible()
      }),
    )
  })
})
