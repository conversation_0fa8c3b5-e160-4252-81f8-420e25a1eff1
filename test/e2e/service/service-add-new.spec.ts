import { DispositionLabel, priceTypeLabel } from '@/api/zodSchemas'
import { ServiceDetailsPageObject } from '@/app/[bankId]/services/[effectiveDate]/(service)/_testing/ServiceDetailsPageObject'
import { UpdateServicePageObject } from '@/app/[bankId]/services/[effectiveDate]/(service)/_testing/UpdateServicePageObject'
import { test, expect } from '@playwright/test'

// ToDo: Disabled until the test strategy for the environments will be defined
test.skip('Add new service page', () => {
  test.beforeEach(
    UpdateServicePageObject.provider(async (page) => {
      await page.goto({ parentCategoryCode: 'root' })
    }),
  )

  test(
    'should display "Add new service" text',
    UpdateServicePageObject.provider(async (page) => {
      await expect(page.page.getByText('Add new service')).toBeVisible()
    }),
  )

  test.describe('when new service is created, navigate to service details page', () => {
    test.beforeEach(
      UpdateServicePageObject.provider(async (page) => {
        await page.selectFrom('Service category', 'ACH AUTO FUND SERVICES')
        await page.fill('Service name', 'test_name')
        await page.fill('Service code', 'testcode')
        await page.selectFrom('Price type', 'Not priced')
        await page.selectFrom('Disposition', 'Analyzed')
        await page.confirm('Create')
      }),
    )

    test(
      'should display the details of the service that was created',
      ServiceDetailsPageObject.provider(async (page) => {
        await expect(
          page.getServiceInformation('ACH AUTO FUND SERVICES'),
        ).toBeVisible()
        await expect(page.getServiceInformation('test_name')).toBeVisible()
        await expect(page.getServiceInformation('testcode')).toBeVisible()

        await expect(
          page.getStandardPricing(priceTypeLabel('NOT_PRICED') as string),
        ).toBeVisible()
        await expect(
          page.getStandardPricing('Analyzed' as DispositionLabel),
        ).toBeVisible()
      }),
    )

    test.describe('when service is updated', () => {
      test.beforeEach(
        ServiceDetailsPageObject.provider(async (page) => {
          const editPage = await page.edit()
          await editPage.fill('Service name', 'service_name_changed')
          await editPage.confirm('Save')
        }),
      )

      test(
        'service details page should have updated values',
        ServiceDetailsPageObject.provider(async (page) => {
          await expect(
            page.getServiceInformation('service_name_changed'),
          ).toBeVisible()
        }),
      )
    })
  })
})
