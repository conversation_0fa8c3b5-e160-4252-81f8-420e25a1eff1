import { test, expect } from '@playwright/test'
import { MOCK_DATA } from '@/app/api/v1/mocks/MockData'
import { ServiceDetailsPageObject } from '@/app/[bankId]/services/[effectiveDate]/(service)/_testing/ServiceDetailsPageObject'
import { ColumnViewPageObject } from '@/app/[bankId]/services/[effectiveDate]/catalog/_testing/ColumnViewPageObject'

// Delete service functionality is disabled
test.skip('Open service with processed transactions', () => {
  const serviceWithTransactions =
    MOCK_DATA.get('ServiceCatalog').getAll().serviceByCode['B25']

  test.beforeEach(
    ServiceDetailsPageObject.provider(async (page) => {
      await page.goto({
        serviceCode: serviceWithTransactions.code,
        version: serviceWithTransactions.effectiveDate,
      })
    }),
  )

  test(
    'Delete button should be disabled',
    ServiceDetailsPageObject.provider(async (page) => {
      await expect(
        page.getServiceInformation(serviceWithTransactions.name),
      ).toBeVisible()
      await expect(
        page.page.getByRole('button', { name: 'Delete' }),
      ).toBeDisabled()
    }),
  )
})

// Delete service functionality is disabled
test.skip('Open service details page', () => {
  const columnIndex = 0
  const serviceToDelete =
    MOCK_DATA.get('ServiceCatalog').getAll().serviceByCode['B23']

  test.beforeEach(
    ServiceDetailsPageObject.provider(async (page) => {
      await page.goto({
        serviceCode: serviceToDelete.code,
        version: serviceToDelete.effectiveDate,
        column: columnIndex,
      })
    }),
  )

  test(
    'verify service name and code',
    ServiceDetailsPageObject.provider(async (page) => {
      await expect(
        page.getServiceInformation(serviceToDelete.code),
      ).toBeVisible()

      await expect(
        page.getServiceInformation(serviceToDelete.name),
      ).toBeVisible()
    }),
  )

  test.describe('when service is deleted', () => {
    test.beforeEach(
      ServiceDetailsPageObject.provider(async (page) => {
        const modal = await page.delete()
        await modal.deleteService()
      }),
    )

    test(
      'verify it does not shows up on catalog',
      ColumnViewPageObject.provider(async (page) => {
        expect(
          await page
            .column(columnIndex)
            .getByText(serviceToDelete.name)
            .isVisible(),
        ).toBe(false)
      }),
    )
  })
})
