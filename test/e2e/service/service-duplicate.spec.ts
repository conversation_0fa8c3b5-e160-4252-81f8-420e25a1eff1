import { MOCK_DATA } from '@/app/api/v1/mocks/MockData'
import { ServiceDetailsPageObject } from '@/app/[bankId]/services/[effectiveDate]/(service)/_testing/ServiceDetailsPageObject'
import { expect, test } from '@playwright/test'

const serviceToDuplicate =
  MOCK_DATA.get('ServiceCatalog').getAll().serviceByCode['B23']

// ToDo: Disabled until the test strategy for the environments will be defined
test.skip('Duplicate service', () => {
  test.beforeEach(
    ServiceDetailsPageObject.provider(async (page) => {
      await page.goto({
        serviceCode: serviceToDuplicate.code,
        version: serviceToDuplicate.effectiveDate,
      })

      const editPage = await page.duplicate()

      await editPage.fill(
        'Service name',
        `${serviceToDuplicate.name} - Duplicate`,
      )
      await editPage.fill('Service code', `${serviceToDuplicate.code}DUP`)
      await editPage.confirm('Create')
    }),
  )

  test(
    'verify that duplicate service is created',
    ServiceDetailsPageObject.provider(async (page) => {
      await expect(
        page.getServiceInformation(`${serviceToDuplicate.name} - Duplicate`),
      ).toBeVisible()

      await expect(
        page.getServiceInformation(`${serviceToDuplicate.code}DUP`),
      ).toBeVisible()
    }),
  )
})
