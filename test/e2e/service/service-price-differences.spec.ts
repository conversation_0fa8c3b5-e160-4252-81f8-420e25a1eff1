import { PricingDifferencesPage } from '@/app/[bankId]/services/[effectiveDate]/(service)/view/_components/PricingDifferencesPageObject'
import { test, expect } from '@playwright/test'

// Known service with mocked server data containing one price, one promotion, and one account override
const serviceCode = 'B24'
const effectiveDate = '2024-10-25'

// ToDo: Disabled until the test strategy for the environments will be defined
test.skip('Pricing differences view', () => {
  test.beforeEach(
    PricingDifferencesPage.provider(async (page) => {
      await page.goto(serviceCode, effectiveDate)
    }),
  )

  test(
    'Price lists tab renders correctly',
    PricingDifferencesPage.provider(async (page) => {
      await page.switchTab('Price lists')

      await expect(page.getColumnHeader('Price list name')).toBeVisible()
      await expect(page.getColumnHeader('Price list ID')).toBeVisible()
      await expect(page.getColumnHeader('Lead price list')).toBeVisible()
      await expect(page.getColumnHeader('Price type')).toBeVisible()
      await expect(page.getColumnHeader('Price')).toBeVisible()
      await expect(page.getColumnHeader('Disposition')).toBeVisible()

      await expect(page.getRowAndCellByIndex(0, 0)).toHaveText('CAD Standard')
      await expect(page.getRowAndCellByIndex(0, 1)).toHaveText('123')
      await expect(page.getRowAndCellByIndex(0, 2)).toHaveText('Yes')
      await expect(page.getRowAndCellByIndex(0, 3)).toHaveText('Unit price')
      await expect(page.getRowAndCellByIndex(0, 4)).toHaveText('$12000.00')
      await expect(page.getRowAndCellByIndex(0, 5)).toHaveText('Analyzed')
    }),
  )

  test(
    'Promotions lists tab renders correctly',
    PricingDifferencesPage.provider(async (page) => {
      await page.switchTab('Promotions')

      await expect(page.getColumnHeader('Price list name')).toBeVisible()
      await expect(page.getColumnHeader('Price list ID')).toBeVisible()
      await expect(page.getColumnHeader('Expiration date')).toBeVisible()
      await expect(page.getColumnHeader('Number of accounts')).toBeVisible()
      await expect(page.getColumnHeader('Price Type')).toBeVisible()
      await expect(page.getColumnHeader('Price')).toBeVisible()
      await expect(page.getColumnHeader('Disposition')).toBeVisible()

      await expect(page.getRowAndCellByIndex(0, 0)).toHaveText('Demo Promotion')
      await expect(page.getRowAndCellByIndex(0, 1)).toHaveText('PRO')
      await expect(page.getRowAndCellByIndex(0, 2)).toHaveText('03/09/2026')
      await expect(page.getRowAndCellByIndex(0, 3)).toHaveText('20')
      await expect(page.getRowAndCellByIndex(0, 4)).toHaveText('Threshold tier')
      await expect(page.getRowAndCellByIndex(0, 5)).toHaveText(
        '2 tiers available',
      )
      await expect(page.getRowAndCellByIndex(0, 6)).toHaveText('Analyzed')
    }),
  )

  test(
    'Account overrides tab renders correctly',
    PricingDifferencesPage.provider(async (page) => {
      await page.switchTab('Account overrides')

      await expect(page.getColumnHeader('Account name')).toBeVisible()
      await expect(page.getColumnHeader('Account number')).toBeVisible()
      await expect(page.getColumnHeader('Expiration date')).toBeVisible()
      await expect(page.getColumnHeader('Price Type')).toBeVisible()
      await expect(page.getColumnHeader('Price')).toBeVisible()
      await expect(page.getColumnHeader('Disposition')).toBeVisible()

      await expect(page.getRowAndCellByIndex(0, 0)).toHaveText(
        'Simple override',
      )
      await expect(page.getRowAndCellByIndex(0, 1)).toHaveText('SAO')
      await expect(page.getRowAndCellByIndex(0, 2)).toHaveText('03/09/2026')
      await expect(page.getRowAndCellByIndex(0, 3)).toHaveText('Threshold tier')
      await expect(page.getRowAndCellByIndex(0, 4)).toHaveText('$0.99')
      await expect(page.getRowAndCellByIndex(0, 5)).toHaveText('Analyzed')
    }),
  )
})
