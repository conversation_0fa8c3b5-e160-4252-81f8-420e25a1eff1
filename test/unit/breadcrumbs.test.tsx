import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import {
  Breadcrumbs,
  BreadcrumbSegment,
} from '@/components/Breadcrumbs/Breadcrumbs'
import '@testing-library/jest-dom'

// Mock Next.js Link component
jest.mock('next/link', () => {
  const Link = ({
    children,
    href,
  }: {
    children: React.ReactNode
    href: string
  }) => <a href={href}>{children}</a>
  Link.displayName = 'Link'
  return Link
})

describe('Breadcrumbs component (Next.js)', () => {
  const segments = [
    { title: 'Home', href: '/' },
    { title: 'Library', href: '/library' },
    { title: 'Data', href: '/library/data' },
    { title: 'Details', href: '/library/data/details' },
  ]

  const renderBreadcrumbs = (truncate?: number) =>
    render(
      <Breadcrumbs truncate={truncate}>
        {segments.map((seg) => (
          <BreadcrumbSegment key={seg.href} href={seg.href}>
            {seg.title}
          </BreadcrumbSegment>
        ))}
      </Breadcrumbs>,
    )

  it('renders all breadcrumb segments when not truncated', () => {
    renderBreadcrumbs()

    segments.forEach((seg) => {
      expect(screen.getByText(seg.title)).toBeInTheDocument()
    })

    expect(screen.queryByLabelText('More')).not.toBeInTheDocument()
  })

  it('renders truncated breadcrumbs with ellipsis and visible segments', () => {
    renderBreadcrumbs(2)

    // Only last 2 segments should be visible
    expect(screen.getByText('Data')).toBeInTheDocument()
    expect(screen.getByText('Details')).toBeInTheDocument()

    // First segments should not be visible
    expect(screen.queryByText('Home')).not.toBeInTheDocument()
    expect(screen.queryByText('Library')).not.toBeInTheDocument()

    // Ellipsis button should be present
    expect(screen.getByLabelText('More')).toBeInTheDocument()
  })

  it('shows all segments in popover when ellipsis is clicked', () => {
    renderBreadcrumbs(2)

    const ellipsisButton = screen.getByLabelText('More')
    fireEvent.click(ellipsisButton)

    // All segments should be in the popover panel
    segments.forEach((seg) => {
      expect(screen.getAllByText(seg.title).length).toBeGreaterThan(0)
    })
  })
})
