import { toUIFormat } from '@/lib/date'

describe('Dates', () => {
  const date = '2025-05-01'

  describe('describe title', () => {
    it('test string', () => {
      const dateTest = toUIFormat(date)
      const dateObj = new Date(2025, 4, 1) // Note: Month is 0-indexed
      expect(dateTest).toEqual(date)
    })

    it('test date', () => {
      const dateObj = new Date(2025, 4, 1)
      const dateTestObj = toUIFormat(dateObj)
      expect(dateTestObj).toEqual(date)
    })
  })
})
