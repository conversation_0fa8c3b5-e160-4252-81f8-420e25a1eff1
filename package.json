{"name": "af-fpb", "version": "1.0.0", "description": "FIS Pricing & Billing Frontend", "private": true, "scripts": {"dev": "next dev --turbopack --experimental-https", "e2e": "npx playwright test", "test:unit": "jest --collectCoverage --", "prettier:fix": "prettier --write \"./**/*.{ts,tsx}\"", "build": "next build", "start": "next start", "lint": "next lint --dir ./test ./src && prettier --check --ignore-unknown ./src ./test", "format": "next lint --fix --dir ./test ./src && prettier --write --list-different --ignore-unknown ./src ./test", "prepare": "husky", "codegen": "openapi-typescript src/api/schema.json --output src/api/schema.d.ts --export-type --array-length && npm run format", "schemagen": "npx tsx ./src/api/generateZodSchemas.ts", "ts": "tsc --noEmit --watch", "test:ts": "tsc --noEmit", "sqlgen": "npx tsx ./sql-data/generateSql.ts", "dbcleanup": "psql -d postgres -f ./sql-data/cleanup.sql", "dbinsert": "psql -d postgres -f ./sql-data/insert.sql"}, "dependencies": {"@dagrejs/dagre": "^1.1.4", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@headlessui/react": "^2.2.0", "@heroicons/react": "^2.1.5", "@lexical/react": "^0.27.0", "@melloware/coloris": "^0.24.0", "@tanstack/react-form": "^0.42.1", "@tanstack/react-query": "^5.59.20", "@tanstack/react-query-devtools": "^5.59.20", "@tanstack/react-table": "^8.20.5", "@tanstack/react-virtual": "^3.10.9", "@xyflow/react": "^12.4.4", "clsx": "^2.1.1", "dagre": "^0.8.5", "date-fns": "^4.1.0", "decimal.js": "^10.5.0", "lexical": "^0.27.0", "next": "15.3.1", "react": "19.0.0", "react-apexcharts": "^1.5.0", "react-day-picker": "^9.3.0", "react-dom": "19.0.0", "zod": "^3.24.1"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.16.0", "@playwright/test": "^1.45.3", "@tailwindcss/forms": "^0.5.9", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/jest": "^29.5.14", "@types/node": "^20", "@types/react": "19.0.0", "@types/react-dom": "19.0.0", "eslint": "^9.16.0", "eslint-config-next": "15.0.4", "eslint-config-prettier": "9.1.0", "husky": "^9.1.4", "jest": "^30.0.0", "jest-environment-jsdom": "^30.0.0", "lint-staged": "^15.2.7", "openapi-typescript": "^7.4.4", "postcss": "^8", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.5", "tailwindcss": "^3.4.1", "ts-node": "^10.9.2", "typescript": "^5.7.3"}, "overrides": {"react": "19.0.0", "react-dom": "19.0.0"}}